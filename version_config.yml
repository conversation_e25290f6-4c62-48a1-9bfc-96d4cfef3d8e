# IEC61850设计检查器版本配置文件

project:
  name: "iec61850-design-checker"
  version: "0.1.0"
  description: "专业的IEC61850智能变电站二次设计验证工具"
  author: "Development Team"
  license: "MIT"
  homepage: "https://github.com/company/iec61850-design-checker"
  repository: "https://github.com/company/iec61850-design-checker.git"
  min_python_version: "3.9"

# 模块版本管理
modules:
  core-data-model:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      pydantic: ">=1.8.0,<2.0.0"
      sqlalchemy: ">=1.4.0,<2.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  xml-parser:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      lxml: ">=4.6.0,<5.0.0"
      xmlschema: ">=1.7.0,<2.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  knowledge-base:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      spacy: ">=3.4.0,<4.0.0"
      neo4j: ">=4.4.0,<5.0.0"
      networkx: ">=2.6.0,<3.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  validation-engine:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      xml-parser: ">=1.0.0,<2.0.0"
      knowledge-base: ">=1.0.0,<2.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  comparison-engine:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      xml-parser: ">=1.0.0,<2.0.0"
      difflib: "*"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  interop-checker:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      validation-engine: ">=1.0.0,<2.0.0"
      knowledge-base: ">=1.0.0,<2.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  scd-generator:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      xml-parser: ">=1.0.0,<2.0.0"
      jinja2: ">=3.0.0,<4.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  virtual-terminal-generator:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      xml-parser: ">=1.0.0,<2.0.0"
      openpyxl: ">=3.0.0,<4.0.0"
      reportlab: ">=3.6.0,<4.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []
    
  gui-application:
    version: "1.0.0"
    api_version: "1.0"
    dependencies:
      core-data-model: ">=1.0.0,<2.0.0"
      validation-engine: ">=1.0.0,<2.0.0"
      comparison-engine: ">=1.0.0,<2.0.0"
      scd-generator: ">=1.0.0,<2.0.0"
      virtual-terminal-generator: ">=1.0.0,<2.0.0"
      PySide6: ">=6.2.0,<7.0.0"
    compatible_versions: ["1.0.0"]
    deprecated_versions: []

# 第三方依赖管理
dependencies:
  python_packages:
    # 核心依赖
    pydantic: ">=1.8.0,<2.0.0"
    sqlalchemy: ">=1.4.0,<2.0.0"
    lxml: ">=4.6.0,<5.0.0"
    PySide6: ">=6.2.0,<7.0.0"
    
    # 数据处理
    pandas: ">=1.3.0,<2.0.0"
    numpy: ">=1.21.0,<2.0.0"
    
    # 自然语言处理
    spacy: ">=3.4.0,<4.0.0"
    
    # 图数据库
    neo4j: ">=4.4.0,<5.0.0"
    networkx: ">=2.6.0,<3.0.0"
    
    # 文档处理
    openpyxl: ">=3.0.0,<4.0.0"
    reportlab: ">=3.6.0,<4.0.0"
    jinja2: ">=3.0.0,<4.0.0"
    
    # 测试工具
    pytest: ">=6.2.0,<7.0.0"
    pytest-cov: ">=2.12.0,<3.0.0"
    pytest-mock: ">=3.6.0,<4.0.0"
    
    # 代码质量
    pylint: ">=2.9.0,<3.0.0"
    mypy: ">=0.910,<1.0.0"
    black: ">=21.6.0,<22.0.0"
    isort: ">=5.9.0,<6.0.0"
    
    # 安全检查
    bandit: ">=1.7.0,<2.0.0"
    safety: ">=1.10.0,<2.0.0"

# 兼容性矩阵
compatibility_matrix:
  "1.0.0":
    compatible_with:
      core-data-model: ["1.0.0"]
      xml-parser: ["1.0.0"]
      knowledge-base: ["1.0.0"]
      validation-engine: ["1.0.0"]
      comparison-engine: ["1.0.0"]
      interop-checker: ["1.0.0"]
      scd-generator: ["1.0.0"]
      virtual-terminal-generator: ["1.0.0"]
      gui-application: ["1.0.0"]
    
    breaking_changes: []
    migration_guide: null

# 发布渠道配置
release_channels:
  stable:
    branch: "main"
    auto_deploy: false
    approval_required: true
    testing_required: "full"
    notification_channels: ["email", "slack"]
    
  beta:
    branch: "develop"
    auto_deploy: true
    approval_required: false
    testing_required: "integration"
    notification_channels: ["slack"]
    
  alpha:
    branch: "feature/*"
    auto_deploy: true
    approval_required: false
    testing_required: "unit"
    notification_channels: ["slack"]

# 版本发布配置
release_config:
  build_artifacts:
    - "windows-x64-installer.exe"
    - "linux-x64-appimage"
    - "macos-universal-dmg"
    - "source-distribution.tar.gz"
    - "documentation.zip"
    
  pre_release_checks:
    - "unit_tests"
    - "integration_tests"
    - "security_scan"
    - "dependency_check"
    - "compatibility_check"
    
  post_release_actions:
    - "update_documentation"
    - "notify_users"
    - "update_download_page"
    - "create_release_notes"

# 依赖更新策略
dependency_update_policy:
  security_updates:
    auto_apply: true
    max_delay_hours: 24
    notification_required: true
    
  minor_updates:
    auto_apply: false
    review_required: true
    test_required: true
    approval_required: false
    
  major_updates:
    auto_apply: false
    manual_review: true
    compatibility_test: true
    migration_plan_required: true
    approval_required: true

# 版本保留策略
version_retention_policy:
  stable_versions:
    keep_latest: 5
    keep_lts: true
    minimum_retention_days: 365
    
  beta_versions:
    keep_latest: 3
    minimum_retention_days: 90
    
  alpha_versions:
    keep_latest: 10
    minimum_retention_days: 30

# 监控和告警
monitoring:
  version_usage_tracking: true
  download_statistics: true
  error_reporting: true
  performance_monitoring: true
  
  alerts:
    security_vulnerability: "immediate"
    compatibility_issue: "high"
    performance_degradation: "medium"
    usage_anomaly: "low"
