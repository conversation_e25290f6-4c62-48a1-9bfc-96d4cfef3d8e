#!/usr/bin/env python3
"""
创建专门的报告目录结构
"""

import os
from pathlib import Path
from datetime import datetime


def create_report_structure():
    """创建报告目录结构"""
    
    print("📁 创建专门的报告目录结构")
    print("=" * 50)
    
    # 主报告目录
    base_dir = Path("design_reports")
    
    # 子目录结构
    subdirs = {
        "fault_reports": "故障检测报告",
        "design_check": "设计检查报告", 
        "circuit_analysis": "回路分析报告",
        "compliance_check": "合规性检查报告",
        "virtual_terminal": "虚端子分析报告",
        "communication": "通信配置报告",
        "protection": "保护配置报告",
        "measurement": "测量回路报告",
        "control": "控制回路报告",
        "archives": "历史报告存档"
    }
    
    # 创建目录结构
    for subdir, description in subdirs.items():
        dir_path = base_dir / subdir
        dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建README文件
        readme_file = dir_path / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(f"# {description}\n\n")
            f.write(f"本目录用于存放{description}相关文件。\n\n")
            f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("## 文件命名规范\n\n")
            f.write("- 格式: `{report_type}_{timestamp}_{project_name}.{ext}`\n")
            f.write("- 示例: `fault_check_20250818_001500_substation220kv.html`\n\n")
            f.write("## 支持的文件格式\n\n")
            f.write("- `.html` - 可视化报告\n")
            f.write("- `.json` - 结构化数据\n")
            f.write("- `.txt` - 文本报告\n")
            f.write("- `.pdf` - PDF文档\n")
            f.write("- `.xlsx` - Excel表格\n")
        
        print(f"✅ 创建目录: {dir_path} - {description}")
    
    # 移动现有报告到新目录
    old_reports_dir = Path("reports")
    if old_reports_dir.exists():
        print(f"\n📦 迁移现有报告文件...")
        
        # 移动设计检查报告
        for report_file in old_reports_dir.glob("design_check_report_*"):
            new_path = base_dir / "design_check" / report_file.name
            report_file.rename(new_path)
            print(f"   📄 移动: {report_file.name} → design_check/")
        
        # 移动统一审查报告
        for report_file in old_reports_dir.glob("unified_review_test_report*"):
            new_path = base_dir / "compliance_check" / report_file.name
            report_file.rename(new_path)
            print(f"   📄 移动: {report_file.name} → compliance_check/")
        
        # 移动其他报告到存档
        for report_file in old_reports_dir.glob("*"):
            if report_file.is_file():
                new_path = base_dir / "archives" / report_file.name
                report_file.rename(new_path)
                print(f"   📄 移动: {report_file.name} → archives/")
    
    # 创建主README文件
    main_readme = base_dir / "README.md"
    with open(main_readme, 'w', encoding='utf-8') as f:
        f.write("# 设计报告中心\n\n")
        f.write("智能变电站设计质量检测与分析报告集中管理。\n\n")
        f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## 目录结构\n\n")
        for subdir, description in subdirs.items():
            f.write(f"- `{subdir}/` - {description}\n")
        f.write("\n## 使用说明\n\n")
        f.write("1. 根据报告类型选择相应目录\n")
        f.write("2. 遵循文件命名规范\n")
        f.write("3. 定期清理历史报告到archives目录\n")
        f.write("4. 使用report_manager.py管理报告文件\n")
    
    print(f"\n✅ 报告目录结构创建完成!")
    print(f"📍 主目录: {base_dir.absolute()}")
    
    return base_dir


if __name__ == "__main__":
    create_report_structure()
