#!/usr/bin/env python3
"""
启动Web应用
包含图纸审查功能的IEC61850设计检查器
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.web.app import create_app
except ImportError as e:
    print(f"导入错误: {e}")
    print("尝试使用简化版本...")
    from src.web.views import main_bp
    from flask import Flask

    def create_app():
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'dev-secret-key'
        app.config['UPLOAD_FOLDER'] = 'uploads'
        app.register_blueprint(main_bp)
        return app

def main():
    """启动Web应用"""
    print("正在启动IEC61850设计检查器...")
    print("包含图纸审查功能")
    
    # 创建应用
    app = create_app()
    
    # 创建上传目录
    upload_dir = project_root / "uploads"
    upload_dir.mkdir(exist_ok=True)
    
    print(f"上传目录: {upload_dir}")
    print("支持的图纸格式: DWG, DXF, PDF")
    print("访问地址: http://localhost:5000")
    print("图纸审查页面: http://localhost:5000/drawing-review")
    
    # 启动应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )

if __name__ == "__main__":
    main()
