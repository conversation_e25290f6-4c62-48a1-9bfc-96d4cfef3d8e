{"保护回路A": {"CT输入": "LZZBJ9-252 1S1-1S2 → RCS-978A", "PT输入": "JDZ10-252 a-x,b-y,c-z → RCS-978A", "跳闸出口": "RCS-978A → LW36-252 YT1", "电源": "DC 220V-I → RCS-978A", "GOOSE发送": "RCS-978A → 过程层网络"}, "保护回路B": {"CT输入": "LZZBJ9-252 2S1-2S2 → CSC-278A", "PT输入": "JDZ10-252 a-x,b-y,c-z → CSC-278A", "跳闸出口": "CSC-278A → LW36-252 YT2", "电源": "DC 220V-II → CSC-278A", "GOOSE发送": "CSC-278A → 过程层网络"}, "测量回路": {"CT输入": "LZZBJ9-252 3S1-3S2 → RCS-9611CS", "PT输入": "JDZ10-252 a-x,b-y,c-z → RCS-9611CS", "电源": "DC 220V → RCS-9611CS", "通信": "RCS-9611CS → 站控层网络"}, "控制回路": {"合闸回路": "RCS-9611CS → RCS-9611IT → LW36-252 YC", "分闸回路": "RCS-9611CS → RCS-9611IT → LW36-252 YT1/YT2", "位置信号": "LW36-252辅助触点 → RCS-9611IT → RCS-9611CS", "电源": "DC 220V → 控制回路"}}