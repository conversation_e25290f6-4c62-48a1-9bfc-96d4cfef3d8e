<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整的变电站二次回路图系统</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 30px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .tabs { display: flex; flex-wrap: wrap; border-bottom: 2px solid #ddd; margin-bottom: 20px; }
        .tab { padding: 12px 20px; cursor: pointer; border: none; background: none; font-size: 14px; margin: 2px; border-radius: 5px 5px 0 0; }
        .tab.active { background-color: #667eea; color: white; }
        .tab:hover { background-color: #f0f0f0; }
        .tab.active:hover { background-color: #5a6fd8; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .circuit-container { text-align: center; padding: 20px; }
        .circuit-description { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: left; }
        .circuit-specs { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 20px; }
        .spec-item { background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #667eea; }
        .overview { background: #e8f5e8; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 完整的变电站二次回路图系统</h1>
            <p>基于IEC61850虚端子技术的九大功能回路</p>
            <p>符合GB/T 4728电气图形符号标准</p>
        </div>

        <div class="overview">
            <h3>📋 二次回路系统概述</h3>
            <p>电气二次回路是指为对一次电气设备进行控制、保护、测量、信号、调节和操作等而设置的低压电气连接回路，其电压等级一般为直流220V、110V、48V或交流220V、380V等。</p>
            <p>二次回路虽然不直接参与电能传输，但它是电力系统安全、稳定、经济运行的"神经中枢"和"控制大脑"。</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('control')">🎛️ 控制回路</button>
            <button class="tab" onclick="showTab('protection')">🛡️ 保护回路</button>
            <button class="tab" onclick="showTab('measurement')">📊 测量回路</button>
            <button class="tab" onclick="showTab('signal')">💡 信号回路</button>
            <button class="tab" onclick="showTab('dc_power')">🔋 直流电源</button>
        </div>
        <div id="control" class="tab-content active">
            <div class="circuit-description">
                <h3>控制回路 (Control Circuit)</h3>
                <p><strong>功能描述：</strong>用于对一次设备（主要是断路器、隔离开关等）进行手动或自动控制，实现设备的合闸、分闸操作。</p>

                <div class="circuit-specs">
                    <div class="spec-item">
                        <h4>⚡ 电压等级</h4>
                        <p>DC 220V/110V</p>
                    </div>
                    <div class="spec-item">
                        <h4>🔧 主要组成</h4>
                        <ul>
                            <li>控制开关</li><li>合闸线圈</li><li>跳闸线圈</li><li>辅助触点</li><li>防跳回路</li><li>位置信号</li>
                        </ul>
                    </div>
                    <div class="spec-item">
                        <h4>🌟 技术特点</h4>
                        <ul>
                            <li>远程/就地控制</li><li>防误操作</li><li>状态监视</li><li>虚端子GOOSE控制</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="circuit-container">
                <object data="control_circuit_comprehensive.svg" type="image/svg+xml" width="100%" height="700">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
        <div id="protection" class="tab-content ">
            <div class="circuit-description">
                <h3>保护回路 (Protection Circuit)</h3>
                <p><strong>功能描述：</strong>当电力系统发生故障（如短路、过载、接地等）时，保护装置快速检测并动作，通过跳闸回路使相关断路器跳闸。</p>

                <div class="circuit-specs">
                    <div class="spec-item">
                        <h4>⚡ 电压等级</h4>
                        <p>DC 220V/110V</p>
                    </div>
                    <div class="spec-item">
                        <h4>🔧 主要组成</h4>
                        <ul>
                            <li>继电保护装置</li><li>CT/PT二次回路</li><li>跳闸线圈</li><li>保护出口继电器</li>
                        </ul>
                    </div>
                    <div class="spec-item">
                        <h4>🌟 技术特点</h4>
                        <ul>
                            <li>快速动作</li><li>选择性保护</li><li>SV采样值</li><li>GOOSE跳闸</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="circuit-container">
                <object data="protection_circuit_comprehensive.svg" type="image/svg+xml" width="100%" height="700">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
        <div id="measurement" class="tab-content ">
            <div class="circuit-description">
                <h3>测量回路 (Measurement Circuit)</h3>
                <p><strong>功能描述：</strong>对电力系统中的各种电气参数进行实时监测，如电流、电压、功率、频率、电能等。</p>

                <div class="circuit-specs">
                    <div class="spec-item">
                        <h4>⚡ 电压等级</h4>
                        <p>5A/1A, 100V</p>
                    </div>
                    <div class="spec-item">
                        <h4>🔧 主要组成</h4>
                        <ul>
                            <li>电流互感器</li><li>电压互感器</li><li>测量仪表</li><li>变送器</li><li>合并单元</li>
                        </ul>
                    </div>
                    <div class="spec-item">
                        <h4>🌟 技术特点</h4>
                        <ul>
                            <li>高精度测量</li><li>数字化采样</li><li>远程传输</li><li>多参数监测</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="circuit-container">
                <object data="measurement_circuit_comprehensive.svg" type="image/svg+xml" width="100%" height="700">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
        <div id="signal" class="tab-content ">
            <div class="circuit-description">
                <h3>信号回路 (Signal Circuit)</h3>
                <p><strong>功能描述：</strong>用于反映一次设备的运行状态、保护动作情况、异常与故障信息，通过灯光、音响、显示屏等方式通知运行人员。</p>

                <div class="circuit-specs">
                    <div class="spec-item">
                        <h4>⚡ 电压等级</h4>
                        <p>DC 220V/AC 220V</p>
                    </div>
                    <div class="spec-item">
                        <h4>🔧 主要组成</h4>
                        <ul>
                            <li>状态信号</li><li>保护动作信号</li><li>异常信号</li><li>故障信号</li><li>信号继电器</li><li>音响装置</li>
                        </ul>
                    </div>
                    <div class="spec-item">
                        <h4>🌟 技术特点</h4>
                        <ul>
                            <li>快速响应</li><li>分类显示</li><li>远程传输</li><li>音光告警</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="circuit-container">
                <object data="signal_circuit_comprehensive.svg" type="image/svg+xml" width="100%" height="700">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
        <div id="dc_power" class="tab-content ">
            <div class="circuit-description">
                <h3>直流电源回路 (DC Power Supply)</h3>
                <p><strong>功能描述：</strong>为二次回路中的控制、保护、信号、自动装置等提供稳定可靠的直流操作电源，是二次系统的"心脏"。</p>

                <div class="circuit-specs">
                    <div class="spec-item">
                        <h4>⚡ 电压等级</h4>
                        <p>DC 220V/110V/48V</p>
                    </div>
                    <div class="spec-item">
                        <h4>🔧 主要组成</h4>
                        <ul>
                            <li>蓄电池组</li><li>充电装置</li><li>直流母线</li><li>绝缘监测装置</li>
                        </ul>
                    </div>
                    <div class="spec-item">
                        <h4>🌟 技术特点</h4>
                        <ul>
                            <li>不间断供电</li><li>双系统备用</li><li>绝缘监测</li><li>故障告警</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="circuit-container">
                <object data="dc_power_circuit_comprehensive.svg" type="image/svg+xml" width="100%" height="700">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            var contents = document.getElementsByClassName('tab-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].classList.remove('active');
            }

            // 移除所有标签的active类
            var tabs = document.getElementsByClassName('tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>