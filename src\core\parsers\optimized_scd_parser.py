"""
优化的SCD文件解析器
支持大文件流式解析和性能优化
"""

import os
import logging
from typing import Dict, List, Any, Optional, Iterator
from pathlib import Path
import xml.etree.ElementTree as ET
from xml.etree.ElementTree import iterparse
import mmap

from .scd_parser import SCDParser
from ..performance.performance_optimizer import (
    PerformanceOptimizer, StreamingXMLParser, optimize_performance, with_memory_monitor
)


logger = logging.getLogger(__name__)


class OptimizedSCDParser(SCDParser):
    """优化的SCD文件解析器"""
    
    def __init__(self, validate_schema: bool = True, strict_mode: bool = False):
        """初始化优化解析器"""
        super().__init__(validate_schema, strict_mode)
        self.optimizer = PerformanceOptimizer()
        self.streaming_parser = StreamingXMLParser()
        self.large_file_threshold = 50 * 1024 * 1024  # 50MB
        
        logger.info("优化SCD解析器初始化完成")
    
    @optimize_performance
    def parse_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        解析SCD文件，自动选择最优解析策略
        
        Args:
            file_path: SCD文件路径
            
        Returns:
            解析结果字典
        """
        try:
            file_size = os.path.getsize(file_path)
            logger.info(f"开始解析SCD文件: {file_path} (大小: {file_size/1024/1024:.2f}MB)")
            
            # 根据文件大小选择解析策略
            if file_size > self.large_file_threshold:
                logger.info("使用流式解析处理大文件")
                return self._parse_large_file_streaming(file_path)
            else:
                logger.info("使用标准解析处理文件")
                return self._parse_standard(file_path)
                
        except Exception as e:
            logger.error(f"SCD文件解析失败: {e}")
            return None
    
    @with_memory_monitor("大文件流式解析")
    def _parse_large_file_streaming(self, file_path: str) -> Optional[Dict[str, Any]]:
        """流式解析大文件"""
        try:
            result = {
                'header': None,
                'substation': None,
                'ieds': [],
                'communication': None,
                'data_type_templates': None
            }
            
            # 定义元素处理器
            element_handlers = {
                'Header': self._process_header_element,
                'Substation': self._process_substation_element,
                'IED': self._process_ied_element,
                'Communication': self._process_communication_element,
                'DataTypeTemplates': self._process_datatypes_element
            }
            
            # 流式解析
            for parsed_element in self.streaming_parser.parse_large_file(file_path, element_handlers):
                if parsed_element:
                    element_type = parsed_element.get('type')
                    element_data = parsed_element.get('data')
                    
                    if element_type == 'header':
                        result['header'] = element_data
                    elif element_type == 'substation':
                        result['substation'] = element_data
                    elif element_type == 'ied':
                        result['ieds'].append(element_data)
                    elif element_type == 'communication':
                        result['communication'] = element_data
                    elif element_type == 'datatypes':
                        result['data_type_templates'] = element_data
            
            logger.info(f"流式解析完成，解析到 {len(result['ieds'])} 个IED")
            return result
            
        except Exception as e:
            logger.error(f"流式解析失败: {e}")
            return None
    
    def _parse_standard(self, file_path: str) -> Optional[Dict[str, Any]]:
        """标准解析方法"""
        return super().parse_file(file_path)
    
    def parse_multiple_files(self, file_paths: List[str], max_workers: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        并行解析多个文件
        
        Args:
            file_paths: 文件路径列表
            max_workers: 最大工作线程数
            
        Returns:
            解析结果列表
        """
        logger.info(f"开始并行解析 {len(file_paths)} 个文件")
        
        def parse_single_file(file_path: str) -> Dict[str, Any]:
            try:
                result = self.parse_file(file_path)
                return {
                    'file_path': file_path,
                    'success': result is not None,
                    'data': result,
                    'error': None
                }
            except Exception as e:
                return {
                    'file_path': file_path,
                    'success': False,
                    'data': None,
                    'error': str(e)
                }
        
        results = self.optimizer.parallel_process_elements(
            file_paths, parse_single_file, max_workers
        )
        
        successful_count = len([r for r in results if r and r['success']])
        logger.info(f"并行解析完成，成功解析 {successful_count}/{len(file_paths)} 个文件")
        
        return results
    
    def _process_header_element(self, elem) -> Optional[Dict[str, Any]]:
        """处理Header元素"""
        try:
            header_data = {
                'id': elem.get('id'),
                'version': elem.get('version'),
                'revision': elem.get('revision'),
                'toolID': elem.get('toolID'),
                'nameStructure': elem.get('nameStructure')
            }
            
            # 处理Text子元素
            text_elem = elem.find('.//{http://www.iec.ch/61850/2003/SCL}Text')
            if text_elem is not None:
                header_data['text'] = text_elem.text
            
            return {
                'type': 'header',
                'data': header_data
            }
            
        except Exception as e:
            logger.error(f"处理Header元素失败: {e}")
            return None
    
    def _process_substation_element(self, elem) -> Optional[Dict[str, Any]]:
        """处理Substation元素"""
        try:
            substation_data = {
                'name': elem.get('name'),
                'desc': elem.get('desc'),
                'voltage_levels': []
            }
            
            # 处理VoltageLevel子元素
            for vl_elem in elem.findall('.//{http://www.iec.ch/61850/2003/SCL}VoltageLevel'):
                vl_data = {
                    'name': vl_elem.get('name'),
                    'desc': vl_elem.get('desc'),
                    'bays': []
                }
                
                # 处理Bay子元素
                for bay_elem in vl_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}Bay'):
                    bay_data = {
                        'name': bay_elem.get('name'),
                        'desc': bay_elem.get('desc'),
                        'conducting_equipment': []
                    }
                    
                    # 处理ConductingEquipment子元素
                    for ce_elem in bay_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}ConductingEquipment'):
                        ce_data = {
                            'name': ce_elem.get('name'),
                            'type': ce_elem.get('type'),
                            'desc': ce_elem.get('desc')
                        }
                        bay_data['conducting_equipment'].append(ce_data)
                    
                    vl_data['bays'].append(bay_data)
                
                substation_data['voltage_levels'].append(vl_data)
            
            return {
                'type': 'substation',
                'data': substation_data
            }
            
        except Exception as e:
            logger.error(f"处理Substation元素失败: {e}")
            return None
    
    def _process_ied_element(self, elem) -> Optional[Dict[str, Any]]:
        """处理IED元素"""
        try:
            ied_data = {
                'name': elem.get('name'),
                'type': elem.get('type'),
                'manufacturer': elem.get('manufacturer'),
                'configVersion': elem.get('configVersion'),
                'desc': elem.get('desc'),
                'access_points': []
            }
            
            # 处理AccessPoint子元素
            for ap_elem in elem.findall('.//{http://www.iec.ch/61850/2003/SCL}AccessPoint'):
                ap_data = {
                    'name': ap_elem.get('name'),
                    'logical_devices': []
                }
                
                # 处理Server和LDevice
                server_elem = ap_elem.find('.//{http://www.iec.ch/61850/2003/SCL}Server')
                if server_elem is not None:
                    for ld_elem in server_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}LDevice'):
                        ld_data = {
                            'inst': ld_elem.get('inst'),
                            'desc': ld_elem.get('desc'),
                            'logical_nodes': []
                        }
                        
                        # 处理LogicalNode
                        for ln_elem in ld_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}LN'):
                            ln_data = {
                                'lnClass': ln_elem.get('lnClass'),
                                'inst': ln_elem.get('inst'),
                                'lnType': ln_elem.get('lnType'),
                                'desc': ln_elem.get('desc')
                            }
                            ld_data['logical_nodes'].append(ln_data)
                        
                        # 处理LN0
                        ln0_elem = ld_elem.find('.//{http://www.iec.ch/61850/2003/SCL}LN0')
                        if ln0_elem is not None:
                            ln0_data = {
                                'lnClass': ln0_elem.get('lnClass'),
                                'inst': ln0_elem.get('inst'),
                                'lnType': ln0_elem.get('lnType'),
                                'desc': ln0_elem.get('desc')
                            }
                            ld_data['ln0'] = ln0_data
                        
                        ap_data['logical_devices'].append(ld_data)
                
                ied_data['access_points'].append(ap_data)
            
            return {
                'type': 'ied',
                'data': ied_data
            }
            
        except Exception as e:
            logger.error(f"处理IED元素失败: {e}")
            return None
    
    def _process_communication_element(self, elem) -> Optional[Dict[str, Any]]:
        """处理Communication元素"""
        try:
            comm_data = {
                'subnets': []
            }
            
            # 处理SubNetwork
            for subnet_elem in elem.findall('.//{http://www.iec.ch/61850/2003/SCL}SubNetwork'):
                subnet_data = {
                    'name': subnet_elem.get('name'),
                    'type': subnet_elem.get('type'),
                    'desc': subnet_elem.get('desc'),
                    'connected_aps': []
                }
                
                # 处理ConnectedAP
                for cap_elem in subnet_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}ConnectedAP'):
                    cap_data = {
                        'iedName': cap_elem.get('iedName'),
                        'apName': cap_elem.get('apName'),
                        'address': {}
                    }
                    
                    # 处理Address
                    addr_elem = cap_elem.find('.//{http://www.iec.ch/61850/2003/SCL}Address')
                    if addr_elem is not None:
                        for p_elem in addr_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}P'):
                            p_type = p_elem.get('type')
                            p_value = p_elem.text
                            if p_type and p_value:
                                cap_data['address'][p_type] = p_value
                    
                    subnet_data['connected_aps'].append(cap_data)
                
                comm_data['subnets'].append(subnet_data)
            
            return {
                'type': 'communication',
                'data': comm_data
            }
            
        except Exception as e:
            logger.error(f"处理Communication元素失败: {e}")
            return None
    
    def _process_datatypes_element(self, elem) -> Optional[Dict[str, Any]]:
        """处理DataTypeTemplates元素"""
        try:
            dt_data = {
                'lnode_types': [],
                'do_types': [],
                'da_types': [],
                'enum_types': []
            }
            
            # 处理LNodeType
            for lnt_elem in elem.findall('.//{http://www.iec.ch/61850/2003/SCL}LNodeType'):
                lnt_data = {
                    'id': lnt_elem.get('id'),
                    'lnClass': lnt_elem.get('lnClass'),
                    'desc': lnt_elem.get('desc'),
                    'data_objects': []
                }
                
                # 处理DO
                for do_elem in lnt_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}DO'):
                    do_data = {
                        'name': do_elem.get('name'),
                        'type': do_elem.get('type'),
                        'desc': do_elem.get('desc')
                    }
                    lnt_data['data_objects'].append(do_data)
                
                dt_data['lnode_types'].append(lnt_data)
            
            # 处理DOType
            for dot_elem in elem.findall('.//{http://www.iec.ch/61850/2003/SCL}DOType'):
                dot_data = {
                    'id': dot_elem.get('id'),
                    'cdc': dot_elem.get('cdc'),
                    'desc': dot_elem.get('desc'),
                    'data_attributes': []
                }
                
                # 处理DA
                for da_elem in dot_elem.findall('.//{http://www.iec.ch/61850/2003/SCL}DA'):
                    da_data = {
                        'name': da_elem.get('name'),
                        'fc': da_elem.get('fc'),
                        'bType': da_elem.get('bType'),
                        'type': da_elem.get('type'),
                        'desc': da_elem.get('desc')
                    }
                    dot_data['data_attributes'].append(da_data)
                
                dt_data['do_types'].append(dot_data)
            
            return {
                'type': 'datatypes',
                'data': dt_data
            }
            
        except Exception as e:
            logger.error(f"处理DataTypeTemplates元素失败: {e}")
            return None
    
    def get_parsing_statistics(self) -> Dict[str, Any]:
        """获取解析统计信息"""
        return {
            'optimizer_stats': self.optimizer.get_statistics() if hasattr(self.optimizer, 'get_statistics') else {},
            'memory_usage': self.optimizer._get_memory_usage() / 1024 / 1024,  # MB
            'large_file_threshold': self.large_file_threshold / 1024 / 1024  # MB
        }
    
    def cleanup(self):
        """清理资源"""
        if self.optimizer:
            self.optimizer.cleanup_resources()
        
        logger.info("优化SCD解析器资源清理完成")
