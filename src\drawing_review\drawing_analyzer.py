"""
智能图纸分析器
基于AI的图纸内容识别和分析
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple, Set
import numpy as np
from collections import defaultdict, Counter

from .drawing_models import (
    DrawingDocument, DrawingElement, Line, Arc, Circle, Text, Dimension, Block,
    Point, ElementType, LineType, Color
)

logger = logging.getLogger(__name__)


class DrawingAnalyzer:
    """智能图纸分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.element_patterns = self._load_element_patterns()
        self.symbol_library = self._load_symbol_library()
        self.text_patterns = self._load_text_patterns()
        
        logger.info("智能图纸分析器初始化完成")
    
    def analyze_drawing(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析图纸内容"""
        try:
            logger.info(f"开始分析图纸: {drawing.file_path}")
            
            analysis_result = {
                'drawing_info': self._analyze_drawing_info(drawing),
                'element_analysis': self._analyze_elements(drawing),
                'layout_analysis': self._analyze_layout(drawing),
                'text_analysis': self._analyze_text_content(drawing),
                'symbol_analysis': self._analyze_symbols(drawing),
                'connection_analysis': self._analyze_connections(drawing),
                'layer_analysis': self._analyze_layers(drawing),
                'dimension_analysis': self._analyze_dimensions(drawing)
            }
            
            logger.info("图纸分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"图纸分析失败: {e}")
            return {}
    
    def _analyze_drawing_info(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析图纸基本信息"""
        info = {
            'file_type': drawing.file_type,
            'title': drawing.title,
            'drawing_number': drawing.drawing_number,
            'scale': drawing.scale,
            'units': drawing.units,
            'paper_size': drawing.paper_size,
            'total_elements': len(drawing.elements),
            'total_layers': len(drawing.layers),
            'total_blocks': len(drawing.blocks)
        }
        
        # 分析图纸类型
        drawing_type = self._identify_drawing_type(drawing)
        info['drawing_type'] = drawing_type
        
        # 分析图纸复杂度
        complexity = self._calculate_drawing_complexity(drawing)
        info['complexity'] = complexity
        
        return info
    
    def _analyze_elements(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析图元"""
        element_stats = defaultdict(int)
        element_details = defaultdict(list)
        
        for element in drawing.elements:
            element_type = element.element_type.value
            element_stats[element_type] += 1
            
            # 收集详细信息
            if isinstance(element, Line):
                element_details['lines'].append({
                    'length': element.length(),
                    'layer': element.layer,
                    'line_type': element.line_type.value
                })
            elif isinstance(element, Circle):
                element_details['circles'].append({
                    'radius': element.radius,
                    'layer': element.layer
                })
            elif isinstance(element, Text):
                element_details['texts'].append({
                    'content': element.content,
                    'height': element.height,
                    'layer': element.layer
                })
        
        return {
            'element_counts': dict(element_stats),
            'element_details': dict(element_details),
            'total_elements': len(drawing.elements)
        }
    
    def _analyze_layout(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析图纸布局"""
        layout_info = {}
        
        # 计算图纸边界
        if drawing.viewport_bounds:
            min_point, max_point = drawing.viewport_bounds
            layout_info['bounds'] = {
                'min_x': min_point.x,
                'min_y': min_point.y,
                'max_x': max_point.x,
                'max_y': max_point.y,
                'width': max_point.x - min_point.x,
                'height': max_point.y - min_point.y
            }
        
        # 分析元素分布
        element_distribution = self._analyze_element_distribution(drawing.elements)
        layout_info['element_distribution'] = element_distribution
        
        # 分析对齐情况
        alignment_analysis = self._analyze_alignment(drawing.elements)
        layout_info['alignment'] = alignment_analysis
        
        # 分析间距
        spacing_analysis = self._analyze_spacing(drawing.elements)
        layout_info['spacing'] = spacing_analysis
        
        return layout_info
    
    def _analyze_text_content(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析文本内容"""
        text_elements = drawing.get_text_elements()
        
        text_analysis = {
            'total_texts': len(text_elements),
            'text_heights': [],
            'fonts_used': set(),
            'text_categories': defaultdict(list),
            'text_issues': []
        }
        
        for text in text_elements:
            text_analysis['text_heights'].append(text.height)
            text_analysis['fonts_used'].add(text.font)
            
            # 分类文本内容
            category = self._categorize_text(text.content)
            text_analysis['text_categories'][category].append(text.content)
            
            # 检查文本问题
            issues = self._check_text_issues(text)
            text_analysis['text_issues'].extend(issues)
        
        # 统计信息
        if text_analysis['text_heights']:
            text_analysis['height_stats'] = {
                'min': min(text_analysis['text_heights']),
                'max': max(text_analysis['text_heights']),
                'avg': sum(text_analysis['text_heights']) / len(text_analysis['text_heights'])
            }
        
        text_analysis['fonts_used'] = list(text_analysis['fonts_used'])
        text_analysis['text_categories'] = dict(text_analysis['text_categories'])
        
        return text_analysis
    
    def _analyze_symbols(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析电气符号"""
        symbol_analysis = {
            'identified_symbols': [],
            'unknown_symbols': [],
            'symbol_counts': defaultdict(int),
            'symbol_issues': []
        }
        
        # 分析块引用（通常包含符号）
        for block_name, block in drawing.blocks.items():
            symbol_type = self._identify_symbol_type(block)
            if symbol_type:
                symbol_analysis['identified_symbols'].append({
                    'block_name': block_name,
                    'symbol_type': symbol_type,
                    'elements_count': len(block.elements)
                })
                symbol_analysis['symbol_counts'][symbol_type] += 1
            else:
                symbol_analysis['unknown_symbols'].append(block_name)
        
        # 分析几何图形组合（可能是符号）
        potential_symbols = self._identify_geometric_symbols(drawing.elements)
        symbol_analysis['potential_symbols'] = potential_symbols
        
        return symbol_analysis
    
    def _analyze_connections(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析连接关系"""
        connection_analysis = {
            'connection_points': [],
            'wire_segments': [],
            'junction_points': [],
            'connection_issues': []
        }
        
        lines = [elem for elem in drawing.elements if isinstance(elem, Line)]
        
        # 查找连接点
        connection_points = self._find_connection_points(lines)
        connection_analysis['connection_points'] = connection_points
        
        # 分析线段
        wire_segments = self._analyze_wire_segments(lines)
        connection_analysis['wire_segments'] = wire_segments
        
        # 查找交叉点
        junction_points = self._find_junction_points(lines)
        connection_analysis['junction_points'] = junction_points
        
        # 检查连接问题
        connection_issues = self._check_connection_issues(lines, connection_points)
        connection_analysis['connection_issues'] = connection_issues
        
        return connection_analysis
    
    def _analyze_layers(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析图层使用"""
        layer_analysis = {
            'layer_usage': {},
            'layer_issues': [],
            'layer_recommendations': []
        }
        
        # 统计图层使用情况
        for layer_name, layer in drawing.layers.items():
            elements_on_layer = drawing.get_elements_by_layer(layer_name)
            layer_analysis['layer_usage'][layer_name] = {
                'element_count': len(elements_on_layer),
                'color': layer.color.to_hex() if layer.color else None,
                'line_type': layer.line_type.value,
                'visible': layer.visible,
                'locked': layer.locked
            }
        
        # 检查图层问题
        layer_issues = self._check_layer_issues(drawing)
        layer_analysis['layer_issues'] = layer_issues
        
        return layer_analysis
    
    def _analyze_dimensions(self, drawing: DrawingDocument) -> Dict[str, Any]:
        """分析尺寸标注"""
        dimension_elements = drawing.get_dimension_elements()
        
        dimension_analysis = {
            'total_dimensions': len(dimension_elements),
            'dimension_types': defaultdict(int),
            'dimension_issues': []
        }
        
        for dim in dimension_elements:
            dimension_analysis['dimension_types'][dim.dim_type] += 1
            
            # 检查尺寸标注问题
            issues = self._check_dimension_issues(dim)
            dimension_analysis['dimension_issues'].extend(issues)
        
        dimension_analysis['dimension_types'] = dict(dimension_analysis['dimension_types'])
        
        return dimension_analysis
    
    def _identify_drawing_type(self, drawing: DrawingDocument) -> str:
        """识别图纸类型"""
        text_elements = drawing.get_text_elements()
        text_content = ' '.join([text.content.lower() for text in text_elements])
        
        # 基于文本内容识别图纸类型
        if any(keyword in text_content for keyword in ['二次', '控制', '保护', '测控']):
            return "二次回路图"
        elif any(keyword in text_content for keyword in ['一次', '主接线', '电气主接线']):
            return "一次接线图"
        elif any(keyword in text_content for keyword in ['平面', '布置', '设备布置']):
            return "平面布置图"
        elif any(keyword in text_content for keyword in ['安装', '接线', '端子']):
            return "安装接线图"
        elif any(keyword in text_content for keyword in ['原理', '逻辑', '功能']):
            return "原理图"
        else:
            return "未知类型"
    
    def _calculate_drawing_complexity(self, drawing: DrawingDocument) -> str:
        """计算图纸复杂度"""
        element_count = len(drawing.elements)
        layer_count = len(drawing.layers)
        block_count = len(drawing.blocks)
        
        complexity_score = element_count * 0.1 + layer_count * 2 + block_count * 5
        
        if complexity_score < 50:
            return "简单"
        elif complexity_score < 200:
            return "中等"
        elif complexity_score < 500:
            return "复杂"
        else:
            return "非常复杂"
    
    def _analyze_element_distribution(self, elements: List[DrawingElement]) -> Dict[str, Any]:
        """分析元素分布"""
        if not elements:
            return {}
        
        # 计算元素中心点
        centers = []
        for element in elements:
            bounds = element.get_bounds()
            center_x = (bounds[0].x + bounds[1].x) / 2
            center_y = (bounds[0].y + bounds[1].y) / 2
            centers.append((center_x, center_y))
        
        # 计算分布统计
        x_coords = [c[0] for c in centers]
        y_coords = [c[1] for c in centers]
        
        return {
            'center_of_mass': (sum(x_coords) / len(x_coords), sum(y_coords) / len(y_coords)),
            'x_range': (min(x_coords), max(x_coords)),
            'y_range': (min(y_coords), max(y_coords)),
            'density': len(elements) / ((max(x_coords) - min(x_coords)) * (max(y_coords) - min(y_coords)))
        }
    
    def _analyze_alignment(self, elements: List[DrawingElement]) -> Dict[str, Any]:
        """分析对齐情况"""
        alignment_tolerance = 1.0  # 对齐容差
        
        # 收集所有关键点
        x_coords = set()
        y_coords = set()
        
        for element in elements:
            if isinstance(element, Line):
                x_coords.update([element.start_point.x, element.end_point.x])
                y_coords.update([element.start_point.y, element.end_point.y])
            elif isinstance(element, (Circle, Arc)):
                x_coords.add(element.center.x)
                y_coords.add(element.center.y)
            elif isinstance(element, Text):
                x_coords.add(element.position.x)
                y_coords.add(element.position.y)
        
        # 查找对齐线
        x_alignments = self._find_alignments(list(x_coords), alignment_tolerance)
        y_alignments = self._find_alignments(list(y_coords), alignment_tolerance)
        
        return {
            'x_alignments': len(x_alignments),
            'y_alignments': len(y_alignments),
            'alignment_score': (len(x_alignments) + len(y_alignments)) / len(elements)
        }
    
    def _analyze_spacing(self, elements: List[DrawingElement]) -> Dict[str, Any]:
        """分析间距"""
        spacings = []
        
        # 计算相邻元素间距
        for i, elem1 in enumerate(elements):
            for elem2 in elements[i+1:]:
                distance = self._calculate_element_distance(elem1, elem2)
                if distance < 50:  # 只考虑较近的元素
                    spacings.append(distance)
        
        if spacings:
            return {
                'min_spacing': min(spacings),
                'max_spacing': max(spacings),
                'avg_spacing': sum(spacings) / len(spacings),
                'spacing_consistency': self._calculate_spacing_consistency(spacings)
            }
        else:
            return {}
    
    def _categorize_text(self, text_content: str) -> str:
        """分类文本内容"""
        content = text_content.lower().strip()
        
        # 设备标识
        if re.match(r'^[a-z]+\d+', content):
            return "设备标识"
        
        # 端子号
        if re.match(r'^\d+[a-z]?$', content) or re.match(r'^[a-z]\d+$', content):
            return "端子号"
        
        # 电压等级
        if re.search(r'\d+kv|\d+v', content):
            return "电压等级"
        
        # 回路编号
        if re.search(r'回路|circuit', content):
            return "回路编号"
        
        # 说明文字
        if len(content) > 10:
            return "说明文字"
        
        return "其他"
    
    def _check_text_issues(self, text: Text) -> List[str]:
        """检查文本问题"""
        issues = []
        
        # 检查文字高度
        if text.height < 2.0:
            issues.append(f"文字高度{text.height}mm过小")
        elif text.height > 15.0:
            issues.append(f"文字高度{text.height}mm过大")
        
        # 检查文字内容
        if not text.content.strip():
            issues.append("空白文字")
        
        # 检查特殊字符
        if re.search(r'[^\w\s\u4e00-\u9fff\-\.\(\)\/]', text.content):
            issues.append("包含特殊字符")
        
        return issues
    
    def _identify_symbol_type(self, block: Block) -> Optional[str]:
        """识别符号类型"""
        # 基于块名称识别
        block_name = block.name.lower()
        
        symbol_keywords = {
            'transformer': '变压器',
            'breaker': '断路器',
            'switch': '开关',
            'relay': '继电器',
            'ct': '电流互感器',
            'pt': '电压互感器',
            'capacitor': '电容器',
            'resistor': '电阻器',
            'inductor': '电感器'
        }
        
        for keyword, symbol_type in symbol_keywords.items():
            if keyword in block_name:
                return symbol_type
        
        return None
    
    def _identify_geometric_symbols(self, elements: List[DrawingElement]) -> List[Dict[str, Any]]:
        """识别几何图形组合符号"""
        potential_symbols = []
        
        # 查找圆形和矩形组合（可能是设备符号）
        circles = [elem for elem in elements if isinstance(elem, Circle)]
        lines = [elem for elem in elements if isinstance(elem, Line)]
        
        for circle in circles:
            # 查找围绕圆形的线条
            surrounding_lines = []
            for line in lines:
                if self._is_line_near_circle(line, circle, tolerance=5.0):
                    surrounding_lines.append(line)
            
            if len(surrounding_lines) >= 3:
                potential_symbols.append({
                    'type': '可能的设备符号',
                    'center': (circle.center.x, circle.center.y),
                    'elements': len(surrounding_lines) + 1
                })
        
        return potential_symbols
    
    def _find_connection_points(self, lines: List[Line]) -> List[Dict[str, Any]]:
        """查找连接点"""
        connection_points = []
        tolerance = 1.0
        
        # 收集所有端点
        points = []
        for line in lines:
            points.append(('start', line.start_point, line))
            points.append(('end', line.end_point, line))
        
        # 查找重合的端点
        for i, (type1, point1, line1) in enumerate(points):
            connections = []
            for j, (type2, point2, line2) in enumerate(points):
                if i != j and point1.distance_to(point2) < tolerance:
                    connections.append((line2, type2))
            
            if len(connections) > 0:
                connection_points.append({
                    'position': (point1.x, point1.y),
                    'connected_lines': len(connections) + 1,
                    'connection_type': 'T型连接' if len(connections) == 1 else '多线连接'
                })
        
        return connection_points
    
    def _analyze_wire_segments(self, lines: List[Line]) -> List[Dict[str, Any]]:
        """分析线段"""
        wire_segments = []
        
        for line in lines:
            segment_info = {
                'length': line.length(),
                'direction': self._calculate_line_direction(line),
                'layer': line.layer,
                'line_type': line.line_type.value
            }
            wire_segments.append(segment_info)
        
        return wire_segments
    
    def _find_junction_points(self, lines: List[Line]) -> List[Dict[str, Any]]:
        """查找交叉点"""
        junction_points = []
        
        for i, line1 in enumerate(lines):
            for line2 in lines[i+1:]:
                intersection = self._calculate_line_intersection(line1, line2)
                if intersection:
                    junction_points.append({
                        'position': intersection,
                        'lines': [line1.element_id, line2.element_id]
                    })
        
        return junction_points
    
    def _check_connection_issues(self, lines: List[Line], connection_points: List[Dict[str, Any]]) -> List[str]:
        """检查连接问题"""
        issues = []
        
        # 检查悬空线段
        for line in lines:
            start_connected = any(
                abs(cp['position'][0] - line.start_point.x) < 1.0 and 
                abs(cp['position'][1] - line.start_point.y) < 1.0
                for cp in connection_points
            )
            end_connected = any(
                abs(cp['position'][0] - line.end_point.x) < 1.0 and 
                abs(cp['position'][1] - line.end_point.y) < 1.0
                for cp in connection_points
            )
            
            if not start_connected:
                issues.append(f"线段起点悬空: {line.element_id}")
            if not end_connected:
                issues.append(f"线段终点悬空: {line.element_id}")
        
        return issues
    
    def _check_layer_issues(self, drawing: DrawingDocument) -> List[str]:
        """检查图层问题"""
        issues = []
        
        # 检查空图层
        for layer_name, layer in drawing.layers.items():
            elements_on_layer = drawing.get_elements_by_layer(layer_name)
            if not elements_on_layer:
                issues.append(f"图层 '{layer_name}' 为空")
        
        # 检查图层命名
        for layer_name in drawing.layers.keys():
            if not re.match(r'^[A-Z][A-Z0-9_]*$', layer_name) and layer_name != "0":
                issues.append(f"图层名称 '{layer_name}' 不符合命名规范")
        
        return issues
    
    def _check_dimension_issues(self, dimension: Dimension) -> List[str]:
        """检查尺寸标注问题"""
        issues = []
        
        # 检查测量点
        if len(dimension.measurement_points) < 2:
            issues.append("尺寸标注缺少测量点")
        
        return issues
    
    def _load_element_patterns(self) -> Dict[str, Any]:
        """加载图元模式"""
        return {
            'electrical_symbols': {},
            'connection_patterns': {},
            'layout_patterns': {}
        }
    
    def _load_symbol_library(self) -> Dict[str, Any]:
        """加载符号库"""
        return {
            'standard_symbols': {},
            'custom_symbols': {}
        }
    
    def _load_text_patterns(self) -> Dict[str, Any]:
        """加载文本模式"""
        return {
            'device_labels': r'^[A-Z]+\d+',
            'terminal_numbers': r'^\d+[A-Z]?$',
            'voltage_levels': r'\d+[kK][vV]'
        }
    
    def _find_alignments(self, coordinates: List[float], tolerance: float) -> List[List[float]]:
        """查找对齐线"""
        alignments = []
        coordinates.sort()
        
        current_group = [coordinates[0]]
        for coord in coordinates[1:]:
            if coord - current_group[-1] <= tolerance:
                current_group.append(coord)
            else:
                if len(current_group) >= 3:  # 至少3个点才算对齐
                    alignments.append(current_group)
                current_group = [coord]
        
        if len(current_group) >= 3:
            alignments.append(current_group)
        
        return alignments
    
    def _calculate_element_distance(self, elem1: DrawingElement, elem2: DrawingElement) -> float:
        """计算元素间距离"""
        bounds1 = elem1.get_bounds()
        bounds2 = elem2.get_bounds()
        
        center1 = Point(
            (bounds1[0].x + bounds1[1].x) / 2,
            (bounds1[0].y + bounds1[1].y) / 2
        )
        center2 = Point(
            (bounds2[0].x + bounds2[1].x) / 2,
            (bounds2[0].y + bounds2[1].y) / 2
        )
        
        return center1.distance_to(center2)
    
    def _calculate_spacing_consistency(self, spacings: List[float]) -> float:
        """计算间距一致性"""
        if len(spacings) < 2:
            return 1.0
        
        avg_spacing = sum(spacings) / len(spacings)
        variance = sum((s - avg_spacing) ** 2 for s in spacings) / len(spacings)
        std_dev = variance ** 0.5
        
        # 一致性分数：标准差越小，一致性越高
        consistency = max(0, 1 - (std_dev / avg_spacing))
        return consistency
    
    def _is_line_near_circle(self, line: Line, circle: Circle, tolerance: float) -> bool:
        """判断线条是否靠近圆形"""
        # 简化实现：检查线条端点是否在圆形附近
        start_distance = line.start_point.distance_to(circle.center)
        end_distance = line.end_point.distance_to(circle.center)
        
        return (abs(start_distance - circle.radius) < tolerance or 
                abs(end_distance - circle.radius) < tolerance)
    
    def _calculate_line_direction(self, line: Line) -> float:
        """计算线条方向（角度）"""
        dx = line.end_point.x - line.start_point.x
        dy = line.end_point.y - line.start_point.y
        
        import math
        angle = math.atan2(dy, dx) * 180 / math.pi
        return angle
    
    def _calculate_line_intersection(self, line1: Line, line2: Line) -> Optional[Tuple[float, float]]:
        """计算两条线的交点"""
        # 简化实现：只处理直线相交
        x1, y1 = line1.start_point.x, line1.start_point.y
        x2, y2 = line1.end_point.x, line1.end_point.y
        x3, y3 = line2.start_point.x, line2.start_point.y
        x4, y4 = line2.end_point.x, line2.end_point.y
        
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:  # 平行线
            return None
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom
        
        if 0 <= t <= 1 and 0 <= u <= 1:  # 交点在线段上
            x = x1 + t * (x2 - x1)
            y = y1 + t * (y2 - y1)
            return (x, y)
        
        return None
