"""
存储引擎
提供知识库的数据存储和检索功能
"""

from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod
import json
import sqlite3
import logging
from pathlib import Path
from datetime import datetime

from .knowledge_entity import KnowledgeEntity, EntityType, EntityRelationship


logger = logging.getLogger(__name__)


class StorageEngine(ABC):
    """存储引擎抽象基类"""
    
    @abstractmethod
    def store_entity(self, entity: KnowledgeEntity) -> bool:
        """存储实体"""
        pass
    
    @abstractmethod
    def get_entity(self, entity_id: str) -> Optional[KnowledgeEntity]:
        """获取实体"""
        pass
    
    @abstractmethod
    def update_entity(self, entity: KnowledgeEntity) -> bool:
        """更新实体"""
        pass
    
    @abstractmethod
    def delete_entity(self, entity_id: str) -> bool:
        """删除实体"""
        pass
    
    @abstractmethod
    def search_entities(self, 
                       query: str = None,
                       entity_type: EntityType = None,
                       tags: List[str] = None,
                       limit: int = 100) -> List[KnowledgeEntity]:
        """搜索实体"""
        pass
    
    @abstractmethod
    def store_relationship(self, relationship: EntityRelationship) -> bool:
        """存储关系"""
        pass
    
    @abstractmethod
    def get_related_entities(self, 
                           entity_id: str,
                           relationship_type: str = None,
                           depth: int = 1) -> List[KnowledgeEntity]:
        """获取相关实体"""
        pass


class SQLiteStorageEngine(StorageEngine):
    """SQLite存储引擎实现"""
    
    def __init__(self, db_path: str = "knowledge.db"):
        """
        初始化SQLite存储引擎
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self._init_database()
        logger.info(f"SQLite存储引擎初始化完成: {db_path}")
    
    def _init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建实体表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS entities (
                    id TEXT PRIMARY KEY,
                    entity_type TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    content TEXT,
                    source TEXT,
                    version TEXT,
                    language TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    confidence REAL,
                    confidence_level TEXT,
                    quality_score REAL,
                    tags TEXT,
                    keywords TEXT,
                    attributes TEXT
                )
            ''')
            
            # 创建关系表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS relationships (
                    id TEXT PRIMARY KEY,
                    source_entity_id TEXT NOT NULL,
                    target_entity_id TEXT NOT NULL,
                    relationship_type TEXT NOT NULL,
                    strength REAL,
                    confidence REAL,
                    created_at TEXT,
                    created_by TEXT,
                    attributes TEXT,
                    FOREIGN KEY (source_entity_id) REFERENCES entities (id),
                    FOREIGN KEY (target_entity_id) REFERENCES entities (id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_entity_type ON entities (entity_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_entity_name ON entities (name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_relationship_type ON relationships (relationship_type)')
            
            conn.commit()
    
    def store_entity(self, entity: KnowledgeEntity) -> bool:
        """存储实体到SQLite"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO entities (
                        id, entity_type, name, description, content, source, version,
                        language, created_at, updated_at, confidence, confidence_level,
                        quality_score, tags, keywords, attributes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    entity.id,
                    entity.entity_type.value,
                    entity.name,
                    entity.description,
                    entity.content,
                    entity.source,
                    entity.version,
                    entity.language,
                    entity.created_at.isoformat(),
                    entity.updated_at.isoformat(),
                    entity.confidence,
                    entity.confidence_level.value,
                    entity.quality_score,
                    json.dumps(entity.tags),
                    json.dumps(entity.keywords),
                    json.dumps(entity.attributes)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"存储实体失败: {e}")
            return False
    
    def get_entity(self, entity_id: str) -> Optional[KnowledgeEntity]:
        """从SQLite获取实体"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM entities WHERE id = ?', (entity_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_entity(row)
                return None
                
        except Exception as e:
            logger.error(f"获取实体失败: {e}")
            return None
    
    def update_entity(self, entity: KnowledgeEntity) -> bool:
        """更新实体"""
        return self.store_entity(entity)  # SQLite使用INSERT OR REPLACE
    
    def delete_entity(self, entity_id: str) -> bool:
        """删除实体"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 删除相关关系
                cursor.execute('''
                    DELETE FROM relationships 
                    WHERE source_entity_id = ? OR target_entity_id = ?
                ''', (entity_id, entity_id))
                
                # 删除实体
                cursor.execute('DELETE FROM entities WHERE id = ?', (entity_id,))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"删除实体失败: {e}")
            return False
    
    def search_entities(self, 
                       query: str = None,
                       entity_type: EntityType = None,
                       tags: List[str] = None,
                       limit: int = 100) -> List[KnowledgeEntity]:
        """搜索实体"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                sql = 'SELECT * FROM entities WHERE 1=1'
                params = []
                
                if entity_type:
                    sql += ' AND entity_type = ?'
                    params.append(entity_type.value)
                
                if query:
                    sql += ' AND (name LIKE ? OR description LIKE ? OR content LIKE ?)'
                    query_param = f'%{query}%'
                    params.extend([query_param, query_param, query_param])
                
                if tags:
                    for tag in tags:
                        sql += ' AND tags LIKE ?'
                        params.append(f'%"{tag}"%')
                
                sql += f' LIMIT {limit}'
                
                cursor.execute(sql, params)
                rows = cursor.fetchall()
                
                return [self._row_to_entity(row) for row in rows]
                
        except Exception as e:
            logger.error(f"搜索实体失败: {e}")
            return []
    
    def store_relationship(self, relationship: EntityRelationship) -> bool:
        """存储关系"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO relationships (
                        id, source_entity_id, target_entity_id, relationship_type,
                        strength, confidence, created_at, created_by, attributes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    relationship.id,
                    relationship.source_entity_id,
                    relationship.target_entity_id,
                    relationship.relationship_type.value,
                    relationship.strength,
                    relationship.confidence,
                    relationship.created_at.isoformat(),
                    relationship.created_by,
                    json.dumps(relationship.attributes)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"存储关系失败: {e}")
            return False
    
    def get_related_entities(self, 
                           entity_id: str,
                           relationship_type: str = None,
                           depth: int = 1) -> List[KnowledgeEntity]:
        """获取相关实体"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                sql = '''
                    SELECT e.* FROM entities e
                    JOIN relationships r ON (e.id = r.target_entity_id OR e.id = r.source_entity_id)
                    WHERE (r.source_entity_id = ? OR r.target_entity_id = ?) AND e.id != ?
                '''
                params = [entity_id, entity_id, entity_id]
                
                if relationship_type:
                    sql += ' AND r.relationship_type = ?'
                    params.append(relationship_type)
                
                cursor.execute(sql, params)
                rows = cursor.fetchall()
                
                return [self._row_to_entity(row) for row in rows]
                
        except Exception as e:
            logger.error(f"获取相关实体失败: {e}")
            return []
    
    def _row_to_entity(self, row) -> KnowledgeEntity:
        """将数据库行转换为实体对象"""
        entity_data = {
            'id': row[0],
            'entity_type': row[1],
            'name': row[2],
            'description': row[3] or '',
            'content': row[4] or '',
            'source': row[5] or '',
            'version': row[6] or '1.0.0',
            'language': row[7] or 'zh-CN',
            'created_at': datetime.fromisoformat(row[8]) if row[8] else datetime.now(),
            'updated_at': datetime.fromisoformat(row[9]) if row[9] else datetime.now(),
            'confidence': row[10] or 1.0,
            'confidence_level': row[11] or 'high',
            'quality_score': row[12] or 1.0,
            'tags': json.loads(row[13]) if row[13] else [],
            'keywords': json.loads(row[14]) if row[14] else [],
            'attributes': json.loads(row[15]) if row[15] else {}
        }
        
        return KnowledgeEntity(**entity_data)
    
    def export_data(self, format: str = "json") -> str:
        """导出数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 导出实体
                cursor.execute('SELECT * FROM entities')
                entities = cursor.fetchall()
                
                # 导出关系
                cursor.execute('SELECT * FROM relationships')
                relationships = cursor.fetchall()
                
                if format == "json":
                    return json.dumps({
                        'entities': entities,
                        'relationships': relationships
                    }, indent=2, default=str)
                
                return ""
                
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return ""
    
    def import_data(self, data: str, format: str = "json") -> bool:
        """导入数据"""
        try:
            if format == "json":
                imported_data = json.loads(data)
                
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 导入实体
                    for entity_row in imported_data.get('entities', []):
                        cursor.execute('''
                            INSERT OR REPLACE INTO entities VALUES 
                            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', entity_row)
                    
                    # 导入关系
                    for rel_row in imported_data.get('relationships', []):
                        cursor.execute('''
                            INSERT OR REPLACE INTO relationships VALUES 
                            (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', rel_row)
                    
                    conn.commit()
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"导入数据失败: {e}")
            return False
