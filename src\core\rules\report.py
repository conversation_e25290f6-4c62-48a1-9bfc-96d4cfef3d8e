"""
验证报告生成器
生成详细的验证报告，包括错误分类、统计信息和修复建议
"""

import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field

from .base import RuleSeverity, ValidationIssue
from .engine import ExecutionResult


@dataclass
class ReportSection:
    """报告章节"""
    title: str
    content: str
    subsections: List['ReportSection'] = field(default_factory=list)
    
    def add_subsection(self, subsection: 'ReportSection') -> None:
        """添加子章节"""
        self.subsections.append(subsection)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'title': self.title,
            'content': self.content,
            'subsections': [sub.to_dict() for sub in self.subsections]
        }


@dataclass
class ValidationReport:
    """验证报告"""
    # 报告基本信息
    title: str = "IEC61850设计验证报告"
    generated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    
    # 执行结果
    execution_result: Optional[ExecutionResult] = None
    
    # 报告章节
    sections: List[ReportSection] = field(default_factory=list)
    
    # 统计信息
    statistics: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_section(self, section: ReportSection) -> None:
        """添加章节"""
        self.sections.append(section)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'title': self.title,
            'generated_at': self.generated_at.isoformat(),
            'version': self.version,
            'statistics': self.statistics,
            'metadata': self.metadata,
            'sections': [section.to_dict() for section in self.sections]
        }
    
    def to_json(self, indent: int = 2) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.templates = {
            'html': self._get_html_template(),
            'markdown': self._get_markdown_template()
        }
    
    def generate_report(self, execution_result: ExecutionResult,
                       title: str = "IEC61850设计验证报告") -> ValidationReport:
        """
        生成验证报告
        
        Args:
            execution_result: 执行结果
            title: 报告标题
            
        Returns:
            验证报告
        """
        report = ValidationReport(
            title=title,
            execution_result=execution_result
        )
        
        # 生成统计信息
        report.statistics = self._generate_statistics(execution_result)
        
        # 生成报告章节
        report.add_section(self._generate_summary_section(execution_result))
        report.add_section(self._generate_statistics_section(execution_result))
        report.add_section(self._generate_issues_section(execution_result))
        report.add_section(self._generate_rules_section(execution_result))
        report.add_section(self._generate_recommendations_section(execution_result))
        
        # 设置元数据
        report.metadata = {
            'generator': 'IEC61850DesignChecker',
            'generator_version': '1.0',
            'total_issues': len(execution_result.get_all_issues()),
            'critical_issues': len([issue for issue in execution_result.get_all_issues() 
                                  if issue.severity == RuleSeverity.CRITICAL]),
            'error_issues': len(execution_result.get_errors()),
            'warning_issues': len(execution_result.get_warnings()),
            'info_issues': len(execution_result.get_infos())
        }
        
        return report
    
    def _generate_statistics(self, execution_result: ExecutionResult) -> Dict[str, Any]:
        """生成统计信息"""
        all_issues = execution_result.get_all_issues()
        
        # 按严重程度统计
        severity_stats = {}
        for severity in RuleSeverity:
            count = len([issue for issue in all_issues if issue.severity == severity])
            severity_stats[severity.value] = count
        
        # 按规则分类统计
        rule_stats = {}
        for rule_id, result in execution_result.rule_results.items():
            rule_stats[rule_id] = {
                'success': result.success,
                'issues_count': len(result.issues),
                'execution_time': result.execution_time
            }
        
        # 按路径统计
        path_stats = {}
        for issue in all_issues:
            path = issue.path or 'root'
            if path not in path_stats:
                path_stats[path] = 0
            path_stats[path] += 1
        
        return {
            'execution_summary': {
                'executed_rules': execution_result.executed_rules,
                'skipped_rules': execution_result.skipped_rules,
                'failed_rules': execution_result.failed_rules,
                'total_time': execution_result.total_time
            },
            'severity_distribution': severity_stats,
            'rule_statistics': rule_stats,
            'path_statistics': path_stats,
            'top_issues_paths': sorted(path_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        }
    
    def _generate_summary_section(self, execution_result: ExecutionResult) -> ReportSection:
        """生成摘要章节"""
        all_issues = execution_result.get_all_issues()
        errors = execution_result.get_errors()
        warnings = execution_result.get_warnings()
        infos = execution_result.get_infos()
        
        summary_content = f"""
验证执行摘要：
- 执行规则数量: {execution_result.executed_rules}
- 跳过规则数量: {execution_result.skipped_rules}
- 失败规则数量: {execution_result.failed_rules}
- 总执行时间: {execution_result.total_time:.3f} 秒

问题统计：
- 总问题数量: {len(all_issues)}
- 错误数量: {len(errors)}
- 警告数量: {len(warnings)}
- 信息数量: {len(infos)}

验证结果: {'通过' if len(errors) == 0 else '未通过'}
        """.strip()
        
        return ReportSection(title="执行摘要", content=summary_content)
    
    def _generate_statistics_section(self, execution_result: ExecutionResult) -> ReportSection:
        """生成统计章节"""
        stats = self._generate_statistics(execution_result)
        
        # 严重程度分布
        severity_content = "问题严重程度分布：\n"
        for severity, count in stats['severity_distribution'].items():
            severity_content += f"- {severity.upper()}: {count}\n"
        
        severity_section = ReportSection(title="严重程度分布", content=severity_content)
        
        # 规则执行统计
        rule_content = "规则执行统计：\n"
        successful_rules = sum(1 for stat in stats['rule_statistics'].values() if stat['success'])
        total_rules = len(stats['rule_statistics'])
        rule_content += f"- 成功执行: {successful_rules}/{total_rules}\n"
        avg_time = stats['execution_summary']['total_time']/total_rules if total_rules > 0 else 0
        rule_content += f"- 平均执行时间: {avg_time:.3f} 秒\n"
        
        rule_section = ReportSection(title="规则执行统计", content=rule_content)
        
        # 问题分布
        path_content = "问题分布（按路径）：\n"
        for path, count in stats['top_issues_paths'][:5]:
            path_content += f"- {path}: {count} 个问题\n"
        
        path_section = ReportSection(title="问题分布", content=path_content)
        
        # 组合统计章节
        statistics_section = ReportSection(title="详细统计", content="")
        statistics_section.add_subsection(severity_section)
        statistics_section.add_subsection(rule_section)
        statistics_section.add_subsection(path_section)
        
        return statistics_section
    
    def _generate_issues_section(self, execution_result: ExecutionResult) -> ReportSection:
        """生成问题章节"""
        issues_section = ReportSection(title="发现的问题", content="")
        
        # 按严重程度分组
        errors = execution_result.get_errors()
        warnings = execution_result.get_warnings()
        infos = execution_result.get_infos()
        
        if errors:
            error_content = self._format_issues(errors, "错误")
            issues_section.add_subsection(ReportSection(title="错误", content=error_content))
        
        if warnings:
            warning_content = self._format_issues(warnings, "警告")
            issues_section.add_subsection(ReportSection(title="警告", content=warning_content))
        
        if infos:
            info_content = self._format_issues(infos, "信息")
            issues_section.add_subsection(ReportSection(title="信息", content=info_content))
        
        if not errors and not warnings and not infos:
            issues_section.content = "未发现任何问题。"
        
        return issues_section
    
    def _format_issues(self, issues: List[ValidationIssue], issue_type: str) -> str:
        """格式化问题列表"""
        content = f"发现 {len(issues)} 个{issue_type}：\n\n"
        
        for i, issue in enumerate(issues, 1):
            content += f"{i}. **{issue.rule_id}**: {issue.message}\n"
            if issue.path:
                content += f"   位置: {issue.path}\n"
            if issue.details:
                content += f"   详情: {issue.details}\n"
            if issue.suggestion:
                content += f"   建议: {issue.suggestion}\n"
            content += "\n"
        
        return content
    
    def _generate_rules_section(self, execution_result: ExecutionResult) -> ReportSection:
        """生成规则章节"""
        rules_section = ReportSection(title="规则执行详情", content="")
        
        # 成功的规则
        successful_rules = []
        failed_rules = []
        
        for rule_id, result in execution_result.rule_results.items():
            if result.success and not result.issues:
                successful_rules.append(rule_id)
            else:
                failed_rules.append((rule_id, result))
        
        # 成功规则
        if successful_rules:
            success_content = f"以下 {len(successful_rules)} 个规则执行成功且未发现问题：\n"
            for rule_id in successful_rules:
                success_content += f"- {rule_id}\n"
            rules_section.add_subsection(ReportSection(title="成功规则", content=success_content))
        
        # 有问题的规则
        if failed_rules:
            failed_content = f"以下 {len(failed_rules)} 个规则发现了问题：\n\n"
            for rule_id, result in failed_rules:
                failed_content += f"**{rule_id}**:\n"
                failed_content += f"- 执行时间: {result.execution_time:.3f} 秒\n"
                failed_content += f"- 发现问题: {len(result.issues)} 个\n"
                if result.issues:
                    for issue in result.issues:
                        failed_content += f"  - {issue.severity.value.upper()}: {issue.message}\n"
                failed_content += "\n"
            rules_section.add_subsection(ReportSection(title="有问题的规则", content=failed_content))
        
        return rules_section
    
    def _generate_recommendations_section(self, execution_result: ExecutionResult) -> ReportSection:
        """生成建议章节"""
        recommendations_section = ReportSection(title="修复建议", content="")
        
        # 收集所有建议
        suggestions = []
        for issue in execution_result.get_all_issues():
            if issue.suggestion:
                suggestions.append((issue.severity, issue.rule_id, issue.message, issue.suggestion))
        
        if not suggestions:
            recommendations_section.content = "所有检查都通过了，没有修复建议。"
            return recommendations_section
        
        # 按严重程度分组建议
        critical_suggestions = [s for s in suggestions if s[0] == RuleSeverity.CRITICAL]
        error_suggestions = [s for s in suggestions if s[0] == RuleSeverity.ERROR]
        warning_suggestions = [s for s in suggestions if s[0] == RuleSeverity.WARNING]
        
        if critical_suggestions or error_suggestions:
            urgent_content = "**紧急修复建议**（必须修复）：\n\n"
            for severity, rule_id, message, suggestion in critical_suggestions + error_suggestions:
                urgent_content += f"- **{rule_id}**: {message}\n"
                urgent_content += f"  修复建议: {suggestion}\n\n"
            recommendations_section.add_subsection(ReportSection(title="紧急修复", content=urgent_content))
        
        if warning_suggestions:
            optional_content = "**可选修复建议**（建议修复）：\n\n"
            for severity, rule_id, message, suggestion in warning_suggestions:
                optional_content += f"- **{rule_id}**: {message}\n"
                optional_content += f"  修复建议: {suggestion}\n\n"
            recommendations_section.add_subsection(ReportSection(title="可选修复", content=optional_content))
        
        # 总体建议
        overall_content = self._generate_overall_recommendations(execution_result)
        if overall_content:
            recommendations_section.add_subsection(ReportSection(title="总体建议", content=overall_content))
        
        return recommendations_section
    
    def _generate_overall_recommendations(self, execution_result: ExecutionResult) -> str:
        """生成总体建议"""
        errors = execution_result.get_errors()
        warnings = execution_result.get_warnings()
        
        recommendations = []
        
        if len(errors) > 10:
            recommendations.append("发现大量错误，建议优先修复最严重的问题。")
        
        if len(warnings) > 20:
            recommendations.append("发现大量警告，建议分批次处理以提高配置质量。")
        
        if execution_result.failed_rules > execution_result.executed_rules * 0.5:
            recommendations.append("超过一半的规则发现了问题，建议全面检查配置文件。")
        
        # 根据问题类型给出建议
        rule_categories = {}
        for issue in execution_result.get_all_issues():
            category = issue.rule_id[:3]  # 取规则ID前缀作为类别
            if category not in rule_categories:
                rule_categories[category] = 0
            rule_categories[category] += 1
        
        if rule_categories.get('STD', 0) > 5:
            recommendations.append("发现多个标准符合性问题，建议参考IEC61850标准进行修正。")
        
        if rule_categories.get('COM', 0) > 3:
            recommendations.append("发现多个通信配置问题，建议检查网络拓扑和IP地址分配。")
        
        if rule_categories.get('DEV', 0) > 3:
            recommendations.append("发现多个设备配置问题，建议检查IED配置和服务定义。")
        
        return "\n".join(f"- {rec}" for rec in recommendations)
    
    def save_report(self, report: ValidationReport, file_path: Union[str, Path],
                   format: str = 'json') -> None:
        """
        保存报告到文件
        
        Args:
            report: 验证报告
            file_path: 保存路径
            format: 文件格式 ('json', 'html', 'markdown')
        """
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        if format.lower() == 'json':
            with open(path, 'w', encoding='utf-8') as f:
                f.write(report.to_json())
        elif format.lower() == 'html':
            html_content = self._generate_html_report(report)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(html_content)
        elif format.lower() == 'markdown':
            md_content = self._generate_markdown_report(report)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(md_content)
        else:
            raise ValueError(f"不支持的格式: {format}")
    
    def _generate_html_report(self, report: ValidationReport) -> str:
        """生成HTML报告"""
        # 简化的HTML生成
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{report.title}</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1, h2, h3 {{ color: #333; }}
        .error {{ color: #d32f2f; }}
        .warning {{ color: #f57c00; }}
        .info {{ color: #1976d2; }}
        .success {{ color: #388e3c; }}
        pre {{ background: #f5f5f5; padding: 10px; border-radius: 4px; }}
    </style>
</head>
<body>
    <h1>{report.title}</h1>
    <p>生成时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
"""
        
        for section in report.sections:
            html += self._section_to_html(section)
        
        html += "</body></html>"
        return html
    
    def _section_to_html(self, section: ReportSection, level: int = 2) -> str:
        """将章节转换为HTML"""
        html = f"<h{level}>{section.title}</h{level}>\n"
        html += f"<pre>{section.content}</pre>\n"
        
        for subsection in section.subsections:
            html += self._section_to_html(subsection, level + 1)
        
        return html
    
    def _generate_markdown_report(self, report: ValidationReport) -> str:
        """生成Markdown报告"""
        md = f"# {report.title}\n\n"
        md += f"生成时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        for section in report.sections:
            md += self._section_to_markdown(section)
        
        return md
    
    def _section_to_markdown(self, section: ReportSection, level: int = 2) -> str:
        """将章节转换为Markdown"""
        md = f"{'#' * level} {section.title}\n\n"
        md += f"{section.content}\n\n"
        
        for subsection in section.subsections:
            md += self._section_to_markdown(subsection, level + 1)
        
        return md
    
    def _get_html_template(self) -> str:
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html>
<head>
    <title>{{title}}</title>
    <meta charset="utf-8">
    <style>
        /* CSS样式 */
    </style>
</head>
<body>
    {{content}}
</body>
</html>
        """
    
    def _get_markdown_template(self) -> str:
        """获取Markdown模板"""
        return """
# {{title}}

{{content}}
        """
