<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .current-line { stroke: red; stroke-width: 2; fill: none; }
            .voltage-line { stroke: blue; stroke-width: 2; fill: none; }
            .trip-line { stroke: red; stroke-width: 3; fill: none; }
            .goose-line { stroke: purple; stroke-width: 2; fill: none; stroke-dasharray: 10,5; }
        </style>
    </defs>

    <!-- 背景 -->
    <rect width="1200" height="900" fill="#f8f9fa" stroke="#ddd"/>

    <!-- 标题 -->
    <text x="600" y="30" text-anchor="middle" class="title-text">线路保护回路图</text>
    <text x="600" y="50" text-anchor="middle" font-size="12" fill="#666">基于CT/PT二次回路的继电保护系统</text>

    <!-- 一次系统示意 -->
    <g transform="translate(50, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">一次系统:</text>
        <line x1="0" y1="20" x2="300" y2="20" stroke="black" stroke-width="6"/>
        <text x="150" y="15" text-anchor="middle" font-size="10">220kV出线</text>

        <!-- 电流互感器位置 -->
        <circle cx="100" cy="20" r="15" fill="none" stroke="red" stroke-width="3"/>
        <text x="100" y="45" text-anchor="middle" font-size="8">TA1</text>

        <!-- 断路器位置 -->
        <rect x="180" y="10" width="20" height="20" fill="none" stroke="black" stroke-width="2"/>
        <text x="190" y="50" text-anchor="middle" font-size="8">QF1</text>
    </g>

    <!-- 电流互感器二次回路 -->
    <g transform="translate(100, 200)">
        <circle cx="50" cy="50" r="25" fill="none" stroke="red" stroke-width="2"/>
        <circle cx="50" cy="50" r="15" fill="none" stroke="red" stroke-width="1"/>
        <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TA1</text>
        <text x="50" y="85" text-anchor="middle" font-size="8">220kV出线CT</text>

        <!-- 一次侧 -->
        <circle cx="25" cy="50" r="2" fill="black"/>
        <text x="20" y="45" font-size="8">P1</text>
        <circle cx="75" cy="50" r="2" fill="black"/>
        <text x="80" y="45" font-size="8">P2</text>

        <!-- 二次侧 -->
        <circle cx="35" cy="75" r="2" fill="red"/>
        <text x="30" y="90" font-size="8">S1</text>
        <circle cx="65" cy="75" r="2" fill="red"/>
        <text x="70" y="90" font-size="8">S2</text>

        <!-- 警告标识 -->
        <text x="50" y="105" text-anchor="middle" font-size="8" fill="red" font-weight="bold">⚠ CT二次侧禁止开路</text>
    </g>

    <!-- 电压互感器二次回路 -->
    <g transform="translate(100, 350)">
        <circle cx="50" cy="50" r="25" fill="none" stroke="blue" stroke-width="2"/>
        <circle cx="50" cy="50" r="15" fill="none" stroke="blue" stroke-width="1"/>
        <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TV1</text>
        <text x="50" y="85" text-anchor="middle" font-size="8">220kV母线PT</text>

        <!-- 一次侧 -->
        <circle cx="25" cy="50" r="2" fill="black"/>
        <text x="20" y="45" font-size="8">P1</text>
        <circle cx="75" cy="50" r="2" fill="black"/>
        <text x="80" y="45" font-size="8">P2</text>

        <!-- 二次侧 -->
        <circle cx="35" cy="75" r="2" fill="blue"/>
        <text x="30" y="90" font-size="8">S1</text>
        <circle cx="65" cy="75" r="2" fill="blue"/>
        <text x="70" y="90" font-size="8">S2</text>

        <!-- 警告标识 -->
        <text x="50" y="105" text-anchor="middle" font-size="8" fill="red" font-weight="bold">⚠ PT二次侧禁止短路</text>
    </g>

    <!-- 保护装置 -->
    <g transform="translate(400, 250)">
        <rect x="0" y="0" width="120" height="100" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="60" y="20" text-anchor="middle" font-size="12" font-weight="bold">线路保护装置</text>
        <text x="60" y="35" text-anchor="middle" font-size="8">KA1 (微机保护)</text>

        <!-- 保护功能 -->
        <text x="10" y="55" font-size="8">• 速断保护</text>
        <text x="10" y="70" font-size="8">• 过流保护</text>
        <text x="10" y="85" font-size="8">• 距离保护</text>
        <text x="10" y="100" font-size="8">• 零序保护</text>

        <!-- 输入端子 -->
        <circle cx="-5" cy="20" r="2" fill="red"/>
        <text x="-15" y="15" font-size="6">Ia</text>
        <circle cx="-5" cy="35" r="2" fill="red"/>
        <text x="-15" y="30" font-size="6">Ib</text>
        <circle cx="-5" cy="50" r="2" fill="red"/>
        <text x="-15" y="45" font-size="6">Ic</text>
        <circle cx="-5" cy="65" r="2" fill="blue"/>
        <text x="-15" y="60" font-size="6">Ua</text>
        <circle cx="-5" cy="80" r="2" fill="blue"/>
        <text x="-15" y="75" font-size="6">Ub</text>

        <!-- 输出端子 -->
        <circle cx="125" cy="30" r="2" fill="red"/>
        <text x="130" y="25" font-size="6">跳闸</text>
        <circle cx="125" cy="50" r="2" fill="orange"/>
        <text x="130" y="45" font-size="6">信号</text>
        <circle cx="125" cy="70" r="2" fill="purple"/>
        <text x="130" y="65" font-size="6">GOOSE</text>
    </g>

    <!-- 跳闸回路 -->
    <g transform="translate(700, 270)">
        <rect x="0" y="0" width="60" height="30" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="30" y="20" text-anchor="middle" font-size="10" font-weight="bold">YT1</text>
        <text x="30" y="45" text-anchor="middle" font-size="8">跳闸线圈</text>
        <circle cx="-5" cy="15" r="2" fill="red"/>
        <circle cx="65" cy="15" r="2" fill="red"/>
    </g>

    <!-- 连接线 -->
    <!-- CT到保护装置 -->
    <line x1="165" y1="275" x2="395" y2="270" class="current-line"/>
    <line x1="165" y1="285" x2="395" y2="285" class="current-line"/>
    <line x1="165" y1="295" x2="395" y2="300" class="current-line"/>

    <!-- PT到保护装置 -->
    <line x1="165" y1="425" x2="395" y2="315" class="voltage-line"/>
    <line x1="165" y1="435" x2="395" y2="330" class="voltage-line"/>

    <!-- 保护装置到跳闸线圈 -->
    <line x1="525" y1="280" x2="695" y2="285" class="trip-line"/>

    <!-- GOOSE连接 -->
    <line x1="525" y1="320" x2="600" y2="320" class="goose-line"/>
    <text x="560" y="315" text-anchor="middle" font-size="8">GOOSE</text>

    <!-- 保护原理说明 -->
    <g transform="translate(50, 550)">
        <text x="0" y="0" font-size="14" font-weight="bold">保护回路工作原理:</text>
        <text x="0" y="25" font-size="11">1. CT将一次大电流转换为二次小电流(5A或1A)</text>
        <text x="0" y="45" font-size="11">2. PT将一次高电压转换为二次低电压(100V)</text>
        <text x="0" y="65" font-size="11">3. 保护装置实时监测电流、电压信号</text>
        <text x="0" y="85" font-size="11">4. 故障时保护装置快速动作，发出跳闸命令</text>
        <text x="0" y="105" font-size="11">5. 跳闸线圈得电，断路器跳闸切除故障</text>

        <text x="600" y="0" font-size="14" font-weight="bold">保护配置:</text>
        <text x="600" y="25" font-size="11">• 主保护：差动保护、距离保护</text>
        <text x="600" y="45" font-size="11">• 后备保护：过流保护、零序保护</text>
        <text x="600" y="65" font-size="11">• 辅助保护：重合闸、故障录波</text>
        <text x="600" y="85" font-size="11">• 通信：GOOSE、SV虚端子技术</text>
    </g>

    <!-- 虚端子技术在保护中的应用 -->
    <g transform="translate(50, 700)">
        <rect x="0" y="0" width="1100" height="150" fill="#fff3cd" stroke="#ffc107" stroke-width="2"/>
        <text x="550" y="25" text-anchor="middle" font-size="14" font-weight="bold">虚端子技术在保护回路中的革命性应用</text>

        <g transform="translate(50, 50)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#d63384">传统保护回路问题:</text>
            <text x="0" y="20" font-size="10">• CT/PT二次线缆长，压降大，精度低</text>
            <text x="0" y="35" font-size="10">• 大量硬接线，接线复杂，故障率高</text>
            <text x="0" y="50" font-size="10">• CT开路、PT短路风险</text>
            <text x="0" y="65" font-size="10">• 保护装置分散，配合困难</text>
            <text x="0" y="80" font-size="10">• 维护工作量大，扩展困难</text>
        </g>

        <g transform="translate(550, 50)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">虚端子数字化保护:</text>
            <text x="0" y="20" font-size="10">• SV采样值：数字化传输CT/PT信号</text>
            <text x="0" y="35" font-size="10">• GOOSE：快速传输跳闸、闭锁信号</text>
            <text x="0" y="50" font-size="10">• 合并单元：就地数字化采样</text>
            <text x="0" y="65" font-size="10">• 智能终端：分布式保护架构</text>
            <text x="0" y="80" font-size="10">• 网络化：统一时钟、信息共享</text>
        </g>
    </g>

</svg>