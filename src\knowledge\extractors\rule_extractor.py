"""
规则提取器
从标准文档中提取验证规则
"""

import re
import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime

from ..base.knowledge_entity import RuleEntity, EntityType
from .text_analyzer import TextAnalyzer


logger = logging.getLogger(__name__)


class RuleExtractor:
    """规则提取器"""
    
    def __init__(self):
        """初始化规则提取器"""
        self.text_analyzer = TextAnalyzer()
        
        # 规则模式定义
        self.rule_patterns = {
            'mandatory': [
                r'必须(?P<content>[^。！？；]*)',
                r'应当(?P<content>[^。！？；]*)',
                r'不得(?P<content>[^。！？；]*)',
                r'禁止(?P<content>[^。！？；]*)',
                r'shall\s+(?P<content>[^.!?;]*)',
                r'must\s+(?P<content>[^.!?;]*)',
                r'shall\s+not\s+(?P<content>[^.!?;]*)'
            ],
            'recommended': [
                r'应该(?P<content>[^。！？；]*)',
                r'宜(?P<content>[^。！？；]*)',
                r'建议(?P<content>[^。！？；]*)',
                r'should\s+(?P<content>[^.!?;]*)',
                r'it\s+is\s+recommended\s+(?P<content>[^.!?;]*)'
            ],
            'optional': [
                r'可以(?P<content>[^。！？；]*)',
                r'可(?P<content>[^。！？；]*)',
                r'may\s+(?P<content>[^.!?;]*)',
                r'can\s+(?P<content>[^.!?;]*)'
            ]
        }
        
        # 规则类别关键词
        self.rule_categories = {
            'syntax': ['格式', '语法', '结构', 'format', 'syntax', 'structure'],
            'semantic': ['语义', '逻辑', '含义', 'semantic', 'logic', 'meaning'],
            'safety': ['安全', '保护', '防护', 'safety', 'protection', 'security'],
            'performance': ['性能', '效率', '速度', 'performance', 'efficiency', 'speed'],
            'compatibility': ['兼容', '互操作', 'compatibility', 'interoperability'],
            'configuration': ['配置', '设置', 'configuration', 'setting'],
            'communication': ['通信', '传输', 'communication', 'transmission'],
            'data': ['数据', '信息', 'data', 'information']
        }
        
        # 设备类型关键词
        self.device_keywords = {
            'transformer': ['变压器', '主变', 'transformer'],
            'breaker': ['断路器', '开关', 'breaker', 'switch'],
            'protection': ['保护装置', '继电保护', 'protection', 'relay'],
            'measurement': ['测控装置', '测量装置', 'measurement', 'metering'],
            'communication': ['通信装置', '网关', 'communication', 'gateway'],
            'ied': ['IED', '智能电子设备', 'intelligent electronic device']
        }
        
        logger.info("规则提取器初始化完成")
    
    def extract_rules_from_text(self, text: str, source: str = "") -> List[RuleEntity]:
        """
        从文本中提取规则
        
        Args:
            text: 输入文本
            source: 文档来源
            
        Returns:
            List[RuleEntity]: 提取的规则列表
        """
        rules = []
        
        try:
            # 按段落分割文本
            paragraphs = self._split_into_paragraphs(text)
            
            for i, paragraph in enumerate(paragraphs):
                if not paragraph.strip():
                    continue
                
                # 提取段落中的规则
                paragraph_rules = self._extract_rules_from_paragraph(
                    paragraph, source, i
                )
                rules.extend(paragraph_rules)
            
            logger.info(f"从文档提取了 {len(rules)} 条规则")
            return rules
            
        except Exception as e:
            logger.error(f"规则提取失败: {e}")
            return []
    
    def _split_into_paragraphs(self, text: str) -> List[str]:
        """将文本分割为段落"""
        # 按双换行符分割
        paragraphs = text.split('\n\n')
        
        # 进一步按句号分割长段落
        result = []
        for paragraph in paragraphs:
            if len(paragraph) > 500:  # 长段落
                sentences = re.split(r'[。！？]', paragraph)
                result.extend([s.strip() + '。' for s in sentences if s.strip()])
            else:
                result.append(paragraph.strip())
        
        return [p for p in result if p]
    
    def _extract_rules_from_paragraph(self, paragraph: str, source: str, index: int) -> List[RuleEntity]:
        """从段落中提取规则"""
        rules = []
        
        for severity, patterns in self.rule_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, paragraph, re.IGNORECASE)
                
                for match in matches:
                    try:
                        rule = self._create_rule_from_match(
                            match, paragraph, source, severity, index
                        )
                        if rule:
                            rules.append(rule)
                    except Exception as e:
                        logger.warning(f"创建规则失败: {e}")
                        continue
        
        return rules
    
    def _create_rule_from_match(self, match, paragraph: str, source: str, 
                               severity: str, index: int) -> Optional[RuleEntity]:
        """从匹配结果创建规则实体"""
        try:
            # 提取规则内容
            if 'content' in match.groupdict():
                rule_content = match.group('content').strip()
            else:
                rule_content = match.group(0).strip()
            
            if not rule_content or len(rule_content) < 5:
                return None
            
            # 生成规则名称
            rule_name = self._generate_rule_name(rule_content, index)
            
            # 确定规则类型和类别
            rule_type = self._determine_rule_type(paragraph)
            category = self._determine_rule_category(paragraph)
            
            # 提取适用设备
            applicable_devices = self._extract_applicable_devices(paragraph)
            
            # 提取条件和动作
            condition, action = self._extract_condition_action(rule_content, paragraph)
            
            # 生成消息模板
            message_template = self._generate_message_template(rule_name, rule_content)
            
            # 提取修复建议
            fix_suggestions = self._extract_fix_suggestions(paragraph)
            
            # 确定严重程度
            rule_severity = self._map_severity(severity)
            
            # 计算置信度
            confidence = self._calculate_confidence(rule_content, paragraph)
            
            rule = RuleEntity(
                name=rule_name,
                description=f"从标准文档提取的规则: {rule_content[:100]}",
                content=rule_content,
                source=source,
                rule_type=rule_type,
                severity=rule_severity,
                category=category,
                condition=condition,
                action=action,
                message_template=message_template,
                applicable_devices=applicable_devices,
                fix_suggestions=fix_suggestions,
                confidence=confidence,
                attributes={
                    'paragraph_index': index,
                    'extraction_method': 'pattern_matching',
                    'original_paragraph': paragraph[:200]
                }
            )
            
            return rule
            
        except Exception as e:
            logger.error(f"创建规则实体失败: {e}")
            return None
    
    def _generate_rule_name(self, rule_content: str, index: int) -> str:
        """生成规则名称"""
        # 提取关键词作为规则名称
        keywords = self.text_analyzer.extract_keywords(rule_content, top_k=3)
        
        if keywords:
            key_terms = [kw[0] for kw in keywords[:2]]
            name = '_'.join(key_terms)
        else:
            # 使用前20个字符
            name = re.sub(r'[^\w\u4e00-\u9fff]', '_', rule_content[:20])
        
        return f"rule_{index}_{name}"
    
    def _determine_rule_type(self, paragraph: str) -> str:
        """确定规则类型"""
        for rule_type, keywords in self.rule_categories.items():
            if any(keyword in paragraph.lower() for keyword in keywords):
                return rule_type
        return 'business'
    
    def _determine_rule_category(self, paragraph: str) -> str:
        """确定规则类别"""
        # 基于内容确定类别
        if any(word in paragraph for word in ['IEC61850', 'SCL', 'SCD']):
            return 'iec61850_compliance'
        elif any(word in paragraph for word in ['通信', '网络', 'communication']):
            return 'communication'
        elif any(word in paragraph for word in ['保护', 'protection']):
            return 'protection'
        elif any(word in paragraph for word in ['配置', 'configuration']):
            return 'configuration'
        else:
            return 'general'
    
    def _extract_applicable_devices(self, paragraph: str) -> List[str]:
        """提取适用设备"""
        applicable_devices = []
        
        for device_type, keywords in self.device_keywords.items():
            if any(keyword in paragraph.lower() for keyword in keywords):
                applicable_devices.append(device_type)
        
        return applicable_devices
    
    def _extract_condition_action(self, rule_content: str, paragraph: str) -> Tuple[str, str]:
        """提取规则条件和动作"""
        # 查找条件关键词
        condition_keywords = ['当', '如果', '若', 'when', 'if', 'where']
        action_keywords = ['则', '应', '必须', 'then', 'shall', 'must']
        
        condition = ""
        action = rule_content
        
        # 尝试分离条件和动作
        for ck in condition_keywords:
            if ck in paragraph:
                parts = paragraph.split(ck, 1)
                if len(parts) == 2:
                    potential_condition = parts[0].strip()
                    remaining = parts[1].strip()
                    
                    # 查找动作部分
                    for ak in action_keywords:
                        if ak in remaining:
                            action_parts = remaining.split(ak, 1)
                            if len(action_parts) == 2:
                                condition = potential_condition
                                action = action_parts[1].strip()
                                break
                    break
        
        return condition or "适用条件", action or "执行检查"
    
    def _generate_message_template(self, rule_name: str, rule_content: str) -> str:
        """生成消息模板"""
        return f"规则违反 [{rule_name}]: {rule_content[:50]}... 详情: {{details}}"
    
    def _extract_fix_suggestions(self, paragraph: str) -> List[str]:
        """提取修复建议"""
        suggestions = []
        
        # 查找建议关键词
        suggestion_keywords = ['建议', '应该', '可以', '推荐', 'recommend', 'suggest']
        
        sentences = re.split(r'[。！？；]', paragraph)
        for sentence in sentences:
            if any(keyword in sentence for keyword in suggestion_keywords):
                suggestions.append(sentence.strip())
        
        return suggestions[:3]  # 最多3个建议
    
    def _map_severity(self, pattern_severity: str) -> str:
        """映射严重程度"""
        severity_map = {
            'mandatory': 'error',
            'recommended': 'warning',
            'optional': 'info'
        }
        return severity_map.get(pattern_severity, 'warning')
    
    def _calculate_confidence(self, rule_content: str, paragraph: str) -> float:
        """计算置信度"""
        confidence = 0.5  # 基础置信度
        
        # 根据规则内容长度调整
        if len(rule_content) > 20:
            confidence += 0.1
        
        # 根据关键词密度调整
        technical_terms = self.text_analyzer.extract_technical_terms(paragraph)
        if len(technical_terms) > 3:
            confidence += 0.2
        
        # 根据结构完整性调整
        if '应当' in rule_content or 'must' in rule_content.lower():
            confidence += 0.1
        
        return min(confidence, 1.0)
