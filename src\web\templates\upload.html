{% extends "base.html" %}

{% block title %}文件上传验证 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <i class="fas fa-upload fa-2x text-primary me-3"></i>
                <div>
                    <h1 class="mb-1">IEC61850配置文件验证</h1>
                    <p class="text-muted mb-0">上传您的配置文件，获得专业的验证分析</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 使命说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-primary border-0">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="fas fa-info-circle fa-2x"></i>
                    </div>
                    <div class="col-md-11">
                        <h6 class="mb-2">
                            <i class="fas fa-bullseye me-2"></i>
                            解决虚端子连接检查难题
                        </h6>
                        <p class="mb-0">
                            智能变电站的虚端子使传统回路检查变得困难。
                            本工具通过智能分析，帮助您快速发现配置错误，确保设计质量。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 文件上传区域 -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cloud-upload-alt me-2"></i>
                        文件上传
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 拖拽上传区域 -->
                    <div id="dropZone" class="drop-zone border-2 border-dashed rounded p-5 text-center mb-4">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">拖拽文件到此处或点击选择</h5>
                        <p class="text-muted mb-3">
                            支持 .scd, .icd, .cid, .xml 格式文件
                        </p>
                        <input type="file" id="fileInput" class="d-none" accept=".scd,.icd,.cid,.xml">
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            <i class="fas fa-folder-open me-2"></i>
                            选择文件
                        </button>
                    </div>

                    <!-- 文件信息显示 -->
                    <div id="fileInfo" class="d-none">
                        <div class="alert alert-success border-0">
                            <div class="row align-items-center">
                                <div class="col-md-1 text-center">
                                    <i class="fas fa-file-check fa-2x"></i>
                                </div>
                                <div class="col-md-11">
                                    <h6 class="mb-2">文件已选择</h6>
                                    <div id="fileDetails"></div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-success me-2" onclick="startValidation()">
                                            <i class="fas fa-play me-2"></i>
                                            开始验证
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="resetUpload()">
                                            <i class="fas fa-redo me-2"></i>
                                            重新选择
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 验证进度 -->
                    <div id="validationProgress" class="d-none">
                        <div class="alert alert-info border-0">
                            <h6 class="mb-3">
                                <i class="fas fa-cogs me-2"></i>
                                正在验证配置文件...
                            </h6>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText">准备开始验证...</div>
                        </div>
                    </div>

                    <!-- 验证结果 -->
                    <div id="validationResult" class="d-none">
                        <!-- 结果将通过JavaScript动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="col-lg-4">
            <!-- 支持的文件格式 -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        支持的文件格式
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-code text-primary me-3"></i>
                                <div>
                                    <strong>.scd</strong>
                                    <small class="text-muted d-block">系统配置描述文件</small>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-code text-success me-3"></i>
                                <div>
                                    <strong>.icd</strong>
                                    <small class="text-muted d-block">IED能力描述文件</small>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-code text-warning me-3"></i>
                                <div>
                                    <strong>.cid</strong>
                                    <small class="text-muted d-block">已配置IED描述文件</small>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-code text-info me-3"></i>
                                <div>
                                    <strong>.xml</strong>
                                    <small class="text-muted d-block">通用XML配置文件</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 验证规则说明 -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        验证规则
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            IEC61850标准符合性检查
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            设备配置完整性验证
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            网络通信配置检查
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            数据类型引用完整性
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            命名规范和唯一性
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 使用提示 -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        使用提示
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            文件大小限制：50MB
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            支持拖拽上传
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            验证结果可导出报告
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            提供详细修复建议
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedFile = null;
let fileId = null;

// 拖拽上传功能
const dropZone = document.getElementById('dropZone');
const fileInput = document.getElementById('fileInput');

// 拖拽事件处理
dropZone.addEventListener('dragover', (e) => {
    e.preventDefault();
    dropZone.classList.add('drag-over');
});

dropZone.addEventListener('dragleave', () => {
    dropZone.classList.remove('drag-over');
});

dropZone.addEventListener('drop', (e) => {
    e.preventDefault();
    dropZone.classList.remove('drag-over');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

// 文件选择事件
fileInput.addEventListener('change', (e) => {
    if (e.target.files.length > 0) {
        handleFileSelect(e.target.files[0]);
    }
});

// 处理文件选择
function handleFileSelect(file) {
    // 验证文件类型
    const allowedTypes = ['.scd', '.icd', '.cid', '.xml'];
    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExt)) {
        showToast('错误', '不支持的文件类型', 'error');
        return;
    }
    
    // 验证文件大小 (50MB)
    if (file.size > 50 * 1024 * 1024) {
        showToast('错误', '文件大小不能超过50MB', 'error');
        return;
    }
    
    selectedFile = file;
    showFileInfo(file);
}

// 显示文件信息
function showFileInfo(file) {
    const fileInfo = document.getElementById('fileInfo');
    const fileDetails = document.getElementById('fileDetails');
    
    fileDetails.innerHTML = `
        <div class="row">
            <div class="col-sm-3"><strong>文件名：</strong></div>
            <div class="col-sm-9">${file.name}</div>
        </div>
        <div class="row">
            <div class="col-sm-3"><strong>文件大小：</strong></div>
            <div class="col-sm-9">${formatFileSize(file.size)}</div>
        </div>
        <div class="row">
            <div class="col-sm-3"><strong>文件类型：</strong></div>
            <div class="col-sm-9">${file.type || '未知'}</div>
        </div>
    `;
    
    fileInfo.classList.remove('d-none');
}

// 开始验证
async function startValidation() {
    if (!selectedFile) {
        showToast('错误', '请先选择文件', 'error');
        return;
    }
    
    try {
        // 显示进度
        showProgress();
        updateProgress(10, '正在上传文件...');
        
        // 上传文件
        const uploadResult = await uploadFile(selectedFile);
        if (!uploadResult.success) {
            throw new Error(uploadResult.error);
        }
        
        fileId = uploadResult.data.file_id;
        updateProgress(50, '文件上传成功，开始验证...');
        
        // 执行验证
        const validationResult = await validateFile(fileId);
        updateProgress(100, '验证完成');
        
        // 显示结果
        setTimeout(() => {
            hideProgress();
            showValidationResult(validationResult);
        }, 1000);
        
    } catch (error) {
        hideProgress();
        showToast('验证失败', error.message, 'error');
    }
}

// 上传文件
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 验证文件
async function validateFile(fileId) {
    const response = await fetch(`/api/validate/${fileId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            max_workers: 2,
            rule_timeout: 30.0,
            stop_on_error: false
        })
    });
    
    return await response.json();
}

// 显示验证结果
function showValidationResult(result) {
    const resultDiv = document.getElementById('validationResult');
    
    if (result.success) {
        const summary = result.data.validation_summary;
        const issues = result.data.issues;
        
        resultDiv.innerHTML = `
            <div class="alert alert-success border-0">
                <h5 class="mb-3">
                    <i class="fas fa-check-circle me-2"></i>
                    验证完成
                </h5>
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-primary">${summary.executed_rules}</div>
                            <small class="text-muted">执行规则</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-danger">${summary.errors}</div>
                            <small class="text-muted">错误</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning">${summary.warnings}</div>
                            <small class="text-muted">警告</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info">${summary.infos}</div>
                            <small class="text-muted">信息</small>
                        </div>
                    </div>
                </div>
                <div class="d-flex gap-2">
                    <a href="/validate?report_id=${result.data.report_id}" class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>查看详细结果
                    </a>
                    <a href="/api/report/${result.data.report_id}/download" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>下载报告
                    </a>
                </div>
            </div>
        `;
    } else {
        resultDiv.innerHTML = `
            <div class="alert alert-danger border-0">
                <h5 class="mb-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    验证失败
                </h5>
                <p class="mb-0">${result.error}</p>
            </div>
        `;
    }
    
    resultDiv.classList.remove('d-none');
}

// 工具函数
function showProgress() {
    document.getElementById('validationProgress').classList.remove('d-none');
}

function hideProgress() {
    document.getElementById('validationProgress').classList.add('d-none');
}

function updateProgress(percent, text) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = percent + '%';
    progressText.textContent = text;
}

function resetUpload() {
    selectedFile = null;
    fileId = null;
    document.getElementById('fileInfo').classList.add('d-none');
    document.getElementById('validationResult').classList.add('d-none');
    document.getElementById('fileInput').value = '';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function showToast(title, message, type = 'info') {
    // 实现Toast提示功能
    const toast = document.getElementById('globalToast');
    const toastBody = toast.querySelector('.toast-body');
    
    toastBody.textContent = message;
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}
</script>

<style>
.drop-zone {
    transition: all 0.3s ease;
    cursor: pointer;
}

.drop-zone:hover,
.drop-zone.drag-over {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}

.progress-bar {
    transition: width 0.3s ease;
}
</style>
{% endblock %}
