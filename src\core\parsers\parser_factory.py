"""
解析器工厂类
根据文件类型自动选择合适的解析器
"""

from pathlib import Path
from typing import Optional, Dict, Type, List, Union
from lxml import etree
import logging

from .base_parser import BaseParser, ParseResult, ParseError
from .scd_parser import SCDParser
from .icd_parser import ICDParser
from .cid_parser import CIDParser


class ParserFactory:
    """解析器工厂"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 注册解析器
        self._parsers: Dict[str, Type[BaseParser]] = {
            'SCD': SCDParser,
            'ICD': ICDParser,
            'CID': CIDParser,
            'SCL': SCDParser  # 通用SCL文件使用SCD解析器
        }
        
        # 文件扩展名映射
        self._extension_mapping = {
            '.scd': 'SCD',
            '.icd': 'ICD',
            '.cid': 'CID',
            '.xml': None  # XML文件需要进一步检测
        }
    
    def create_parser(self, file_path: Union[str, Path], 
                     parser_type: Optional[str] = None,
                     validate_schema: bool = True,
                     strict_mode: bool = False) -> BaseParser:
        """
        创建解析器
        
        Args:
            file_path: 文件路径
            parser_type: 指定解析器类型，如果为None则自动检测
            validate_schema: 是否进行Schema验证
            strict_mode: 是否启用严格模式
            
        Returns:
            解析器实例
        """
        if parser_type is None:
            parser_type = self.detect_file_type(file_path)
        
        if parser_type not in self._parsers:
            raise ValueError(f"不支持的解析器类型: {parser_type}")
        
        parser_class = self._parsers[parser_type]
        return parser_class(validate_schema=validate_schema, strict_mode=strict_mode)
    
    def detect_file_type(self, file_path: Union[str, Path]) -> str:
        """
        检测文件类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件类型字符串
        """
        path = Path(file_path)
        
        # 首先根据文件扩展名判断
        extension = path.suffix.lower()
        if extension in self._extension_mapping:
            mapped_type = self._extension_mapping[extension]
            if mapped_type:
                return mapped_type
        
        # 对于.xml文件或未知扩展名，需要检查文件内容
        if path.exists():
            return self._detect_by_content(path)
        else:
            # 文件不存在时，尝试从文件名推断
            return self._detect_by_filename(path)
    
    def _detect_by_content(self, file_path: Path) -> str:
        """
        通过文件内容检测类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件类型字符串
        """
        try:
            # 解析XML文件
            tree = etree.parse(str(file_path))
            root = tree.getroot()
            
            # 检查根元素
            if root.tag != 'SCL':
                raise ValueError(f"不支持的根元素: {root.tag}")
            
            # 分析文件内容特征来判断类型
            return self._analyze_scl_content(root)
            
        except etree.XMLSyntaxError as e:
            raise ParseError(f"XML语法错误: {e.msg}", line_number=e.lineno)
        except Exception as e:
            self.logger.warning(f"内容检测失败: {str(e)}")
            return 'SCL'  # 默认返回通用SCL类型
    
    def _detect_by_filename(self, file_path: Path) -> str:
        """
        通过文件名检测类型
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件类型字符串
        """
        filename = file_path.name.lower()
        
        # 检查文件名中的关键词
        if 'scd' in filename or 'system' in filename:
            return 'SCD'
        elif 'icd' in filename or 'capability' in filename:
            return 'ICD'
        elif 'cid' in filename or 'configured' in filename:
            return 'CID'
        else:
            return 'SCL'  # 默认类型
    
    def _analyze_scl_content(self, root: etree.Element) -> str:
        """
        分析SCL内容特征
        
        Args:
            root: SCL根元素
            
        Returns:
            文件类型字符串
        """
        # 检查子元素来判断文件类型
        has_substation = root.find('Substation') is not None
        has_communication = root.find('Communication') is not None
        has_ied = root.find('IED') is not None
        has_data_types = root.find('DataTypeTemplates') is not None
        
        ied_count = len(root.findall('IED'))
        
        # 根据内容特征判断类型
        if has_substation and has_communication and ied_count > 1:
            # 包含变电站、通信配置和多个IED，很可能是SCD
            return 'SCD'
        elif has_communication and ied_count >= 1:
            # 包含通信配置和IED，很可能是CID
            return 'CID'
        elif has_ied and has_data_types and ied_count == 1:
            # 包含单个IED和数据类型模板，很可能是ICD
            return 'ICD'
        elif has_ied and ied_count == 1:
            # 只包含单个IED，可能是ICD或简化的CID
            if has_communication:
                return 'CID'
            else:
                return 'ICD'
        else:
            # 默认为SCD类型
            return 'SCD'
    
    def parse_file(self, file_path: Union[str, Path],
                  parser_type: Optional[str] = None,
                  validate_schema: bool = True,
                  strict_mode: bool = False) -> ParseResult:
        """
        解析文件（便捷方法）
        
        Args:
            file_path: 文件路径
            parser_type: 指定解析器类型
            validate_schema: 是否进行Schema验证
            strict_mode: 是否启用严格模式
            
        Returns:
            解析结果
        """
        try:
            parser = self.create_parser(
                file_path=file_path,
                parser_type=parser_type,
                validate_schema=validate_schema,
                strict_mode=strict_mode
            )
            
            result = parser.parse_file(file_path)
            
            # 添加检测到的文件类型信息
            if 'detected_type' not in result.metadata:
                detected_type = parser_type or self.detect_file_type(file_path)
                result.metadata['detected_type'] = detected_type
                result.metadata['parser_class'] = parser.__class__.__name__
            
            return result
            
        except Exception as e:
            result = ParseResult(file_path=str(file_path))
            result.add_error(f"创建解析器失败: {str(e)}")
            return result
    
    def parse_string(self, xml_content: str,
                    parser_type: str = 'SCL',
                    validate_schema: bool = True,
                    strict_mode: bool = False) -> ParseResult:
        """
        解析XML字符串（便捷方法）
        
        Args:
            xml_content: XML内容字符串
            parser_type: 解析器类型
            validate_schema: 是否进行Schema验证
            strict_mode: 是否启用严格模式
            
        Returns:
            解析结果
        """
        try:
            if parser_type not in self._parsers:
                raise ValueError(f"不支持的解析器类型: {parser_type}")
            
            parser_class = self._parsers[parser_type]
            parser = parser_class(validate_schema=validate_schema, strict_mode=strict_mode)
            
            result = parser.parse_string(xml_content)
            result.metadata['parser_type'] = parser_type
            result.metadata['parser_class'] = parser.__class__.__name__
            
            return result
            
        except Exception as e:
            result = ParseResult()
            result.add_error(f"创建解析器失败: {str(e)}")
            return result
    
    def get_supported_types(self) -> List[str]:
        """获取支持的解析器类型"""
        return list(self._parsers.keys())
    
    def get_supported_extensions(self) -> List[str]:
        """获取支持的文件扩展名"""
        return list(self._extension_mapping.keys())
    
    def register_parser(self, parser_type: str, parser_class: Type[BaseParser]) -> None:
        """
        注册新的解析器
        
        Args:
            parser_type: 解析器类型名称
            parser_class: 解析器类
        """
        if not issubclass(parser_class, BaseParser):
            raise ValueError("解析器类必须继承自BaseParser")
        
        self._parsers[parser_type] = parser_class
        self.logger.info(f"注册解析器: {parser_type} -> {parser_class.__name__}")
    
    def unregister_parser(self, parser_type: str) -> bool:
        """
        注销解析器
        
        Args:
            parser_type: 解析器类型名称
            
        Returns:
            是否成功注销
        """
        if parser_type in self._parsers:
            del self._parsers[parser_type]
            self.logger.info(f"注销解析器: {parser_type}")
            return True
        return False
    
    def get_parser_info(self, parser_type: str) -> Optional[Dict[str, any]]:
        """
        获取解析器信息
        
        Args:
            parser_type: 解析器类型
            
        Returns:
            解析器信息字典
        """
        if parser_type not in self._parsers:
            return None
        
        parser_class = self._parsers[parser_type]
        
        # 创建临时实例获取信息
        temp_parser = parser_class()
        
        return {
            'type': parser_type,
            'class_name': parser_class.__name__,
            'supported_file_types': temp_parser.get_supported_file_types(),
            'root_element_name': temp_parser.get_root_element_name(),
            'module': parser_class.__module__,
            'doc': parser_class.__doc__
        }
    
    def get_all_parser_info(self) -> Dict[str, Dict[str, any]]:
        """获取所有解析器信息"""
        info = {}
        for parser_type in self._parsers:
            info[parser_type] = self.get_parser_info(parser_type)
        return info


# 创建全局工厂实例
parser_factory = ParserFactory()
