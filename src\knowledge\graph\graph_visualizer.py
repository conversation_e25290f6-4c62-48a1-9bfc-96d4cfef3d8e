"""
知识图谱可视化模块
提供电力系统知识图谱的可视化功能
"""

import logging
from typing import Dict, List, Any, Optional
import matplotlib.pyplot as plt
import networkx as nx

logger = logging.getLogger(__name__)


class GraphVisualizer:
    """知识图谱可视化器"""
    
    def __init__(self, knowledge_graph):
        """
        初始化可视化器
        
        Args:
            knowledge_graph: 知识图谱实例
        """
        self.graph = knowledge_graph
        logger.info("知识图谱可视化器初始化完成")
    
    def visualize_knowledge_graph(self, 
                                output_path: Optional[str] = None,
                                layout: str = 'spring',
                                max_nodes: int = 100) -> None:
        """
        可视化知识图谱
        
        Args:
            output_path: 输出路径，如果为None则显示图形
            layout: 布局类型 ('spring', 'circular', 'random')
            max_nodes: 最大显示节点数
        """
        try:
            # 创建NetworkX图
            nx_graph = nx.DiGraph()
            
            # 添加节点
            node_count = 0
            for entity_id, entity in list(self.graph.entities.items())[:max_nodes]:
                node_label = f"{entity.name}\n({entity.type.value})"
                nx_graph.add_node(entity_id, label=node_label, type=entity.type.value)
                node_count += 1
            
            # 添加边
            edge_count = 0
            for relation_id, relation in self.graph.relations.items():
                if relation.source in nx_graph.nodes() and relation.target in nx_graph.nodes():
                    nx_graph.add_edge(
                        relation.source, 
                        relation.target, 
                        label=relation.type.value,
                        confidence=relation.confidence
                    )
                    edge_count += 1
            
            # 选择布局
            if layout == 'spring':
                pos = nx.spring_layout(nx_graph)
            elif layout == 'circular':
                pos = nx.circular_layout(nx_graph)
            else:
                pos = nx.random_layout(nx_graph)
            
            # 绘制图形
            plt.figure(figsize=(12, 8))
            
            # 绘制节点
            nx.draw_networkx_nodes(nx_graph, pos, node_size=800, node_color='lightblue', alpha=0.7)
            
            # 绘制边
            nx.draw_networkx_edges(nx_graph, pos, arrowstyle='->', arrowsize=20, edge_color='gray', alpha=0.5)
            
            # 绘制标签
            labels = nx.get_node_attributes(nx_graph, 'label')
            nx.draw_networkx_labels(nx_graph, pos, labels, font_size=8)
            
            # 绘制边标签
            edge_labels = nx.get_edge_attributes(nx_graph, 'label')
            nx.draw_networkx_edge_labels(nx_graph, pos, edge_labels, font_size=6)
            
            plt.title(f"电力系统知识图谱 (节点数: {node_count}, 边数: {edge_count})")
            plt.axis('off')
            
            # 保存或显示
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                logger.info(f"知识图谱已保存到: {output_path}")
            else:
                plt.show()
                
        except Exception as e:
            logger.error(f"知识图谱可视化失败: {e}")
    
    def visualize_circuit_logic(self, 
                              circuit_id: str,
                              output_path: Optional[str] = None) -> None:
        """
        可视化特定回路逻辑
        
        Args:
            circuit_id: 回路ID
            output_path: 输出路径
        """
        try:
            if circuit_id not in self.graph.circuits:
                logger.warning(f"回路 {circuit_id} 不存在")
                return
            
            circuit = self.graph.circuits[circuit_id]
            
            # 创建回路图
            circuit_graph = nx.DiGraph()
            
            # 添加回路中的节点
            for entity_id in circuit.entities:
                if entity_id in self.graph.entities:
                    entity = self.graph.entities[entity_id]
                    node_label = f"{entity.name}\n({entity.type.value})"
                    circuit_graph.add_node(entity_id, label=node_label, type=entity.type.value)
            
            # 添加回路中的关系
            for relation_id in circuit.relations:
                if relation_id in self.graph.relations:
                    relation = self.graph.relations[relation_id]
                    if (relation.source in circuit_graph.nodes() and 
                        relation.target in circuit_graph.nodes()):
                        circuit_graph.add_edge(
                            relation.source, 
                            relation.target, 
                            label=relation.type.value
                        )
            
            # 绘制回路图
            plt.figure(figsize=(10, 6))
            pos = nx.spring_layout(circuit_graph)
            
            # 绘制节点
            nx.draw_networkx_nodes(circuit_graph, pos, node_size=1000, node_color='lightgreen', alpha=0.7)
            
            # 绘制边
            nx.draw_networkx_edges(circuit_graph, pos, arrowstyle='->', arrowsize=20, edge_color='blue', alpha=0.7)
            
            # 绘制标签
            labels = nx.get_node_attributes(circuit_graph, 'label')
            nx.draw_networkx_labels(circuit_graph, pos, labels, font_size=8)
            
            # 绘制边标签
            edge_labels = nx.get_edge_attributes(circuit_graph, 'label')
            nx.draw_networkx_edge_labels(circuit_graph, pos, edge_labels, font_size=6)
            
            plt.title(f"回路逻辑图: {circuit.name}")
            plt.axis('off')
            
            # 保存或显示
            if output_path:
                plt.savefig(output_path, dpi=300, bbox_inches='tight')
                logger.info(f"回路逻辑图已保存到: {output_path}")
            else:
                plt.show()
                
        except Exception as e:
            logger.error(f"回路逻辑可视化失败: {e}")
    
    def generate_statistics_report(self) -> Dict[str, Any]:
        """
        生成知识图谱统计报告
        
        Returns:
            Dict[str, Any]: 统计报告
        """
        try:
            # 实体统计
            entity_types = {}
            for entity in self.graph.entities.values():
                entity_type = entity.type.value
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            # 关系统计
            relation_types = {}
            for relation in self.graph.relations.values():
                relation_type = relation.type.value
                relation_types[relation_type] = relation_types.get(relation_type, 0) + 1
            
            # 回路统计
            circuit_types = {}
            for circuit in self.graph.circuits.values():
                circuit_type = circuit.type
                circuit_types[circuit_type] = circuit_types.get(circuit_type, 0) + 1
            
            report = {
                'entity_statistics': {
                    'total_entities': len(self.graph.entities),
                    'entity_types': entity_types
                },
                'relation_statistics': {
                    'total_relations': len(self.graph.relations),
                    'relation_types': relation_types
                },
                'circuit_statistics': {
                    'total_circuits': len(self.graph.circuits),
                    'circuit_types': circuit_types
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成统计报告失败: {e}")
            return {}


def main():
    """主函数 - 演示可视化功能"""
    print("知识图谱可视化模块演示")
    print("=" * 30)
    print("该模块提供知识图谱的可视化功能")


if __name__ == "__main__":
    main()