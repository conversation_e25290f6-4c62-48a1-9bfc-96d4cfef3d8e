"""
数据类型规则
实现数据类型相关的验证规则
"""

from typing import List, Set, Dict, Any
from ..models import DataTypeTemplates, LNodeType, DOType, DAType, EnumType
from ..parsers.scd_parser import SCLDocument
from .base import (
    BaseRule, RuleContext, RuleResult, RuleCategory, RuleSeverity,
    rule, applicable_to
)


class DataTypeRules:
    """数据类型规则集合"""
    
    @staticmethod
    @rule(
        rule_id="DT001",
        name="数据类型引用完整性",
        description="检查数据类型引用的完整性",
        category=RuleCategory.DATATYPE,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(DataTypeTemplates)
    def check_type_reference_integrity(context: RuleContext) -> RuleResult:
        """检查类型引用完整性"""
        result = RuleResult(rule_id="DT001", success=True)
        templates = context.data
        
        # 收集所有定义的类型
        defined_lnode_types = {lnt.name for lnt in templates.lnode_types}
        defined_do_types = {dot.name for dot in templates.do_types}
        defined_da_types = {dat.name for dat in templates.da_types}
        defined_enum_types = {et.name for et in templates.enum_types}
        
        # 检查LNodeType中的DO类型引用
        for lnode_type in templates.lnode_types:
            for do in lnode_type.dos:
                if do.type and do.type not in defined_do_types:
                    result.add_error(
                        f"LNodeType '{lnode_type.name}' 中的DO '{do.name}' 引用了未定义的DOType: {do.type}",
                        f"{context.get_path_string()}.lnode_types"
                    )
        
        # 检查DOType中的DA类型引用
        for do_type in templates.do_types:
            for da in do_type.das:
                if da.type:
                    # DA可能引用DAType或EnumType
                    if (da.type not in defined_da_types and 
                        da.type not in defined_enum_types):
                        result.add_error(
                            f"DOType '{do_type.name}' 中的DA '{da.name}' 引用了未定义的类型: {da.type}",
                            f"{context.get_path_string()}.do_types"
                        )
            
            for sdo in do_type.sdos:
                if sdo.type and sdo.type not in defined_do_types:
                    result.add_error(
                        f"DOType '{do_type.name}' 中的SDO '{sdo.name}' 引用了未定义的DOType: {sdo.type}",
                        f"{context.get_path_string()}.do_types"
                    )
        
        # 检查DAType中的BDA类型引用
        for da_type in templates.da_types:
            for bda in da_type.bdas:
                if bda.type:
                    if (bda.type not in defined_da_types and 
                        bda.type not in defined_enum_types):
                        result.add_error(
                            f"DAType '{da_type.name}' 中的BDA '{bda.name}' 引用了未定义的类型: {bda.type}",
                            f"{context.get_path_string()}.da_types"
                        )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DT002",
        name="逻辑节点类型标准性检查",
        description="检查逻辑节点类型是否符合IEC61850标准",
        category=RuleCategory.DATATYPE,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(LNodeType)
    def check_lnode_type_standard(context: RuleContext) -> RuleResult:
        """检查逻辑节点类型标准性"""
        result = RuleResult(rule_id="DT002", success=True)
        lnode_type = context.data
        
        # IEC61850标准逻辑节点类
        standard_ln_classes = {
            # 系统逻辑节点
            'LLN0': '逻辑节点零',
            'LPHD': '物理设备',
            
            # 保护功能
            'PTOC': '过流保护',
            'PDIF': '差动保护',
            'PDIR': '方向保护',
            'PDIS': '距离保护',
            'PTOV': '过压保护',
            'PTUF': '欠频保护',
            'PTOF': '过频保护',
            'PTUV': '欠压保护',
            
            # 控制功能
            'CSWI': '开关控制',
            'CILO': '联锁',
            'CALH': '报警处理',
            'CCGR': '通用控制',
            
            # 测量功能
            'MMXU': '测量',
            'MMXN': '中性点测量',
            'MMTR': '变压器测量',
            'MSQI': '序分量测量',
            'MHAI': '谐波分析',
            
            # 监视功能
            'MSTA': '统计',
            'MMET': '计量',
            'MDIF': '差值监视',
            
            # 自动化功能
            'ATCC': '分接头控制',
            'AVCO': '电压控制',
            'ARCO': '无功控制',
            'ANCO': '中性点控制',
            
            # 通用功能
            'GGIO': '通用过程I/O',
            'GAPC': '通用自动过程控制',
            'GSAL': '通用安全应用逻辑',
            'GLOG': '通用事件记录',
            'GFIL': '通用文件传输',
            'GCOM': '通用通信',
            'GSET': '通用设置',
            'GSAL': '通用安全应用逻辑'
        }
        
        if not lnode_type.lnclass:
            result.add_error(
                f"LNodeType '{lnode_type.name}' 缺少lnClass属性",
                context.get_path_string()
            )
        elif lnode_type.lnclass not in standard_ln_classes:
            result.add_warning(
                f"LNodeType '{lnode_type.name}' 使用了非标准逻辑节点类: {lnode_type.lnclass}",
                context.get_path_string(),
                details=f"标准逻辑节点类包括: {', '.join(list(standard_ln_classes.keys())[:10])}...",
                suggestion="使用标准逻辑节点类或确认自定义类的必要性"
            )
        else:
            result.add_info(
                f"LNodeType '{lnode_type.name}' 使用标准逻辑节点类: {lnode_type.lnclass} ({standard_ln_classes[lnode_type.lnclass]})",
                context.get_path_string()
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DT003",
        name="数据对象类型CDC检查",
        description="检查数据对象类型的CDC是否符合标准",
        category=RuleCategory.DATATYPE,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(DOType)
    def check_do_type_cdc(context: RuleContext) -> RuleResult:
        """检查数据对象类型CDC"""
        result = RuleResult(rule_id="DT003", success=True)
        do_type = context.data
        
        # IEC61850标准CDC类型
        standard_cdcs = {
            # 状态信息
            'SPS': '单点状态',
            'DPS': '双点状态',
            'INS': '整数状态',
            'ENS': '枚举状态',
            'ACT': '保护激活信息',
            'ACD': '保护启动信息',
            
            # 测量值
            'MV': '测量值',
            'CMV': '复数测量值',
            'SAV': '采样值',
            'WYE': '三相测量值',
            'DEL': '三角形测量值',
            'SEQ': '序分量',
            
            # 控制
            'SPC': '单点控制',
            'DPC': '双点控制',
            'INC': '整数控制',
            'ENC': '枚举控制',
            'BSC': '二进制状态控制',
            'ISC': '整数状态控制',
            'APC': '模拟过程控制',
            
            # 设置值
            'SPG': '设置点组',
            'ING': '整数设置组',
            'ENG': '枚举设置组',
            'ASG': '模拟设置组',
            'CURVE': '曲线',
            
            # 其他
            'LPL': '逻辑节点名牌',
            'DPL': '设备名牌',
            'CSD': '曲线形状描述',
            'CSG': '曲线设置组',
            'ORG': '操作记录组',
            'TSG': '时间同步组',
            'VSG': '可见字符串组'
        }
        
        if not do_type.cdc:
            result.add_error(
                f"DOType '{do_type.name}' 缺少cdc属性",
                context.get_path_string()
            )
        elif do_type.cdc not in standard_cdcs:
            result.add_warning(
                f"DOType '{do_type.name}' 使用了非标准CDC: {do_type.cdc}",
                context.get_path_string(),
                details=f"标准CDC包括: {', '.join(list(standard_cdcs.keys())[:10])}...",
                suggestion="使用标准CDC或确认自定义CDC的必要性"
            )
        else:
            result.add_info(
                f"DOType '{do_type.name}' 使用标准CDC: {do_type.cdc} ({standard_cdcs[do_type.cdc]})",
                context.get_path_string()
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DT004",
        name="枚举类型值检查",
        description="检查枚举类型的值定义",
        category=RuleCategory.DATATYPE,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(EnumType)
    def check_enum_type_values(context: RuleContext) -> RuleResult:
        """检查枚举类型值"""
        result = RuleResult(rule_id="DT004", success=True)
        enum_type = context.data
        
        if not enum_type.enum_vals:
            result.add_error(
                f"EnumType '{enum_type.name}' 没有定义枚举值",
                context.get_path_string()
            )
            return result
        
        # 检查枚举值的序号
        ord_values = [ev.ord for ev in enum_type.enum_vals]
        
        # 检查序号唯一性
        if len(ord_values) != len(set(ord_values)):
            duplicates = [ord_val for ord_val in set(ord_values) if ord_values.count(ord_val) > 1]
            result.add_error(
                f"EnumType '{enum_type.name}' 中存在重复的序号: {duplicates}",
                context.get_path_string()
            )
        
        # 检查序号连续性
        sorted_ords = sorted(ord_values)
        if sorted_ords != list(range(min(sorted_ords), max(sorted_ords) + 1)):
            result.add_warning(
                f"EnumType '{enum_type.name}' 的序号不连续: {sorted_ords}",
                context.get_path_string(),
                suggestion="建议使用连续的序号以提高兼容性"
            )
        
        # 检查是否从0开始
        if min(sorted_ords) != 0:
            result.add_info(
                f"EnumType '{enum_type.name}' 的序号不从0开始",
                context.get_path_string(),
                suggestion="建议枚举序号从0开始"
            )
        
        # 检查枚举值名称
        for enum_val in enum_type.enum_vals:
            if not enum_val.value:
                result.add_error(
                    f"EnumType '{enum_type.name}' 中序号 {enum_val.ord} 的值为空",
                    context.get_path_string()
                )
            elif not enum_val.value.replace('-', '').replace('_', '').isalnum():
                result.add_warning(
                    f"EnumType '{enum_type.name}' 中的值 '{enum_val.value}' 包含特殊字符",
                    context.get_path_string(),
                    suggestion="建议使用字母、数字、下划线和连字符"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DT005",
        name="数据类型命名一致性",
        description="检查数据类型命名的一致性",
        category=RuleCategory.DATATYPE,
        severity=RuleSeverity.INFO
    )
    @applicable_to(DataTypeTemplates)
    def check_type_naming_consistency(context: RuleContext) -> RuleResult:
        """检查类型命名一致性"""
        result = RuleResult(rule_id="DT005", success=True)
        templates = context.data
        
        # 检查命名模式
        lnode_type_pattern = r'.*_Type$|.*Type$'
        do_type_pattern = r'.*_Type$|.*Type$'
        da_type_pattern = r'.*_Type$|.*Type$'
        enum_type_pattern = r'.*Enum$|.*_Enum$'
        
        import re
        
        # 检查LNodeType命名
        for lnode_type in templates.lnode_types:
            if not re.match(lnode_type_pattern, lnode_type.name):
                result.add_info(
                    f"LNodeType '{lnode_type.name}' 的命名不符合常见模式",
                    f"{context.get_path_string()}.lnode_types",
                    suggestion="建议LNodeType名称以'Type'结尾"
                )
        
        # 检查DOType命名
        for do_type in templates.do_types:
            if not re.match(do_type_pattern, do_type.name):
                result.add_info(
                    f"DOType '{do_type.name}' 的命名不符合常见模式",
                    f"{context.get_path_string()}.do_types",
                    suggestion="建议DOType名称以'Type'结尾"
                )
        
        # 检查DAType命名
        for da_type in templates.da_types:
            if not re.match(da_type_pattern, da_type.name):
                result.add_info(
                    f"DAType '{da_type.name}' 的命名不符合常见模式",
                    f"{context.get_path_string()}.da_types",
                    suggestion="建议DAType名称以'Type'结尾"
                )
        
        # 检查EnumType命名
        for enum_type in templates.enum_types:
            if not re.match(enum_type_pattern, enum_type.name):
                result.add_info(
                    f"EnumType '{enum_type.name}' 的命名不符合常见模式",
                    f"{context.get_path_string()}.enum_types",
                    suggestion="建议EnumType名称以'Enum'结尾"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DT006",
        name="数据类型复杂度检查",
        description="检查数据类型定义的复杂度",
        category=RuleCategory.DATATYPE,
        severity=RuleSeverity.INFO
    )
    @applicable_to(DataTypeTemplates)
    def check_type_complexity(context: RuleContext) -> RuleResult:
        """检查类型复杂度"""
        result = RuleResult(rule_id="DT006", success=True)
        templates = context.data
        
        # 检查DOType复杂度
        for do_type in templates.do_types:
            total_elements = len(do_type.das) + len(do_type.sdos)
            
            if total_elements > 50:
                result.add_warning(
                    f"DOType '{do_type.name}' 包含 {total_elements} 个元素，复杂度较高",
                    f"{context.get_path_string()}.do_types",
                    suggestion="考虑将复杂的DOType分解为多个简单类型"
                )
            elif total_elements == 0:
                result.add_warning(
                    f"DOType '{do_type.name}' 没有定义任何DA或SDO",
                    f"{context.get_path_string()}.do_types",
                    suggestion="DOType应该包含至少一个DA或SDO"
                )
        
        # 检查DAType复杂度
        for da_type in templates.da_types:
            bda_count = len(da_type.bdas)
            
            if bda_count > 20:
                result.add_warning(
                    f"DAType '{da_type.name}' 包含 {bda_count} 个BDA，复杂度较高",
                    f"{context.get_path_string()}.da_types",
                    suggestion="考虑简化DAType结构"
                )
        
        # 检查EnumType复杂度
        for enum_type in templates.enum_types:
            enum_count = len(enum_type.enum_vals)
            
            if enum_count > 100:
                result.add_warning(
                    f"EnumType '{enum_type.name}' 包含 {enum_count} 个枚举值，数量较多",
                    f"{context.get_path_string()}.enum_types",
                    suggestion="考虑是否需要这么多枚举值"
                )
            elif enum_count < 2:
                result.add_warning(
                    f"EnumType '{enum_type.name}' 只有 {enum_count} 个枚举值",
                    f"{context.get_path_string()}.enum_types",
                    suggestion="枚举类型通常应该有至少2个值"
                )
        
        return result


# 自动注册所有规则
def register_datatype_rules():
    """注册所有数据类型规则"""
    from .registry import rule_registry
    import inspect
    
    for name, method in inspect.getmembers(DataTypeRules, predicate=inspect.isfunction):
        if hasattr(method, 'rule_id'):
            try:
                rule_registry.register(method)
            except Exception as e:
                print(f"注册规则 {name} 失败: {e}")


# 在模块加载时自动注册规则
register_datatype_rules()
