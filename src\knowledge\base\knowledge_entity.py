"""
知识实体定义
定义知识库中的各种实体类型和数据结构
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field
import uuid


class EntityType(str, Enum):
    """实体类型枚举"""
    STANDARD = "standard"           # 标准文档
    RULE = "rule"                  # 验证规则
    DEVICE = "device"              # 设备类型
    PROTOCOL = "protocol"          # 通信协议
    REQUIREMENT = "requirement"    # 技术要求
    CONSTRAINT = "constraint"      # 约束条件
    BEST_PRACTICE = "best_practice" # 最佳实践
    CASE_STUDY = "case_study"      # 案例研究


class ConfidenceLevel(str, Enum):
    """置信度级别"""
    HIGH = "high"       # 高置信度 (>0.8)
    MEDIUM = "medium"   # 中等置信度 (0.5-0.8)
    LOW = "low"         # 低置信度 (<0.5)


class KnowledgeEntity(BaseModel):
    """知识实体基类"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    entity_type: EntityType
    name: str
    description: str
    content: str
    
    # 元数据
    source: str                    # 来源
    version: str = "1.0.0"
    language: str = "zh-CN"
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    # 置信度和质量
    confidence: float = Field(ge=0.0, le=1.0, default=1.0)
    confidence_level: ConfidenceLevel = ConfidenceLevel.HIGH
    quality_score: float = Field(ge=0.0, le=1.0, default=1.0)
    
    # 关联关系
    related_entities: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    keywords: List[str] = Field(default_factory=list)
    
    # 扩展属性
    attributes: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True


class StandardEntity(KnowledgeEntity):
    """标准文档实体"""
    
    entity_type: EntityType = EntityType.STANDARD
    
    # 标准特有属性
    standard_number: str           # 标准编号 (如: GB/T 51072-2014)
    standard_title: str           # 标准标题
    issuing_organization: str     # 发布机构
    effective_date: Optional[datetime] = None
    superseded_by: Optional[str] = None  # 被哪个标准替代
    supersedes: List[str] = Field(default_factory=list)  # 替代了哪些标准
    
    # 标准分类
    category: str = "electrical"   # 标准类别
    domain: str = "substation"     # 应用领域
    scope: str                     # 适用范围
    
    # 章节结构
    chapters: List[Dict[str, Any]] = Field(default_factory=list)
    
    def __init__(self, **data):
        super().__init__(**data)
        if not hasattr(self, 'standard_title'):
            self.standard_title = self.name


class RuleEntity(KnowledgeEntity):
    """验证规则实体"""
    
    entity_type: EntityType = EntityType.RULE
    
    # 规则特有属性
    rule_type: str                 # 规则类型 (syntax, semantic, business)
    severity: str = "error"        # 严重程度 (error, warning, info)
    category: str                  # 规则分类
    
    # 规则逻辑
    condition: str                 # 触发条件
    action: str                   # 执行动作
    message_template: str         # 消息模板
    
    # 适用范围
    applicable_standards: List[str] = Field(default_factory=list)
    applicable_devices: List[str] = Field(default_factory=list)
    applicable_protocols: List[str] = Field(default_factory=list)
    
    # 规则参数
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    # 修复建议
    fix_suggestions: List[str] = Field(default_factory=list)
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.message_template:
            self.message_template = f"Rule {self.name} violation: {self.description}"


class DeviceEntity(KnowledgeEntity):
    """设备实体"""
    
    entity_type: EntityType = EntityType.DEVICE
    
    # 设备特有属性
    device_type: str              # 设备类型
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    version: Optional[str] = None
    
    # 技术规格
    specifications: Dict[str, Any] = Field(default_factory=dict)
    supported_protocols: List[str] = Field(default_factory=list)
    supported_functions: List[str] = Field(default_factory=list)
    
    # 兼容性信息
    compatible_devices: List[str] = Field(default_factory=list)
    incompatible_devices: List[str] = Field(default_factory=list)


class ProtocolEntity(KnowledgeEntity):
    """通信协议实体"""
    
    entity_type: EntityType = EntityType.PROTOCOL
    
    # 协议特有属性
    protocol_type: str            # 协议类型
    version: str                  # 协议版本
    standard_reference: str       # 标准参考
    
    # 协议规格
    specifications: Dict[str, Any] = Field(default_factory=dict)
    message_types: List[str] = Field(default_factory=list)
    data_types: List[str] = Field(default_factory=list)
    
    # 兼容性
    compatible_versions: List[str] = Field(default_factory=list)
    backward_compatible: bool = True


class RequirementEntity(KnowledgeEntity):
    """技术要求实体"""
    
    entity_type: EntityType = EntityType.REQUIREMENT
    
    # 要求特有属性
    requirement_type: str         # 要求类型
    priority: str = "medium"      # 优先级 (high, medium, low)
    mandatory: bool = True        # 是否强制
    
    # 验证方法
    verification_method: str      # 验证方法
    test_procedures: List[str] = Field(default_factory=list)
    acceptance_criteria: str      # 验收标准
    
    # 关联标准
    source_standard: str          # 来源标准
    clause_reference: str         # 条款引用


class RelationshipType(str, Enum):
    """关系类型枚举"""
    DEPENDS_ON = "depends_on"
    IMPLEMENTS = "implements"
    REFERENCES = "references"
    SUPERSEDES = "supersedes"
    COMPATIBLE_WITH = "compatible_with"
    CONFLICTS_WITH = "conflicts_with"
    DERIVED_FROM = "derived_from"
    APPLIES_TO = "applies_to"


class EntityRelationship(BaseModel):
    """实体关系"""
    
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source_entity_id: str
    target_entity_id: str
    relationship_type: RelationshipType
    
    # 关系属性
    strength: float = Field(ge=0.0, le=1.0, default=1.0)
    confidence: float = Field(ge=0.0, le=1.0, default=1.0)
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now)
    created_by: str = "system"
    
    # 扩展属性
    attributes: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True
