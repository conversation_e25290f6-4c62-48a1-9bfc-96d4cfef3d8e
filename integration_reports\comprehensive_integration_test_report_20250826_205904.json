{"test_summary": {"test_time": "2025-08-26T20:59:04.711412", "total_scenarios": 7, "successful_scenarios": 7, "average_score": 67.85714285714286, "success_rate": 100.0, "functional_test_rate": 100.0}, "test_results": {"端到端SCD审查流程": {"scenario_name": "端到端SCD审查流程", "description": "完整的SCD文件审查流程，从解析到报告生成", "start_time": "2025-08-26T20:59:03.496131", "success": true, "integration_score": 15, "performance_metrics": {}, "functional_tests": {"scd_file_creation": true}, "issues": ["SCD解析失败: ['根元素不是SCL，不是有效的SCD文件']"], "recommendations": [], "end_time": "2025-08-26T20:59:03.514183", "duration": 0.018052}, "智能审查系统集成": {"scenario_name": "智能审查系统集成", "description": "智能审查系统的完整功能验证", "start_time": "2025-08-26T20:59:03.515110", "success": true, "integration_score": 45, "performance_metrics": {}, "functional_tests": {"demo_availability": 100.0, "intelligence_rate": 100.0}, "issues": ["demo_scd_intelligent_review.py 执行失败:   File \"F:\\baohu1\\demo_scd_intelligent_review.py\", line 243\n    def _step_knowledge_base_matching(self):\n                                            ^\nIndentationError: unindent does not match any outer indentation level\n", "统一审查引擎测试失败: attempted relative import with no known parent package"], "recommendations": ["建议完善智能审查系统的功能模块"], "end_time": "2025-08-26T20:59:03.687976", "duration": 0.172866}, "知识推理引擎协作": {"scenario_name": "知识推理引擎协作", "description": "多个知识推理引擎的协同工作验证", "start_time": "2025-08-26T20:59:03.688523", "success": true, "integration_score": 88, "performance_metrics": {}, "functional_tests": {"engine_availability": 100.0, "import_success_rate": 66.66666666666666, "collaboration_rate": 100.0, "knowledge_sharing_score": 12}, "issues": ["引擎导入失败 src/knowledge/reasoning/iec61850_logic_verification_engine.py: 'NoneType' object has no attribute '__dict__'"], "recommendations": ["知识推理引擎协作优秀，智能化程度高"], "end_time": "2025-08-26T20:59:03.706542", "duration": 0.018019}, "专业报告生成集成": {"scenario_name": "专业报告生成集成", "description": "专业技术报告的完整生成流程", "start_time": "2025-08-26T20:59:03.707039", "success": true, "integration_score": 60, "performance_metrics": {}, "functional_tests": {"report_files_count": 4, "report_quality_score": 10}, "issues": ["报告生成演示失败: Traceback (most recent call last):\n  File \"F:\\baohu1\\demo_professional_report.py\", line 448, in <module>\n    sys.exit(main())\n             ~~~~^^\n  File \"F:\\baohu1\\demo_professional_report.py\", line 435, in main\n    success = demo_professional_report()\n  File \"F:\\baohu1\\demo_professional_report.py\", line 15, in demo_professional_report\n    print(\"\\U0001f4cb 专业级技术检查报告生成演示\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f4cb' in position 0: illegal multibyte sequence\n"], "recommendations": ["建议完善报告生成功能和提升报告质量"], "end_time": "2025-08-26T20:59:03.845115", "duration": 0.138076}, "并发处理能力测试": {"scenario_name": "并发处理能力测试", "description": "系统在并发场景下的处理能力", "start_time": "2025-08-26T20:59:03.845632", "success": true, "integration_score": 100, "performance_metrics": {"single_thread_time": 0.5026767253875732, "multi_thread_time": 0.2159738540649414, "performance_improvement": 57.03523892051666, "memory_usage": 2.0703125}, "functional_tests": {"concurrent_safety": true, "concurrent_error_handling": true}, "issues": [], "recommendations": ["并发处理能力优秀，支持高并发场景"], "end_time": "2025-08-26T20:59:04.588914", "duration": 0.743282}, "系统稳定性测试": {"scenario_name": "系统稳定性测试", "description": "长时间运行和压力测试", "start_time": "2025-08-26T20:59:04.589446", "success": true, "integration_score": 92, "performance_metrics": {"long_run_time": 0.10506224632263184, "success_rate": 100.0, "operations_per_second": 951.8166943900441, "memory_growth": 0.0, "recovery_rate": 75.0}, "functional_tests": {}, "issues": [], "recommendations": ["系统稳定性优秀，可以投入生产环境"], "end_time": "2025-08-26T20:59:04.697726", "duration": 0.10828}, "错误恢复能力测试": {"scenario_name": "错误恢复能力测试", "description": "系统在异常情况下的恢复能力", "start_time": "2025-08-26T20:59:04.698212", "success": true, "integration_score": 75, "performance_metrics": {"error_recovery_rate": 75.0}, "functional_tests": {"log_quality_score": 25, "notification_score": 20}, "issues": [], "recommendations": ["建议加强错误处理和恢复机制"], "end_time": "2025-08-26T20:59:04.699765", "duration": 0.001553}}}