"""
规则引擎基础框架
定义规则的基本接口和数据结构
"""

from abc import ABC, abstractmethod
from enum import Enum
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union, Type
from datetime import datetime
import logging


class RuleSeverity(Enum):
    """规则严重程度"""
    INFO = "info"           # 信息
    WARNING = "warning"     # 警告
    ERROR = "error"         # 错误
    CRITICAL = "critical"   # 严重错误


class RuleCategory(Enum):
    """规则类别"""
    STANDARD = "standard"           # IEC61850标准规则
    DEVICE = "device"              # 设备配置规则
    COMMUNICATION = "communication" # 通信网络规则
    DATATYPE = "datatype"          # 数据类型规则
    CUSTOM = "custom"              # 自定义规则


@dataclass
class RuleContext:
    """规则执行上下文"""
    # 被验证的数据对象
    data: Any
    
    # 全局上下文数据
    global_context: Dict[str, Any] = field(default_factory=dict)
    
    # 规则配置参数
    config: Dict[str, Any] = field(default_factory=dict)
    
    # 执行路径（用于嵌套验证）
    path: List[str] = field(default_factory=list)
    
    # 父级上下文（用于嵌套验证）
    parent: Optional['RuleContext'] = None
    
    def get_path_string(self) -> str:
        """获取路径字符串"""
        return ".".join(self.path) if self.path else "root"
    
    def create_child_context(self, path_segment: str, data: Any) -> 'RuleContext':
        """创建子上下文"""
        return RuleContext(
            data=data,
            global_context=self.global_context,
            config=self.config,
            path=self.path + [path_segment],
            parent=self
        )


@dataclass
class ValidationIssue:
    """验证问题基类"""
    rule_id: str                    # 规则ID
    severity: RuleSeverity          # 严重程度
    message: str                    # 问题描述
    path: str                       # 问题路径
    details: Optional[str] = None   # 详细信息
    suggestion: Optional[str] = None # 修复建议
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'rule_id': self.rule_id,
            'severity': self.severity.value,
            'message': self.message,
            'path': self.path,
            'details': self.details,
            'suggestion': self.suggestion,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class ValidationError(ValidationIssue):
    """验证错误"""
    severity: RuleSeverity = field(default=RuleSeverity.ERROR, init=False)


@dataclass
class ValidationWarning(ValidationIssue):
    """验证警告"""
    severity: RuleSeverity = field(default=RuleSeverity.WARNING, init=False)


@dataclass
class ValidationInfo(ValidationIssue):
    """验证信息"""
    severity: RuleSeverity = field(default=RuleSeverity.INFO, init=False)


@dataclass
class RuleResult:
    """规则执行结果"""
    rule_id: str                                    # 规则ID
    success: bool                                   # 是否成功
    issues: List[ValidationIssue] = field(default_factory=list)  # 发现的问题
    execution_time: float = 0.0                     # 执行时间（秒）
    metadata: Dict[str, Any] = field(default_factory=dict)       # 元数据
    
    def add_error(self, message: str, path: str = "", **kwargs) -> None:
        """添加错误"""
        error = ValidationError(
            rule_id=self.rule_id,
            message=message,
            path=path,
            **kwargs
        )
        self.issues.append(error)
        self.success = False
    
    def add_warning(self, message: str, path: str = "", **kwargs) -> None:
        """添加警告"""
        warning = ValidationWarning(
            rule_id=self.rule_id,
            message=message,
            path=path,
            **kwargs
        )
        self.issues.append(warning)
    
    def add_info(self, message: str, path: str = "", **kwargs) -> None:
        """添加信息"""
        info = ValidationInfo(
            rule_id=self.rule_id,
            message=message,
            path=path,
            **kwargs
        )
        self.issues.append(info)
    
    def get_errors(self) -> List[ValidationError]:
        """获取所有错误"""
        return [issue for issue in self.issues if isinstance(issue, ValidationError)]
    
    def get_warnings(self) -> List[ValidationWarning]:
        """获取所有警告"""
        return [issue for issue in self.issues if isinstance(issue, ValidationWarning)]
    
    def get_infos(self) -> List[ValidationInfo]:
        """获取所有信息"""
        return [issue for issue in self.issues if isinstance(issue, ValidationInfo)]
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.get_errors()) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.get_warnings()) > 0


class BaseRule(ABC):
    """规则基类"""
    
    def __init__(self, rule_id: str, name: str, description: str, 
                 category: RuleCategory = RuleCategory.CUSTOM,
                 severity: RuleSeverity = RuleSeverity.ERROR,
                 enabled: bool = True):
        """
        初始化规则
        
        Args:
            rule_id: 规则唯一标识
            name: 规则名称
            description: 规则描述
            category: 规则类别
            severity: 默认严重程度
            enabled: 是否启用
        """
        self.rule_id = rule_id
        self.name = name
        self.description = description
        self.category = category
        self.severity = severity
        self.enabled = enabled
        self.logger = logging.getLogger(f"rule.{rule_id}")
    
    @abstractmethod
    def validate(self, context: RuleContext) -> RuleResult:
        """
        执行规则验证
        
        Args:
            context: 规则执行上下文
            
        Returns:
            规则执行结果
        """
        pass
    
    def is_applicable(self, context: RuleContext) -> bool:
        """
        检查规则是否适用于当前上下文
        
        Args:
            context: 规则执行上下文
            
        Returns:
            是否适用
        """
        return True
    
    def get_dependencies(self) -> List[str]:
        """
        获取规则依赖
        
        Returns:
            依赖的规则ID列表
        """
        return []
    
    def get_config_schema(self) -> Dict[str, Any]:
        """
        获取规则配置模式
        
        Returns:
            配置模式字典
        """
        return {}
    
    def __str__(self) -> str:
        return f"Rule({self.rule_id}: {self.name})"
    
    def __repr__(self) -> str:
        return (f"BaseRule(rule_id='{self.rule_id}', name='{self.name}', "
                f"category={self.category}, severity={self.severity}, enabled={self.enabled})")


class RuleDecorator:
    """规则装饰器，用于简化规则定义"""
    
    @staticmethod
    def rule(rule_id: str, name: str, description: str, 
             category: RuleCategory = RuleCategory.CUSTOM,
             severity: RuleSeverity = RuleSeverity.ERROR,
             enabled: bool = True):
        """
        规则装饰器
        
        Args:
            rule_id: 规则ID
            name: 规则名称
            description: 规则描述
            category: 规则类别
            severity: 严重程度
            enabled: 是否启用
        """
        def decorator(func):
            class DecoratedRule(BaseRule):
                def __init__(self):
                    super().__init__(rule_id, name, description, category, severity, enabled)
                
                def validate(self, context: RuleContext) -> RuleResult:
                    return func(context)
            
            return DecoratedRule()
        
        return decorator
    
    @staticmethod
    def applicable_to(*types: Type):
        """
        指定规则适用的数据类型

        Args:
            types: 适用的数据类型
        """
        def decorator(rule_obj):
            # 如果是函数（由@rule装饰器创建的规则实例）
            if hasattr(rule_obj, 'is_applicable'):
                original_is_applicable = rule_obj.is_applicable

                def is_applicable(self, context: RuleContext) -> bool:
                    if not isinstance(context.data, types):
                        return False
                    return original_is_applicable(self, context)

                rule_obj.is_applicable = is_applicable
            else:
                # 如果是类
                original_is_applicable = getattr(rule_obj, 'is_applicable', None)

                def is_applicable(self, context: RuleContext) -> bool:
                    if not isinstance(context.data, types):
                        return False
                    if original_is_applicable:
                        return original_is_applicable(self, context)
                    return True

                rule_obj.is_applicable = is_applicable

            return rule_obj

        return decorator
    
    @staticmethod
    def depends_on(*rule_ids: str):
        """
        指定规则依赖
        
        Args:
            rule_ids: 依赖的规则ID
        """
        def decorator(rule_class):
            def get_dependencies(self) -> List[str]:
                return list(rule_ids)
            
            rule_class.get_dependencies = get_dependencies
            return rule_class
        
        return decorator


# 便捷的装饰器实例
rule = RuleDecorator.rule
applicable_to = RuleDecorator.applicable_to
depends_on = RuleDecorator.depends_on
