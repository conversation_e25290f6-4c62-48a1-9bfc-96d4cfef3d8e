"""
图纸审查引擎
整合图纸解析、分析和规范检查功能
"""

import logging
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from .drawing_parser import DrawingParserFactory
from .drawing_analyzer import DrawingAnalyzer
from .compliance_checker import ComplianceChecker
from .drawing_models import DrawingDocument, DrawingReviewResult, ReviewIssue, ReviewSeverity
from .standards_knowledge import StandardsKnowledgeBase

logger = logging.getLogger(__name__)


class DrawingReviewEngine:
    """图纸审查引擎"""
    
    def __init__(self):
        """初始化审查引擎"""
        self.parser_factory = DrawingParserFactory()
        self.analyzer = DrawingAnalyzer()
        self.compliance_checker = ComplianceChecker()
        self.knowledge_base = StandardsKnowledgeBase()
        
        logger.info("图纸审查引擎初始化完成")
    
    def review_drawing(self, file_path: str, 
                      check_categories: Optional[List[str]] = None,
                      severity_filter: Optional[ReviewSeverity] = None) -> Optional[DrawingReviewResult]:
        """审查图纸文件"""
        try:
            logger.info(f"开始审查图纸: {file_path}")
            
            # 1. 解析图纸文件
            drawing_doc = self._parse_drawing(file_path)
            if not drawing_doc:
                return None
            
            # 2. 创建审查结果对象
            review_result = DrawingReviewResult(
                drawing_document=drawing_doc,
                review_date=datetime.now()
            )
            
            # 3. 执行规范检查
            issues = self._check_compliance(drawing_doc, check_categories)
            
            # 4. 过滤问题
            if severity_filter:
                issues = [issue for issue in issues if issue.severity.value >= severity_filter.value]
            
            # 5. 添加问题到结果
            for issue in issues:
                review_result.add_issue(issue)
            
            # 6. 设置统计信息
            review_result.total_elements = len(drawing_doc.elements)
            review_result.total_layers = len(drawing_doc.layers)
            review_result.total_blocks = len(drawing_doc.blocks)
            
            # 7. 计算合规性评分
            review_result.calculate_compliance_score()
            
            # 8. 生成修改建议
            self._generate_fix_suggestions(review_result)
            
            logger.info(f"图纸审查完成: {len(issues)} 个问题，合规性评分: {review_result.compliance_score:.1f}")
            
            return review_result
            
        except Exception as e:
            logger.error(f"图纸审查失败: {e}")
            return None
    
    def review_multiple_drawings(self, file_paths: List[str]) -> List[DrawingReviewResult]:
        """批量审查图纸"""
        results = []
        
        for file_path in file_paths:
            result = self.review_drawing(file_path)
            if result:
                results.append(result)
        
        return results
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return self.parser_factory.get_supported_formats()
    
    def get_available_check_categories(self) -> List[str]:
        """获取可用的检查分类"""
        rules = self.compliance_checker.get_available_rules()
        categories = set(rule['category'] for rule in rules if rule['category'])
        return sorted(list(categories))
    
    def get_standards_summary(self) -> Dict[str, Any]:
        """获取标准摘要"""
        return self.knowledge_base.get_rules_summary()
    
    def auto_fix_issues(self, review_result: DrawingReviewResult) -> Dict[str, Any]:
        """自动修复问题"""
        fix_result = {
            'total_issues': len(review_result.issues),
            'fixable_issues': 0,
            'fixed_issues': 0,
            'failed_fixes': 0,
            'fix_details': []
        }
        
        auto_fixable_issues = review_result.get_auto_fixable_issues()
        fix_result['fixable_issues'] = len(auto_fixable_issues)
        
        for issue in auto_fixable_issues:
            try:
                success = self._apply_auto_fix(review_result.drawing_document, issue)
                if success:
                    fix_result['fixed_issues'] += 1
                    fix_result['fix_details'].append({
                        'issue_id': issue.issue_id,
                        'status': 'fixed',
                        'description': issue.title
                    })
                else:
                    fix_result['failed_fixes'] += 1
                    fix_result['fix_details'].append({
                        'issue_id': issue.issue_id,
                        'status': 'failed',
                        'description': issue.title
                    })
            except Exception as e:
                fix_result['failed_fixes'] += 1
                fix_result['fix_details'].append({
                    'issue_id': issue.issue_id,
                    'status': 'error',
                    'description': issue.title,
                    'error': str(e)
                })
        
        return fix_result
    
    def generate_review_report(self, review_result: DrawingReviewResult, 
                             output_format: str = 'html') -> str:
        """生成审查报告"""
        try:
            if output_format.lower() == 'html':
                return self._generate_html_report(review_result)
            elif output_format.lower() == 'json':
                return self._generate_json_report(review_result)
            elif output_format.lower() == 'pdf':
                return self._generate_pdf_report(review_result)
            else:
                raise ValueError(f"不支持的报告格式: {output_format}")
        except Exception as e:
            logger.error(f"生成审查报告失败: {e}")
            return ""
    
    def _parse_drawing(self, file_path: str) -> Optional[DrawingDocument]:
        """解析图纸文件"""
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None
        
        return self.parser_factory.parse_file(file_path)
    
    def _check_compliance(self, drawing: DrawingDocument, 
                         check_categories: Optional[List[str]] = None) -> List[ReviewIssue]:
        """执行规范检查"""
        if check_categories:
            # 只检查指定分类的规则
            rule_ids = []
            for category in check_categories:
                category_rules = self.compliance_checker.get_rules_by_category(category)
                rule_ids.extend(category_rules)
            
            return self.compliance_checker.check_specific_rules(drawing, rule_ids)
        else:
            # 检查所有规则
            return self.compliance_checker.check_compliance(drawing)
    
    def _generate_fix_suggestions(self, review_result: DrawingReviewResult):
        """生成修改建议"""
        # 按问题类型分组并生成综合建议
        issues_by_category = {}
        for issue in review_result.issues:
            category = issue.category
            if category not in issues_by_category:
                issues_by_category[category] = []
            issues_by_category[category].append(issue)
        
        # 为每个分类生成建议
        for category, issues in issues_by_category.items():
            if len(issues) > 1:
                # 如果同一分类有多个问题，生成批量修改建议
                for issue in issues:
                    if not issue.suggestion:
                        issue.suggestion = self._generate_category_suggestion(category, len(issues))
    
    def _generate_category_suggestion(self, category: str, issue_count: int) -> str:
        """生成分类建议"""
        suggestions = {
            '线型': f"发现 {issue_count} 个线型问题，建议统一检查和修改线型设置",
            '线宽': f"发现 {issue_count} 个线宽问题，建议按照标准调整线宽",
            '文字标注': f"发现 {issue_count} 个文字问题，建议统一文字格式",
            '图层': f"发现 {issue_count} 个图层问题，建议规范图层管理",
            '尺寸标注': f"发现 {issue_count} 个尺寸标注问题，建议检查标注设置"
        }
        return suggestions.get(category, f"发现 {issue_count} 个 {category} 问题，建议逐一检查修改")
    
    def _apply_auto_fix(self, drawing: DrawingDocument, issue: ReviewIssue) -> bool:
        """应用自动修复"""
        try:
            # 根据问题类型应用相应的修复
            if issue.rule_code == "LINE_TYPE_STANDARD":
                return self._fix_line_type_issue(drawing, issue)
            elif issue.rule_code == "LINE_WIDTH_STANDARD":
                return self._fix_line_width_issue(drawing, issue)
            elif issue.rule_code == "TEXT_ANNOTATION_STANDARD":
                return self._fix_text_issue(drawing, issue)
            else:
                return False
        except Exception as e:
            logger.error(f"自动修复失败: {e}")
            return False
    
    def _fix_line_type_issue(self, drawing: DrawingDocument, issue: ReviewIssue) -> bool:
        """修复线型问题"""
        # 实际实现中需要修改图纸文件
        # 这里只是示例
        logger.info(f"修复线型问题: {issue.title}")
        return True
    
    def _fix_line_width_issue(self, drawing: DrawingDocument, issue: ReviewIssue) -> bool:
        """修复线宽问题"""
        logger.info(f"修复线宽问题: {issue.title}")
        return True
    
    def _fix_text_issue(self, drawing: DrawingDocument, issue: ReviewIssue) -> bool:
        """修复文字问题"""
        logger.info(f"修复文字问题: {issue.title}")
        return True
    
    def _generate_html_report(self, review_result: DrawingReviewResult) -> str:
        """生成HTML报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>图纸审查报告</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .summary { margin: 20px 0; }
                .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
                .critical { border-left-color: #d32f2f; }
                .error { border-left-color: #f57c00; }
                .warning { border-left-color: #fbc02d; }
                .info { border-left-color: #1976d2; }
                .score { font-size: 24px; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>图纸审查报告</h1>
                <p><strong>文件:</strong> {file_path}</p>
                <p><strong>审查时间:</strong> {review_date}</p>
                <p><strong>合规性评分:</strong> <span class="score">{compliance_score:.1f}/100</span></p>
            </div>
            
            <div class="summary">
                <h2>问题摘要</h2>
                <ul>
                    <li>总问题数: {total_issues}</li>
                    <li>关键问题: {critical_issues}</li>
                    <li>错误: {error_issues}</li>
                    <li>警告: {warning_issues}</li>
                    <li>信息: {info_issues}</li>
                </ul>
            </div>
            
            <div class="issues">
                <h2>问题详情</h2>
                {issues_html}
            </div>
        </body>
        </html>
        """
        
        # 生成问题HTML
        issues_html = ""
        for issue in review_result.issues:
            severity_class = issue.severity.value
            issues_html += f"""
            <div class="issue {severity_class}">
                <h3>{issue.title}</h3>
                <p><strong>严重程度:</strong> {issue.severity.value.upper()}</p>
                <p><strong>描述:</strong> {issue.description}</p>
                <p><strong>建议:</strong> {issue.suggestion}</p>
                <p><strong>标准引用:</strong> {issue.standard_reference} {issue.standard_clause}</p>
            </div>
            """
        
        summary = review_result.get_summary()
        
        return html_template.format(
            file_path=review_result.drawing_document.file_path,
            review_date=review_result.review_date.strftime('%Y-%m-%d %H:%M:%S'),
            compliance_score=review_result.compliance_score,
            total_issues=summary['total_issues'],
            critical_issues=summary['critical_issues'],
            error_issues=summary['error_issues'],
            warning_issues=summary['warning_issues'],
            info_issues=summary['info_issues'],
            issues_html=issues_html
        )
    
    def _generate_json_report(self, review_result: DrawingReviewResult) -> str:
        """生成JSON报告"""
        import json
        
        report_data = {
            'review_info': {
                'review_id': review_result.review_id,
                'file_path': review_result.drawing_document.file_path,
                'review_date': review_result.review_date.isoformat(),
                'compliance_score': review_result.compliance_score
            },
            'summary': review_result.get_summary(),
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'severity': issue.severity.value,
                    'category': issue.category,
                    'title': issue.title,
                    'description': issue.description,
                    'suggestion': issue.suggestion,
                    'auto_fixable': issue.auto_fixable,
                    'standard_reference': issue.standard_reference,
                    'standard_clause': issue.standard_clause,
                    'location': {
                        'x': issue.location.x,
                        'y': issue.location.y
                    } if issue.location else None
                }
                for issue in review_result.issues
            ]
        }
        
        return json.dumps(report_data, ensure_ascii=False, indent=2)
    
    def _generate_pdf_report(self, review_result: DrawingReviewResult) -> str:
        """生成PDF报告"""
        # PDF生成需要额外的库支持，如reportlab
        # 这里返回HTML报告作为替代
        logger.warning("PDF报告生成功能待实现，返回HTML报告")
        return self._generate_html_report(review_result)
    
    def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            'supported_formats': self.get_supported_formats(),
            'available_categories': self.get_available_check_categories(),
            'total_rules': len(self.compliance_checker.rules),
            'standards_summary': self.get_standards_summary()
        }
