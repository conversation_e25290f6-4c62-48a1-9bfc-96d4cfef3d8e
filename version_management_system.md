# IEC61850设计检查器版本管理体系

## 1. 版本管理总体架构

### 1.1 多层次版本管理
```
版本管理体系
├── 代码版本控制 (Git)
│   ├── 分支管理策略
│   ├── 提交规范
│   ├── 标签管理
│   └── 合并策略
├── 模块版本管理
│   ├── 语义化版本控制
│   ├── 模块间依赖管理
│   ├── 兼容性矩阵
│   └── 版本升级策略
├── 依赖版本管理
│   ├── 第三方库版本锁定
│   ├── 安全漏洞监控
│   ├── 依赖更新策略
│   └── 许可证合规检查
├── 发布版本管理
│   ├── 版本命名规范
│   ├── 发布流程管理
│   ├── 版本打包策略
│   └── 回滚机制
└── 版本文档管理
    ├── 变更日志
    ├── 版本文档
    ├── 迁移指南
    └── 兼容性说明
```

## 2. Git版本控制策略

### 2.1 分支管理策略 (Git Flow)
```
main (主分支)
├── develop (开发分支)
│   ├── feature/module-1-data-model (功能分支)
│   ├── feature/module-2-xml-parser (功能分支)
│   ├── feature/module-3-knowledge-base (功能分支)
│   └── ...
├── release/v1.0.0 (发布分支)
├── release/v1.1.0 (发布分支)
└── hotfix/v1.0.1 (热修复分支)
```

### 2.2 分支命名规范
```bash
# 功能分支
feature/module-{number}-{description}
feature/gui-validation-panel
feature/knowledge-base-integration

# 发布分支
release/v{major}.{minor}.{patch}
release/v1.0.0
release/v1.1.0

# 热修复分支
hotfix/v{major}.{minor}.{patch}
hotfix/v1.0.1

# 修复分支
bugfix/issue-{number}-{description}
bugfix/issue-123-parser-memory-leak
```

### 2.3 提交信息规范 (Conventional Commits)
```bash
# 格式: <type>(<scope>): <description>
# 类型:
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例:
feat(parser): add SCD file validation support
fix(gui): resolve memory leak in file comparison panel
docs(api): update module interface documentation
test(validator): add unit tests for rule engine
```

### 2.4 标签管理策略
```bash
# 版本标签
v1.0.0          # 正式发布版本
v1.0.0-rc.1     # 发布候选版本
v1.0.0-beta.1   # 测试版本
v1.0.0-alpha.1  # 内测版本

# 模块标签
module-parser-v1.2.0    # 模块版本标签
module-gui-v2.1.0       # 模块版本标签
```

## 3. 模块版本管理机制

### 3.1 语义化版本控制 (SemVer)
```
版本格式: MAJOR.MINOR.PATCH[-PRERELEASE][+BUILD]

MAJOR: 不兼容的API修改
MINOR: 向后兼容的功能性新增
PATCH: 向后兼容的问题修正
PRERELEASE: 预发布版本标识
BUILD: 构建元数据

示例:
1.0.0         # 正式版本
1.1.0         # 新增功能
1.1.1         # 修复bug
2.0.0         # 重大更新
1.0.0-beta.1  # 测试版本
```

### 3.2 模块版本配置文件
```python
# module_version.py
class ModuleVersion:
    def __init__(self):
        self.name = "iec61850-parser"
        self.version = "1.2.0"
        self.api_version = "1.0"
        self.min_python_version = "3.9"
        self.dependencies = {
            "iec61850-core": ">=1.0.0,<2.0.0",
            "lxml": ">=4.6.0,<5.0.0",
            "pydantic": ">=1.8.0,<2.0.0"
        }
        self.compatible_versions = ["1.0.0", "1.1.0", "1.2.0"]
        self.deprecated_versions = ["0.9.0", "0.8.0"]
```

### 3.3 模块兼容性矩阵
```yaml
# compatibility_matrix.yml
compatibility_matrix:
  core_model:
    "1.0.0":
      compatible_with:
        parser: ["1.0.0", "1.1.0"]
        validator: ["1.0.0"]
        gui: ["1.0.0"]
    "1.1.0":
      compatible_with:
        parser: ["1.1.0", "1.2.0"]
        validator: ["1.0.0", "1.1.0"]
        gui: ["1.0.0", "1.1.0"]
  
  breaking_changes:
    "2.0.0":
      changes:
        - "API重构: 数据模型接口变更"
        - "配置格式变更"
      migration_guide: "docs/migration/v1-to-v2.md"
```

## 4. 依赖版本锁定系统

### 4.1 依赖锁定文件
```python
# requirements.lock
# 自动生成的精确版本锁定文件
lxml==4.6.3
pydantic==1.8.2
PySide6==6.2.4
fastapi==0.68.0
pytest==6.2.4
# ... 包含所有传递依赖的精确版本

# requirements.txt
# 开发者维护的版本范围文件
lxml>=4.6.0,<5.0.0
pydantic>=1.8.0,<2.0.0
PySide6>=6.2.0,<7.0.0
fastapi>=0.68.0,<1.0.0
pytest>=6.2.0,<7.0.0
```

### 4.2 依赖安全检查
```python
# security_check.py
class DependencySecurityChecker:
    def check_vulnerabilities(self):
        """检查依赖包的已知安全漏洞"""
        pass
    
    def check_licenses(self):
        """检查依赖包的许可证合规性"""
        pass
    
    def generate_security_report(self):
        """生成安全检查报告"""
        pass
```

### 4.3 依赖更新策略
```yaml
# dependency_update_policy.yml
update_policy:
  security_updates:
    auto_apply: true
    max_delay: "24h"
    notification: true
  
  minor_updates:
    auto_apply: false
    review_required: true
    test_required: true
  
  major_updates:
    auto_apply: false
    manual_review: true
    compatibility_test: true
    migration_plan: required

## 5. 发布版本管理

### 5.1 版本发布流程
```mermaid
graph TD
    A[开发完成] --> B[代码审查]
    B --> C[自动化测试]
    C --> D[版本号确定]
    D --> E[创建发布分支]
    E --> F[构建和打包]
    F --> G[发布前测试]
    G --> H[版本标签创建]
    H --> I[正式发布]
    I --> J[发布后验证]
    J --> K[文档更新]
```

### 5.2 版本发布配置
```python
# release_config.py
class ReleaseConfig:
    def __init__(self):
        self.release_channels = {
            "stable": {
                "branch": "main",
                "auto_deploy": False,
                "approval_required": True,
                "testing_required": "full"
            },
            "beta": {
                "branch": "develop",
                "auto_deploy": True,
                "approval_required": False,
                "testing_required": "integration"
            },
            "alpha": {
                "branch": "feature/*",
                "auto_deploy": True,
                "approval_required": False,
                "testing_required": "unit"
            }
        }

        self.build_artifacts = [
            "windows-x64-installer.exe",
            "linux-x64-appimage",
            "source-distribution.tar.gz",
            "documentation.zip"
        ]

### 5.3 自动化发布脚本
```bash
#!/bin/bash
# release.sh - 自动化发布脚本

set -e

VERSION=$1
CHANNEL=${2:-stable}

echo "开始发布版本 $VERSION 到 $CHANNEL 渠道"

# 1. 版本验证
python scripts/validate_version.py $VERSION

# 2. 运行完整测试套件
pytest tests/ --cov=src/ --cov-report=html

# 3. 构建所有平台的安装包
python scripts/build_all_platforms.py

# 4. 运行安全扫描
bandit -r src/
safety check

# 5. 创建版本标签
git tag -a v$VERSION -m "Release version $VERSION"

# 6. 推送到远程仓库
git push origin v$VERSION

# 7. 上传发布包
python scripts/upload_release.py $VERSION $CHANNEL

echo "版本 $VERSION 发布完成"
```

## 6. 版本兼容性管理

### 6.1 向后兼容性检查
```python
# compatibility_checker.py
class CompatibilityChecker:
    def check_api_compatibility(self, old_version, new_version):
        """检查API接口的向后兼容性"""
        changes = self.analyze_api_changes(old_version, new_version)

        breaking_changes = []
        for change in changes:
            if change.is_breaking:
                breaking_changes.append(change)

        return CompatibilityReport(
            compatible=len(breaking_changes) == 0,
            breaking_changes=breaking_changes,
            recommendations=self.generate_recommendations(changes)
        )

    def check_data_compatibility(self, old_version, new_version):
        """检查数据格式的向后兼容性"""
        pass

    def check_config_compatibility(self, old_version, new_version):
        """检查配置文件的向后兼容性"""
        pass
```

### 6.2 版本迁移指南
```markdown
# 版本迁移指南

## 从 v1.x 升级到 v2.0

### 重大变更
1. **API接口变更**
   - `parse_scd_file()` 重命名为 `parse_scd()`
   - 返回值格式从字典改为数据类

2. **配置文件格式变更**
   - 配置文件从INI格式改为YAML格式
   - 部分配置项名称变更

3. **依赖要求变更**
   - Python最低版本要求从3.8升级到3.9
   - PySide6从6.2升级到6.4

### 迁移步骤
1. 备份现有配置和数据
2. 更新Python环境到3.9+
3. 运行迁移脚本: `python migrate_v1_to_v2.py`
4. 验证迁移结果
5. 更新自定义脚本中的API调用
```

## 7. 版本文档和追踪系统

### 7.1 变更日志格式
```markdown
# 变更日志

## [2.0.0] - 2024-03-15

### 新增
- 新增智能规则推理引擎
- 支持IEC61850-9-2标准
- 新增批量文件处理功能

### 变更
- 重构数据模型API接口
- 优化文件解析性能，提升50%
- 更新用户界面设计

### 修复
- 修复大文件解析内存泄漏问题
- 修复Windows平台文件路径问题
- 修复验证规则冲突检测bug

### 移除
- 移除已废弃的v1.x API接口
- 移除对Python 3.8的支持

### 安全
- 修复XML外部实体注入漏洞
- 更新所有依赖包到最新安全版本
```

### 7.2 版本追踪数据库
```sql
-- version_tracking.sql
CREATE TABLE version_releases (
    id INTEGER PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    release_date DATETIME NOT NULL,
    channel VARCHAR(10) NOT NULL,
    commit_hash VARCHAR(40) NOT NULL,
    release_notes TEXT,
    download_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active'
);

CREATE TABLE version_dependencies (
    id INTEGER PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    dependency_name VARCHAR(100) NOT NULL,
    dependency_version VARCHAR(20) NOT NULL,
    dependency_type VARCHAR(20) NOT NULL
);

CREATE TABLE compatibility_matrix (
    id INTEGER PRIMARY KEY,
    version_a VARCHAR(20) NOT NULL,
    version_b VARCHAR(20) NOT NULL,
    compatible BOOLEAN NOT NULL,
    notes TEXT
);
```
```
