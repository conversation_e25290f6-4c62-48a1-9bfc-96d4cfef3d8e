"""
SCD文件生成器
用于生成标准的IEC61850 SCD配置文件
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import xml.etree.ElementTree as ET

from ..models import SCLDocument, Header, SubStation, IED


class SCDGenerator:
    """
    SCD文件生成器
    
    提供标准化的SCD文件生成功能：
    1. 从模板生成SCD文件
    2. 合并多个ICD文件
    3. 生成标准化的配置结构
    4. 验证生成的文件完整性
    """
    
    def __init__(self):
        self.namespaces = {
            'scl': 'http://www.iec.ch/61850/2003/SCL',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        }
    
    def generate_scd(self, project_info: Dict[str, Any], 
                    ieds: List[IED], 
                    substation: Optional[SubStation] = None) -> SCLDocument:
        """
        生成SCD文档
        
        Args:
            project_info: 项目信息
            ieds: IED设备列表
            substation: 变电站配置
            
        Returns:
            SCLDocument: 生成的SCD文档
        """
        # 创建SCL文档
        scd_doc = SCLDocument()
        scd_doc.version = "2007"
        
        # 创建Header
        header = Header()
        header.id = project_info.get('id', f"SCD_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        header.version = project_info.get('version', '1.0')
        header.tool_id = project_info.get('tool_id', 'IEC61850DesignChecker')
        
        # 添加历史记录
        header.history = [{
            'version': header.version,
            'revision': '1',
            'when': datetime.now().isoformat(),
            'who': project_info.get('author', 'System'),
            'what': 'Initial creation',
            'why': 'Generated by IEC61850 Design Checker'
        }]
        
        scd_doc.header = header
        
        # 添加变电站配置
        if substation:
            scd_doc.substation = substation
        else:
            # 创建默认变电站
            default_substation = SubStation()
            default_substation.name = project_info.get('substation_name', 'DefaultSubstation')
            default_substation.desc = "Generated substation configuration"
            scd_doc.substation = default_substation
        
        # 添加IED设备
        scd_doc.ieds = ieds
        
        return scd_doc
    
    def save_to_file(self, scd_doc: SCLDocument, output_path: str):
        """
        保存SCD文档到文件
        
        Args:
            scd_doc: SCD文档
            output_path: 输出文件路径
        """
        # 创建XML根元素
        root = ET.Element('SCL')
        root.set('version', scd_doc.version)
        root.set('xmlns', scd_doc.xmlns)
        root.set('xmlns:xsi', self.namespaces['xsi'])
        
        # 添加Header
        if scd_doc.header:
            header_elem = ET.SubElement(root, 'Header')
            header_elem.set('id', scd_doc.header.id)
            header_elem.set('version', scd_doc.header.version)
            header_elem.set('toolID', scd_doc.header.tool_id)
            header_elem.set('nameStructure', scd_doc.header.name_structure)
            
            # 添加历史记录
            for history_item in scd_doc.header.history:
                history_elem = ET.SubElement(header_elem, 'History')
                for key, value in history_item.items():
                    history_elem.set(key, str(value))
        
        # 添加Substation
        if scd_doc.substation:
            substation_elem = ET.SubElement(root, 'Substation')
            substation_elem.set('name', scd_doc.substation.name)
            if scd_doc.substation.desc:
                substation_elem.set('desc', scd_doc.substation.desc)
        
        # 添加IED
        for ied in scd_doc.ieds:
            ied_elem = ET.SubElement(root, 'IED')
            ied_elem.set('name', ied.name)
            if ied.manufacturer:
                ied_elem.set('manufacturer', ied.manufacturer)
            if ied.type:
                ied_elem.set('type', ied.type)
            if ied.desc:
                ied_elem.set('desc', ied.desc)
        
        # 格式化并保存XML
        self._format_xml(root)
        tree = ET.ElementTree(root)
        
        # 确保输出目录存在
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        tree.write(output_path, encoding='utf-8', xml_declaration=True)
    
    def _format_xml(self, elem, level=0):
        """格式化XML元素"""
        indent = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = indent + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = indent
            for child in elem:
                self._format_xml(child, level + 1)
            if not child.tail or not child.tail.strip():
                child.tail = indent
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = indent
    
    def merge_icd_files(self, icd_paths: List[str], project_info: Dict[str, Any]) -> SCLDocument:
        """
        合并多个ICD文件生成SCD
        
        Args:
            icd_paths: ICD文件路径列表
            project_info: 项目信息
            
        Returns:
            SCLDocument: 合并后的SCD文档
        """
        merged_ieds = []
        
        # 解析每个ICD文件
        from ..parsers import ParserFactory
        parser_factory = ParserFactory()
        
        for icd_path in icd_paths:
            try:
                parse_result = parser_factory.parse_file(icd_path)
                if parse_result.success and parse_result.data.ieds:
                    merged_ieds.extend(parse_result.data.ieds)
            except Exception as e:
                print(f"解析ICD文件失败 {icd_path}: {e}")
        
        # 生成SCD
        return self.generate_scd(project_info, merged_ieds)
    
    def validate_scd(self, scd_doc: SCLDocument) -> List[str]:
        """
        验证SCD文档
        
        Args:
            scd_doc: SCD文档
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        # 基本结构验证
        if not scd_doc.header:
            errors.append("缺少Header信息")
        
        if not scd_doc.substation:
            errors.append("缺少Substation配置")
        
        if not scd_doc.ieds:
            errors.append("缺少IED设备")
        
        # IED名称唯一性检查
        ied_names = [ied.name for ied in scd_doc.ieds if ied.name]
        if len(ied_names) != len(set(ied_names)):
            errors.append("存在重复的IED名称")
        
        # Header验证
        if scd_doc.header:
            if not scd_doc.header.id:
                errors.append("Header缺少ID")
            if not scd_doc.header.version:
                errors.append("Header缺少版本信息")
        
        # IED验证
        for i, ied in enumerate(scd_doc.ieds):
            if not ied.name:
                errors.append(f"IED[{i}]缺少名称")
            if not ied.manufacturer:
                errors.append(f"IED[{i}]缺少制造商信息")
        
        return errors
    
    def generate_template(self, template_type: str = "basic") -> Dict[str, Any]:
        """
        生成SCD模板
        
        Args:
            template_type: 模板类型
            
        Returns:
            Dict: 模板数据
        """
        if template_type == "basic":
            return {
                'id': 'BasicSCD_Template',
                'version': '1.0',
                'tool_id': 'IEC61850DesignChecker',
                'author': 'Template Generator',
                'substation_name': 'TemplateSubstation',
                'description': 'Basic SCD template for IEC61850 projects'
            }
        elif template_type == "protection":
            return {
                'id': 'ProtectionSCD_Template',
                'version': '1.0',
                'tool_id': 'IEC61850DesignChecker',
                'author': 'Template Generator',
                'substation_name': 'ProtectionSubstation',
                'description': 'Protection system SCD template'
            }
        else:
            raise ValueError(f"不支持的模板类型: {template_type}")
    
    def export_statistics(self, scd_doc: SCLDocument) -> Dict[str, Any]:
        """
        导出SCD统计信息
        
        Args:
            scd_doc: SCD文档
            
        Returns:
            Dict: 统计信息
        """
        stats = scd_doc.get_statistics()
        
        # 添加生成器特定的统计
        stats['generator_info'] = {
            'generated_by': 'IEC61850DesignChecker',
            'generation_time': datetime.now().isoformat(),
            'file_size': 0,  # 需要在保存后计算
            'validation_status': 'valid' if not self.validate_scd(scd_doc) else 'invalid'
        }
        
        return stats
