"""
IEC61850数据模型基础类
提供所有数据模型的基础功能，包括验证、序列化等
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Type, TypeVar, Union
from uuid import uuid4
import json
from dataclasses import dataclass, field
from enum import Enum

# 类型变量
T = TypeVar('T', bound='BaseModel')


class ValidationError(Exception):
    """数据验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Any = None):
        self.message = message
        self.field = field
        self.value = value
        super().__init__(self.format_message())
    
    def format_message(self) -> str:
        """格式化错误消息"""
        if self.field:
            return f"字段 '{self.field}' 验证失败: {self.message}"
        return self.message


class IECDataType(Enum):
    """IEC61850数据类型枚举"""
    BOOLEAN = "BOOLEAN"
    INT8 = "INT8"
    INT16 = "INT16"
    INT32 = "INT32"
    INT64 = "INT64"
    INT128 = "INT128"
    INT8U = "INT8U"
    INT16U = "INT16U"
    INT24U = "INT24U"
    INT32U = "INT32U"
    FLOAT32 = "FLOAT32"
    FLOAT64 = "FLOAT64"
    ENUMERATED = "Enum"
    CODED_ENUM = "CodedEnum"
    OCTET_STRING = "Octet64"
    VISIBLE_STRING = "VisString255"
    UNICODE_STRING = "Unicode255"
    TIMESTAMP = "Timestamp"
    ENTRY_TIME = "EntryTime"
    CHECK = "Check"
    OBJECT_NAME = "ObjectName"
    OBJECT_REFERENCE = "ObjectReference"
    CURRENCY = "Currency"


@dataclass
class BaseModel(ABC):
    """IEC61850数据模型基类"""
    
    # 基础属性
    id: str = field(default_factory=lambda: str(uuid4()))
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # IEC61850通用属性
    desc: Optional[str] = None  # 描述
    
    def __post_init__(self):
        """初始化后处理"""
        self.validate()
    
    @abstractmethod
    def validate(self) -> None:
        """验证数据模型的有效性"""
        pass
    
    def update_timestamp(self) -> None:
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, BaseModel):
                result[key] = value.to_dict()
            elif isinstance(value, list):
                result[key] = [
                    item.to_dict() if isinstance(item, BaseModel) else item
                    for item in value
                ]
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, Enum):
                result[key] = value.value
            else:
                result[key] = value
        return result
    
    def to_json(self, indent: Optional[int] = None) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)
    
    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """从字典创建实例"""
        # 处理datetime字段
        if 'created_at' in data and isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and isinstance(data['updated_at'], str):
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        return cls(**data)
    
    @classmethod
    def from_json(cls: Type[T], json_str: str) -> T:
        """从JSON字符串创建实例"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def copy(self: T) -> T:
        """创建副本"""
        data = self.to_dict()
        # 生成新的ID
        data['id'] = str(uuid4())
        data['created_at'] = datetime.now().isoformat()
        data['updated_at'] = datetime.now().isoformat()
        return self.__class__.from_dict(data)
    
    def __eq__(self, other) -> bool:
        """相等性比较（基于ID）"""
        if not isinstance(other, BaseModel):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        """哈希值（基于ID）"""
        return hash(self.id)
    
    def __repr__(self) -> str:
        """字符串表示"""
        class_name = self.__class__.__name__
        return f"{class_name}(id='{self.id}')"


@dataclass
class NamedModel(BaseModel):
    """带名称的数据模型基类"""
    
    name: str = ""
    
    def validate(self) -> None:
        """验证名称"""
        if not self.name or not self.name.strip():
            raise ValidationError("名称不能为空", "name", self.name)
        
        # IEC61850名称规范检查
        if not self._is_valid_iec_name(self.name):
            raise ValidationError(
                "名称不符合IEC61850规范（只能包含字母、数字和下划线，且以字母开头）",
                "name", 
                self.name
            )
    
    def _is_valid_iec_name(self, name: str) -> bool:
        """检查是否符合IEC61850名称规范（宽松版本）"""
        if not name:
            return False

        # 对于电压等级等特殊情况，允许以数字开头（如110kV）
        # 但不能包含空格和特殊符号
        forbidden_chars = set(' \t\n\r!@#$%^&*()+={}[]|\\:";\'<>?,./`~')
        for char in name:
            if char in forbidden_chars:
                return False

        return True
    
    def __repr__(self) -> str:
        """字符串表示"""
        class_name = self.__class__.__name__
        return f"{class_name}(name='{self.name}', id='{self.id}')"


@dataclass
class ReferenceModel(BaseModel):
    """引用模型基类"""
    
    reference: str = ""  # 引用路径
    
    def validate(self) -> None:
        """验证引用"""
        if self.reference and not self._is_valid_reference(self.reference):
            raise ValidationError(
                "引用格式不正确",
                "reference",
                self.reference
            )
    
    def _is_valid_reference(self, reference: str) -> bool:
        """检查引用格式是否正确"""
        # IEC61850引用格式检查
        # 例如: IED1/CTRL/CSWI1.Pos
        if not reference:
            return True  # 空引用是允许的
        
        # 基本格式检查
        parts = reference.split('/')
        if len(parts) < 2:
            return False
        
        # 检查每个部分是否符合命名规范
        for part in parts[:-1]:  # 除了最后一部分
            if not self._is_valid_iec_name_part(part):
                return False
        
        # 检查最后一部分（可能包含点号）
        last_part = parts[-1]
        if '.' in last_part:
            node_part, attr_part = last_part.split('.', 1)
            return (self._is_valid_iec_name_part(node_part) and 
                   self._is_valid_iec_name_part(attr_part))
        else:
            return self._is_valid_iec_name_part(last_part)
    
    def _is_valid_iec_name_part(self, name: str) -> bool:
        """检查IEC名称部分是否有效"""
        if not name:
            return False
        return name.replace('_', '').replace('-', '').isalnum()


def validate_required(value: Any, field_name: str) -> None:
    """验证必填字段"""
    if value is None or (isinstance(value, str) and not value.strip()):
        raise ValidationError(f"字段 '{field_name}' 是必填的")


def validate_range(value: Union[int, float], min_val: Union[int, float], 
                  max_val: Union[int, float], field_name: str) -> None:
    """验证数值范围"""
    if not (min_val <= value <= max_val):
        raise ValidationError(
            f"值必须在 {min_val} 到 {max_val} 之间",
            field_name,
            value
        )


def validate_enum(value: Any, enum_class: Type[Enum], field_name: str) -> None:
    """验证枚举值"""
    if value not in [e.value for e in enum_class]:
        valid_values = [e.value for e in enum_class]
        raise ValidationError(
            f"值必须是以下之一: {valid_values}",
            field_name,
            value
        )


def validate_list_not_empty(value: List[Any], field_name: str) -> None:
    """验证列表不为空"""
    if not value:
        raise ValidationError(f"列表 '{field_name}' 不能为空")


def validate_unique_names(items: List[NamedModel], field_name: str) -> None:
    """验证列表中名称的唯一性"""
    names = [item.name for item in items]
    if len(names) != len(set(names)):
        duplicates = [name for name in names if names.count(name) > 1]
        raise ValidationError(
            f"发现重复的名称: {list(set(duplicates))}",
            field_name
        )
