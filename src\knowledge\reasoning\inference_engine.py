"""
智能推理引擎
基于知识图谱进行智能推理和规则选择
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from datetime import datetime

from ..graph.knowledge_graph import KnowledgeGraph
from ..graph.graph_query import GraphQueryEngine
from ..base.knowledge_entity import (
    KnowledgeEntity, EntityType, RelationshipType, RuleEntity
)


logger = logging.getLogger(__name__)


class InferenceEngine:
    """智能推理引擎"""
    
    def __init__(self, knowledge_graph: KnowledgeGraph):
        """
        初始化推理引擎
        
        Args:
            knowledge_graph: 知识图谱实例
        """
        self.graph = knowledge_graph
        self.query_engine = GraphQueryEngine(knowledge_graph)
        
        # 推理规则权重
        self.inference_weights = {
            'direct_match': 1.0,        # 直接匹配
            'type_match': 0.8,          # 类型匹配
            'semantic_match': 0.6,      # 语义匹配
            'context_match': 0.4,       # 上下文匹配
            'confidence_factor': 0.2    # 置信度因子
        }
        
        logger.info("智能推理引擎初始化完成")
    
    def infer_applicable_rules(self, 
                             context: Dict[str, Any],
                             confidence_threshold: float = 0.5) -> List[Tuple[RuleEntity, float]]:
        """
        推理适用的验证规则
        
        Args:
            context: 推理上下文，包含设备信息、协议信息等
            confidence_threshold: 置信度阈值
            
        Returns:
            List[Tuple[RuleEntity, float]]: (规则, 推理置信度)列表
        """
        try:
            applicable_rules = []
            
            # 获取所有规则
            all_rules = self.graph.get_entities_by_type(EntityType.RULE)
            
            for rule in all_rules:
                if isinstance(rule, RuleEntity):
                    # 计算规则适用性分数
                    score = self._calculate_rule_applicability(rule, context)
                    
                    if score >= confidence_threshold:
                        applicable_rules.append((rule, score))
            
            # 按分数排序
            applicable_rules.sort(key=lambda x: x[1], reverse=True)
            
            logger.info(f"推理出 {len(applicable_rules)} 条适用规则")
            return applicable_rules
            
        except Exception as e:
            logger.error(f"规则推理失败: {e}")
            return []
    
    def _calculate_rule_applicability(self, rule: RuleEntity, context: Dict[str, Any]) -> float:
        """
        计算规则适用性分数
        
        Args:
            rule: 规则实体
            context: 推理上下文
            
        Returns:
            float: 适用性分数 (0-1)
        """
        score = 0.0
        
        try:
            # 1. 直接匹配检查
            direct_score = self._check_direct_match(rule, context)
            score += direct_score * self.inference_weights['direct_match']
            
            # 2. 类型匹配检查
            type_score = self._check_type_match(rule, context)
            score += type_score * self.inference_weights['type_match']
            
            # 3. 语义匹配检查
            semantic_score = self._check_semantic_match(rule, context)
            score += semantic_score * self.inference_weights['semantic_match']
            
            # 4. 上下文匹配检查
            context_score = self._check_context_match(rule, context)
            score += context_score * self.inference_weights['context_match']
            
            # 5. 置信度因子
            confidence_factor = rule.confidence * self.inference_weights['confidence_factor']
            score += confidence_factor
            
            # 归一化到0-1范围
            total_weight = sum(self.inference_weights.values())
            normalized_score = min(score / total_weight, 1.0)
            
            return normalized_score
            
        except Exception as e:
            logger.warning(f"计算规则适用性失败: {e}")
            return 0.0
    
    def _check_direct_match(self, rule: RuleEntity, context: Dict[str, Any]) -> float:
        """检查直接匹配"""
        score = 0.0
        
        # 检查设备类型匹配
        device_type = context.get('device_type')
        if device_type and hasattr(rule, 'applicable_devices') and rule.applicable_devices:
            if device_type in rule.applicable_devices:
                score += 0.4
        
        # 检查协议匹配
        protocol = context.get('protocol')
        if protocol and hasattr(rule, 'applicable_protocols') and rule.applicable_protocols:
            if protocol in rule.applicable_protocols:
                score += 0.3
        
        # 检查标准匹配
        standard = context.get('standard')
        if standard and hasattr(rule, 'applicable_standards') and rule.applicable_standards:
            if standard in rule.applicable_standards:
                score += 0.3
        
        return score
    
    def _check_type_match(self, rule: RuleEntity, context: Dict[str, Any]) -> float:
        """检查类型匹配"""
        score = 0.0
        
        # 检查规则类型与上下文的匹配
        rule_category = getattr(rule, 'category', '')
        context_category = context.get('category', '')
        
        if rule_category and context_category:
            if rule_category == context_category:
                score += 0.5
            elif self._are_categories_related(rule_category, context_category):
                score += 0.3
        
        # 检查严重程度匹配
        required_severity = context.get('required_severity')
        if required_severity and hasattr(rule, 'severity'):
            severity_scores = {'error': 1.0, 'warning': 0.7, 'info': 0.4}
            rule_severity_score = severity_scores.get(rule.severity, 0.0)
            required_severity_score = severity_scores.get(required_severity, 0.0)
            
            if rule_severity_score >= required_severity_score:
                score += 0.5
        
        return score
    
    def _check_semantic_match(self, rule: RuleEntity, context: Dict[str, Any]) -> float:
        """检查语义匹配"""
        score = 0.0
        
        # 检查关键词匹配
        context_keywords = context.get('keywords', [])
        rule_keywords = getattr(rule, 'keywords', [])
        
        if context_keywords and rule_keywords:
            common_keywords = set(context_keywords) & set(rule_keywords)
            if common_keywords:
                score += len(common_keywords) / max(len(context_keywords), len(rule_keywords))
        
        # 检查内容相似性
        context_description = context.get('description', '')
        if context_description and rule.description:
            similarity = self._calculate_text_similarity(context_description, rule.description)
            score += similarity * 0.5
        
        return score
    
    def _check_context_match(self, rule: RuleEntity, context: Dict[str, Any]) -> float:
        """检查上下文匹配"""
        score = 0.0
        
        # 检查相关实体
        related_entities = context.get('related_entities', [])
        if related_entities:
            for entity_id in related_entities:
                # 检查规则是否与相关实体有关联
                if self.graph.graph.has_edge(rule.id, entity_id) or self.graph.graph.has_edge(entity_id, rule.id):
                    score += 0.2
        
        # 检查环境上下文
        environment = context.get('environment', {})
        if environment:
            # 检查电压等级
            voltage_level = environment.get('voltage_level')
            if voltage_level and voltage_level in rule.content:
                score += 0.3
            
            # 检查设备厂商
            manufacturer = environment.get('manufacturer')
            if manufacturer and manufacturer in rule.content:
                score += 0.2
        
        return score
    
    def _are_categories_related(self, category1: str, category2: str) -> bool:
        """检查类别是否相关"""
        related_categories = {
            'iec61850_compliance': ['communication', 'protocol'],
            'communication': ['iec61850_compliance', 'network'],
            'protection': ['safety', 'device'],
            'configuration': ['setup', 'installation']
        }
        
        return category2 in related_categories.get(category1, [])
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似性（简化版）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def infer_missing_relationships(self) -> List[Tuple[str, str, RelationshipType, float]]:
        """
        推理缺失的关系
        
        Returns:
            List[Tuple[str, str, RelationshipType, float]]: (源实体ID, 目标实体ID, 关系类型, 置信度)
        """
        missing_relationships = []
        
        try:
            # 获取所有实体
            all_entities = []
            for entity_type in EntityType:
                entities = self.graph.get_entities_by_type(entity_type)
                all_entities.extend(entities)
            
            # 检查实体对之间的潜在关系
            for i, entity1 in enumerate(all_entities):
                for entity2 in all_entities[i+1:]:
                    # 检查是否已存在关系
                    if (not self.graph.graph.has_edge(entity1.id, entity2.id) and
                        not self.graph.graph.has_edge(entity2.id, entity1.id)):
                        
                        # 推理潜在关系
                        potential_relations = self._infer_entity_relationship(entity1, entity2)
                        missing_relationships.extend(potential_relations)
            
            # 按置信度排序
            missing_relationships.sort(key=lambda x: x[3], reverse=True)
            
            logger.info(f"推理出 {len(missing_relationships)} 个潜在关系")
            return missing_relationships[:50]  # 限制返回数量
            
        except Exception as e:
            logger.error(f"推理缺失关系失败: {e}")
            return []
    
    def _infer_entity_relationship(self, entity1: KnowledgeEntity, entity2: KnowledgeEntity) -> List[Tuple[str, str, RelationshipType, float]]:
        """推理两个实体间的关系"""
        relationships = []
        
        try:
            # 标准与规则的关系
            if (entity1.entity_type == EntityType.STANDARD and 
                entity2.entity_type == EntityType.RULE):
                
                if (hasattr(entity2, 'applicable_standards') and 
                    entity1.standard_number in entity2.applicable_standards):
                    relationships.append((
                        entity1.id, entity2.id, 
                        RelationshipType.DERIVED_FROM, 0.9
                    ))
            
            # 规则与设备的关系
            elif (entity1.entity_type == EntityType.RULE and 
                  entity2.entity_type == EntityType.DEVICE):
                
                if (hasattr(entity1, 'applicable_devices') and 
                    entity2.device_type in entity1.applicable_devices):
                    relationships.append((
                        entity1.id, entity2.id,
                        RelationshipType.APPLIES_TO, 0.8
                    ))
            
            # 基于内容的关系推理
            content_similarity = self._calculate_text_similarity(
                entity1.content, entity2.content
            )
            if content_similarity > 0.7:
                relationships.append((
                    entity1.id, entity2.id,
                    RelationshipType.REFERENCES, content_similarity
                ))
            
        except Exception as e:
            logger.warning(f"实体关系推理失败: {e}")
        
        return relationships
    
    def explain_inference(self, rule: RuleEntity, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        解释推理过程
        
        Args:
            rule: 规则实体
            context: 推理上下文
            
        Returns:
            Dict[str, Any]: 推理解释
        """
        explanation = {
            'rule_name': rule.name,
            'applicability_score': 0.0,
            'reasoning_steps': [],
            'confidence_factors': {},
            'recommendations': []
        }
        
        try:
            # 计算适用性分数
            score = self._calculate_rule_applicability(rule, context)
            explanation['applicability_score'] = score
            
            # 详细推理步骤
            if context.get('device_type') in getattr(rule, 'applicable_devices', []):
                explanation['reasoning_steps'].append(
                    f"规则适用于设备类型: {context.get('device_type')}"
                )
            
            if context.get('protocol') in getattr(rule, 'applicable_protocols', []):
                explanation['reasoning_steps'].append(
                    f"规则适用于协议: {context.get('protocol')}"
                )
            
            # 置信度因子
            explanation['confidence_factors'] = {
                'rule_confidence': rule.confidence,
                'direct_match': self._check_direct_match(rule, context),
                'type_match': self._check_type_match(rule, context),
                'semantic_match': self._check_semantic_match(rule, context)
            }
            
            # 推荐
            if score > 0.8:
                explanation['recommendations'].append("强烈推荐应用此规则")
            elif score > 0.6:
                explanation['recommendations'].append("建议应用此规则")
            else:
                explanation['recommendations'].append("谨慎考虑应用此规则")
            
        except Exception as e:
            logger.error(f"推理解释失败: {e}")
        
        return explanation
    
    def infer_circuit_logic_relationships(self, circuit_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        推理回路逻辑关系
        
        Args:
            circuit_context: 回路上下文信息
            
        Returns:
            List[Dict[str, Any]]: 推理出的回路关系列表
        """
        try:
            circuit_relationships = []
            
            # 获取回路类型
            circuit_type = circuit_context.get('circuit_type', '')
            
            # 根据回路类型推理可能的关系
            if circuit_type == 'protection_trip':
                relationships = self._infer_protection_trip_relationships(circuit_context)
                circuit_relationships.extend(relationships)
            elif circuit_type == 'alarm':
                relationships = self._infer_alarm_relationships(circuit_context)
                circuit_relationships.extend(relationships)
            elif circuit_type == 'interlock':
                relationships = self._infer_interlock_relationships(circuit_context)
                circuit_relationships.extend(relationships)
            elif circuit_type == 'control':
                relationships = self._infer_control_relationships(circuit_context)
                circuit_relationships.extend(relationships)
            
            # 通用关系推理
            general_relationships = self._infer_general_circuit_relationships(circuit_context)
            circuit_relationships.extend(general_relationships)
            
            logger.info(f"推理出 {len(circuit_relationships)} 个回路逻辑关系")
            return circuit_relationships
            
        except Exception as e:
            logger.error(f"回路逻辑关系推理失败: {e}")
            return []
    
    def _infer_protection_trip_relationships(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推理保护跳闸回路关系"""
        relationships = []
        
        # 保护装置与跳闸回路的关系
        relationships.append({
            'type': 'protection_trip_logic',
            'description': '保护动作应触发跳闸回路',
            'confidence': 0.95,
            'required_entities': ['PTOC', 'PDIF', 'PDIS', 'PTRC', 'XCBR'],
            'logic_flow': '保护逻辑节点 -> 跳闸逻辑节点 -> 断路器逻辑节点'
        })
        
        # GOOSE通信关系
        relationships.append({
            'type': 'goose_communication',
            'description': '保护跳闸信号应通过GOOSE传输',
            'confidence': 0.9,
            'required_entities': ['PTRC', 'GOOSE'],
            'communication_path': 'PTRC.Op -> GOOSE -> XCBR.Pos'
        })
        
        # 时间配合关系
        relationships.append({
            'type': 'timing_coordination',
            'description': '主保护动作时间应小于后备保护',
            'confidence': 0.85,
            'required_entities': ['PTOC', 'PDIS'],
            'timing_requirement': '主保护 < 后备保护'
        })
        
        return relationships
    
    def _infer_alarm_relationships(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推理告警回路关系"""
        relationships = []
        
        # 告警信号传递关系
        relationships.append({
            'type': 'alarm_signal_flow',
            'description': '设备异常应触发告警信号',
            'confidence': 0.9,
            'required_entities': ['ALM', 'ANN'],
            'signal_flow': '异常检测 -> 告警逻辑 -> 告警输出'
        })
        
        return relationships
    
    def _infer_interlock_relationships(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推理联锁回路关系"""
        relationships = []
        
        # 操作联锁关系
        relationships.append({
            'type': 'operation_interlock',
            'description': '断路器操作应受联锁条件限制',
            'confidence': 0.95,
            'required_entities': ['CILO', 'CSWI', 'XCBR'],
            'interlock_logic': 'CILO.EnaOpn/EnaCls -> CSWI.Pos -> XCBR.Pos'
        })
        
        return relationships
    
    def _infer_control_relationships(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推理控制回路关系"""
        relationships = []
        
        # 控制操作关系
        relationships.append({
            'type': 'control_operation',
            'description': '控制开关应能操作断路器',
            'confidence': 0.9,
            'required_entities': ['CSWI', 'XCBR'],
            'control_flow': 'CSWI.Pos -> XCBR.Pos'
        })
        
        return relationships
    
    def _infer_general_circuit_relationships(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """推理通用回路关系"""
        relationships = []
        
        # 冗余关系
        relationships.append({
            'type': 'redundancy',
            'description': '关键回路应有冗余配置',
            'confidence': 0.8,
            'required_entities': ['XCBR', 'PTOC'],
            'redundancy_requirement': '双重化配置'
        })
        
        # 选择性关系
        relationships.append({
            'type': 'selectivity',
            'description': '保护回路应具有选择性',
            'confidence': 0.85,
            'required_entities': ['PTOC', 'PDIF'],
            'selectivity_principle': '近后备远后备配合'
        })
        
        return relationships
    
    def validate_circuit_logic_consistency(self, circuit_entities: List[str]) -> Dict[str, Any]:
        """
        验证回路逻辑一致性
        
        Args:
            circuit_entities: 回路中的实体列表
            
        Returns:
            Dict[str, Any]: 一致性验证结果
        """
        validation_result = {
            'consistent': True,
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 获取实体类型
            entity_types = []
            for entity_id in circuit_entities:
                if entity_id in self.graph.entities:
                    entity = self.graph.entities[entity_id]
                    entity_types.append((entity_id, entity.type))
            
            # 验证逻辑节点组合的合理性
            ln_entities = [entity_id for entity_id, entity_type in entity_types 
                          if entity_type == EntityType.LN]
            
            if len(ln_entities) >= 2:
                consistency_issues = self._check_ln_consistency(ln_entities)
                validation_result['issues'].extend(consistency_issues)
                
                if consistency_issues:
                    validation_result['consistent'] = False
            
            # 验证通信路径的一致性
            communication_entities = [entity_id for entity_id, entity_type in entity_types 
                                    if entity_type in [EntityType.GOOSE_MESSAGE, EntityType.SMV_MESSAGE]]
            
            if communication_entities:
                comm_issues = self._check_communication_consistency(communication_entities)
                validation_result['issues'].extend(comm_issues)
                
                if comm_issues:
                    validation_result['consistent'] = False
            
            # 生成建议
            if not validation_result['consistent']:
                validation_result['recommendations'].append("检查回路逻辑配置的一致性")
                validation_result['recommendations'].append("验证逻辑节点间的连接关系")
            
        except Exception as e:
            logger.error(f"回路逻辑一致性验证失败: {e}")
            validation_result['issues'].append({
                'type': 'system_error',
                'description': f'验证过程出错: {str(e)}'
            })
            validation_result['consistent'] = False
        
        return validation_result
    
    def _check_ln_consistency(self, ln_entities: List[str]) -> List[Dict[str, Any]]:
        """检查逻辑节点一致性"""
        issues = []
        
        # 获取逻辑节点类名
        ln_classes = []
        for entity_id in ln_entities:
            if entity_id in self.graph.entities:
                entity = self.graph.entities[entity_id]
                ln_class = entity.properties.get('lnClass', '')
                ln_classes.append((entity_id, ln_class))
        
        # 检查常见的逻辑节点组合
        valid_combinations = {
            # 保护跳闸组合
            ('PTOC', 'PTRC', 'XCBR'): '过流保护跳闸回路',
            ('PDIF', 'PTRC', 'XCBR'): '差动保护跳闸回路',
            ('PDIS', 'PTRC', 'XCBR'): '距离保护跳闸回路',
            
            # 控制组合
            ('CSWI', 'XCBR'): '断路器控制回路',
            ('CILO', 'CSWI', 'XCBR'): '联锁控制回路',
            
            # 测量组合
            ('MMXU', 'PTRC'): '测量与保护配合'
        }
        
        # 检查当前组合是否在有效组合中
        current_classes = tuple(ln_class for _, ln_class in ln_classes)
        found_valid = False
        
        for valid_combo, description in valid_combinations.items():
            if all(cls in current_classes for cls in valid_combo):
                found_valid = True
                break
        
        if not found_valid and len(ln_classes) > 1:
            issues.append({
                'type': 'logic_inconsistency',
                'description': f'逻辑节点组合 {current_classes} 不符合标准配置',
                'severity': 'warning',
                'recommendation': '检查逻辑节点间的连接关系是否正确'
            })
        
        return issues
    
    def _check_communication_consistency(self, comm_entities: List[str]) -> List[Dict[str, Any]]:
        """检查通信一致性"""
        issues = []
        
        # 检查GOOSE消息的发布/订阅关系
        for entity_id in comm_entities:
            if entity_id in self.graph.entities:
                entity = self.graph.entities[entity_id]
                
                if entity.type == EntityType.GOOSE_MESSAGE:
                    # 检查是否有发布者
                    publishers = [rel for rel in self.graph.relations.values() 
                                 if rel.type == RelationType.PUBLISHES and rel.target == entity_id]
                    
                    if not publishers:
                        issues.append({
                            'type': 'communication_error',
                            'description': f'GOOSE消息 {entity.name} 缺少发布者',
                            'severity': 'error'
                        })
                    
                    # 检查是否有订阅者
                    subscribers = [rel for rel in self.graph.relations.values() 
                                  if rel.type == RelationType.SUBSCRIBES_TO and rel.target == entity_id]
                    
                    if not subscribers:
                        issues.append({
                            'type': 'communication_warning',
                            'description': f'GOOSE消息 {entity.name} 缺少订阅者',
                            'severity': 'warning'
                        })
        
        return issues
    
    def recommend_circuit_improvements(self, circuit_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        推荐回路改进建议
        
        Args:
            circuit_context: 回路上下文信息
            
        Returns:
            List[Dict[str, Any]]: 改进建议列表
        """
        recommendations = []
        
        try:
            circuit_type = circuit_context.get('circuit_type', '')
            entities = circuit_context.get('entities', [])
            
            # 基于回路类型提供改进建议
            if circuit_type == 'protection_trip':
                recs = self._recommend_protection_improvements(entities)
                recommendations.extend(recs)
            elif circuit_type == 'control':
                recs = self._recommend_control_improvements(entities)
                recommendations.extend(recs)
            
            # 通用改进建议
            general_recs = self._recommend_general_improvements(entities)
            recommendations.extend(general_recs)
            
        except Exception as e:
            logger.error(f"生成回路改进建议失败: {e}")
        
        return recommendations
    
    def _recommend_protection_improvements(self, entities: List[str]) -> List[Dict[str, Any]]:
        """推荐保护回路改进建议"""
        recommendations = []
        
        # 检查是否包含必要的保护逻辑节点
        ln_entities = [entity_id for entity_id in entities 
                      if entity_id in self.graph.entities and 
                      self.graph.entities[entity_id].type == EntityType.LN]
        
        ln_classes = [self.graph.entities[entity_id].properties.get('lnClass', '') 
                     for entity_id in ln_entities]
        
        # 检查主保护配置
        if 'PTOC' not in ln_classes and 'PDIF' not in ln_classes and 'PDIS' not in ln_classes:
            recommendations.append({
                'type': 'missing_main_protection',
                'description': '缺少主保护逻辑节点',
                'recommendation': '添加适当的主保护逻辑节点（PTOC/PDIF/PDIS）',
                'priority': 'high'
            })
        
        # 检查跳闸逻辑
        if 'PTRC' not in ln_classes:
            recommendations.append({
                'type': 'missing_trip_logic',
                'description': '缺少跳闸逻辑节点',
                'recommendation': '添加PTRC跳闸逻辑节点',
                'priority': 'high'
            })
        
        return recommendations
    
    def _recommend_control_improvements(self, entities: List[str]) -> List[Dict[str, Any]]:
        """推荐控制回路改进建议"""
        recommendations = []
        
        # 检查是否包含必要的控制逻辑节点
        ln_entities = [entity_id for entity_id in entities 
                      if entity_id in self.graph.entities and 
                      self.graph.entities[entity_id].type == EntityType.LN]
        
        ln_classes = [self.graph.entities[entity_id].properties.get('lnClass', '') 
                     for entity_id in ln_entities]
        
        # 检查控制开关
        if 'CSWI' not in ln_classes:
            recommendations.append({
                'type': 'missing_control_switch',
                'description': '缺少控制开关逻辑节点',
                'recommendation': '添加CSWI控制开关逻辑节点',
                'priority': 'medium'
            })
        
        return recommendations
    
    def _recommend_general_improvements(self, entities: List[str]) -> List[Dict[str, Any]]:
        """推荐通用改进建议"""
        recommendations = []
        
        # 检查冗余配置
        xcbR_entities = [entity_id for entity_id in entities 
                        if entity_id in self.graph.entities and 
                        self.graph.entities[entity_id].properties.get('lnClass', '') == 'XCBR']
        
        if len(xcbR_entities) == 1:
            recommendations.append({
                'type': 'single_point_failure',
                'description': '断路器控制为单点配置',
                'recommendation': '考虑双重化配置以提高可靠性',
                'priority': 'medium'
            })
        
        # 检查通信配置
        goose_entities = [entity_id for entity_id in entities 
                         if entity_id in self.graph.entities and 
                         self.graph.entities[entity_id].type == EntityType.GOOSE_MESSAGE]
        
        if not goose_entities:
            recommendations.append({
                'type': 'missing_communication',
                'description': '缺少GOOSE通信配置',
                'recommendation': '对于关键信号应配置GOOSE通信',
                'priority': 'high'
            })
        
        return recommendations