"""
IEC61850变电站数据模型
定义变电站、电压等级、间隔、设备等核心实体
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum

from .base import (
    BaseModel, NamedModel, ValidationError, 
    validate_required, validate_range, validate_enum,
    validate_list_not_empty, validate_unique_names
)


class VoltageLevelEnum(Enum):
    """电压等级枚举"""
    KV_0_4 = "0.4kV"
    KV_6 = "6kV"
    KV_10 = "10kV"
    KV_20 = "20kV"
    KV_35 = "35kV"
    KV_66 = "66kV"
    KV_110 = "110kV"
    KV_220 = "220kV"
    KV_330 = "330kV"
    KV_500 = "500kV"
    KV_750 = "750kV"
    KV_1000 = "1000kV"


class EquipmentType(Enum):
    """设备类型枚举"""
    CIRCUIT_BREAKER = "CBR"  # 断路器
    DISCONNECTOR = "DIS"     # 隔离开关
    EARTHING_SWITCH = "ERS"  # 接地开关
    CURRENT_TRANSFORMER = "CTR"  # 电流互感器
    VOLTAGE_TRANSFORMER = "VTR"  # 电压互感器
    POWER_TRANSFORMER = "PTR"    # 电力变压器
    CAPACITOR = "CAP"        # 电容器
    REACTOR = "REA"          # 电抗器
    BUSBAR = "BBR"           # 母线
    CONDUCTOR = "CON"        # 导线
    GENERAL = "GEN"          # 通用设备


@dataclass
class Voltage(BaseModel):
    """电压定义"""
    
    # IEC61850标准属性
    multiplier: str = "k"  # 倍数 (k=千, M=兆, etc.)
    unit: str = "V"        # 单位
    value: float = 0.0     # 数值
    
    def validate(self) -> None:
        """验证电压定义"""
        validate_required(self.unit, "unit")

        if self.value < 0:
            raise ValidationError("电压值不能为负数", "value", self.value)

        # 验证倍数（允许空字符串）
        valid_multipliers = ["", "k", "M", "G", "T", "m", "μ", "n", "p"]
        if self.multiplier not in valid_multipliers:
            raise ValidationError(
                f"无效的倍数: {self.multiplier}",
                "multiplier",
                self.multiplier
            )
    
    def get_voltage_kv(self) -> float:
        """获取以kV为单位的电压值"""
        multiplier_map = {
            "": 1e-3,      # V to kV
            "k": 1.0,      # kV to kV
            "M": 1000.0,   # MV to kV
            "G": 1e6,      # GV to kV
            "m": 1e-6,     # mV to kV
            "μ": 1e-9,     # μV to kV
            "n": 1e-12,    # nV to kV
            "p": 1e-15     # pV to kV
        }
        
        factor = multiplier_map.get(self.multiplier, 1.0)
        return self.value * factor
    
    def __str__(self) -> str:
        return f"{self.value}{self.multiplier}{self.unit}"


@dataclass
class ConductingEquipment(NamedModel):
    """导电设备"""
    
    # IEC61850标准属性
    type: str = ""           # 设备类型
    virtual: bool = False    # 是否为虚拟设备
    
    # 扩展属性
    manufacturer: Optional[str] = None  # 制造商
    model: Optional[str] = None         # 型号
    serial_number: Optional[str] = None # 序列号
    
    def validate(self) -> None:
        """验证导电设备"""
        super().validate()
        validate_required(self.type, "type")
        
        # 验证设备类型
        try:
            EquipmentType(self.type)
        except ValueError:
            # 如果不是标准类型，允许自定义类型，但需要符合命名规范
            if not self._is_valid_iec_name(self.type):
                raise ValidationError(
                    "设备类型必须符合IEC61850命名规范",
                    "type",
                    self.type
                )


@dataclass
class Bay(NamedModel):
    """间隔"""
    
    # IEC61850标准属性
    conducting_equipments: List[ConductingEquipment] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证间隔"""
        super().validate()
        
        # 验证设备名称唯一性
        if self.conducting_equipments:
            validate_unique_names(self.conducting_equipments, "conducting_equipments")
    
    def add_equipment(self, equipment: ConductingEquipment) -> None:
        """添加设备"""
        # 检查名称唯一性
        existing_names = [eq.name for eq in self.conducting_equipments]
        if equipment.name in existing_names:
            raise ValidationError(
                f"设备名称 '{equipment.name}' 已存在",
                "conducting_equipments"
            )
        
        self.conducting_equipments.append(equipment)
        self.update_timestamp()
    
    def remove_equipment(self, equipment_name: str) -> bool:
        """移除设备"""
        for i, equipment in enumerate(self.conducting_equipments):
            if equipment.name == equipment_name:
                del self.conducting_equipments[i]
                self.update_timestamp()
                return True
        return False
    
    def get_equipment(self, equipment_name: str) -> Optional[ConductingEquipment]:
        """获取指定设备"""
        for equipment in self.conducting_equipments:
            if equipment.name == equipment_name:
                return equipment
        return None
    
    def get_equipments_by_type(self, equipment_type: str) -> List[ConductingEquipment]:
        """根据类型获取设备列表"""
        return [eq for eq in self.conducting_equipments if eq.type == equipment_type]


@dataclass
class VoltageLevel(NamedModel):
    """电压等级"""
    
    # IEC61850标准属性
    voltage: Voltage = field(default_factory=Voltage)
    bays: List[Bay] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证电压等级"""
        super().validate()
        
        # 验证电压
        self.voltage.validate()
        
        # 验证间隔名称唯一性
        if self.bays:
            validate_unique_names(self.bays, "bays")
    
    def add_bay(self, bay: Bay) -> None:
        """添加间隔"""
        # 检查名称唯一性
        existing_names = [b.name for b in self.bays]
        if bay.name in existing_names:
            raise ValidationError(
                f"间隔名称 '{bay.name}' 已存在",
                "bays"
            )
        
        self.bays.append(bay)
        self.update_timestamp()
    
    def remove_bay(self, bay_name: str) -> bool:
        """移除间隔"""
        for i, bay in enumerate(self.bays):
            if bay.name == bay_name:
                del self.bays[i]
                self.update_timestamp()
                return True
        return False
    
    def get_bay(self, bay_name: str) -> Optional[Bay]:
        """获取指定间隔"""
        for bay in self.bays:
            if bay.name == bay_name:
                return bay
        return None
    
    def get_all_equipments(self) -> List[ConductingEquipment]:
        """获取所有设备"""
        equipments = []
        for bay in self.bays:
            equipments.extend(bay.conducting_equipments)
        return equipments


@dataclass
class SubStation(NamedModel):
    """变电站"""
    
    # IEC61850标准属性
    voltage_levels: List[VoltageLevel] = field(default_factory=list)
    
    # 扩展属性
    location: Optional[str] = None      # 地理位置
    owner: Optional[str] = None         # 所有者
    operator: Optional[str] = None      # 运营商
    commissioning_date: Optional[str] = None  # 投运日期
    
    def validate(self) -> None:
        """验证变电站"""
        super().validate()
        
        # 验证电压等级名称唯一性
        if self.voltage_levels:
            validate_unique_names(self.voltage_levels, "voltage_levels")
    
    def add_voltage_level(self, voltage_level: VoltageLevel) -> None:
        """添加电压等级"""
        # 检查名称唯一性
        existing_names = [vl.name for vl in self.voltage_levels]
        if voltage_level.name in existing_names:
            raise ValidationError(
                f"电压等级名称 '{voltage_level.name}' 已存在",
                "voltage_levels"
            )
        
        self.voltage_levels.append(voltage_level)
        self.update_timestamp()
    
    def remove_voltage_level(self, voltage_level_name: str) -> bool:
        """移除电压等级"""
        for i, vl in enumerate(self.voltage_levels):
            if vl.name == voltage_level_name:
                del self.voltage_levels[i]
                self.update_timestamp()
                return True
        return False
    
    def get_voltage_level(self, voltage_level_name: str) -> Optional[VoltageLevel]:
        """获取指定电压等级"""
        for vl in self.voltage_levels:
            if vl.name == voltage_level_name:
                return vl
        return None
    
    def get_all_bays(self) -> List[Bay]:
        """获取所有间隔"""
        bays = []
        for vl in self.voltage_levels:
            bays.extend(vl.bays)
        return bays
    
    def get_all_equipments(self) -> List[ConductingEquipment]:
        """获取所有设备"""
        equipments = []
        for vl in self.voltage_levels:
            equipments.extend(vl.get_all_equipments())
        return equipments
    
    def get_equipment_by_path(self, path: str) -> Optional[ConductingEquipment]:
        """根据路径获取设备
        
        Args:
            path: 设备路径，格式为 "voltage_level/bay/equipment"
        
        Returns:
            设备对象或None
        """
        parts = path.split('/')
        if len(parts) != 3:
            return None
        
        vl_name, bay_name, eq_name = parts
        
        vl = self.get_voltage_level(vl_name)
        if not vl:
            return None
        
        bay = vl.get_bay(bay_name)
        if not bay:
            return None
        
        return bay.get_equipment(eq_name)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取变电站统计信息"""
        all_equipments = self.get_all_equipments()
        
        # 按类型统计设备数量
        equipment_count_by_type = {}
        for eq in all_equipments:
            equipment_count_by_type[eq.type] = equipment_count_by_type.get(eq.type, 0) + 1
        
        return {
            "voltage_levels_count": len(self.voltage_levels),
            "bays_count": len(self.get_all_bays()),
            "equipments_count": len(all_equipments),
            "equipment_count_by_type": equipment_count_by_type,
            "voltage_levels": [
                {
                    "name": vl.name,
                    "voltage": str(vl.voltage),
                    "bays_count": len(vl.bays),
                    "equipments_count": len(vl.get_all_equipments())
                }
                for vl in self.voltage_levels
            ]
        }
