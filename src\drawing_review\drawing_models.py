"""
图纸数据模型
定义图纸解析和审查相关的数据结构
"""

from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import uuid


class ElementType(Enum):
    """图元类型"""
    LINE = "line"
    ARC = "arc"
    CIRCLE = "circle"
    POLYLINE = "polyline"
    TEXT = "text"
    MTEXT = "mtext"
    DIMENSION = "dimension"
    BLOCK = "block"
    INSERT = "insert"
    HATCH = "hatch"
    SPLINE = "spline"
    ELLIPSE = "ellipse"
    POINT = "point"


class LineType(Enum):
    """线型"""
    CONTINUOUS = "continuous"
    DASHED = "dashed"
    DOTTED = "dotted"
    DASHDOT = "dashdot"
    DASHDOTDOT = "dashdotdot"
    CENTER = "center"
    PHANTOM = "phantom"
    HIDDEN = "hidden"


class ReviewSeverity(Enum):
    """审查问题严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Point:
    """点坐标"""
    x: float
    y: float
    z: float = 0.0
    
    def distance_to(self, other: 'Point') -> float:
        """计算到另一点的距离"""
        return ((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)**0.5


@dataclass
class Color:
    """颜色"""
    r: int = 0
    g: int = 0
    b: int = 0
    index: Optional[int] = None  # AutoCAD颜色索引
    
    def to_hex(self) -> str:
        """转换为十六进制颜色"""
        return f"#{self.r:02x}{self.g:02x}{self.b:02x}"


@dataclass
class Layer:
    """图层"""
    name: str
    color: Color
    line_type: LineType = LineType.CONTINUOUS
    line_weight: float = 0.25
    visible: bool = True
    locked: bool = False
    frozen: bool = False


@dataclass
class DrawingElement:
    """图纸元素基类"""
    element_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    element_type: ElementType = ElementType.LINE
    layer: str = "0"
    color: Optional[Color] = None
    line_type: LineType = LineType.CONTINUOUS
    line_weight: float = 0.25
    visible: bool = True
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        raise NotImplementedError


@dataclass
class Line(DrawingElement):
    """直线"""
    start_point: Point = field(default_factory=lambda: Point(0, 0))
    end_point: Point = field(default_factory=lambda: Point(1, 1))
    element_type: ElementType = field(default=ElementType.LINE, init=False)
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        min_x = min(self.start_point.x, self.end_point.x)
        max_x = max(self.start_point.x, self.end_point.x)
        min_y = min(self.start_point.y, self.end_point.y)
        max_y = max(self.start_point.y, self.end_point.y)
        return Point(min_x, min_y), Point(max_x, max_y)
    
    def length(self) -> float:
        """计算长度"""
        return self.start_point.distance_to(self.end_point)


@dataclass
class Arc(DrawingElement):
    """圆弧"""
    center: Point = field(default_factory=lambda: Point(0, 0))
    radius: float = 1.0
    start_angle: float = 0.0  # 起始角度（度）
    end_angle: float = 90.0   # 结束角度（度）
    element_type: ElementType = field(default=ElementType.ARC, init=False)
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        # 简化实现，实际需要考虑角度范围
        min_x = self.center.x - self.radius
        max_x = self.center.x + self.radius
        min_y = self.center.y - self.radius
        max_y = self.center.y + self.radius
        return Point(min_x, min_y), Point(max_x, max_y)


@dataclass
class Circle(DrawingElement):
    """圆"""
    center: Point = field(default_factory=lambda: Point(0, 0))
    radius: float = 1.0
    element_type: ElementType = field(default=ElementType.CIRCLE, init=False)
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        min_x = self.center.x - self.radius
        max_x = self.center.x + self.radius
        min_y = self.center.y - self.radius
        max_y = self.center.y + self.radius
        return Point(min_x, min_y), Point(max_x, max_y)


@dataclass
class Text(DrawingElement):
    """文本"""
    content: str = ""
    position: Point = field(default_factory=lambda: Point(0, 0))
    height: float = 2.5
    rotation: float = 0.0
    font: str = "Arial"
    element_type: ElementType = field(default=ElementType.TEXT, init=False)
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        # 简化实现，实际需要根据字体和文本内容计算
        width = len(self.content) * self.height * 0.6
        return (
            Point(self.position.x, self.position.y),
            Point(self.position.x + width, self.position.y + self.height)
        )


@dataclass
class Dimension(DrawingElement):
    """尺寸标注"""
    dim_type: str = "linear"  # linear, angular, radial, diameter
    measurement_points: List[Point] = field(default_factory=list)
    dimension_line_position: Point = field(default_factory=lambda: Point(0, 0))
    text_override: Optional[str] = None
    element_type: ElementType = field(default=ElementType.DIMENSION, init=False)
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        if not self.measurement_points:
            return Point(0, 0), Point(0, 0)
        
        x_coords = [p.x for p in self.measurement_points] + [self.dimension_line_position.x]
        y_coords = [p.y for p in self.measurement_points] + [self.dimension_line_position.y]
        
        return Point(min(x_coords), min(y_coords)), Point(max(x_coords), max(y_coords))


@dataclass
class Block(DrawingElement):
    """块定义"""
    name: str = ""
    base_point: Point = field(default_factory=lambda: Point(0, 0))
    elements: List[DrawingElement] = field(default_factory=list)
    element_type: ElementType = field(default=ElementType.BLOCK, init=False)
    
    def get_bounds(self) -> Tuple[Point, Point]:
        """获取边界框"""
        if not self.elements:
            return self.base_point, self.base_point
        
        bounds = [elem.get_bounds() for elem in self.elements]
        min_points = [bound[0] for bound in bounds]
        max_points = [bound[1] for bound in bounds]
        
        min_x = min(p.x for p in min_points)
        min_y = min(p.y for p in min_points)
        max_x = max(p.x for p in max_points)
        max_y = max(p.y for p in max_points)
        
        return Point(min_x, min_y), Point(max_x, max_y)


@dataclass
class DrawingDocument:
    """图纸文档"""
    file_path: str = ""
    file_type: str = ""  # dwg, dxf, pdf
    title: str = ""
    drawing_number: str = ""
    revision: str = ""
    scale: str = ""
    created_date: Optional[datetime] = None
    modified_date: Optional[datetime] = None
    
    # 图纸内容
    layers: Dict[str, Layer] = field(default_factory=dict)
    elements: List[DrawingElement] = field(default_factory=list)
    blocks: Dict[str, Block] = field(default_factory=dict)
    
    # 图纸属性
    units: str = "mm"
    paper_size: str = "A3"
    viewport_bounds: Optional[Tuple[Point, Point]] = None
    
    def get_elements_by_type(self, element_type: ElementType) -> List[DrawingElement]:
        """按类型获取图元"""
        return [elem for elem in self.elements if elem.element_type == element_type]
    
    def get_elements_by_layer(self, layer_name: str) -> List[DrawingElement]:
        """按图层获取图元"""
        return [elem for elem in self.elements if elem.layer == layer_name]
    
    def get_text_elements(self) -> List[Text]:
        """获取所有文本元素"""
        return [elem for elem in self.elements if isinstance(elem, Text)]
    
    def get_dimension_elements(self) -> List[Dimension]:
        """获取所有尺寸标注"""
        return [elem for elem in self.elements if isinstance(elem, Dimension)]


@dataclass
class ReviewIssue:
    """审查问题"""
    issue_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    severity: ReviewSeverity = ReviewSeverity.WARNING
    category: str = ""  # 问题分类：线型、颜色、标注、尺寸等
    rule_code: str = ""  # 规则编码
    title: str = ""
    description: str = ""
    location: Optional[Point] = None
    affected_elements: List[str] = field(default_factory=list)  # 受影响的图元ID
    
    # 修改建议
    suggestion: str = ""
    auto_fixable: bool = False
    fix_action: Optional[Dict[str, Any]] = None
    
    # 规范引用
    standard_reference: str = ""
    standard_clause: str = ""


@dataclass
class DrawingReviewResult:
    """图纸审查结果"""
    review_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    drawing_document: DrawingDocument = field(default_factory=DrawingDocument)
    review_date: datetime = field(default_factory=datetime.now)
    
    # 审查问题
    issues: List[ReviewIssue] = field(default_factory=list)
    
    # 统计信息
    total_elements: int = 0
    total_layers: int = 0
    total_blocks: int = 0
    
    # 合规性评分
    compliance_score: float = 0.0  # 0-100分
    
    def add_issue(self, issue: ReviewIssue):
        """添加审查问题"""
        self.issues.append(issue)
    
    def get_issues_by_severity(self, severity: ReviewSeverity) -> List[ReviewIssue]:
        """按严重程度获取问题"""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_issues_by_category(self, category: str) -> List[ReviewIssue]:
        """按分类获取问题"""
        return [issue for issue in self.issues if issue.category == category]
    
    def get_critical_issues(self) -> List[ReviewIssue]:
        """获取关键问题"""
        return self.get_issues_by_severity(ReviewSeverity.CRITICAL)
    
    def get_error_issues(self) -> List[ReviewIssue]:
        """获取错误问题"""
        return self.get_issues_by_severity(ReviewSeverity.ERROR)
    
    def get_warning_issues(self) -> List[ReviewIssue]:
        """获取警告问题"""
        return self.get_issues_by_severity(ReviewSeverity.WARNING)
    
    def get_auto_fixable_issues(self) -> List[ReviewIssue]:
        """获取可自动修复的问题"""
        return [issue for issue in self.issues if issue.auto_fixable]
    
    def calculate_compliance_score(self) -> float:
        """计算合规性评分"""
        if not self.issues:
            self.compliance_score = 100.0
            return self.compliance_score
        
        # 根据问题严重程度计算扣分
        deductions = 0
        for issue in self.issues:
            if issue.severity == ReviewSeverity.CRITICAL:
                deductions += 20
            elif issue.severity == ReviewSeverity.ERROR:
                deductions += 10
            elif issue.severity == ReviewSeverity.WARNING:
                deductions += 5
            elif issue.severity == ReviewSeverity.INFO:
                deductions += 1
        
        self.compliance_score = max(0, 100 - deductions)
        return self.compliance_score
    
    def get_summary(self) -> Dict[str, Any]:
        """获取审查结果摘要"""
        return {
            'review_id': self.review_id,
            'drawing_file': self.drawing_document.file_path,
            'review_date': self.review_date.isoformat(),
            'compliance_score': self.compliance_score,
            'total_issues': len(self.issues),
            'critical_issues': len(self.get_critical_issues()),
            'error_issues': len(self.get_error_issues()),
            'warning_issues': len(self.get_warning_issues()),
            'info_issues': len(self.get_issues_by_severity(ReviewSeverity.INFO)),
            'auto_fixable_issues': len(self.get_auto_fixable_issues()),
            'total_elements': self.total_elements,
            'total_layers': self.total_layers,
            'total_blocks': self.total_blocks
        }
