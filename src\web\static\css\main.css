/* IEC61850设计检查器 - 主样式文件 */

/* 全局样式 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --transition: all 0.3s ease;
}

/* 基础样式重置 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
}

/* 特色卡片样式 */
.feature-card {
    border: none;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin: 0 auto;
}

/* 步骤数字样式 */
.step-number {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto;
}

/* 问题和解决方案图标 */
.problem-icon,
.solution-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* 拖拽上传区域 */
.drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
    background-color: #fafafa;
}

.drop-zone:hover,
.drop-zone.drag-over {
    border-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.05);
}

.drop-zone.drag-over {
    border-style: solid;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: var(--border-radius);
    transition: width 0.6s ease;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background-color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--box-shadow-lg);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: var(--border-radius);
}

.alert-primary {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);
    color: var(--primary-color);
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: var(--success-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: var(--danger-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    color: var(--info-color);
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: var(--light-color);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

/* 列表组样式 */
.list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    transition: var(--transition);
}

.list-group-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.list-group-item:last-child {
    border-bottom: none;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--secondary-color);
}

/* 分页样式 */
.pagination .page-link {
    border: none;
    color: var(--primary-color);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.pagination .page-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .feature-card {
        margin-bottom: 1rem;
    }
    
    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .modal,
    .toast-container {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 特殊效果 */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-strong {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* 状态指示器 */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-success {
    background-color: var(--success-color);
}

.status-warning {
    background-color: var(--warning-color);
}

.status-danger {
    background-color: var(--danger-color);
}

.status-info {
    background-color: var(--info-color);
}
