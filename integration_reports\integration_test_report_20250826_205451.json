{"test_summary": {"test_time": "2025-08-26T20:54:51.400860", "total_scenarios": 5, "successful_scenarios": 5, "average_score": 95.0}, "test_results": {"SCD解析与IEC61850验证集成": {"scenario_name": "SCD解析与IEC61850验证集成", "description": "SCD文件解析后直接进行IEC61850标准验证", "modules": ["scd_parser", "iec61850_validator"], "start_time": "2025-08-26T20:54:51.182199", "success": true, "integration_score": 100, "performance_metrics": {"average_processing_time": 0.00040502548217773436, "throughput": 2468.9804567930305}, "issues": [], "recommendations": ["SCD解析与IEC61850验证集成良好，可以投入生产使用"], "end_time": "2025-08-26T20:54:51.203172", "duration": 0.020973}, "完整审查流程集成": {"scenario_name": "完整审查流程集成", "description": "从SCD解析到专业报告生成的完整流程", "modules": ["scd_parser", "iec61850_validator", "report_generator"], "start_time": "2025-08-26T20:54:51.203792", "success": true, "integration_score": 100, "performance_metrics": {}, "issues": [], "recommendations": ["完整审查流程集成优秀，系统可以投入生产"], "end_time": "2025-08-26T20:54:51.205547", "duration": 0.001755}, "知识推理引擎集成": {"scenario_name": "知识推理引擎集成", "description": "多个知识推理引擎协同工作", "modules": ["circuit_interconnection", "deep_regulation", "logic_verification"], "start_time": "2025-08-26T20:54:51.206227", "success": true, "integration_score": 100, "performance_metrics": {}, "issues": [], "recommendations": ["知识推理引擎集成良好，智能化程度高"], "end_time": "2025-08-26T20:54:51.208151", "duration": 0.001924}, "错误处理链集成": {"scenario_name": "错误处理链集成", "description": "验证跨模块的错误处理和恢复机制", "modules": ["all_modules"], "start_time": "2025-08-26T20:54:51.208697", "success": true, "integration_score": 80, "performance_metrics": {}, "issues": ["错误处理失败: 系统资源错误"], "recommendations": ["建议加强错误处理和恢复机制"], "end_time": "2025-08-26T20:54:51.210083", "duration": 0.001386}, "性能和并发集成": {"scenario_name": "性能和并发集成", "description": "测试多模块并发处理和性能表现", "modules": ["all_modules"], "start_time": "2025-08-26T20:54:51.210702", "success": true, "integration_score": 95, "performance_metrics": {"single_thread_time": 0.10521721839904785, "multi_thread_time": 0.05403876304626465, "performance_improvement": 48.64076063927417, "memory_usage": 0.21484375}, "issues": [], "recommendations": ["系统性能优秀，支持高并发处理"], "end_time": "2025-08-26T20:54:51.395904", "duration": 0.185202}}}