"""
IEC61850标准知识库
包含IEC61850系列标准的详细知识
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from ..base.knowledge_entity import (
    StandardEntity, RuleEntity, RequirementEntity, DeviceEntity, ProtocolEntity,
    EntityType, ConfidenceLevel, RelationshipType, EntityRelationship
)


logger = logging.getLogger(__name__)


class IEC61850Knowledge:
    """IEC61850标准知识库"""
    
    def __init__(self):
        """初始化IEC61850知识库"""
        self.standards = {}
        self.rules = {}
        self.requirements = {}
        self.devices = {}
        self.protocols = {}
        
        self._initialize_knowledge()
        logger.info("IEC61850知识库初始化完成")
    
    def _initialize_knowledge(self):
        """初始化知识库内容"""
        # 初始化IEC61850系列标准
        self._init_iec61850_standards()
        
        # 初始化验证规则
        self._init_validation_rules()
        
        # 初始化技术要求
        self._init_technical_requirements()
        
        # 初始化设备知识
        self._init_device_knowledge()
        
        # 初始化协议知识
        self._init_protocol_knowledge()
    
    def _init_iec61850_standards(self):
        """初始化IEC61850系列标准"""
        
        # IEC 61850-1: 概述和一般要求
        iec61850_1 = StandardEntity(
            standard_number="IEC 61850-1",
            standard_title="Communication networks and systems for power utility automation - Part 1: Introduction and overview",
            name="IEC 61850-1 概述和一般要求",
            description="IEC 61850系列标准的概述，定义了电力公用事业自动化通信网络和系统的总体架构",
            content="""
            IEC 61850-1提供了整个IEC 61850系列标准的概述和介绍。
            主要内容包括：
            1. 标准的目标和范围
            2. 系统架构概述
            3. 通信要求
            4. 互操作性原则
            5. 系统集成指导
            """,
            source="IEC 61850-1:2013",
            issuing_organization="International Electrotechnical Commission",
            effective_date=datetime(2013, 1, 1),
            category="electrical",
            domain="substation",
            scope="电力公用事业自动化通信网络和系统",
            confidence=1.0
        )
        self.standards["iec61850-1"] = iec61850_1
        
        # IEC 61850-6: SCL配置语言
        iec61850_6 = StandardEntity(
            standard_number="IEC 61850-6",
            standard_title="Communication networks and systems for power utility automation - Part 6: Configuration description language for communication in electrical substations related to IEDs",
            name="IEC 61850-6 SCL配置语言",
            description="定义了用于描述IED通信配置的SCL(Substation Configuration Language)语言",
            content="""
            IEC 61850-6定义了变电站配置语言(SCL)，用于描述：
            1. IED能力描述(ICD)
            2. 系统配置描述(SCD) 
            3. 已配置IED描述(CID)
            4. 系统规范描述(SSD)
            
            SCL基于XML，提供了标准化的配置文件格式。
            """,
            source="IEC 61850-6:2009+AMD1:2018+AMD2:2024",
            issuing_organization="International Electrotechnical Commission",
            category="electrical",
            domain="substation",
            scope="变电站配置语言和文件格式",
            confidence=1.0
        )
        self.standards["iec61850-6"] = iec61850_6
        
        # IEC 61850-7-1: 基本通信结构
        iec61850_7_1 = StandardEntity(
            standard_number="IEC 61850-7-1",
            standard_title="Communication networks and systems for power utility automation - Part 7-1: Basic communication structure - Principles and models",
            name="IEC 61850-7-1 基本通信结构",
            description="定义了IEC 61850通信的基本结构、原则和模型",
            content="""
            IEC 61850-7-1定义了：
            1. 抽象通信服务接口(ACSI)
            2. 逻辑节点和数据对象模型
            3. 服务模型
            4. 数据类型和通用数据类
            5. 设备模型
            """,
            source="IEC 61850-7-1:2011+AMD1:2020",
            issuing_organization="International Electrotechnical Commission",
            category="electrical",
            domain="substation",
            scope="通信结构和数据模型",
            confidence=1.0
        )
        self.standards["iec61850-7-1"] = iec61850_7_1
        
        # IEC 61850-8-1: 映射到MMS
        iec61850_8_1 = StandardEntity(
            standard_number="IEC 61850-8-1",
            standard_title="Communication networks and systems for power utility automation - Part 8-1: Specific communication service mapping (SCSM) - Mappings to MMS (ISO 9506-1 and ISO 9506-2) and to ISO/IEC 8802-3",
            name="IEC 61850-8-1 MMS映射",
            description="定义了IEC 61850服务到MMS(Manufacturing Message Specification)的映射",
            content="""
            IEC 61850-8-1定义了：
            1. ACSI服务到MMS的映射
            2. 数据对象到MMS变量的映射
            3. 报告服务的实现
            4. 日志服务的实现
            5. 文件传输服务的实现
            """,
            source="IEC 61850-8-1:2011+AMD1:2020",
            issuing_organization="International Electrotechnical Commission",
            category="electrical",
            domain="substation",
            scope="MMS协议映射",
            confidence=1.0
        )
        self.standards["iec61850-8-1"] = iec61850_8_1
        
        # IEC 61850-9-2: 采样值传输
        iec61850_9_2 = StandardEntity(
            standard_number="IEC 61850-9-2",
            standard_title="Communication networks and systems for power utility automation - Part 9-2: Specific communication service mapping (SCSM) - Sampled values over ISO/IEC 8802-3",
            name="IEC 61850-9-2 采样值传输",
            description="定义了采样值(SV)在以太网上的传输规范",
            content="""
            IEC 61850-9-2定义了：
            1. 采样值的数据格式
            2. 以太网传输机制
            3. 同步要求
            4. 性能要求
            5. 测试规范
            """,
            source="IEC 61850-9-2:2011+AMD1:2020",
            issuing_organization="International Electrotechnical Commission",
            category="electrical",
            domain="substation",
            scope="采样值传输协议",
            confidence=1.0
        )
        self.standards["iec61850-9-2"] = iec61850_9_2
    
    def _init_validation_rules(self):
        """初始化验证规则"""
        
        # SCL文件结构规则
        scl_structure_rule = RuleEntity(
            name="SCL_File_Structure_Validation",
            description="验证SCL文件的基本结构符合IEC 61850-6标准",
            content="SCL文件必须包含Header、Substation、IED、DataTypeTemplates等必要元素",
            source="IEC 61850-6",
            rule_type="syntax",
            severity="error",
            category="iec61850_compliance",
            condition="解析SCL文件时",
            action="检查必要元素是否存在",
            message_template="SCL文件结构错误: 缺少必要元素 {missing_elements}",
            applicable_standards=["IEC 61850-6"],
            fix_suggestions=[
                "确保SCL文件包含Header元素",
                "添加必要的Substation定义",
                "包含完整的DataTypeTemplates"
            ],
            confidence=0.95
        )
        self.rules["scl_structure"] = scl_structure_rule
        
        # 逻辑节点命名规则
        ln_naming_rule = RuleEntity(
            name="Logical_Node_Naming_Rule",
            description="验证逻辑节点命名符合IEC 61850-7-1标准",
            content="逻辑节点名称必须符合标准命名约定，使用4个字符的标准前缀",
            source="IEC 61850-7-1",
            rule_type="semantic",
            severity="warning",
            category="iec61850_compliance",
            condition="定义逻辑节点时",
            action="检查命名约定",
            message_template="逻辑节点命名不规范: {ln_name} 不符合标准约定",
            applicable_standards=["IEC 61850-7-1"],
            fix_suggestions=[
                "使用标准的4字符逻辑节点类名",
                "参考IEC 61850-7-4中的标准逻辑节点定义"
            ],
            confidence=0.9
        )
        self.rules["ln_naming"] = ln_naming_rule
        
        # GOOSE配置规则
        goose_config_rule = RuleEntity(
            name="GOOSE_Configuration_Rule",
            description="验证GOOSE配置的正确性",
            content="GOOSE数据集必须包含有效的数据对象引用，发布间隔应符合性能要求",
            source="IEC 61850-8-1",
            rule_type="business",
            severity="error",
            category="communication",
            condition="配置GOOSE发布时",
            action="验证数据集和时间参数",
            message_template="GOOSE配置错误: {error_details}",
            applicable_standards=["IEC 61850-8-1"],
            applicable_protocols=["GOOSE"],
            fix_suggestions=[
                "检查数据集中的数据对象引用",
                "调整发布间隔参数",
                "验证VLAN配置"
            ],
            confidence=0.9
        )
        self.rules["goose_config"] = goose_config_rule
    
    def _init_technical_requirements(self):
        """初始化技术要求"""
        
        # 通信性能要求
        comm_performance_req = RequirementEntity(
            name="Communication_Performance_Requirement",
            description="IEC 61850通信系统的性能要求",
            content="GOOSE消息传输时间应小于4ms，采样值传输延迟应小于3ms",
            source="IEC 61850-5",
            requirement_type="performance",
            priority="high",
            mandatory=True,
            verification_method="性能测试",
            acceptance_criteria="满足时间要求",
            source_standard="IEC 61850-5",
            clause_reference="性能要求章节",
            confidence=0.95
        )
        self.requirements["comm_performance"] = comm_performance_req
        
        # 网络冗余要求
        network_redundancy_req = RequirementEntity(
            name="Network_Redundancy_Requirement", 
            description="变电站通信网络的冗余要求",
            content="关键通信路径应提供冗余，支持无缝切换",
            source="IEC 61850-5",
            requirement_type="safety",
            priority="high",
            mandatory=True,
            verification_method="冗余测试",
            acceptance_criteria="冗余切换时间小于50ms",
            source_standard="IEC 61850-5",
            clause_reference="网络架构要求",
            confidence=0.9
        )
        self.requirements["network_redundancy"] = network_redundancy_req
    
    def _init_device_knowledge(self):
        """初始化设备知识"""
        
        # 保护IED
        protection_ied = DeviceEntity(
            name="Protection_IED",
            description="继电保护智能电子设备",
            content="实现电力系统保护功能的IED，支持IEC 61850通信",
            source="IEC 61850-7-4",
            device_type="protection",
            specifications={
                "communication_protocols": ["IEC 61850", "GOOSE", "SMV"],
                "logical_nodes": ["PTRC", "PDIF", "PDIR", "PTOC"],
                "performance": {
                    "operating_time": "< 20ms",
                    "reset_time": "< 40ms"
                }
            },
            supported_protocols=["IEC 61850-8-1", "IEC 61850-9-2"],
            supported_functions=["差动保护", "距离保护", "过流保护"],
            confidence=0.9
        )
        self.devices["protection_ied"] = protection_ied
        
        # 测控IED
        control_ied = DeviceEntity(
            name="Control_IED",
            description="测量控制智能电子设备",
            content="实现测量、监视和控制功能的IED",
            source="IEC 61850-7-4",
            device_type="control",
            specifications={
                "communication_protocols": ["IEC 61850", "MMS"],
                "logical_nodes": ["MMXU", "CSWI", "CILO", "XCBR"],
                "accuracy": {
                    "voltage": "0.2%",
                    "current": "0.2%",
                    "power": "0.5%"
                }
            },
            supported_protocols=["IEC 61850-8-1"],
            supported_functions=["遥测", "遥信", "遥控"],
            confidence=0.9
        )
        self.devices["control_ied"] = control_ied
    
    def _init_protocol_knowledge(self):
        """初始化协议知识"""
        
        # GOOSE协议
        goose_protocol = ProtocolEntity(
            name="GOOSE_Protocol",
            description="Generic Object Oriented Substation Event协议",
            content="用于变电站内快速信息交换的协议，支持组播传输",
            source="IEC 61850-8-1",
            protocol_type="communication",
            version="Edition 2.1",
            standard_reference="IEC 61850-8-1",
            specifications={
                "transmission_time": "< 4ms",
                "multicast_support": True,
                "vlan_support": True,
                "max_dataset_size": "1500 bytes"
            },
            message_types=["GOOSE PDU"],
            data_types=["Boolean", "Integer", "Float", "Timestamp"],
            compatible_versions=["Edition 1", "Edition 2", "Edition 2.1"],
            backward_compatible=True,
            confidence=1.0
        )
        self.protocols["goose"] = goose_protocol
        
        # SMV协议
        smv_protocol = ProtocolEntity(
            name="SMV_Protocol",
            description="Sampled Measured Values协议",
            content="用于传输采样值数据的协议，支持高精度同步",
            source="IEC 61850-9-2",
            protocol_type="communication",
            version="Edition 2.1",
            standard_reference="IEC 61850-9-2",
            specifications={
                "sampling_rate": "4000 Hz",
                "synchronization": "IEEE 1588 PTP",
                "transmission_delay": "< 3ms",
                "multicast_support": True
            },
            message_types=["SMV PDU"],
            data_types=["Integer32", "Quality", "Timestamp"],
            compatible_versions=["Edition 2", "Edition 2.1"],
            backward_compatible=True,
            confidence=1.0
        )
        self.protocols["smv"] = smv_protocol
    
    def get_all_standards(self) -> List[StandardEntity]:
        """获取所有标准"""
        return list(self.standards.values())
    
    def get_all_rules(self) -> List[RuleEntity]:
        """获取所有规则"""
        return list(self.rules.values())
    
    def get_all_requirements(self) -> List[RequirementEntity]:
        """获取所有技术要求"""
        return list(self.requirements.values())
    
    def get_all_devices(self) -> List[DeviceEntity]:
        """获取所有设备"""
        return list(self.devices.values())
    
    def get_all_protocols(self) -> List[ProtocolEntity]:
        """获取所有协议"""
        return list(self.protocols.values())
    
    def get_rules_for_standard(self, standard_id: str) -> List[RuleEntity]:
        """获取特定标准的相关规则"""
        rules = []
        for rule in self.rules.values():
            if standard_id in rule.applicable_standards:
                rules.append(rule)
        return rules
    
    def get_applicable_rules(self, device_type: str = None, protocol: str = None) -> List[RuleEntity]:
        """获取适用的规则"""
        applicable_rules = []
        
        for rule in self.rules.values():
            # 检查设备类型
            if device_type and rule.applicable_devices:
                if device_type not in rule.applicable_devices:
                    continue
            
            # 检查协议
            if protocol and rule.applicable_protocols:
                if protocol not in rule.applicable_protocols:
                    continue
            
            applicable_rules.append(rule)
        
        return applicable_rules
