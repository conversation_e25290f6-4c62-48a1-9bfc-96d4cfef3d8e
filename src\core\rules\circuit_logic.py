"""
二次回路逻辑校验规则
基于IEC61850标准和电力系统规程，校验二次回路的逻辑正确性
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .base import BaseRule, RuleContext, RuleResult, RuleSeverity, RuleCategory, ValidationIssue
from .registry import rule_registry


class CircuitElementType(Enum):
    """回路元素类型"""
    IED = "ied"                    # 智能电子设备
    MU = "mu"                      # 合并单元
    SUBNET = "subnet"              # 子网
    DEVICE = "device"              # 一次设备
    LN = "ln"                      # 逻辑节点
    NETWORK = "network"            # 网络
    MONITORING = "monitoring"      # 监控系统


class CircuitConnectionType(Enum):
    """回路连接类型"""
    SMV = "smv"                    # 采样值
    GOOSE = "goose"                # 面向通用对象的变电站事件
    MMS = "mms"                    # 制造消息规范
    TRIP = "trip"                  # 跳闸
    CONTROL = "ctl"                # 控制
    CTL = "control"                # 控制（别名）
    INTERLOCK = "interlock"        # 闭锁/联跳
    PRIMARY = "primary"            # 一次接线


@dataclass
class CircuitNode:
    """回路节点"""
    id: str
    name: str
    type: CircuitElementType
    category: str
    metadata: Dict[str, Any] = None


@dataclass
class CircuitLink:
    """回路连接"""
    source: str
    target: str
    type: CircuitConnectionType
    metadata: Dict[str, Any] = None


@dataclass
class CircuitValidationResult:
    """回路校验结果"""
    is_valid: bool
    issues: List[ValidationIssue]
    invalid_links: List[Dict[str, Any]]  # 包含违规连接信息


class CircuitLogicRule(BaseRule):
    """二次回路逻辑校验规则基类"""
    
    def __init__(self):
        super().__init__(
            rule_id="CIRCUIT_LOGIC_BASE",
            name="二次回路逻辑校验",
            description="校验二次回路的逻辑正确性",
            category=RuleCategory.STANDARD,
            severity=RuleSeverity.ERROR
        )
    
    def is_applicable(self, context: RuleContext) -> bool:
        """检查规则是否适用"""
        if isinstance(context.data, dict):
            return 'nodes' in context.data and 'links' in context.data
        else:
            return hasattr(context.data, 'nodes') and hasattr(context.data, 'links')
    
    def validate(self, context: RuleContext) -> RuleResult:
        """执行校验"""
        result = RuleResult(rule_id=self.rule_id, success=True)
        
        try:
            # 解析回路数据
            circuit_data = self._parse_circuit_data(context.data)
            
            # 执行具体校验逻辑
            validation_result = self._validate_circuit_logic(circuit_data)
            
            # 转换结果
            for issue in validation_result.issues:
                result.add_error(issue.message, issue.path, details=issue.details, suggestion=issue.suggestion)
            
            # 添加违规连接信息到元数据
            result.metadata['invalid_links'] = validation_result.invalid_links
            
        except Exception as e:
            result.add_error(f"回路逻辑校验失败: {str(e)}", context.get_path_string())
        
        return result
    
    def _parse_circuit_data(self, data: Any) -> Dict[str, Any]:
        """解析回路数据"""
        nodes = []
        links = []
        
        # 解析节点
        if isinstance(data, dict):
            node_list = data.get('nodes', [])
            link_list = data.get('links', [])
        else:
            node_list = data.nodes
            link_list = data.links
        
        for node_data in node_list:
            node = CircuitNode(
                id=node_data.get('id'),
                name=node_data.get('name'),
                type=CircuitElementType(node_data.get('type')),
                category=node_data.get('category'),
                metadata=node_data.get('metadata', {})
            )
            nodes.append(node)
        
        # 解析连接
        for link_data in link_list:
            link = CircuitLink(
                source=link_data.get('source'),
                target=link_data.get('target'),
                type=CircuitConnectionType(link_data.get('type')),
                metadata=link_data.get('metadata', {})
            )
            links.append(link)
        
        return {
            'nodes': nodes,
            'links': links,
            'node_map': {node.id: node for node in nodes}
        }
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验回路逻辑（子类实现）"""
        raise NotImplementedError

    def _std_ref(self, std_id: str, reason: str) -> Dict[str, Any]:
        return {
            'standard_reference': std_id,
            'standard_source': '国家/国网二次回路规则参考',
            'source_link': 'https://yuanbao.tencent.com/bot/app/share/chat/jKppzlfOsfqB',
            'reason': reason
        }


class SMVConnectionRule(CircuitLogicRule):
    """SMV连接校验规则"""
    
    def __init__(self):
        super().__init__()
        self.rule_id = "CIRCUIT_SMV_001"
        self.name = "SMV连接校验"
        self.description = "校验SMV连接的逻辑正确性"
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验SMV连接逻辑"""
        issues = []
        invalid_links = []
        node_map = circuit_data['node_map']
        
        for link in circuit_data['links']:
            if link.type != CircuitConnectionType.SMV:
                continue
            
            source_node = node_map.get(link.source)
            target_node = node_map.get(link.target)
            
            if not source_node or not target_node:
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="SMV连接端点不存在",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点: {link.source}, 目标节点: {link.target}",
                    suggestion="检查节点ID是否正确"
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'smv',
                    'invalid': True,
                    'reason': '端点不存在'
                })
                continue
            
            # 校验SMV连接规则
            is_valid = False
            
            # 规则1: MU -> SMV子网
            if (source_node.type == CircuitElementType.IED and 
                source_node.category == 'measurement' and
                target_node.type == CircuitElementType.SUBNET and
                target_node.category == 'smv'):
                is_valid = True
            
            # 规则2: 保护IED -> SMV子网（订阅）
            elif (source_node.type == CircuitElementType.IED and
                  source_node.category in ['protection', 'measurement'] and
                  target_node.type == CircuitElementType.SUBNET and
                  target_node.category == 'smv'):
                is_valid = True
            
            if not is_valid:
                meta = self._std_ref('STD-CIRCUIT-SMV-001', 'SMV仅应从MU/测量类IED指向SMV子网')
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="SMV连接不符合规程要求",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点类型: {source_node.type.value}({source_node.category}), "
                           f"目标节点类型: {target_node.type.value}({target_node.category})",
                    suggestion="SMV连接应从MU或测量类IED指向SMV子网",
                    metadata=meta
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'smv',
                    'invalid': True,
                    'reason': meta['reason'],
                    'standard_reference': meta['standard_reference'],
                    'source_link': meta['source_link']
                })
        
        return CircuitValidationResult(
            is_valid=len(invalid_links) == 0,
            issues=issues,
            invalid_links=invalid_links
        )


class GOOSEConnectionRule(CircuitLogicRule):
    """GOOSE连接校验规则"""
    
    def __init__(self):
        super().__init__()
        self.rule_id = "CIRCUIT_GOOSE_001"
        self.name = "GOOSE连接校验"
        self.description = "校验GOOSE连接的逻辑正确性"
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验GOOSE连接逻辑"""
        issues = []
        invalid_links = []
        node_map = circuit_data['node_map']
        
        for link in circuit_data['links']:
            if link.type != CircuitConnectionType.GOOSE:
                continue
            
            source_node = node_map.get(link.source)
            target_node = node_map.get(link.target)
            
            if not source_node or not target_node:
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="GOOSE连接端点不存在",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点: {link.source}, 目标节点: {link.target}",
                    suggestion="检查节点ID是否正确"
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'goose',
                    'invalid': True,
                    'reason': '端点不存在'
                })
                continue
            
            # 校验GOOSE连接规则
            is_valid = False
            
            # 规则: IED -> GOOSE子网，禁止直达一次设备
            if (source_node.type == CircuitElementType.IED and
                target_node.type == CircuitElementType.SUBNET and
                target_node.category == 'goose'):
                is_valid = True
            
            if not is_valid:
                meta = self._std_ref('STD-CIRCUIT-GOOSE-001', 'GOOSE应由IED发布到GOOSE子网，禁止直达一次设备')
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="GOOSE连接不符合规程要求",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点类型: {source_node.type.value}({source_node.category}), "
                           f"目标节点类型: {target_node.type.value}({target_node.category})",
                    suggestion="GOOSE连接应从IED发布到GOOSE子网，禁止直达一次设备",
                    metadata=meta
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'goose',
                    'invalid': True,
                    'reason': meta['reason'],
                    'standard_reference': meta['standard_reference'],
                    'source_link': meta['source_link']
                })
        
        return CircuitValidationResult(
            is_valid=len(invalid_links) == 0,
            issues=issues,
            invalid_links=invalid_links
        )


class TripConnectionRule(CircuitLogicRule):
    """跳闸连接校验规则"""
    
    def __init__(self):
        super().__init__()
        self.rule_id = "CIRCUIT_TRIP_001"
        self.name = "跳闸连接校验"
        self.description = "校验跳闸连接的逻辑正确性"
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验跳闸连接逻辑"""
        issues = []
        invalid_links = []
        node_map = circuit_data['node_map']
        
        for link in circuit_data['links']:
            if link.type != CircuitConnectionType.TRIP:
                continue
            
            source_node = node_map.get(link.source)
            target_node = node_map.get(link.target)
            
            if not source_node or not target_node:
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="跳闸连接端点不存在",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点: {link.source}, 目标节点: {link.target}",
                    suggestion="检查节点ID是否正确"
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'trip',
                    'invalid': True,
                    'reason': '端点不存在'
                })
                continue
            
            # 校验跳闸连接规则
            is_valid = False
            
            # 规则1: 保护IED -> 控制IED（如BCU），禁止直达一次设备
            if (source_node.type == CircuitElementType.IED and
                source_node.category in ['protection'] and
                target_node.type == CircuitElementType.IED and
                target_node.category == 'control'):
                is_valid = True
            
            # 规则2: 联跳装置 -> 控制IED
            elif (source_node.type == CircuitElementType.IED and
                  source_node.category == 'control' and
                  'TPL' in source_node.id and
                  target_node.type == CircuitElementType.IED and
                  target_node.category == 'control'):
                is_valid = True
            
            if not is_valid:
                meta = self._std_ref('STD-CIRCUIT-TRIP-001', '跳闸应由保护/联跳发往BCU等控制IED，禁止直达一次设备')
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="跳闸连接不符合规程要求",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点类型: {source_node.type.value}({source_node.category}), "
                           f"目标节点类型: {target_node.type.value}({target_node.category})",
                    suggestion="跳闸信号应由保护/联跳装置发往BCU等控制IED，禁止直达一次设备",
                    metadata=meta
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'trip',
                    'invalid': True,
                    'reason': meta['reason'],
                    'standard_reference': meta['standard_reference'],
                    'source_link': meta['source_link']
                })
        
        return CircuitValidationResult(
            is_valid=len(invalid_links) == 0,
            issues=issues,
            invalid_links=invalid_links
        )


class ControlConnectionRule(CircuitLogicRule):
    """控制连接校验规则"""
    
    def __init__(self):
        super().__init__()
        self.rule_id = "CIRCUIT_CONTROL_001"
        self.name = "控制连接校验"
        self.description = "校验控制连接的逻辑正确性"
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验控制连接逻辑"""
        issues = []
        invalid_links = []
        node_map = circuit_data['node_map']
        
        for link in circuit_data['links']:
            if link.type != CircuitConnectionType.CONTROL:
                continue
            
            source_node = node_map.get(link.source)
            target_node = node_map.get(link.target)
            
            if not source_node or not target_node:
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="控制连接端点不存在",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点: {link.source}, 目标节点: {link.target}",
                    suggestion="检查节点ID是否正确"
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'ctl',
                    'invalid': True,
                    'reason': '端点不存在'
                })
                continue
            
            # 校验控制连接规则
            is_valid = False
            
            # 规则: 控制IED -> 一次设备（断路器/刀闸）
            if (source_node.type == CircuitElementType.IED and
                source_node.category == 'control' and
                target_node.type == CircuitElementType.DEVICE):
                is_valid = True
            
            if not is_valid:
                meta = self._std_ref('STD-CIRCUIT-CTL-001', '控制命令应由控制IED发往一次设备')
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="控制连接不符合规程要求",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点类型: {source_node.type.value}({source_node.category}), "
                           f"目标节点类型: {target_node.type.value}({target_node.category})",
                    suggestion="控制命令应由控制IED发往一次设备",
                    metadata=meta
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'ctl',
                    'invalid': True,
                    'reason': meta['reason'],
                    'standard_reference': meta['standard_reference'],
                    'source_link': meta['source_link']
                })
        
        return CircuitValidationResult(
            is_valid=len(invalid_links) == 0,
            issues=issues,
            invalid_links=invalid_links
        )


class InterlockConnectionRule(CircuitLogicRule):
    """闭锁/联跳连接校验规则"""
    
    def __init__(self):
        super().__init__()
        self.rule_id = "CIRCUIT_INTERLOCK_001"
        self.name = "闭锁/联跳连接校验"
        self.description = "校验闭锁/联跳连接的逻辑正确性"
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验闭锁/联跳连接逻辑"""
        issues = []
        invalid_links = []
        node_map = circuit_data['node_map']
        
        for link in circuit_data['links']:
            if link.type != CircuitConnectionType.INTERLOCK:
                continue
            
            source_node = node_map.get(link.source)
            target_node = node_map.get(link.target)
            
            if not source_node or not target_node:
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="闭锁/联跳连接端点不存在",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点: {link.source}, 目标节点: {link.target}",
                    suggestion="检查节点ID是否正确"
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'interlock',
                    'invalid': True,
                    'reason': '端点不存在'
                })
                continue
            
            # 校验闭锁/联跳连接规则
            is_valid = False
            
            # 规则1: 断路器失灵/保护 -> 联跳/BCU，不能直达一次设备
            if (source_node.type == CircuitElementType.IED and
                source_node.category in ['protection'] and
                target_node.type == CircuitElementType.IED):
                is_valid = True
            
            # 规则2: 断路器失灵装置 -> 联跳装置
            elif (source_node.type == CircuitElementType.IED and
                  'BF' in source_node.id and
                  target_node.type == CircuitElementType.IED):
                is_valid = True
            
            if not is_valid:
                meta = self._std_ref('STD-CIRCUIT-INTERLOCK-001', '闭锁/联跳应来自失灵/保护，送往联跳/BCU，禁止直达一次设备')
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="闭锁/联跳连接不符合规程要求",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点类型: {source_node.type.value}({source_node.category}), "
                           f"目标节点类型: {target_node.type.value}({target_node.category})",
                    suggestion="闭锁/联跳信号应来自失灵/保护装置，送往联跳/BCU，禁止直达一次设备",
                    metadata=meta
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'interlock',
                    'invalid': True,
                    'reason': meta['reason'],
                    'standard_reference': meta['standard_reference'],
                    'source_link': meta['source_link']
                })
        
        return CircuitValidationResult(
            is_valid=len(invalid_links) == 0,
            issues=issues,
            invalid_links=invalid_links
        )


class PrimaryConnectionRule(CircuitLogicRule):
    """一次连接校验规则"""
    
    def __init__(self):
        super().__init__()
        self.rule_id = "CIRCUIT_PRIMARY_001"
        self.name = "一次连接校验"
        self.description = "校验一次连接的逻辑正确性"
    
    def _validate_circuit_logic(self, circuit_data: Dict[str, Any]) -> CircuitValidationResult:
        """校验一次连接逻辑"""
        issues = []
        invalid_links = []
        node_map = circuit_data['node_map']
        
        for link in circuit_data['links']:
            if link.type != CircuitConnectionType.PRIMARY:
                continue
            
            source_node = node_map.get(link.source)
            target_node = node_map.get(link.target)
            
            if not source_node or not target_node:
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="一次连接端点不存在",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点: {link.source}, 目标节点: {link.target}",
                    suggestion="检查节点ID是否正确"
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'primary',
                    'invalid': True,
                    'reason': '端点不存在'
                })
                continue
            
            # 校验一次连接规则
            is_valid = False
            
            # 规则: 一次设备 -> 一次设备
            if (source_node.type == CircuitElementType.DEVICE and
                target_node.type == CircuitElementType.DEVICE):
                is_valid = True
            
            if not is_valid:
                meta = self._std_ref('STD-CIRCUIT-PRIMARY-001', '一次接线应限定在一次设备之间')
                issues.append(ValidationIssue(
                    rule_id=self.rule_id,
                    severity=RuleSeverity.ERROR,
                    message="一次连接不符合规程要求",
                    path=f"links.{link.source}->{link.target}",
                    details=f"源节点类型: {source_node.type.value}({source_node.category}), "
                           f"目标节点类型: {target_node.type.value}({target_node.category})",
                    suggestion="一次接线应限定在一次设备之间",
                    metadata=meta
                ))
                invalid_links.append({
                    'source': link.source,
                    'target': link.target,
                    'type': 'primary',
                    'invalid': True,
                    'reason': meta['reason'],
                    'standard_reference': meta['standard_reference'],
                    'source_link': meta['source_link']
                })
        
        return CircuitValidationResult(
            is_valid=len(invalid_links) == 0,
            issues=issues,
            invalid_links=invalid_links
        )


# 注册所有回路逻辑校验规则
def register_circuit_logic_rules():
    """注册回路逻辑校验规则"""
    rules = [
        SMVConnectionRule(),
        GOOSEConnectionRule(),
        TripConnectionRule(),
        ControlConnectionRule(),
        InterlockConnectionRule(),
        PrimaryConnectionRule()
    ]
    
    for rule in rules:
        # 检查规则是否已存在
        existing_rule = rule_registry.get_rule(rule.rule_id)
        if existing_rule is None:
            rule_registry.register(rule)
    
    return rules
