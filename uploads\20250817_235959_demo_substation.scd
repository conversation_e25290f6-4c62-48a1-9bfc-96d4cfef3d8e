<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" version="2007" revision="B" release="4">
  <Header id="DemoSubstation" version="1.0" revision="A" toolID="IEC61850DesignChecker" nameStructure="IEDName">
    <Text>某220kV智能变电站系统配置描述文件 - 演示项目</Text>
    <History>
      <Hitem version="1.0" revision="A" when="2024-08-17T15:30:00Z" who="设计工程师" what="初始版本" why="新建变电站设计"/>
    </History>
  </Header>
  
  <Substation name="DemoSubstation220kV" desc="某220kV智能变电站">
    <VoltageLevel name="220kV" desc="220kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">220</Voltage>
      <Bay name="220kV_Line1" desc="220kV出线1">
        <ConductingEquipment name="QF1" type="CBR" desc="220kV出线1断路器">
          <Terminal name="T1" connectivityNode="DemoSubstation220kV/220kV/L1" substationName="DemoSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line1" cNodeName="L1"/>
        </ConductingEquipment>
        <ConductingEquipment name="QS1" type="DIS" desc="220kV出线1隔离开关">
          <Terminal name="T1" connectivityNode="DemoSubstation220kV/220kV/L1" substationName="DemoSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line1" cNodeName="L1"/>
        </ConductingEquipment>
      </Bay>
      <Bay name="220kV_Line2" desc="220kV出线2">
        <ConductingEquipment name="QF2" type="CBR" desc="220kV出线2断路器">
          <Terminal name="T1" connectivityNode="DemoSubstation220kV/220kV/L2" substationName="DemoSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line2" cNodeName="L2"/>
        </ConductingEquipment>
      </Bay>
    </VoltageLevel>
    
    <VoltageLevel name="110kV" desc="110kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">110</Voltage>
      <Bay name="110kV_Line1" desc="110kV出线1">
        <ConductingEquipment name="QF3" type="CBR" desc="110kV出线1断路器">
          <Terminal name="T1" connectivityNode="DemoSubstation220kV/110kV/L1" substationName="DemoSubstation220kV" voltageLevelName="110kV" bayName="110kV_Line1" cNodeName="L1"/>
        </ConductingEquipment>
      </Bay>
    </VoltageLevel>
  </Substation>
  
  <Communication>
    <SubNetwork name="StationBus" desc="站控层网络" type="8-MMS">
      <BitRate unit="b/s" multiplier="M">100</BitRate>
      <ConnectedAP iedName="ProtectionIED1" apName="S1">
        <Address>
          <P type="IP">************</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
        </Address>
      </ConnectedAP>
      <ConnectedAP iedName="ProtectionIED2" apName="S1">
        <Address>
          <P type="IP">***********1</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
        </Address>
      </ConnectedAP>
      <ConnectedAP iedName="MeasurementIED1" apName="S1">
        <Address>
          <P type="IP">************</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
        </Address>
      </ConnectedAP>
    </SubNetwork>
    
    <SubNetwork name="ProcessBus" desc="过程层网络" type="8-MMS">
      <BitRate unit="b/s" multiplier="M">100</BitRate>
      <ConnectedAP iedName="MergeUnit1" apName="S1">
        <Address>
          <P type="IP">***********0</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
        </Address>
      </ConnectedAP>
    </SubNetwork>
  </Communication>
  
  <IED name="ProtectionIED1" desc="220kV线路保护装置1" type="Protection" manufacturer="某厂商" configVersion="1.0">
    <AccessPoint name="S1" desc="站控层接入点">
      <Server desc="服务器配置">
        <Authentication none="true"/>
        <LDevice inst="PROT" desc="保护逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type">
            <DataSet name="Events" desc="事件数据集">
              <FCDA ldInst="PROT" prefix="" lnClass="PTRC" lnInst="1" doName="Str" fc="ST"/>
              <FCDA ldInst="PROT" prefix="" lnClass="PTRC" lnInst="1" doName="Op" fc="ST"/>
            </DataSet>
            <ReportControl name="EventsRpt" desc="事件报告" datSet="Events" rptID="Events01" buffered="true" bufTime="50" intgPd="1000">
              <TrgOps period="true" gi="true" dchg="true" qchg="true"/>
              <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="true" entryID="true" configRef="true"/>
              <RptEnabled max="5"/>
            </ReportControl>
          </LN0>
          <LN lnClass="PTRC" inst="1" lnType="PTRC_Type" desc="距离保护">
            <DOI name="Str" desc="启动">
              <DAI name="stVal">
                <Val>false</Val>
              </DAI>
            </DOI>
            <DOI name="Op" desc="动作">
              <DAI name="stVal">
                <Val>false</Val>
              </DAI>
            </DOI>
          </LN>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  
  <IED name="ProtectionIED2" desc="220kV线路保护装置2" type="Protection" manufacturer="某厂商" configVersion="1.0">
    <AccessPoint name="S1" desc="站控层接入点">
      <Server desc="服务器配置">
        <Authentication none="true"/>
        <LDevice inst="PROT" desc="保护逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
          <LN lnClass="PTRC" inst="1" lnType="PTRC_Type" desc="距离保护"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  
  <IED name="MeasurementIED1" desc="测量装置1" type="Measurement" manufacturer="某厂商" configVersion="1.0">
    <AccessPoint name="S1" desc="站控层接入点">
      <Server desc="服务器配置">
        <Authentication none="true"/>
        <LDevice inst="MEAS" desc="测量逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
          <LN lnClass="MMXU" inst="1" lnType="MMXU_Type" desc="测量单元"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  
  <IED name="MergeUnit1" desc="合并单元1" type="MergeUnit" manufacturer="某厂商" configVersion="1.0">
    <AccessPoint name="S1" desc="过程层接入点">
      <Server desc="服务器配置">
        <Authentication none="true"/>
        <LDevice inst="MU" desc="合并单元逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
          <LN lnClass="TCTR" inst="1" lnType="TCTR_Type" desc="电流互感器"/>
          <LN lnClass="TVTR" inst="1" lnType="TVTR_Type" desc="电压互感器"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  
  <DataTypeTemplates>
    <LNodeType id="LLN0_Type" lnClass="LLN0">
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
      <DO name="NamPlt" type="LPL_NamPlt"/>
    </LNodeType>
    
    <LNodeType id="PTRC_Type" lnClass="PTRC">
      <DO name="Str" type="ACD_Str"/>
      <DO name="Op" type="ACT_Op"/>
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
      <DO name="NamPlt" type="LPL_NamPlt"/>
    </LNodeType>
    
    <LNodeType id="MMXU_Type" lnClass="MMXU">
      <DO name="A" type="WYE_A"/>
      <DO name="PhV" type="WYE_PhV"/>
      <DO name="Hz" type="MV_Hz"/>
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
      <DO name="NamPlt" type="LPL_NamPlt"/>
    </LNodeType>
    
    <LNodeType id="TCTR_Type" lnClass="TCTR">
      <DO name="Amp" type="SAV_Amp"/>
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
      <DO name="NamPlt" type="LPL_NamPlt"/>
    </LNodeType>
    
    <LNodeType id="TVTR_Type" lnClass="TVTR">
      <DO name="Vol" type="SAV_Vol"/>
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
      <DO name="NamPlt" type="LPL_NamPlt"/>
    </LNodeType>
    
    <DOType id="ENC_Mod" cdc="ENC">
      <DA name="stVal" fc="ST" dchg="true" bType="Enum" type="Mod"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="ENS_Beh" cdc="ENS">
      <DA name="stVal" fc="ST" dchg="true" bType="Enum" type="Beh"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="ENS_Health" cdc="ENS">
      <DA name="stVal" fc="ST" dchg="true" bType="Enum" type="Health"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="LPL_NamPlt" cdc="LPL">
      <DA name="vendor" fc="DC" bType="VisString255"/>
      <DA name="swRev" fc="DC" bType="VisString255"/>
      <DA name="d" fc="DC" bType="VisString255"/>
    </DOType>
    
    <DOType id="ACD_Str" cdc="ACD">
      <DA name="general" fc="ST" dchg="true" bType="BOOLEAN"/>
      <DA name="dirGeneral" fc="ST" dchg="true" bType="Enum" type="Dir"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="ACT_Op" cdc="ACT">
      <DA name="general" fc="ST" dchg="true" bType="BOOLEAN"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="WYE_A" cdc="WYE">
      <SDO name="phsA" type="CMV_A"/>
      <SDO name="phsB" type="CMV_A"/>
      <SDO name="phsC" type="CMV_A"/>
    </DOType>
    
    <DOType id="WYE_PhV" cdc="WYE">
      <SDO name="phsA" type="CMV_PhV"/>
      <SDO name="phsB" type="CMV_PhV"/>
      <SDO name="phsC" type="CMV_PhV"/>
    </DOType>
    
    <DOType id="MV_Hz" cdc="MV">
      <DA name="mag" fc="MX" dchg="true" bType="Struct" type="AnalogueValue"/>
      <DA name="q" fc="MX" qchg="true" bType="Quality"/>
      <DA name="t" fc="MX" bType="Timestamp"/>
    </DOType>
    
    <DOType id="SAV_Amp" cdc="SAV">
      <DA name="instMag" fc="MX" bType="Struct" type="AnalogueValue"/>
      <DA name="q" fc="MX" qchg="true" bType="Quality"/>
      <DA name="t" fc="MX" bType="Timestamp"/>
    </DOType>
    
    <DOType id="SAV_Vol" cdc="SAV">
      <DA name="instMag" fc="MX" bType="Struct" type="AnalogueValue"/>
      <DA name="q" fc="MX" qchg="true" bType="Quality"/>
      <DA name="t" fc="MX" bType="Timestamp"/>
    </DOType>
    
    <DOType id="CMV_A" cdc="CMV">
      <DA name="cVal" fc="MX" dchg="true" bType="Struct" type="Vector"/>
      <DA name="q" fc="MX" qchg="true" bType="Quality"/>
      <DA name="t" fc="MX" bType="Timestamp"/>
    </DOType>
    
    <DOType id="CMV_PhV" cdc="CMV">
      <DA name="cVal" fc="MX" dchg="true" bType="Struct" type="Vector"/>
      <DA name="q" fc="MX" qchg="true" bType="Quality"/>
      <DA name="t" fc="MX" bType="Timestamp"/>
    </DOType>
    
    <DAType id="AnalogueValue">
      <BDA name="f" bType="FLOAT32"/>
    </DAType>
    
    <DAType id="Vector">
      <BDA name="mag" bType="Struct" type="AnalogueValue"/>
      <BDA name="ang" bType="Struct" type="AnalogueValue"/>
    </DAType>
    
    <EnumType id="Mod">
      <EnumVal ord="1">on</EnumVal>
      <EnumVal ord="2">blocked</EnumVal>
      <EnumVal ord="3">test</EnumVal>
      <EnumVal ord="4">test/blocked</EnumVal>
      <EnumVal ord="5">off</EnumVal>
    </EnumType>
    
    <EnumType id="Beh">
      <EnumVal ord="1">on</EnumVal>
      <EnumVal ord="2">blocked</EnumVal>
      <EnumVal ord="3">test</EnumVal>
      <EnumVal ord="4">test/blocked</EnumVal>
      <EnumVal ord="5">off</EnumVal>
    </EnumType>
    
    <EnumType id="Health">
      <EnumVal ord="1">Ok</EnumVal>
      <EnumVal ord="2">Warning</EnumVal>
      <EnumVal ord="3">Alarm</EnumVal>
    </EnumType>
    
    <EnumType id="Dir">
      <EnumVal ord="0">unknown</EnumVal>
      <EnumVal ord="1">forward</EnumVal>
      <EnumVal ord="2">backward</EnumVal>
      <EnumVal ord="3">both</EnumVal>
    </EnumType>
  </DataTypeTemplates>
</SCL>
