"""
WebSocket处理器
提供实时的验证进度和结果推送
"""

import logging
import json
from typing import Dict, Any, List
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from datetime import datetime

logger = logging.getLogger(__name__)


class WebSocketHandler:
    """WebSocket处理器"""
    
    def __init__(self, socketio: SocketIO):
        """初始化WebSocket处理器"""
        self.socketio = socketio
        self.active_sessions = {}  # session_id -> client_info
        self.validation_progress = {}  # task_id -> progress_info
        
        # 注册事件处理器
        self._register_handlers()
        
        logger.info("WebSocket处理器初始化完成")
    
    def _register_handlers(self):
        """注册事件处理器"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """客户端连接"""
            session_id = self._get_session_id()
            self.active_sessions[session_id] = {
                'connected_at': datetime.now(),
                'rooms': []
            }
            
            emit('connected', {
                'session_id': session_id,
                'timestamp': datetime.now().isoformat(),
                'message': '连接成功'
            })
            
            logger.info(f"客户端连接: {session_id}")
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """客户端断开连接"""
            session_id = self._get_session_id()
            
            if session_id in self.active_sessions:
                # 离开所有房间
                for room in self.active_sessions[session_id]['rooms']:
                    leave_room(room)
                
                del self.active_sessions[session_id]
            
            logger.info(f"客户端断开连接: {session_id}")
        
        @self.socketio.on('join_validation')
        def handle_join_validation(data):
            """加入验证任务房间"""
            task_id = data.get('task_id')
            if not task_id:
                emit('error', {'message': '缺少task_id参数'})
                return
            
            session_id = self._get_session_id()
            room_name = f"validation_{task_id}"
            
            join_room(room_name)
            
            if session_id in self.active_sessions:
                self.active_sessions[session_id]['rooms'].append(room_name)
            
            emit('joined_validation', {
                'task_id': task_id,
                'room': room_name,
                'message': f'已加入验证任务 {task_id}'
            })
            
            # 如果任务已有进度信息，立即发送
            if task_id in self.validation_progress:
                emit('validation_progress', self.validation_progress[task_id])
        
        @self.socketio.on('leave_validation')
        def handle_leave_validation(data):
            """离开验证任务房间"""
            task_id = data.get('task_id')
            if not task_id:
                emit('error', {'message': '缺少task_id参数'})
                return
            
            session_id = self._get_session_id()
            room_name = f"validation_{task_id}"
            
            leave_room(room_name)
            
            if session_id in self.active_sessions:
                if room_name in self.active_sessions[session_id]['rooms']:
                    self.active_sessions[session_id]['rooms'].remove(room_name)
            
            emit('left_validation', {
                'task_id': task_id,
                'message': f'已离开验证任务 {task_id}'
            })
        
        @self.socketio.on('get_validation_status')
        def handle_get_validation_status(data):
            """获取验证状态"""
            task_id = data.get('task_id')
            if not task_id:
                emit('error', {'message': '缺少task_id参数'})
                return
            
            if task_id in self.validation_progress:
                emit('validation_status', self.validation_progress[task_id])
            else:
                emit('validation_status', {
                    'task_id': task_id,
                    'status': 'not_found',
                    'message': '未找到验证任务'
                })
    
    def broadcast_validation_progress(self, task_id: str, progress_data: Dict[str, Any]):
        """广播验证进度"""
        try:
            # 更新进度缓存
            self.validation_progress[task_id] = {
                'task_id': task_id,
                'timestamp': datetime.now().isoformat(),
                **progress_data
            }
            
            # 广播到对应房间
            room_name = f"validation_{task_id}"
            self.socketio.emit('validation_progress', 
                             self.validation_progress[task_id], 
                             room=room_name)
            
            logger.debug(f"广播验证进度: {task_id} - {progress_data.get('status', 'unknown')}")
            
        except Exception as e:
            logger.error(f"广播验证进度失败: {e}")
    
    def broadcast_validation_complete(self, task_id: str, result_data: Dict[str, Any]):
        """广播验证完成"""
        try:
            complete_data = {
                'task_id': task_id,
                'timestamp': datetime.now().isoformat(),
                'status': 'completed',
                **result_data
            }
            
            # 更新进度缓存
            self.validation_progress[task_id] = complete_data
            
            # 广播到对应房间
            room_name = f"validation_{task_id}"
            self.socketio.emit('validation_complete', complete_data, room=room_name)
            
            logger.info(f"广播验证完成: {task_id}")
            
        except Exception as e:
            logger.error(f"广播验证完成失败: {e}")
    
    def broadcast_validation_error(self, task_id: str, error_data: Dict[str, Any]):
        """广播验证错误"""
        try:
            error_info = {
                'task_id': task_id,
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                **error_data
            }
            
            # 更新进度缓存
            self.validation_progress[task_id] = error_info
            
            # 广播到对应房间
            room_name = f"validation_{task_id}"
            self.socketio.emit('validation_error', error_info, room=room_name)
            
            logger.error(f"广播验证错误: {task_id} - {error_data.get('message', 'Unknown error')}")
            
        except Exception as e:
            logger.error(f"广播验证错误失败: {e}")
    
    def broadcast_system_notification(self, notification: Dict[str, Any]):
        """广播系统通知"""
        try:
            notification_data = {
                'timestamp': datetime.now().isoformat(),
                'type': 'system_notification',
                **notification
            }
            
            # 广播给所有连接的客户端
            self.socketio.emit('system_notification', notification_data)
            
            logger.info(f"广播系统通知: {notification.get('message', 'No message')}")
            
        except Exception as e:
            logger.error(f"广播系统通知失败: {e}")
    
    def get_active_sessions_count(self) -> int:
        """获取活跃会话数量"""
        return len(self.active_sessions)
    
    def get_validation_tasks_count(self) -> int:
        """获取活跃验证任务数量"""
        return len(self.validation_progress)
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的任务"""
        try:
            current_time = datetime.now()
            tasks_to_remove = []
            
            for task_id, progress in self.validation_progress.items():
                if progress.get('status') in ['completed', 'error']:
                    task_time = datetime.fromisoformat(progress['timestamp'])
                    age_hours = (current_time - task_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self.validation_progress[task_id]
                logger.debug(f"清理已完成任务: {task_id}")
            
            if tasks_to_remove:
                logger.info(f"清理了 {len(tasks_to_remove)} 个已完成任务")
            
        except Exception as e:
            logger.error(f"清理已完成任务失败: {e}")
    
    def _get_session_id(self) -> str:
        """获取会话ID"""
        from flask import request
        return request.sid if hasattr(request, 'sid') else 'unknown'
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            status_counts = {}
            for progress in self.validation_progress.values():
                status = progress.get('status', 'unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                'active_sessions': len(self.active_sessions),
                'validation_tasks': len(self.validation_progress),
                'task_status_distribution': status_counts,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}


class ValidationProgressTracker:
    """验证进度跟踪器"""
    
    def __init__(self, websocket_handler: WebSocketHandler, task_id: str):
        """初始化进度跟踪器"""
        self.websocket_handler = websocket_handler
        self.task_id = task_id
        self.current_step = 0
        self.total_steps = 0
        self.step_details = []
    
    def start(self, total_steps: int, description: str = "开始验证"):
        """开始跟踪"""
        self.total_steps = total_steps
        self.current_step = 0
        
        self.websocket_handler.broadcast_validation_progress(self.task_id, {
            'status': 'started',
            'progress': 0,
            'current_step': 0,
            'total_steps': total_steps,
            'description': description,
            'step_details': []
        })
    
    def update_step(self, step_name: str, description: str = "", status: str = "running"):
        """更新步骤"""
        self.current_step += 1
        
        step_detail = {
            'step': self.current_step,
            'name': step_name,
            'description': description,
            'status': status,
            'timestamp': datetime.now().isoformat()
        }
        
        self.step_details.append(step_detail)
        
        progress = (self.current_step / self.total_steps) * 100 if self.total_steps > 0 else 0
        
        self.websocket_handler.broadcast_validation_progress(self.task_id, {
            'status': 'running',
            'progress': progress,
            'current_step': self.current_step,
            'total_steps': self.total_steps,
            'description': f"正在执行: {step_name}",
            'step_details': self.step_details,
            'current_step_detail': step_detail
        })
    
    def complete(self, result_summary: Dict[str, Any]):
        """完成跟踪"""
        self.websocket_handler.broadcast_validation_complete(self.task_id, {
            'progress': 100,
            'current_step': self.total_steps,
            'total_steps': self.total_steps,
            'description': "验证完成",
            'step_details': self.step_details,
            'result_summary': result_summary
        })
    
    def error(self, error_message: str, error_details: Dict[str, Any] = None):
        """错误跟踪"""
        self.websocket_handler.broadcast_validation_error(self.task_id, {
            'progress': (self.current_step / self.total_steps) * 100 if self.total_steps > 0 else 0,
            'current_step': self.current_step,
            'total_steps': self.total_steps,
            'description': f"验证失败: {error_message}",
            'step_details': self.step_details,
            'error_message': error_message,
            'error_details': error_details or {}
        })
