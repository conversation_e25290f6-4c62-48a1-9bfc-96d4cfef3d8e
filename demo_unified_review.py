#!/usr/bin/env python3
"""
统一审查功能演示脚本
模拟某220kV智能变电站二次设计审查项目
"""

import sys
import os
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_review_engine import UnifiedReviewEngine

def print_banner():
    """打印演示横幅"""
    print("=" * 80)
    print("🏭 某220kV智能变电站二次设计审查演示")
    print("=" * 80)
    print("📋 项目概况:")
    print("   • 项目名称: 某220kV智能变电站")
    print("   • 电压等级: 220kV/110kV")
    print("   • 审查内容: IEC61850配置文件 + 二次图纸")
    print("   • 审查工具: 统一审查引擎")
    print("=" * 80)

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*20} {title} {'='*20}")

def print_subsection(title):
    """打印子章节标题"""
    print(f"\n--- {title} ---")

def format_issue(issue, index):
    """格式化问题显示"""
    severity_icons = {
        'CRITICAL': '🔴',
        'ERROR': '🟠',
        'WARNING': '🟡',
        'INFO': '🔵'
    }

    icon = severity_icons.get(issue.severity.upper(), '⚪')

    print(f"  {index}. {icon} [{issue.severity.upper()}] {issue.title}")
    if issue.description:
        print(f"     📝 描述: {issue.description}")
    if issue.location:
        print(f"     📍 位置: {issue.location}")
    if issue.suggestion:
        print(f"     💡 建议: {issue.suggestion}")
    if issue.rule_id:
        print(f"     📋 规则: {issue.rule_id}")
    print()

def demo_unified_review():
    """演示统一审查功能"""
    
    print_banner()
    
    try:
        print_section("🚀 初始化统一审查引擎")
        
        # 初始化统一审查引擎
        engine = UnifiedReviewEngine()
        print("✅ 统一审查引擎初始化成功")
        
        # 显示支持的格式
        formats = engine.get_supported_formats()
        print(f"✅ 支持的文件格式: {', '.join(formats)}")
        
        # 显示可用的检查分类
        categories = engine.get_available_categories()
        print(f"✅ 可用的检查分类: {len(categories)} 个")
        for category, rules in categories.items():
            print(f"   • {category}: {len(rules)} 条规则")
        
        print_section("📁 准备测试文件")
        
        # 测试文件列表
        test_files = [
            "test_project/demo_substation.scd",
            "test_project/demo_drawing.dxf"
        ]
        
        # 检查文件是否存在
        existing_files = []
        for file_path in test_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path) / 1024  # KB
                file_type = engine._detect_file_type(file_path)
                print(f"✅ {file_path} ({file_size:.1f} KB, 类型: {file_type})")
                existing_files.append(file_path)
            else:
                print(f"❌ {file_path} (文件不存在)")
        
        if not existing_files:
            print("❌ 没有找到测试文件，请先创建测试文件")
            return
        
        print_section("🔍 执行统一审查")
        
        # 逐个审查文件
        all_results = []
        
        for file_path in existing_files:
            print_subsection(f"审查文件: {file_path}")
            
            start_time = time.time()
            
            try:
                # 执行审查
                result = engine.review_file(file_path)
                
                end_time = time.time()
                duration = end_time - start_time
                
                print(f"✅ 审查完成 (耗时: {duration:.2f}秒)")
                print(f"📊 发现问题: {len(result.issues)} 个")
                print(f"📈 合规性评分: {result.compliance_score:.1f}/100")
                
                # 按严重程度统计
                severity_counts = {}
                for issue in result.issues:
                    severity_counts[issue.severity] = severity_counts.get(issue.severity, 0) + 1
                
                if severity_counts:
                    print("📋 问题分布:")
                    for severity, count in severity_counts.items():
                        print(f"   • {severity}: {count} 个")
                
                all_results.append((file_path, result))
                
            except Exception as e:
                print(f"❌ 审查失败: {e}")
                continue
        
        print_section("📊 综合审查结果")
        
        if not all_results:
            print("❌ 没有成功的审查结果")
            return
        
        # 合并所有结果
        print_subsection("批量审查结果")

        try:
            file_paths = [file_path for file_path, _ in all_results]
            batch_results = engine.review_multiple_files(file_paths)

            print(f"✅ 批量审查完成")
            print(f"📁 审查文件数: {len(file_paths)}")
            print(f"📊 成功审查: {len(batch_results)} 个文件")

            # 统计所有问题
            all_issues = []
            total_score = 0
            for result in batch_results:
                all_issues.extend(result.issues)
                total_score += result.compliance_score

            avg_score = total_score / len(batch_results) if batch_results else 0

            print(f"📊 总问题数: {len(all_issues)}")
            print(f"📈 平均合规性评分: {avg_score:.1f}/100")

            # 按来源分类显示问题
            config_issues = [issue for issue in all_issues if issue.source_type == 'config']
            drawing_issues = [issue for issue in all_issues if issue.source_type == 'drawing']

            print(f"\n📋 问题分类:")
            print(f"   • 配置文件问题: {len(config_issues)} 个")
            print(f"   • 图纸文件问题: {len(drawing_issues)} 个")

        except Exception as e:
            print(f"❌ 批量审查失败: {e}")
            import traceback
            traceback.print_exc()
        
        print_section("🔍 详细问题分析")
        
        # 显示每个文件的详细问题
        for file_path, result in all_results:
            if result.issues:
                print_subsection(f"文件: {os.path.basename(file_path)}")
                
                for i, issue in enumerate(result.issues[:5], 1):  # 只显示前5个问题
                    format_issue(issue, i)
                
                if len(result.issues) > 5:
                    print(f"   ... 还有 {len(result.issues) - 5} 个问题")
            else:
                print_subsection(f"文件: {os.path.basename(file_path)}")
                print("✅ 未发现问题，完全符合规范")
        
        print_section("📄 生成审查报告")
        
        # 生成报告
        try:
            if all_results:
                # 使用第一个结果生成报告演示
                file_path, result = all_results[0]
                
                # 生成JSON报告
                json_report = engine.generate_unified_report(result, 'json')
                print("✅ JSON格式报告生成成功")
                
                # 生成HTML报告
                html_report = engine.generate_unified_report(result, 'html')
                print("✅ HTML格式报告生成成功")
                
                # 保存报告文件
                report_dir = Path("reports")
                report_dir.mkdir(exist_ok=True)
                
                # 保存JSON报告
                json_file = report_dir / f"demo_review_{int(time.time())}.json"
                with open(json_file, 'w', encoding='utf-8') as f:
                    f.write(json_report)
                print(f"📁 JSON报告已保存: {json_file}")
                
                # 保存HTML报告
                html_file = report_dir / f"demo_review_{int(time.time())}.html"
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(html_report)
                print(f"📁 HTML报告已保存: {html_file}")
                
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
        
        print_section("🎯 演示总结")
        
        print("✅ 统一审查功能演示完成！")
        print("\n🌟 核心特性验证:")
        print("   ✅ 智能文件类型识别")
        print("   ✅ 统一的审查接口")
        print("   ✅ 分类问题显示")
        print("   ✅ 批量文件处理")
        print("   ✅ 多格式报告生成")
        print("   ✅ 合规性评分计算")
        
        print("\n💡 实际应用价值:")
        print("   • 一站式解决配置文件和图纸审查需求")
        print("   • 避免重复代码，提高开发效率")
        print("   • 统一的用户体验和操作流程")
        print("   • 智能化的文件识别和处理")
        print("   • 标准化的问题分类和报告格式")
        
        print("\n🚀 Web界面访问:")
        print("   • 统一审查页面: http://localhost:5000/unified-review")
        print("   • 使用指南: http://localhost:5000/help")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_unified_review()
