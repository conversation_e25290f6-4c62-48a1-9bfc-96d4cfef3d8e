#!/usr/bin/env python3
"""
系统清理和安全配置工具
用于清理冗余文件，提升系统安全性
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SystemCleanupAndSecurity:
    """系统清理和安全配置"""
    
    def __init__(self):
        """初始化"""
        self.project_root = Path.cwd()
        self.cleanup_stats = {
            'files_removed': 0,
            'dirs_removed': 0,
            'space_saved': 0
        }
    
    def analyze_system(self) -> Dict[str, Any]:
        """分析系统状态"""
        logger.info("开始分析系统状态...")
        
        analysis = {
            'total_files': 0,
            'total_size': 0,
            'redundant_files': [],
            'security_issues': [],
            'recommendations': []
        }
        
        # 统计文件
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                analysis['total_files'] += 1
                try:
                    analysis['total_size'] += file_path.stat().st_size
                except OSError:
                    pass
        
        # 识别冗余文件
        analysis['redundant_files'] = self._identify_redundant_files()
        
        # 识别安全问题
        analysis['security_issues'] = self._identify_security_issues()
        
        # 生成建议
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        logger.info(f"分析完成: {analysis['total_files']} 个文件, "
                   f"{analysis['total_size'] / 1024 / 1024:.1f} MB")
        
        return analysis
    
    def _identify_redundant_files(self) -> List[Dict[str, Any]]:
        """识别冗余文件"""
        redundant_files = []
        
        # 定义冗余文件模式
        redundant_patterns = [
            # 演示文件（保留核心演示）
            {
                'pattern': 'demo_*.py',
                'keep': ['demo_expert_system_comprehensive.py'],
                'reason': '过多的演示文件'
            },
            # 测试文件（保留核心测试）
            {
                'pattern': 'test_*.py',
                'keep': ['test_scd_parser_fix.py', 'test_improved_modules.py'],
                'reason': '过多的测试文件'
            },
            # 报告文件（保留最新的）
            {
                'pattern': '*报告*.md',
                'keep': ['深度技术规程学习系统设计.md', '修复完成报告.md'],
                'reason': '过多的报告文件'
            },
            # 临时文件
            {
                'pattern': '*.tmp',
                'keep': [],
                'reason': '临时文件'
            },
            # 缓存文件
            {
                'pattern': '__pycache__',
                'keep': [],
                'reason': 'Python缓存目录'
            },
            # 日志文件
            {
                'pattern': '*.log',
                'keep': [],
                'reason': '日志文件'
            }
        ]
        
        for pattern_info in redundant_patterns:
            pattern = pattern_info['pattern']
            keep_files = set(pattern_info['keep'])
            
            for file_path in self.project_root.rglob(pattern):
                if file_path.name not in keep_files:
                    try:
                        size = file_path.stat().st_size if file_path.is_file() else 0
                        redundant_files.append({
                            'path': str(file_path.relative_to(self.project_root)),
                            'size': size,
                            'reason': pattern_info['reason'],
                            'type': 'file' if file_path.is_file() else 'directory'
                        })
                    except OSError:
                        pass
        
        return redundant_files
    
    def _identify_security_issues(self) -> List[Dict[str, Any]]:
        """识别安全问题"""
        security_issues = []
        
        # 检查Python文件中的安全问题
        for py_file in self.project_root.rglob('*.py'):
            if py_file.is_file():
                try:
                    content = py_file.read_text(encoding='utf-8', errors='ignore')
                    issues = self._check_file_security(py_file, content)
                    security_issues.extend(issues)
                except Exception as e:
                    logger.warning(f"检查文件安全性失败 {py_file}: {e}")
        
        return security_issues
    
    def _check_file_security(self, file_path: Path, content: str) -> List[Dict[str, Any]]:
        """检查单个文件的安全问题"""
        issues = []
        
        # 危险模式检查
        dangerous_patterns = [
            {
                'pattern': 'eval(',
                'severity': 'high',
                'description': '使用eval()函数存在代码注入风险'
            },
            {
                'pattern': 'exec(',
                'severity': 'high',
                'description': '使用exec()函数存在代码注入风险'
            },
            {
                'pattern': '__import__(',
                'severity': 'medium',
                'description': '动态导入可能存在安全风险'
            },
            {
                'pattern': 'subprocess.call(',
                'severity': 'medium',
                'description': '系统调用需要验证输入参数'
            },
            {
                'pattern': 'os.system(',
                'severity': 'high',
                'description': '使用os.system()存在命令注入风险'
            },
            {
                'pattern': 'pickle.loads(',
                'severity': 'high',
                'description': '反序列化不可信数据存在安全风险'
            },
            {
                'pattern': 'ET.parse(',
                'severity': 'medium',
                'description': 'XML解析未使用安全解析器'
            }
        ]
        
        for pattern_info in dangerous_patterns:
            if pattern_info['pattern'] in content:
                issues.append({
                    'file': str(file_path.relative_to(self.project_root)),
                    'type': 'security_risk',
                    'severity': pattern_info['severity'],
                    'description': pattern_info['description'],
                    'pattern': pattern_info['pattern']
                })
        
        return issues
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于冗余文件的建议
        if analysis['redundant_files']:
            total_redundant = len(analysis['redundant_files'])
            redundant_size = sum(f['size'] for f in analysis['redundant_files'])
            recommendations.append(
                f"清理 {total_redundant} 个冗余文件，可节省 {redundant_size / 1024 / 1024:.1f} MB 空间"
            )
        
        # 基于安全问题的建议
        if analysis['security_issues']:
            high_risk = len([i for i in analysis['security_issues'] if i['severity'] == 'high'])
            if high_risk > 0:
                recommendations.append(f"修复 {high_risk} 个高风险安全问题")
            
            medium_risk = len([i for i in analysis['security_issues'] if i['severity'] == 'medium'])
            if medium_risk > 0:
                recommendations.append(f"修复 {medium_risk} 个中等风险安全问题")
        
        # 架构建议
        recommendations.extend([
            "使用安全的XML解析器（defusedxml）",
            "实施输入验证和清理",
            "添加访问控制和权限检查",
            "建立安全的配置管理",
            "实施日志记录和监控"
        ])
        
        return recommendations
    
    def cleanup_system(self, analysis: Dict[str, Any], confirm: bool = False) -> Dict[str, Any]:
        """清理系统"""
        if not confirm:
            logger.warning("清理操作需要确认，使用 confirm=True 参数")
            return {'status': 'cancelled', 'reason': 'not_confirmed'}
        
        logger.info("开始清理系统...")
        
        cleanup_result = {
            'status': 'success',
            'files_removed': 0,
            'dirs_removed': 0,
            'space_saved': 0,
            'errors': []
        }
        
        # 清理冗余文件
        for file_info in analysis['redundant_files']:
            file_path = self.project_root / file_info['path']
            
            try:
                if file_path.exists():
                    if file_path.is_file():
                        file_path.unlink()
                        cleanup_result['files_removed'] += 1
                        cleanup_result['space_saved'] += file_info['size']
                        logger.info(f"删除文件: {file_info['path']}")
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)
                        cleanup_result['dirs_removed'] += 1
                        logger.info(f"删除目录: {file_info['path']}")
            except Exception as e:
                error_msg = f"删除失败 {file_info['path']}: {e}"
                cleanup_result['errors'].append(error_msg)
                logger.error(error_msg)
        
        logger.info(f"清理完成: 删除 {cleanup_result['files_removed']} 个文件, "
                   f"{cleanup_result['dirs_removed']} 个目录, "
                   f"节省 {cleanup_result['space_saved'] / 1024 / 1024:.1f} MB")
        
        return cleanup_result
    
    def create_security_config(self) -> Dict[str, Any]:
        """创建安全配置"""
        security_config = {
            "security": {
                "xml_parser": {
                    "use_defusedxml": True,
                    "disable_external_entities": True,
                    "max_file_size": 50 * 1024 * 1024,
                    "allowed_extensions": [".scd", ".xml", ".icd", ".cid"]
                },
                "input_validation": {
                    "max_string_length": 1000,
                    "sanitize_input": True,
                    "validate_file_paths": True,
                    "prevent_path_traversal": True
                },
                "access_control": {
                    "require_authentication": False,
                    "log_access_attempts": True,
                    "rate_limiting": {
                        "enabled": True,
                        "max_requests_per_minute": 60
                    }
                },
                "logging": {
                    "log_level": "INFO",
                    "log_file": "iec61850_checker.log",
                    "max_log_size": 10 * 1024 * 1024,
                    "backup_count": 5
                }
            },
            "performance": {
                "max_validation_time": 30,
                "max_issues": 1000,
                "memory_limit": 512 * 1024 * 1024,
                "timeout": 60
            },
            "features": {
                "enable_expert_system": True,
                "enable_knowledge_graph": True,
                "enable_learning": True,
                "enable_reporting": True
            }
        }
        
        # 保存配置文件
        config_file = self.project_root / "security_config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(security_config, f, ensure_ascii=False, indent=2)
            logger.info(f"安全配置已保存到: {config_file}")
        except Exception as e:
            logger.error(f"保存安全配置失败: {e}")
        
        return security_config
    
    def generate_cleanup_report(self, analysis: Dict[str, Any], cleanup_result: Dict[str, Any] = None) -> str:
        """生成清理报告"""
        report_lines = [
            "系统清理和安全分析报告",
            "=" * 50,
            f"分析时间: {self._get_current_time()}",
            ""
        ]
        
        # 系统概况
        report_lines.extend([
            "系统概况:",
            f"  总文件数: {analysis['total_files']}",
            f"  总大小: {analysis['total_size'] / 1024 / 1024:.1f} MB",
            f"  冗余文件: {len(analysis['redundant_files'])} 个",
            f"  安全问题: {len(analysis['security_issues'])} 个",
            ""
        ])
        
        # 冗余文件分析
        if analysis['redundant_files']:
            report_lines.extend([
                "冗余文件分析:",
                ""
            ])
            
            # 按类型分组
            by_reason = {}
            for file_info in analysis['redundant_files']:
                reason = file_info['reason']
                if reason not in by_reason:
                    by_reason[reason] = []
                by_reason[reason].append(file_info)
            
            for reason, files in by_reason.items():
                total_size = sum(f['size'] for f in files)
                report_lines.extend([
                    f"  {reason}: {len(files)} 个文件, {total_size / 1024 / 1024:.1f} MB",
                    ""
                ])
        
        # 安全问题分析
        if analysis['security_issues']:
            report_lines.extend([
                "安全问题分析:",
                ""
            ])
            
            # 按严重程度分组
            by_severity = {}
            for issue in analysis['security_issues']:
                severity = issue['severity']
                if severity not in by_severity:
                    by_severity[severity] = []
                by_severity[severity].append(issue)
            
            for severity in ['high', 'medium', 'low']:
                if severity in by_severity:
                    issues = by_severity[severity]
                    report_lines.extend([
                        f"  {severity.upper()}风险: {len(issues)} 个",
                        ""
                    ])
                    
                    for issue in issues[:5]:  # 显示前5个
                        report_lines.extend([
                            f"    - {issue['description']}",
                            f"      文件: {issue['file']}",
                            ""
                        ])
        
        # 清理结果
        if cleanup_result:
            report_lines.extend([
                "清理结果:",
                f"  删除文件: {cleanup_result['files_removed']} 个",
                f"  删除目录: {cleanup_result['dirs_removed']} 个",
                f"  节省空间: {cleanup_result['space_saved'] / 1024 / 1024:.1f} MB",
                ""
            ])
            
            if cleanup_result['errors']:
                report_lines.extend([
                    "清理错误:",
                    *[f"  - {error}" for error in cleanup_result['errors']],
                    ""
                ])
        
        # 建议
        if analysis['recommendations']:
            report_lines.extend([
                "改进建议:",
                *[f"  • {rec}" for rec in analysis['recommendations']],
                ""
            ])
        
        report_lines.extend([
            "=" * 50,
            "报告生成完成"
        ])
        
        return "\n".join(report_lines)
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def main():
    """主函数"""
    print("🔧 系统清理和安全配置工具")
    print("=" * 50)
    
    cleaner = SystemCleanupAndSecurity()
    
    # 分析系统
    print("📊 正在分析系统状态...")
    analysis = cleaner.analyze_system()
    
    print(f"✅ 分析完成:")
    print(f"   总文件数: {analysis['total_files']}")
    print(f"   冗余文件: {len(analysis['redundant_files'])} 个")
    print(f"   安全问题: {len(analysis['security_issues'])} 个")
    
    # 显示主要问题
    if analysis['security_issues']:
        high_risk = [i for i in analysis['security_issues'] if i['severity'] == 'high']
        if high_risk:
            print(f"\n🚨 发现 {len(high_risk)} 个高风险安全问题:")
            for issue in high_risk[:3]:
                print(f"   • {issue['description']} ({issue['file']})")
    
    # 生成报告
    report = cleaner.generate_cleanup_report(analysis)
    report_file = "system_cleanup_report.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n📄 详细报告已保存到: {report_file}")
    except Exception as e:
        print(f"⚠️ 保存报告失败: {e}")
    
    # 创建安全配置
    print("\n🔒 正在创建安全配置...")
    security_config = cleaner.create_security_config()
    print("✅ 安全配置已创建")
    
    # 询问是否执行清理
    print(f"\n🗑️ 发现 {len(analysis['redundant_files'])} 个冗余文件")
    
    if analysis['redundant_files']:
        total_size = sum(f['size'] for f in analysis['redundant_files'])
        print(f"   可节省空间: {total_size / 1024 / 1024:.1f} MB")
        
        response = input("\n是否执行清理？(y/N): ").strip().lower()
        
        if response == 'y':
            print("🧹 正在清理系统...")
            cleanup_result = cleaner.cleanup_system(analysis, confirm=True)
            
            if cleanup_result['status'] == 'success':
                print(f"✅ 清理完成:")
                print(f"   删除文件: {cleanup_result['files_removed']} 个")
                print(f"   删除目录: {cleanup_result['dirs_removed']} 个")
                print(f"   节省空间: {cleanup_result['space_saved'] / 1024 / 1024:.1f} MB")
                
                # 更新报告
                updated_report = cleaner.generate_cleanup_report(analysis, cleanup_result)
                try:
                    with open(report_file, 'w', encoding='utf-8') as f:
                        f.write(updated_report)
                    print(f"📄 报告已更新")
                except Exception as e:
                    print(f"⚠️ 更新报告失败: {e}")
            else:
                print(f"❌ 清理失败: {cleanup_result.get('reason', 'unknown')}")
        else:
            print("⏭️ 跳过清理操作")
    
    print("\n🎉 系统分析和配置完成！")
    print("\n📋 下一步建议:")
    print("   1. 查看详细报告了解系统状态")
    print("   2. 根据安全建议修复发现的问题")
    print("   3. 使用 secure_main.py 进行安全的SCD文件检查")
    print("   4. 定期运行此工具进行系统维护")


if __name__ == "__main__":
    main()