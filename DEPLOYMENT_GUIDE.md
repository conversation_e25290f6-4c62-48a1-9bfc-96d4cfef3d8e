# IEC61850智能设计检查器 - 部署指南

## 📋 部署概览

**项目名称**: IEC61850智能设计检查器  
**版本**: 1.0.0  
**支持平台**: Windows, Linux, macOS  
**部署方式**: 桌面应用 + Web服务  

## 🔧 系统要求

### 最低配置
- **操作系统**: Windows 10/Linux Ubuntu 18.04+/macOS 10.15+
- **Python版本**: Python 3.8+
- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **网络**: 可选（Web服务需要）

### 推荐配置
- **操作系统**: Windows 11/Linux Ubuntu 20.04+/macOS 12+
- **Python版本**: Python 3.9+
- **内存**: 8GB+ RAM
- **存储**: 10GB+ 可用空间
- **CPU**: 4核心+
- **网络**: 千兆网络（多用户环境）

## 📦 安装部署

### 方式一：标准安装（推荐）

#### 1. 环境准备
```bash
# 检查Python版本
python --version  # 应该 >= 3.8

# 创建虚拟环境
python -m venv iec61850_env

# 激活虚拟环境
# Windows:
iec61850_env\Scripts\activate
# Linux/macOS:
source iec61850_env/bin/activate
```

#### 2. 安装依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装核心依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install -r requirements-dev.txt
```

#### 3. 配置数据库
```bash
# 安装Neo4j（知识库）
# Windows: 下载Neo4j Desktop
# Linux:
sudo apt-get install neo4j
# macOS:
brew install neo4j

# 启动Neo4j
neo4j start
```

#### 4. 初始化应用
```bash
# 运行初始化脚本
python scripts/init_database.py

# 验证安装
python main.py --version
```

### 方式二：Docker部署

#### 1. 构建镜像
```bash
# 构建应用镜像
docker build -t iec61850-validator:latest .

# 或使用docker-compose
docker-compose build
```

#### 2. 启动服务
```bash
# 启动完整服务栈
docker-compose up -d

# 检查服务状态
docker-compose ps
```

#### 3. 访问应用
- **Web界面**: http://localhost:8080
- **API文档**: http://localhost:8080/api/docs
- **Neo4j浏览器**: http://localhost:7474

## 🚀 启动应用

### 桌面应用模式
```bash
# 启动桌面GUI
python main.py

# 或使用快捷脚本
./start_gui.sh  # Linux/macOS
start_gui.bat   # Windows
```

### Web服务模式
```bash
# 启动Web服务
python run_web.py

# 或使用生产服务器
gunicorn -w 4 -b 0.0.0.0:8080 src.web.app:app
```

### 混合模式
```bash
# 同时启动桌面和Web服务
python main.py --mode hybrid --web-port 8080
```

## ⚙️ 配置管理

### 主配置文件
```yaml
# config/app_config.yml
app:
  name: "IEC61850智能设计检查器"
  version: "1.0.0"
  debug: false
  
database:
  neo4j:
    uri: "bolt://localhost:7687"
    username: "neo4j"
    password: "password"
  
web:
  host: "0.0.0.0"
  port: 8080
  secret_key: "your-secret-key"
  
performance:
  max_file_size: 500  # MB
  thread_pool_size: 4
  memory_limit: 2048  # MB
  
logging:
  level: "INFO"
  file: "logs/app.log"
  max_size: 100  # MB
  backup_count: 5
```

### 环境变量
```bash
# .env 文件
IEC61850_ENV=production
IEC61850_SECRET_KEY=your-secret-key
IEC61850_NEO4J_URI=bolt://localhost:7687
IEC61850_NEO4J_USER=neo4j
IEC61850_NEO4J_PASSWORD=password
IEC61850_LOG_LEVEL=INFO
IEC61850_MAX_FILE_SIZE=500
```

## 🔒 安全配置

### 1. 数据库安全
```bash
# 修改Neo4j默认密码
cypher-shell -u neo4j -p neo4j
CALL dbms.changePassword('new-strong-password');
```

### 2. Web服务安全
```python
# 在config/security.py中配置
SECURITY_CONFIG = {
    'SECRET_KEY': 'your-very-strong-secret-key',
    'SESSION_TIMEOUT': 3600,  # 1小时
    'MAX_UPLOAD_SIZE': 500 * 1024 * 1024,  # 500MB
    'ALLOWED_EXTENSIONS': ['.scd', '.icd', '.cid', '.xml'],
    'CORS_ORIGINS': ['http://localhost:3000'],
    'RATE_LIMITING': {
        'enabled': True,
        'requests_per_minute': 60
    }
}
```

### 3. 文件权限
```bash
# 设置适当的文件权限
chmod 755 main.py
chmod 644 config/*.yml
chmod 700 logs/
chmod 600 .env
```

## 📊 性能优化

### 1. 内存优化
```python
# config/performance.py
PERFORMANCE_CONFIG = {
    'MEMORY_LIMIT': 2048,  # MB
    'CHUNK_SIZE': 1024,    # KB
    'GC_THRESHOLD': 0.8,   # 80%内存使用时触发GC
    'STREAMING_THRESHOLD': 50,  # MB，超过此大小使用流式解析
}
```

### 2. 并发优化
```python
# 配置线程池
THREAD_POOL_CONFIG = {
    'MAX_WORKERS': 4,
    'QUEUE_SIZE': 100,
    'TIMEOUT': 300  # 5分钟
}
```

### 3. 缓存配置
```python
# 配置Redis缓存（可选）
CACHE_CONFIG = {
    'REDIS_URL': 'redis://localhost:6379/0',
    'DEFAULT_TIMEOUT': 3600,  # 1小时
    'KEY_PREFIX': 'iec61850:'
}
```

## 🔍 监控和日志

### 1. 日志配置
```python
# config/logging.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        }
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/app.log',
            'maxBytes': 100 * 1024 * 1024,  # 100MB
            'backupCount': 5,
            'formatter': 'detailed'
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        }
    },
    'loggers': {
        '': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

### 2. 健康检查
```python
# scripts/health_check.py
def health_check():
    """系统健康检查"""
    checks = {
        'database': check_neo4j_connection(),
        'memory': check_memory_usage(),
        'disk': check_disk_space(),
        'services': check_running_services()
    }
    return checks
```

### 3. 性能监控
```bash
# 启动性能监控
python scripts/monitor.py --interval 60 --output logs/performance.log
```

## 🔄 备份和恢复

### 1. 数据备份
```bash
# 备份Neo4j数据库
neo4j-admin dump --database=neo4j --to=backup/neo4j-backup.dump

# 备份配置文件
tar -czf backup/config-backup.tar.gz config/

# 备份用户数据
tar -czf backup/data-backup.tar.gz uploads/ reports/
```

### 2. 数据恢复
```bash
# 恢复Neo4j数据库
neo4j-admin load --from=backup/neo4j-backup.dump --database=neo4j --force

# 恢复配置文件
tar -xzf backup/config-backup.tar.gz

# 恢复用户数据
tar -xzf backup/data-backup.tar.gz
```

### 3. 自动备份脚本
```bash
#!/bin/bash
# scripts/backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/$DATE"

mkdir -p $BACKUP_DIR

# 备份数据库
neo4j-admin dump --database=neo4j --to=$BACKUP_DIR/neo4j.dump

# 备份配置和数据
tar -czf $BACKUP_DIR/config.tar.gz config/
tar -czf $BACKUP_DIR/data.tar.gz uploads/ reports/

# 清理旧备份（保留30天）
find backup/ -type d -mtime +30 -exec rm -rf {} \;
```

## 🚨 故障排除

### 常见问题

#### 1. 内存不足
```bash
# 症状：应用崩溃，OutOfMemory错误
# 解决：
# 1. 增加系统内存
# 2. 调整内存限制配置
# 3. 启用流式解析
export IEC61850_MEMORY_LIMIT=4096
```

#### 2. 数据库连接失败
```bash
# 症状：Neo4j连接错误
# 解决：
# 1. 检查Neo4j服务状态
systemctl status neo4j
# 2. 检查连接配置
# 3. 验证用户名密码
```

#### 3. 文件解析失败
```bash
# 症状：XML解析错误
# 解决：
# 1. 检查文件格式
# 2. 验证文件编码
# 3. 启用严格模式调试
python main.py --strict-mode --debug
```

#### 4. 性能问题
```bash
# 症状：处理速度慢
# 解决：
# 1. 启用性能分析
python scripts/profile.py --file large_file.scd
# 2. 调整并发配置
# 3. 优化硬件配置
```

## 📈 扩展部署

### 1. 集群部署
```yaml
# docker-compose.cluster.yml
version: '3.8'
services:
  app1:
    image: iec61850-validator:latest
    ports:
      - "8081:8080"
  app2:
    image: iec61850-validator:latest
    ports:
      - "8082:8080"
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### 2. 负载均衡
```nginx
# nginx.conf
upstream iec61850_backend {
    server app1:8080;
    server app2:8080;
}

server {
    listen 80;
    location / {
        proxy_pass http://iec61850_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 云部署
```yaml
# kubernetes/deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iec61850-validator
spec:
  replicas: 3
  selector:
    matchLabels:
      app: iec61850-validator
  template:
    metadata:
      labels:
        app: iec61850-validator
    spec:
      containers:
      - name: app
        image: iec61850-validator:latest
        ports:
        - containerPort: 8080
        env:
        - name: IEC61850_ENV
          value: "production"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
```

## 📞 技术支持

### 联系方式
- **技术文档**: [项目Wiki](https://github.com/your-org/iec61850-validator/wiki)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/iec61850-validator/issues)
- **邮件支持**: <EMAIL>

### 维护计划
- **定期更新**: 每月第一个周五
- **安全补丁**: 发现后24小时内
- **功能更新**: 每季度一次
- **备份检查**: 每周一次

---

**部署完成后，请运行健康检查确保所有组件正常工作！**

```bash
python scripts/health_check.py --full
```
