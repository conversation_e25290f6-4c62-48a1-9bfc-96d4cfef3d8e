"""
互操作性检查器
检查IED设备间的互操作性和通信兼容性
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.models.base_model import BaseModel
from ..core.rules.base_rule import ValidationResult


logger = logging.getLogger(__name__)


class InteroperabilityLevel(Enum):
    """互操作性级别"""
    FULL = "full"           # 完全兼容
    PARTIAL = "partial"     # 部分兼容
    LIMITED = "limited"     # 有限兼容
    INCOMPATIBLE = "incompatible"  # 不兼容


class CompatibilityIssueType(Enum):
    """兼容性问题类型"""
    PROTOCOL_VERSION = "protocol_version"      # 协议版本不匹配
    DATA_TYPE_MISMATCH = "data_type_mismatch"  # 数据类型不匹配
    SERVICE_UNAVAILABLE = "service_unavailable"  # 服务不可用
    CONFIGURATION_CONFLICT = "configuration_conflict"  # 配置冲突
    TIMING_CONSTRAINT = "timing_constraint"    # 时序约束
    SECURITY_INCOMPATIBLE = "security_incompatible"  # 安全机制不兼容


@dataclass
class CompatibilityIssue:
    """兼容性问题"""
    issue_type: CompatibilityIssueType
    severity: str  # error, warning, info
    source_device: str
    target_device: str
    description: str
    details: Dict[str, Any]
    recommendations: List[str]
    impact_level: str  # high, medium, low


@dataclass
class InteroperabilityResult:
    """互操作性检查结果"""
    source_device: str
    target_device: str
    compatibility_level: InteroperabilityLevel
    overall_score: float  # 0.0 - 1.0
    issues: List[CompatibilityIssue]
    supported_services: List[str]
    unsupported_services: List[str]
    recommendations: List[str]


class InteroperabilityChecker:
    """互操作性检查器"""
    
    def __init__(self):
        """初始化互操作性检查器"""
        self.compatibility_rules = self._load_compatibility_rules()
        self.protocol_versions = self._load_protocol_versions()
        self.service_mappings = self._load_service_mappings()
        
        logger.info("互操作性检查器初始化完成")
    
    def check_interoperability(self, 
                             source_device: Dict[str, Any],
                             target_device: Dict[str, Any]) -> InteroperabilityResult:
        """
        检查两个设备间的互操作性
        
        Args:
            source_device: 源设备信息
            target_device: 目标设备信息
            
        Returns:
            InteroperabilityResult: 互操作性检查结果
        """
        try:
            logger.info(f"开始检查设备互操作性: {source_device.get('name')} -> {target_device.get('name')}")
            
            issues = []
            
            # 1. 协议版本兼容性检查
            protocol_issues = self._check_protocol_compatibility(source_device, target_device)
            issues.extend(protocol_issues)
            
            # 2. 数据类型兼容性检查
            data_type_issues = self._check_data_type_compatibility(source_device, target_device)
            issues.extend(data_type_issues)
            
            # 3. 服务兼容性检查
            service_issues, supported_services, unsupported_services = self._check_service_compatibility(
                source_device, target_device
            )
            issues.extend(service_issues)
            
            # 4. 配置兼容性检查
            config_issues = self._check_configuration_compatibility(source_device, target_device)
            issues.extend(config_issues)
            
            # 5. 时序约束检查
            timing_issues = self._check_timing_constraints(source_device, target_device)
            issues.extend(timing_issues)
            
            # 6. 安全兼容性检查
            security_issues = self._check_security_compatibility(source_device, target_device)
            issues.extend(security_issues)
            
            # 计算兼容性级别和分数
            compatibility_level, overall_score = self._calculate_compatibility_level(issues)
            
            # 生成推荐
            recommendations = self._generate_recommendations(issues)
            
            result = InteroperabilityResult(
                source_device=source_device.get('name', 'Unknown'),
                target_device=target_device.get('name', 'Unknown'),
                compatibility_level=compatibility_level,
                overall_score=overall_score,
                issues=issues,
                supported_services=supported_services,
                unsupported_services=unsupported_services,
                recommendations=recommendations
            )
            
            logger.info(f"互操作性检查完成: {compatibility_level.value}, 分数: {overall_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"互操作性检查失败: {e}")
            return InteroperabilityResult(
                source_device=source_device.get('name', 'Unknown'),
                target_device=target_device.get('name', 'Unknown'),
                compatibility_level=InteroperabilityLevel.INCOMPATIBLE,
                overall_score=0.0,
                issues=[],
                supported_services=[],
                unsupported_services=[],
                recommendations=[]
            )
    
    def check_system_interoperability(self, devices: List[Dict[str, Any]]) -> List[InteroperabilityResult]:
        """
        检查系统中所有设备间的互操作性
        
        Args:
            devices: 设备列表
            
        Returns:
            List[InteroperabilityResult]: 互操作性检查结果列表
        """
        results = []
        
        try:
            # 检查每对设备间的互操作性
            for i, source_device in enumerate(devices):
                for target_device in devices[i+1:]:
                    result = self.check_interoperability(source_device, target_device)
                    results.append(result)
            
            logger.info(f"系统互操作性检查完成，共检查 {len(results)} 对设备")
            return results
            
        except Exception as e:
            logger.error(f"系统互操作性检查失败: {e}")
            return []
    
    def _check_protocol_compatibility(self, 
                                    source_device: Dict[str, Any],
                                    target_device: Dict[str, Any]) -> List[CompatibilityIssue]:
        """检查协议兼容性"""
        issues = []
        
        try:
            source_protocols = source_device.get('supported_protocols', [])
            target_protocols = target_device.get('supported_protocols', [])
            
            # 检查协议版本兼容性
            for protocol in source_protocols:
                if protocol in target_protocols:
                    source_version = source_device.get('protocol_versions', {}).get(protocol)
                    target_version = target_device.get('protocol_versions', {}).get(protocol)
                    
                    if source_version and target_version:
                        if not self._are_versions_compatible(source_version, target_version):
                            issue = CompatibilityIssue(
                                issue_type=CompatibilityIssueType.PROTOCOL_VERSION,
                                severity="warning",
                                source_device=source_device.get('name', 'Unknown'),
                                target_device=target_device.get('name', 'Unknown'),
                                description=f"协议版本不匹配: {protocol} {source_version} vs {target_version}",
                                details={
                                    'protocol': protocol,
                                    'source_version': source_version,
                                    'target_version': target_version
                                },
                                recommendations=[
                                    f"升级设备以支持兼容的{protocol}版本",
                                    "检查协议版本兼容性矩阵"
                                ],
                                impact_level="medium"
                            )
                            issues.append(issue)
            
            # 检查是否有共同支持的协议
            common_protocols = set(source_protocols) & set(target_protocols)
            if not common_protocols:
                issue = CompatibilityIssue(
                    issue_type=CompatibilityIssueType.PROTOCOL_VERSION,
                    severity="error",
                    source_device=source_device.get('name', 'Unknown'),
                    target_device=target_device.get('name', 'Unknown'),
                    description="设备间没有共同支持的通信协议",
                    details={
                        'source_protocols': source_protocols,
                        'target_protocols': target_protocols
                    },
                    recommendations=[
                        "配置设备以支持共同的通信协议",
                        "使用协议转换网关"
                    ],
                    impact_level="high"
                )
                issues.append(issue)
            
        except Exception as e:
            logger.error(f"协议兼容性检查失败: {e}")
        
        return issues
    
    def _check_data_type_compatibility(self,
                                     source_device: Dict[str, Any],
                                     target_device: Dict[str, Any]) -> List[CompatibilityIssue]:
        """检查数据类型兼容性"""
        issues = []
        
        try:
            source_data_types = source_device.get('supported_data_types', [])
            target_data_types = target_device.get('supported_data_types', [])
            
            # 检查关键数据类型的支持
            critical_types = ['BOOLEAN', 'INT32', 'FLOAT32', 'Timestamp', 'Quality']
            
            for data_type in critical_types:
                source_supports = data_type in source_data_types
                target_supports = data_type in target_data_types
                
                if source_supports and not target_supports:
                    issue = CompatibilityIssue(
                        issue_type=CompatibilityIssueType.DATA_TYPE_MISMATCH,
                        severity="warning",
                        source_device=source_device.get('name', 'Unknown'),
                        target_device=target_device.get('name', 'Unknown'),
                        description=f"目标设备不支持数据类型: {data_type}",
                        details={
                            'data_type': data_type,
                            'source_supports': source_supports,
                            'target_supports': target_supports
                        },
                        recommendations=[
                            f"确保目标设备支持{data_type}数据类型",
                            "使用数据类型转换"
                        ],
                        impact_level="medium"
                    )
                    issues.append(issue)
            
        except Exception as e:
            logger.error(f"数据类型兼容性检查失败: {e}")
        
        return issues
    
    def _check_service_compatibility(self,
                                   source_device: Dict[str, Any],
                                   target_device: Dict[str, Any]) -> Tuple[List[CompatibilityIssue], List[str], List[str]]:
        """检查服务兼容性"""
        issues = []
        supported_services = []
        unsupported_services = []
        
        try:
            source_services = source_device.get('supported_services', [])
            target_services = target_device.get('supported_services', [])
            
            # 检查服务支持情况
            for service in source_services:
                if service in target_services:
                    supported_services.append(service)
                else:
                    unsupported_services.append(service)
                    
                    # 检查是否为关键服务
                    if service in ['GetDataValues', 'SetDataValues', 'Report', 'GOOSE']:
                        issue = CompatibilityIssue(
                            issue_type=CompatibilityIssueType.SERVICE_UNAVAILABLE,
                            severity="error" if service in ['Report', 'GOOSE'] else "warning",
                            source_device=source_device.get('name', 'Unknown'),
                            target_device=target_device.get('name', 'Unknown'),
                            description=f"目标设备不支持关键服务: {service}",
                            details={
                                'service': service,
                                'is_critical': True
                            },
                            recommendations=[
                                f"确保目标设备支持{service}服务",
                                "使用替代服务或协议转换"
                            ],
                            impact_level="high"
                        )
                        issues.append(issue)
            
        except Exception as e:
            logger.error(f"服务兼容性检查失败: {e}")
        
        return issues, supported_services, unsupported_services
    
    def _check_configuration_compatibility(self,
                                         source_device: Dict[str, Any],
                                         target_device: Dict[str, Any]) -> List[CompatibilityIssue]:
        """检查配置兼容性"""
        issues = []
        
        try:
            # 检查VLAN配置
            source_vlan = source_device.get('vlan_config', {})
            target_vlan = target_device.get('vlan_config', {})
            
            if source_vlan and target_vlan:
                if source_vlan.get('vlan_id') != target_vlan.get('vlan_id'):
                    issue = CompatibilityIssue(
                        issue_type=CompatibilityIssueType.CONFIGURATION_CONFLICT,
                        severity="warning",
                        source_device=source_device.get('name', 'Unknown'),
                        target_device=target_device.get('name', 'Unknown'),
                        description="VLAN配置不匹配",
                        details={
                            'source_vlan': source_vlan.get('vlan_id'),
                            'target_vlan': target_vlan.get('vlan_id')
                        },
                        recommendations=[
                            "统一VLAN配置",
                            "配置VLAN间路由"
                        ],
                        impact_level="medium"
                    )
                    issues.append(issue)
            
            # 检查IP地址配置
            source_ip = source_device.get('ip_address')
            target_ip = target_device.get('ip_address')
            
            if source_ip and target_ip:
                if not self._are_in_same_subnet(source_ip, target_ip):
                    issue = CompatibilityIssue(
                        issue_type=CompatibilityIssueType.CONFIGURATION_CONFLICT,
                        severity="warning",
                        source_device=source_device.get('name', 'Unknown'),
                        target_device=target_device.get('name', 'Unknown'),
                        description="设备不在同一子网",
                        details={
                            'source_ip': source_ip,
                            'target_ip': target_ip
                        },
                        recommendations=[
                            "配置路由以连接不同子网",
                            "调整IP地址配置"
                        ],
                        impact_level="low"
                    )
                    issues.append(issue)
            
        except Exception as e:
            logger.error(f"配置兼容性检查失败: {e}")
        
        return issues
    
    def _check_timing_constraints(self,
                                source_device: Dict[str, Any],
                                target_device: Dict[str, Any]) -> List[CompatibilityIssue]:
        """检查时序约束"""
        issues = []
        
        try:
            # 检查GOOSE发布间隔
            source_goose_config = source_device.get('goose_config', {})
            target_goose_config = target_device.get('goose_config', {})
            
            if source_goose_config and target_goose_config:
                source_interval = source_goose_config.get('publish_interval', 0)
                target_timeout = target_goose_config.get('timeout', 0)
                
                if source_interval > 0 and target_timeout > 0:
                    if source_interval > target_timeout:
                        issue = CompatibilityIssue(
                            issue_type=CompatibilityIssueType.TIMING_CONSTRAINT,
                            severity="error",
                            source_device=source_device.get('name', 'Unknown'),
                            target_device=target_device.get('name', 'Unknown'),
                            description="GOOSE发布间隔超过目标设备超时时间",
                            details={
                                'source_interval': source_interval,
                                'target_timeout': target_timeout
                            },
                            recommendations=[
                                "减少GOOSE发布间隔",
                                "增加目标设备超时时间"
                            ],
                            impact_level="high"
                        )
                        issues.append(issue)
            
        except Exception as e:
            logger.error(f"时序约束检查失败: {e}")
        
        return issues
    
    def _check_security_compatibility(self,
                                    source_device: Dict[str, Any],
                                    target_device: Dict[str, Any]) -> List[CompatibilityIssue]:
        """检查安全兼容性"""
        issues = []
        
        try:
            source_security = source_device.get('security_features', [])
            target_security = target_device.get('security_features', [])
            
            # 检查安全特性兼容性
            if 'TLS' in source_security and 'TLS' not in target_security:
                issue = CompatibilityIssue(
                    issue_type=CompatibilityIssueType.SECURITY_INCOMPATIBLE,
                    severity="warning",
                    source_device=source_device.get('name', 'Unknown'),
                    target_device=target_device.get('name', 'Unknown'),
                    description="目标设备不支持TLS安全传输",
                    details={
                        'source_security': source_security,
                        'target_security': target_security
                    },
                    recommendations=[
                        "启用目标设备的TLS支持",
                        "使用其他安全机制"
                    ],
                    impact_level="medium"
                )
                issues.append(issue)
            
        except Exception as e:
            logger.error(f"安全兼容性检查失败: {e}")
        
        return issues
    
    def _calculate_compatibility_level(self, issues: List[CompatibilityIssue]) -> Tuple[InteroperabilityLevel, float]:
        """计算兼容性级别和分数"""
        if not issues:
            return InteroperabilityLevel.FULL, 1.0
        
        error_count = len([i for i in issues if i.severity == "error"])
        warning_count = len([i for i in issues if i.severity == "warning"])
        
        # 计算分数
        total_penalty = error_count * 0.3 + warning_count * 0.1
        score = max(0.0, 1.0 - total_penalty)
        
        # 确定兼容性级别
        if error_count == 0 and warning_count == 0:
            level = InteroperabilityLevel.FULL
        elif error_count == 0 and warning_count <= 2:
            level = InteroperabilityLevel.PARTIAL
        elif error_count <= 1:
            level = InteroperabilityLevel.LIMITED
        else:
            level = InteroperabilityLevel.INCOMPATIBLE
        
        return level, score
    
    def _generate_recommendations(self, issues: List[CompatibilityIssue]) -> List[str]:
        """生成推荐建议"""
        recommendations = []
        
        # 收集所有推荐
        for issue in issues:
            recommendations.extend(issue.recommendations)
        
        # 去重并排序
        unique_recommendations = list(set(recommendations))
        
        # 按优先级排序（错误相关的推荐优先）
        error_recommendations = []
        warning_recommendations = []
        
        for issue in issues:
            if issue.severity == "error":
                error_recommendations.extend(issue.recommendations)
            else:
                warning_recommendations.extend(issue.recommendations)
        
        # 优先返回错误相关的推荐
        prioritized = []
        for rec in unique_recommendations:
            if rec in error_recommendations:
                prioritized.insert(0, rec)
            else:
                prioritized.append(rec)
        
        return prioritized[:10]  # 限制推荐数量
    
    def _load_compatibility_rules(self) -> Dict[str, Any]:
        """加载兼容性规则"""
        # 这里应该从配置文件或数据库加载规则
        return {
            'protocol_versions': {
                'IEC61850': ['2003', '2004', '2011'],
                'GOOSE': ['2003', '2011'],
                'SMV': ['2011']
            },
            'critical_services': ['Report', 'GOOSE', 'GetDataValues'],
            'timing_constraints': {
                'goose_max_interval': 1000,  # ms
                'report_max_interval': 5000  # ms
            }
        }
    
    def _load_protocol_versions(self) -> Dict[str, List[str]]:
        """加载协议版本信息"""
        return {
            'IEC61850-8-1': ['2003', '2004', '2011'],
            'IEC61850-9-2': ['2004', '2011'],
            'GOOSE': ['2003', '2011'],
            'SMV': ['2004', '2011']
        }
    
    def _load_service_mappings(self) -> Dict[str, List[str]]:
        """加载服务映射信息"""
        return {
            'MMS': ['GetNameList', 'GetVariableAccessAttributes', 'Read', 'Write'],
            'GOOSE': ['Publish', 'Subscribe'],
            'SMV': ['Publish', 'Subscribe'],
            'Report': ['EnableReporting', 'DisableReporting']
        }
    
    def _are_versions_compatible(self, version1: str, version2: str) -> bool:
        """检查版本是否兼容"""
        # 简化的版本兼容性检查
        try:
            v1_year = int(version1)
            v2_year = int(version2)
            
            # 假设向后兼容2个版本
            return abs(v1_year - v2_year) <= 8
        except:
            return version1 == version2
    
    def _are_in_same_subnet(self, ip1: str, ip2: str) -> bool:
        """检查两个IP是否在同一子网"""
        try:
            # 简化的子网检查（假设/24子网）
            ip1_parts = ip1.split('.')[:3]
            ip2_parts = ip2.split('.')[:3]
            return ip1_parts == ip2_parts
        except:
            return False
