"""
性能优化器
提供大规模数据处理的性能优化功能
"""

import logging
import gc
import threading
import multiprocessing
from typing import Dict, Any, List, Optional, Callable, Iterator
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import psutil
import time
from functools import wraps
from contextlib import contextmanager
import xml.etree.ElementTree as ET
from xml.etree.ElementTree import iterparse
import mmap


logger = logging.getLogger(__name__)


class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        """初始化性能优化器"""
        self.cpu_count = multiprocessing.cpu_count()
        self.memory_limit = self._get_available_memory() * 0.8  # 使用80%可用内存
        self.chunk_size = 1024 * 1024  # 1MB块大小
        self.thread_pool = None
        self.process_pool = None
        
        logger.info(f"性能优化器初始化 - CPU核心数: {self.cpu_count}, 内存限制: {self.memory_limit/1024/1024:.0f}MB")
    
    def optimize_xml_parsing(self, file_path: str, chunk_processor: Callable) -> Iterator[Any]:
        """
        优化XML解析 - 流式处理大文件
        
        Args:
            file_path: XML文件路径
            chunk_processor: 块处理函数
            
        Yields:
            处理结果
        """
        try:
            logger.info(f"开始流式解析XML文件: {file_path}")
            
            # 使用内存映射文件
            with open(file_path, 'rb') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mmapped_file:
                    # 使用iterparse进行流式解析
                    context = iterparse(mmapped_file, events=('start', 'end'))
                    context = iter(context)
                    
                    # 获取根元素
                    event, root = next(context)
                    
                    current_element = None
                    element_stack = []
                    
                    for event, elem in context:
                        if event == 'start':
                            element_stack.append(elem)
                            
                        elif event == 'end':
                            if element_stack:
                                element_stack.pop()
                            
                            # 处理完整的元素
                            if self._is_complete_element(elem):
                                try:
                                    result = chunk_processor(elem)
                                    if result is not None:
                                        yield result
                                except Exception as e:
                                    logger.error(f"处理元素失败: {e}")
                                
                                # 清理已处理的元素以释放内存
                                elem.clear()
                                
                                # 定期强制垃圾回收
                                if self._should_gc():
                                    gc.collect()
            
            logger.info("XML流式解析完成")
            
        except Exception as e:
            logger.error(f"XML流式解析失败: {e}")
            raise
    
    def parallel_process_elements(self, elements: List[Any], processor: Callable, 
                                max_workers: Optional[int] = None) -> List[Any]:
        """
        并行处理元素列表
        
        Args:
            elements: 要处理的元素列表
            processor: 处理函数
            max_workers: 最大工作线程数
            
        Returns:
            处理结果列表
        """
        if not elements:
            return []
        
        max_workers = max_workers or min(self.cpu_count, len(elements))
        
        logger.info(f"开始并行处理 {len(elements)} 个元素，使用 {max_workers} 个工作线程")
        
        results = []
        
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_element = {
                    executor.submit(processor, element): element 
                    for element in elements
                }
                
                # 收集结果
                for future in as_completed(future_to_element):
                    element = future_to_element[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"处理元素失败: {e}")
                        results.append(None)
            
            logger.info(f"并行处理完成，成功处理 {len([r for r in results if r is not None])} 个元素")
            
        except Exception as e:
            logger.error(f"并行处理失败: {e}")
            raise
        
        return results
    
    def batch_process_with_memory_limit(self, items: List[Any], processor: Callable,
                                      batch_size: Optional[int] = None) -> List[Any]:
        """
        批量处理，控制内存使用
        
        Args:
            items: 要处理的项目列表
            processor: 处理函数
            batch_size: 批次大小
            
        Returns:
            处理结果列表
        """
        if not items:
            return []
        
        # 动态计算批次大小
        if batch_size is None:
            batch_size = self._calculate_optimal_batch_size(len(items))
        
        logger.info(f"开始批量处理 {len(items)} 个项目，批次大小: {batch_size}")
        
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_start_time = time.time()
            
            logger.debug(f"处理批次 {i//batch_size + 1}/{(len(items) + batch_size - 1)//batch_size}")
            
            try:
                # 处理当前批次
                batch_results = []
                for item in batch:
                    result = processor(item)
                    batch_results.append(result)
                
                results.extend(batch_results)
                
                # 监控内存使用
                memory_usage = self._get_memory_usage()
                if memory_usage > self.memory_limit:
                    logger.warning(f"内存使用超限: {memory_usage/1024/1024:.0f}MB")
                    gc.collect()
                
                batch_time = time.time() - batch_start_time
                logger.debug(f"批次处理完成，耗时: {batch_time:.2f}s")
                
            except Exception as e:
                logger.error(f"批次处理失败: {e}")
                # 继续处理下一批次
                continue
        
        logger.info(f"批量处理完成，总共处理 {len(results)} 个项目")
        return results
    
    def optimize_validation_rules(self, rules: List[Any], data: Any) -> List[Any]:
        """
        优化验证规则执行
        
        Args:
            rules: 验证规则列表
            data: 要验证的数据
            
        Returns:
            验证结果列表
        """
        logger.info(f"开始优化验证规则执行，规则数量: {len(rules)}")
        
        # 按优先级和依赖关系排序规则
        sorted_rules = self._sort_rules_by_priority(rules)
        
        # 分组规则：独立规则可以并行执行
        independent_rules, dependent_rules = self._group_rules_by_dependency(sorted_rules)
        
        results = []
        
        # 并行执行独立规则
        if independent_rules:
            logger.debug(f"并行执行 {len(independent_rules)} 个独立规则")
            independent_results = self.parallel_process_elements(
                independent_rules,
                lambda rule: self._execute_rule(rule, data)
            )
            results.extend(independent_results)
        
        # 串行执行依赖规则
        if dependent_rules:
            logger.debug(f"串行执行 {len(dependent_rules)} 个依赖规则")
            for rule in dependent_rules:
                try:
                    result = self._execute_rule(rule, data)
                    results.append(result)
                except Exception as e:
                    logger.error(f"规则执行失败: {e}")
                    results.append(None)
        
        logger.info("验证规则执行优化完成")
        return results
    
    @contextmanager
    def memory_monitor(self, operation_name: str):
        """内存监控上下文管理器"""
        initial_memory = self._get_memory_usage()
        start_time = time.time()
        
        logger.debug(f"开始监控操作: {operation_name}, 初始内存: {initial_memory/1024/1024:.0f}MB")
        
        try:
            yield
        finally:
            final_memory = self._get_memory_usage()
            elapsed_time = time.time() - start_time
            memory_increase = final_memory - initial_memory
            
            logger.info(f"操作完成: {operation_name}")
            logger.info(f"  耗时: {elapsed_time:.2f}s")
            logger.info(f"  内存增长: {memory_increase/1024/1024:.2f}MB")
            logger.info(f"  最终内存: {final_memory/1024/1024:.0f}MB")
    
    def performance_profile(self, func: Callable) -> Callable:
        """性能分析装饰器"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            initial_memory = self._get_memory_usage()
            
            try:
                result = func(*args, **kwargs)
                
                elapsed_time = time.time() - start_time
                final_memory = self._get_memory_usage()
                memory_increase = final_memory - initial_memory
                
                logger.info(f"函数 {func.__name__} 性能统计:")
                logger.info(f"  执行时间: {elapsed_time:.2f}s")
                logger.info(f"  内存增长: {memory_increase/1024/1024:.2f}MB")
                
                return result
                
            except Exception as e:
                elapsed_time = time.time() - start_time
                logger.error(f"函数 {func.__name__} 执行失败 (耗时: {elapsed_time:.2f}s): {e}")
                raise
        
        return wrapper
    
    def cleanup_resources(self):
        """清理资源"""
        try:
            if self.thread_pool:
                self.thread_pool.shutdown(wait=True)
                self.thread_pool = None
            
            if self.process_pool:
                self.process_pool.shutdown(wait=True)
                self.process_pool = None
            
            # 强制垃圾回收
            gc.collect()
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    def _get_available_memory(self) -> int:
        """获取可用内存（字节）"""
        return psutil.virtual_memory().available
    
    def _get_memory_usage(self) -> int:
        """获取当前进程内存使用（字节）"""
        process = psutil.Process()
        return process.memory_info().rss
    
    def _should_gc(self) -> bool:
        """判断是否应该执行垃圾回收"""
        current_memory = self._get_memory_usage()
        return current_memory > self.memory_limit * 0.9
    
    def _is_complete_element(self, elem) -> bool:
        """判断是否为完整的可处理元素"""
        # 检查是否为IED、LogicalDevice等主要元素
        return elem.tag in ['IED', 'LDevice', 'LN', 'DataSet', 'ReportControl', 'GSEControl', 'SMVControl']
    
    def _calculate_optimal_batch_size(self, total_items: int) -> int:
        """计算最优批次大小"""
        # 基于可用内存和CPU核心数计算
        base_batch_size = max(1, total_items // (self.cpu_count * 4))
        
        # 根据内存限制调整
        available_memory_mb = self._get_available_memory() / 1024 / 1024
        if available_memory_mb < 1000:  # 小于1GB
            base_batch_size = min(base_batch_size, 10)
        elif available_memory_mb < 4000:  # 小于4GB
            base_batch_size = min(base_batch_size, 50)
        else:
            base_batch_size = min(base_batch_size, 100)
        
        return max(1, base_batch_size)
    
    def _sort_rules_by_priority(self, rules: List[Any]) -> List[Any]:
        """按优先级排序规则"""
        # 简化实现：按规则类型排序
        def get_priority(rule):
            if hasattr(rule, 'priority'):
                return rule.priority
            elif hasattr(rule, 'rule_type'):
                priority_map = {
                    'syntax': 1,
                    'semantic': 2,
                    'business': 3
                }
                return priority_map.get(rule.rule_type, 999)
            else:
                return 999
        
        return sorted(rules, key=get_priority)
    
    def _group_rules_by_dependency(self, rules: List[Any]) -> tuple:
        """按依赖关系分组规则"""
        independent_rules = []
        dependent_rules = []
        
        for rule in rules:
            if hasattr(rule, 'dependencies') and rule.dependencies:
                dependent_rules.append(rule)
            else:
                independent_rules.append(rule)
        
        return independent_rules, dependent_rules
    
    def _execute_rule(self, rule: Any, data: Any) -> Any:
        """执行单个规则"""
        try:
            if hasattr(rule, 'execute'):
                return rule.execute(data)
            elif callable(rule):
                return rule(data)
            else:
                logger.warning(f"无法执行规则: {rule}")
                return None
        except Exception as e:
            logger.error(f"规则执行异常: {e}")
            raise


class StreamingXMLParser:
    """流式XML解析器"""
    
    def __init__(self, chunk_size: int = 1024 * 1024):
        """初始化流式解析器"""
        self.chunk_size = chunk_size
        self.optimizer = PerformanceOptimizer()
    
    def parse_large_file(self, file_path: str, element_handlers: Dict[str, Callable]) -> Iterator[Any]:
        """
        解析大型XML文件
        
        Args:
            file_path: XML文件路径
            element_handlers: 元素处理器映射 {tag_name: handler_function}
            
        Yields:
            解析结果
        """
        def chunk_processor(elem):
            tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
            
            if tag_name in element_handlers:
                handler = element_handlers[tag_name]
                return handler(elem)
            
            return None
        
        yield from self.optimizer.optimize_xml_parsing(file_path, chunk_processor)


class MemoryEfficientValidator:
    """内存高效验证器"""
    
    def __init__(self):
        """初始化内存高效验证器"""
        self.optimizer = PerformanceOptimizer()
        self.validation_cache = {}
    
    def validate_large_dataset(self, data: Any, rules: List[Any]) -> List[Any]:
        """验证大型数据集"""
        with self.optimizer.memory_monitor("大型数据集验证"):
            # 使用优化的规则执行
            results = self.optimizer.optimize_validation_rules(rules, data)
            
            # 清理缓存
            self._cleanup_cache()
            
            return results
    
    def _cleanup_cache(self):
        """清理验证缓存"""
        if len(self.validation_cache) > 1000:
            # 保留最近使用的缓存项
            self.validation_cache.clear()
            logger.debug("验证缓存已清理")


# 全局性能优化器实例
global_optimizer = PerformanceOptimizer()


def optimize_performance(func: Callable) -> Callable:
    """性能优化装饰器"""
    return global_optimizer.performance_profile(func)


def with_memory_monitor(operation_name: str):
    """内存监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            with global_optimizer.memory_monitor(operation_name):
                return func(*args, **kwargs)
        return wrapper
    return decorator
