"""
知识库模块
为IEC61850设计检查器提供智能化的知识管理和推理能力

主要功能：
1. 标准文档智能解析
2. 知识图谱构建和管理
3. 智能推理引擎
4. 规则自动提取和更新
5. 专家知识集成
"""

from .base.knowledge_base import KnowledgeBase
from .base.knowledge_entity import KnowledgeEntity, StandardEntity, RuleEntity
from .base.storage_engine import StorageEngine
from .extractors.standard_extractor import StandardExtractor
from .standards.iec61850_knowledge import IEC61850Knowledge
# 暂时注释掉有问题的导入
# from .reasoning.inference_engine import InferenceEngine

__version__ = "1.0.0"

__all__ = [
    'KnowledgeBase',
    'KnowledgeEntity',
    'StandardEntity', 
    'RuleEntity',
    'StorageEngine',
    'StandardExtractor',
    'IEC61850Knowledge',
    # 'InferenceEngine'
]