#!/usr/bin/env python3
"""
简化的设计检查器
避免循环导入问题
"""

import xml.etree.ElementTree as ET
from typing import List, Dict, Set, Optional
from pathlib import Path
import re


class SimpleDesignChecker:
    """简化的设计检查器"""
    
    def __init__(self):
        self.namespace = {'scl': 'http://www.iec.ch/61850/2003/SCL'}
    
    def check_config_design(self, file_path: str) -> List[Dict]:
        """检查配置文件设计问题"""
        issues = []
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # 检查1: 断路器缺少Terminal连接
            issues.extend(self._check_missing_terminals(root))
            
            # 检查2: 缺少必要的互感器
            issues.extend(self._check_missing_transformers(root))
            
            # 检查3: 电流互感器二次侧连接
            issues.extend(self._check_ct_secondary_connections(root))
            
            # 检查4: 电压等级设备配置完整性
            issues.extend(self._check_voltage_level_completeness(root))
            
            # 检查5: 通信网络配置问题
            issues.extend(self._check_communication_issues(root))
            
            # 检查6: IP地址冲突
            issues.extend(self._check_ip_conflicts(root))
            
            # 检查7: 数据集引用完整性
            issues.extend(self._check_dataset_references(root))
            
            # 检查8: 逻辑节点数据对象完整性
            issues.extend(self._check_logical_node_completeness(root))
            
            # 检查9: IED配置完整性
            issues.extend(self._check_ied_completeness(root))
            
            # 检查10: 数据类型定义完整性
            issues.extend(self._check_datatype_completeness(root))
            
        except Exception as e:
            issues.append({
                'title': "配置文件解析错误",
                'description': f"解析配置文件时发生错误: {str(e)}",
                'severity': "critical",
                'category': "解析错误",
                'source_type': "config",
                'suggestion': "检查XML文件格式是否正确"
            })
        
        return issues
    
    def _check_missing_terminals(self, root: ET.Element) -> List[Dict]:
        """检查断路器缺少Terminal连接"""
        issues = []
        
        # 查找所有断路器
        breakers = root.findall(".//ConductingEquipment[@type='CBR']")
        
        for breaker in breakers:
            breaker_name = breaker.get('name', '未知断路器')
            terminals = breaker.findall("Terminal")
            
            if len(terminals) == 0:
                issues.append({
                    'title': f"断路器{breaker_name}缺少Terminal连接",
                    'description': f"断路器{breaker_name}没有定义任何Terminal连接，导致回路不通",
                    'severity': "critical",
                    'category': "回路连接",
                    'source_type': "config",
                    'location': f"断路器: {breaker_name}",
                    'suggestion': f"为断路器{breaker_name}添加至少两个Terminal连接，分别连接到母线和线路侧",
                    'standard_reference': "IEC61850-6: 配置描述语言",
                    'affected_elements': [breaker_name]
                })
            elif len(terminals) == 1:
                issues.append({
                    'title': f"断路器{breaker_name}Terminal连接不完整",
                    'description': f"断路器{breaker_name}只有一个Terminal连接，无法形成完整回路",
                    'severity': "error",
                    'category': "回路连接",
                    'source_type': "config",
                    'location': f"断路器: {breaker_name}",
                    'suggestion': f"为断路器{breaker_name}添加第二个Terminal连接",
                    'standard_reference': "IEC61850-6: 配置描述语言",
                    'affected_elements': [breaker_name]
                })
        
        return issues
    
    def _check_missing_transformers(self, root: ET.Element) -> List[Dict]:
        """检查缺少必要的互感器"""
        issues = []
        
        # 查找所有间隔
        bays = root.findall(".//Bay")
        
        for bay in bays:
            bay_name = bay.get('name', '未知间隔')
            
            # 检查是否有断路器
            breakers = bay.findall(".//ConductingEquipment[@type='CBR']")
            if breakers:
                # 检查是否有电流互感器
                current_transformers = bay.findall(".//ConductingEquipment[@type='CTR']")
                if not current_transformers:
                    issues.append({
                        'title': f"间隔{bay_name}缺少电流互感器",
                        'description': f"间隔{bay_name}有断路器但缺少电流互感器，无法进行电流测量和保护",
                        'severity': "error",
                        'category': "设备配置",
                        'source_type': "config",
                        'location': f"间隔: {bay_name}",
                        'suggestion': f"在间隔{bay_name}中添加电流互感器(CTR)用于电流测量",
                        'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        'affected_elements': [bay_name]
                    })
        
        return issues
    
    def _check_ct_secondary_connections(self, root: ET.Element) -> List[Dict]:
        """检查电流互感器二次侧连接"""
        issues = []
        
        # 查找所有电流互感器
        current_transformers = root.findall(".//ConductingEquipment[@type='CTR']")
        
        for ct in current_transformers:
            ct_name = ct.get('name', '未知电流互感器')
            terminals = ct.findall("Terminal")
            
            if len(terminals) < 2:
                issues.append({
                    'title': f"电流互感器{ct_name}缺少二次侧连接",
                    'description': f"电流互感器{ct_name}缺少二次侧Terminal连接，无法向保护和测量装置提供电流信号",
                    'severity': "error",
                    'category': "虚端子连接",
                    'source_type': "config",
                    'location': f"电流互感器: {ct_name}",
                    'suggestion': f"为电流互感器{ct_name}添加二次侧Terminal连接，连接到保护和测量装置",
                    'standard_reference': "IEC61850-9-2: 采样值传输的特殊通信服务映射",
                    'affected_elements': [ct_name]
                })
        
        return issues
    
    def _check_voltage_level_completeness(self, root: ET.Element) -> List[Dict]:
        """检查电压等级设备配置完整性"""
        issues = []
        
        # 查找所有电压等级
        voltage_levels = root.findall(".//VoltageLevel")
        
        for vl in voltage_levels:
            vl_name = vl.get('name', '未知电压等级')
            
            # 检查是否有间隔
            bays = vl.findall("Bay")
            if not bays:
                issues.append({
                    'title': f"电压等级{vl_name}没有配置任何间隔",
                    'description': f"电压等级{vl_name}下没有定义任何间隔，这是不完整的配置",
                    'severity': "error",
                    'category': "配置完整性",
                    'source_type': "config",
                    'location': f"电压等级: {vl_name}",
                    'suggestion': f"在电压等级{vl_name}下添加相应的间隔配置，如出线间隔、母联间隔等",
                    'standard_reference': "IEC61850-6: 配置描述语言",
                    'affected_elements': [vl_name]
                })
        
        return issues
    
    def _check_communication_issues(self, root: ET.Element) -> List[Dict]:
        """检查通信网络配置问题"""
        issues = []
        
        # 查找通信配置
        communication = root.find("Communication")
        if communication is None:
            issues.append({
                'title': "缺少通信网络配置",
                'description': "配置文件中没有定义Communication部分，无法进行网络通信",
                'severity': "critical",
                'category': "通信配置",
                'source_type': "config",
                'location': "根节点",
                'suggestion': "添加Communication部分，定义子网络和IED连接",
                'standard_reference': "IEC61850-8-1: 特定通信服务映射",
                'affected_elements': ["Communication"]
            })
            return issues
        
        # 检查网关配置
        connected_aps = communication.findall(".//ConnectedAP")
        for ap in connected_aps:
            ied_name = ap.get('iedName', '未知IED')
            address = ap.find("Address")
            if address is not None:
                gateway = address.find("P[@type='IP-GATEWAY']")
                if gateway is None:
                    issues.append({
                        'title': f"IED {ied_name}缺少网关配置",
                        'description': f"IED {ied_name}的网络配置中缺少IP-GATEWAY设置",
                        'severity': "warning",
                        'category': "通信配置",
                        'source_type': "config",
                        'location': f"IED: {ied_name}",
                        'suggestion': f"为IED {ied_name}添加IP-GATEWAY配置",
                        'standard_reference': "IEC61850-8-1: 特定通信服务映射",
                        'affected_elements': [ied_name]
                    })
        
        return issues
    
    def _check_ip_conflicts(self, root: ET.Element) -> List[Dict]:
        """检查IP地址冲突"""
        issues = []
        ip_addresses = {}
        
        # 收集所有IP地址
        connected_aps = root.findall(".//ConnectedAP")
        for ap in connected_aps:
            ied_name = ap.get('iedName', '未知IED')
            address = ap.find("Address")
            if address is not None:
                ip_elem = address.find("P[@type='IP']")
                if ip_elem is not None:
                    ip_addr = ip_elem.text
                    if ip_addr in ip_addresses:
                        issues.append({
                            'title': f"IP地址冲突: {ip_addr}",
                            'description': f"IP地址{ip_addr}被多个IED使用: {ip_addresses[ip_addr]} 和 {ied_name}",
                            'severity': "critical",
                            'category': "通信配置",
                            'source_type': "config",
                            'location': f"IED: {ied_name}",
                            'suggestion': f"为IED {ied_name}分配唯一的IP地址",
                            'standard_reference': "IEC61850-8-1: 特定通信服务映射",
                            'affected_elements': [ip_addresses[ip_addr], ied_name]
                        })
                    else:
                        ip_addresses[ip_addr] = ied_name
        
        return issues
    
    def _check_dataset_references(self, root: ET.Element) -> List[Dict]:
        """检查数据集引用完整性"""
        issues = []
        
        # 查找所有数据集
        datasets = root.findall(".//DataSet")
        for dataset in datasets:
            dataset_name = dataset.get('name', '未知数据集')
            
            # 检查数据集中的FCDA引用
            fcdas = dataset.findall("FCDA")
            for fcda in fcdas:
                ln_class = fcda.get('lnClass')
                ln_inst = fcda.get('lnInst')
                ld_inst = fcda.get('ldInst')
                
                # 查找对应的逻辑节点
                ln_xpath = f".//LN[@lnClass='{ln_class}'][@inst='{ln_inst}']"
                logical_nodes = root.findall(ln_xpath)
                
                if not logical_nodes:
                    issues.append({
                        'title': f"数据集{dataset_name}引用不存在的逻辑节点",
                        'description': f"数据集{dataset_name}中引用了不存在的逻辑节点: {ln_class}{ln_inst}",
                        'severity': "error",
                        'category': "数据集配置",
                        'source_type': "config",
                        'location': f"数据集: {dataset_name}",
                        'suggestion': f"检查逻辑节点{ln_class}{ln_inst}是否正确定义，或修正数据集中的引用",
                        'standard_reference': "IEC61850-7-2: 抽象通信服务接口",
                        'affected_elements': [dataset_name, f"{ln_class}{ln_inst}"]
                    })
        
        return issues
    
    def _check_logical_node_completeness(self, root: ET.Element) -> List[Dict]:
        """检查逻辑节点数据对象完整性"""
        issues = []
        
        # 查找所有保护逻辑节点
        protection_lns = root.findall(".//LN[@lnClass='PTRC']")
        for ln in protection_lns:
            ln_inst = ln.get('inst', '未知实例')
            ln_type = ln.get('lnType')
            
            # 检查逻辑节点类型是否存在
            if ln_type:
                type_xpath = f".//LNodeType[@id='{ln_type}']"
                type_def = root.find(type_xpath)
                if type_def is None:
                    issues.append({
                        'title': f"保护逻辑节点PTRC{ln_inst}引用不存在的类型",
                        'description': f"保护逻辑节点PTRC{ln_inst}引用了不存在的类型定义: {ln_type}",
                        'severity': "error",
                        'category': "逻辑节点配置",
                        'source_type': "config",
                        'location': f"逻辑节点: PTRC{ln_inst}",
                        'suggestion': f"检查类型定义{ln_type}是否存在，或修正逻辑节点的类型引用",
                        'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        'affected_elements': [f"PTRC{ln_inst}", ln_type]
                    })
        
        return issues
    
    def _check_ied_completeness(self, root: ET.Element) -> List[Dict]:
        """检查IED配置完整性"""
        issues = []
        
        # 查找所有IED
        ieds = root.findall(".//IED")
        for ied in ieds:
            ied_name = ied.get('name', '未知IED')
            ied_type = ied.get('type', '未知类型')
            
            # 检查保护IED是否有保护逻辑节点
            if ied_type == 'Protection':
                protection_lns = ied.findall(".//LN[@lnClass='PTRC']")
                if not protection_lns:
                    issues.append({
                        'title': f"保护IED {ied_name}缺少保护逻辑节点",
                        'description': f"保护类型的IED {ied_name}没有定义任何保护逻辑节点(PTRC)",
                        'severity': "error",
                        'category': "IED配置",
                        'source_type': "config",
                        'location': f"IED: {ied_name}",
                        'suggestion': f"为保护IED {ied_name}添加相应的保护逻辑节点(PTRC)",
                        'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        'affected_elements': [ied_name]
                    })
        
        return issues
    
    def _check_datatype_completeness(self, root: ET.Element) -> List[Dict]:
        """检查数据类型定义完整性"""
        issues = []
        
        # 查找数据类型模板部分
        templates = root.find("DataTypeTemplates")
        if templates is None:
            issues.append({
                'title': "缺少数据类型模板定义",
                'description': "配置文件中没有DataTypeTemplates部分，无法定义数据类型",
                'severity': "critical",
                'category': "数据类型定义",
                'source_type': "config",
                'location': "根节点",
                'suggestion': "添加DataTypeTemplates部分，定义必要的数据类型",
                'standard_reference': "IEC61850-6: 配置描述语言",
                'affected_elements': ["DataTypeTemplates"]
            })
            return issues
        
        # 检查LLN0类型是否有NamPlt
        lln0_types = templates.findall("LNodeType[@lnClass='LLN0']")
        for lln0_type in lln0_types:
            type_id = lln0_type.get('id', '未知类型')
            namPlt_do = lln0_type.find("DO[@name='NamPlt']")
            if namPlt_do is None:
                issues.append({
                    'title': f"LLN0类型{type_id}缺少NamPlt数据对象",
                    'description': f"LLN0类型{type_id}缺少必要的NamPlt(名牌)数据对象",
                    'severity': "error",
                    'category': "数据类型定义",
                    'source_type': "config",
                    'location': f"LNodeType: {type_id}",
                    'suggestion': f"为LLN0类型{type_id}添加NamPlt数据对象",
                    'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                    'affected_elements': [type_id]
                })
        
        return issues
