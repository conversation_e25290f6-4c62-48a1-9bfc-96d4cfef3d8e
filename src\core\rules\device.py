"""
设备配置规则
实现IED设备配置相关的验证规则
"""

from typing import List, Set, Dict, Any
from ..models import IED, AccessPoint, Server, LDevice, Services
from ..parsers.scd_parser import SCLDocument
from .base import (
    BaseRule, RuleContext, RuleResult, RuleCategory, RuleSeverity,
    rule, applicable_to
)


class DeviceRules:
    """设备配置规则集合"""
    
    @staticmethod
    @rule(
        rule_id="DEV001",
        name="IED基本配置检查",
        description="检查IED的基本配置信息",
        category=RuleCategory.DEVICE,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(IED)
    def check_ied_basic_config(context: RuleContext) -> RuleResult:
        """检查IED基本配置"""
        result = RuleResult(rule_id="DEV001", success=True)
        ied = context.data
        
        # 检查必需属性
        if not ied.name:
            result.add_error("IED名称不能为空", context.get_path_string())
        
        if not ied.manufacturer:
            result.add_warning(
                f"IED '{ied.name}' 缺少制造商信息",
                context.get_path_string(),
                suggestion="添加制造商信息以便设备识别和维护"
            )
        
        if not ied.type:
            result.add_warning(
                f"IED '{ied.name}' 缺少设备类型信息",
                context.get_path_string(),
                suggestion="添加设备类型信息，如Protection、Control、Measurement等"
            )
        
        if not ied.config_version:
            result.add_info(
                f"IED '{ied.name}' 缺少配置版本信息",
                context.get_path_string(),
                suggestion="添加配置版本信息以便版本管理"
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DEV002",
        name="逻辑设备配置检查",
        description="检查逻辑设备的配置完整性",
        category=RuleCategory.DEVICE,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(IED)
    def check_logical_device_config(context: RuleContext) -> RuleResult:
        """检查逻辑设备配置"""
        result = RuleResult(rule_id="DEV002", success=True)
        ied = context.data
        
        # 获取所有逻辑设备
        all_ldevices = ied.get_all_ldevices()
        
        if not all_ldevices:
            result.add_warning(
                f"IED '{ied.name}' 没有逻辑设备定义",
                context.get_path_string(),
                suggestion="定义至少一个逻辑设备"
            )
            return result
        
        # 检查逻辑设备名称和实例名唯一性
        ld_names = [ld.name for ld in all_ldevices if ld.name]
        ld_insts = [ld.inst for ld in all_ldevices if ld.inst]
        
        # 检查名称重复
        name_duplicates = [name for name in set(ld_names) if ld_names.count(name) > 1]
        for duplicate in name_duplicates:
            result.add_error(
                f"IED '{ied.name}' 中逻辑设备名称 '{duplicate}' 重复",
                context.get_path_string()
            )
        
        # 检查实例名重复
        inst_duplicates = [inst for inst in set(ld_insts) if ld_insts.count(inst) > 1]
        for duplicate in inst_duplicates:
            result.add_error(
                f"IED '{ied.name}' 中逻辑设备实例名 '{duplicate}' 重复",
                context.get_path_string()
            )
        
        # 检查每个逻辑设备的配置
        for i, ldevice in enumerate(all_ldevices):
            ld_path = f"{context.get_path_string()}.ldevices[{i}]"
            
            if not ldevice.name:
                result.add_error("逻辑设备名称不能为空", ld_path)
            
            if not ldevice.inst:
                result.add_error("逻辑设备实例名不能为空", ld_path)
            
            # 检查逻辑设备名称格式
            if ldevice.name and not ldevice.name.replace('_', '').replace('-', '').isalnum():
                result.add_warning(
                    f"逻辑设备名称 '{ldevice.name}' 包含特殊字符",
                    ld_path,
                    suggestion="建议使用字母、数字、下划线和连字符"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DEV003",
        name="服务配置检查",
        description="检查IED支持的服务配置",
        category=RuleCategory.DEVICE,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(AccessPoint)
    def check_services_config(context: RuleContext) -> RuleResult:
        """检查服务配置"""
        result = RuleResult(rule_id="DEV003", success=True)
        access_point = context.data
        
        if not access_point.services:
            result.add_warning(
                f"访问点 '{access_point.name}' 没有服务定义",
                context.get_path_string(),
                suggestion="定义支持的服务以明确设备能力"
            )
            return result
        
        services = access_point.services
        
        # 检查基本服务
        basic_services = [
            ('get_directory', 'GetDirectory'),
            ('get_data_object_definition', 'GetDataObjectDefinition'),
            ('data_object_directory', 'DataObjectDirectory'),
            ('get_data_set_value', 'GetDataSetValue'),
            ('data_set_directory', 'DataSetDirectory'),
            ('read_write', 'ReadWrite')
        ]
        
        missing_basic = []
        for attr, service_name in basic_services:
            if not getattr(services, attr, False):
                missing_basic.append(service_name)
        
        if missing_basic:
            result.add_info(
                f"访问点 '{access_point.name}' 缺少基本服务: {', '.join(missing_basic)}",
                context.get_path_string(),
                suggestion="考虑添加基本服务以提高互操作性"
            )
        
        # 检查报告服务
        if not services.conf_report_control:
            result.add_info(
                f"访问点 '{access_point.name}' 不支持报告控制",
                context.get_path_string(),
                suggestion="如果需要数据报告功能，请启用ConfReportControl"
            )
        
        # 检查GOOSE服务
        if not services.goose:
            result.add_info(
                f"访问点 '{access_point.name}' 不支持GOOSE",
                context.get_path_string(),
                suggestion="如果需要快速数据交换，请启用GOOSE服务"
            )
        
        # 检查SMV服务
        if not services.sv:
            result.add_info(
                f"访问点 '{access_point.name}' 不支持采样值传输",
                context.get_path_string(),
                suggestion="如果需要采样值传输，请启用SMV服务"
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DEV004",
        name="IED功能一致性检查",
        description="检查IED类型与其功能配置的一致性",
        category=RuleCategory.DEVICE,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(IED)
    def check_ied_function_consistency(context: RuleContext) -> RuleResult:
        """检查IED功能一致性"""
        result = RuleResult(rule_id="DEV004", success=True)
        ied = context.data
        
        if not ied.type:
            return result  # 没有类型信息，跳过检查
        
        ied_type = ied.type.lower()
        
        # 获取IED的服务能力
        has_goose = False
        has_smv = False
        has_control = False
        has_report = False
        
        for ap in ied.access_points:
            if ap.services:
                if ap.services.goose:
                    has_goose = True
                if ap.services.sv:
                    has_smv = True
                if ap.services.timer_activated_control:
                    has_control = True
                if ap.services.conf_report_control:
                    has_report = True
        
        # 根据IED类型检查功能一致性
        if 'protection' in ied_type:
            if not has_goose:
                result.add_warning(
                    f"保护类型IED '{ied.name}' 通常应支持GOOSE服务",
                    context.get_path_string(),
                    suggestion="保护设备通常需要GOOSE进行快速跳闸"
                )
            
            if not has_report:
                result.add_info(
                    f"保护类型IED '{ied.name}' 建议支持报告服务",
                    context.get_path_string(),
                    suggestion="报告服务用于事件记录和状态监视"
                )
        
        elif 'control' in ied_type:
            if not has_control:
                result.add_warning(
                    f"控制类型IED '{ied.name}' 应支持控制服务",
                    context.get_path_string(),
                    suggestion="控制设备需要TimerActivatedControl等控制服务"
                )
            
            if not has_goose:
                result.add_info(
                    f"控制类型IED '{ied.name}' 建议支持GOOSE服务",
                    context.get_path_string(),
                    suggestion="GOOSE用于接收状态信息和发送控制命令"
                )
        
        elif 'measurement' in ied_type or 'meter' in ied_type:
            if not has_smv and not has_report:
                result.add_warning(
                    f"测量类型IED '{ied.name}' 应支持SMV或报告服务",
                    context.get_path_string(),
                    suggestion="测量设备需要SMV或报告服务来传输测量数据"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DEV005",
        name="访问点配置合理性",
        description="检查访问点配置的合理性",
        category=RuleCategory.DEVICE,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(IED)
    def check_access_point_rationality(context: RuleContext) -> RuleResult:
        """检查访问点配置合理性"""
        result = RuleResult(rule_id="DEV005", success=True)
        ied = context.data
        
        if not ied.access_points:
            return result
        
        # 检查访问点数量
        ap_count = len(ied.access_points)
        if ap_count > 4:
            result.add_warning(
                f"IED '{ied.name}' 有 {ap_count} 个访问点，数量较多",
                context.get_path_string(),
                suggestion="考虑是否真的需要这么多访问点"
            )
        
        # 检查访问点功能分配
        mms_count = 0
        goose_count = 0
        smv_count = 0
        
        for ap in ied.access_points:
            if ap.server:
                mms_count += 1
            
            if ap.services:
                if ap.services.goose:
                    goose_count += 1
                if ap.services.sv:
                    smv_count += 1
        
        # 检查MMS访问点
        if mms_count == 0:
            result.add_warning(
                f"IED '{ied.name}' 没有MMS访问点",
                context.get_path_string(),
                suggestion="通常需要至少一个MMS访问点用于配置和监视"
            )
        elif mms_count > 1:
            result.add_info(
                f"IED '{ied.name}' 有 {mms_count} 个MMS访问点",
                context.get_path_string(),
                suggestion="多个MMS访问点可能用于冗余或不同功能"
            )
        
        # 检查GOOSE访问点
        if goose_count > 2:
            result.add_info(
                f"IED '{ied.name}' 有 {goose_count} 个GOOSE访问点",
                context.get_path_string(),
                suggestion="多个GOOSE访问点可能用于不同的功能组"
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="DEV006",
        name="设备冗余配置检查",
        description="检查设备的冗余配置",
        category=RuleCategory.DEVICE,
        severity=RuleSeverity.INFO
    )
    @applicable_to(SCLDocument)
    def check_device_redundancy(context: RuleContext) -> RuleResult:
        """检查设备冗余配置"""
        result = RuleResult(rule_id="DEV006", success=True)
        scl_doc = context.data
        
        if not scl_doc.ieds:
            return result
        
        # 按类型分组IED
        ied_by_type = {}
        for ied in scl_doc.ieds:
            if ied.type:
                ied_type = ied.type.lower()
                if ied_type not in ied_by_type:
                    ied_by_type[ied_type] = []
                ied_by_type[ied_type].append(ied)
        
        # 检查关键设备的冗余
        critical_types = ['protection', 'control']
        
        for ied_type, ieds in ied_by_type.items():
            if any(critical in ied_type for critical in critical_types):
                if len(ieds) == 1:
                    result.add_info(
                        f"关键设备类型 '{ied_type}' 只有一个IED: {ieds[0].name}",
                        context.get_path_string(),
                        suggestion="考虑为关键设备配置冗余"
                    )
                elif len(ieds) >= 2:
                    ied_names = [ied.name for ied in ieds]
                    result.add_info(
                        f"设备类型 '{ied_type}' 有冗余配置: {', '.join(ied_names)}",
                        context.get_path_string()
                    )
        
        return result


# 自动注册所有规则
def register_device_rules():
    """注册所有设备规则"""
    from .registry import rule_registry
    import inspect
    
    for name, method in inspect.getmembers(DeviceRules, predicate=inspect.isfunction):
        if hasattr(method, 'rule_id'):
            try:
                rule_registry.register(method)
            except Exception as e:
                print(f"注册规则 {name} 失败: {e}")


# 在模块加载时自动注册规则
register_device_rules()
