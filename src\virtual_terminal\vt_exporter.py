"""
虚端子导出器
将虚端子表导出为各种格式
"""

import logging
import json
import csv
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import xml.etree.ElementTree as ET

from .vt_models import VirtualTerminalTable, VirtualTerminal, VirtualConnection


logger = logging.getLogger(__name__)


class VirtualTerminalExporter:
    """虚端子导出器"""
    
    def __init__(self):
        """初始化导出器"""
        self.supported_formats = ['json', 'csv', 'excel', 'xml', 'html', 'pdf']
        logger.info("虚端子导出器初始化完成")
    
    def export_table(self, 
                    table: VirtualTerminalTable,
                    output_path: str,
                    format_type: str = 'json',
                    options: Optional[Dict[str, Any]] = None) -> bool:
        """
        导出虚端子表
        
        Args:
            table: 虚端子表
            output_path: 输出路径
            format_type: 导出格式
            options: 导出选项
            
        Returns:
            bool: 导出是否成功
        """
        try:
            logger.info(f"开始导出虚端子表: {table.name} -> {format_type}")
            
            if format_type not in self.supported_formats:
                raise ValueError(f"不支持的导出格式: {format_type}")
            
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            options = options or {}
            
            # 根据格式调用相应的导出方法
            if format_type == 'json':
                success = self._export_json(table, output_path, options)
            elif format_type == 'csv':
                success = self._export_csv(table, output_path, options)
            elif format_type == 'excel':
                success = self._export_excel(table, output_path, options)
            elif format_type == 'xml':
                success = self._export_xml(table, output_path, options)
            elif format_type == 'html':
                success = self._export_html(table, output_path, options)
            elif format_type == 'pdf':
                success = self._export_pdf(table, output_path, options)
            else:
                success = False
            
            if success:
                logger.info(f"虚端子表导出成功: {output_path}")
            else:
                logger.error(f"虚端子表导出失败: {output_path}")
            
            return success
            
        except Exception as e:
            logger.error(f"虚端子表导出失败: {e}")
            return False
    
    def export_terminals_only(self,
                             table: VirtualTerminalTable,
                             output_path: str,
                             format_type: str = 'csv') -> bool:
        """
        仅导出端子信息
        
        Args:
            table: 虚端子表
            output_path: 输出路径
            format_type: 导出格式
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            
            if format_type == 'csv':
                return self._export_terminals_csv(table, output_path)
            elif format_type == 'excel':
                return self._export_terminals_excel(table, output_path)
            else:
                logger.error(f"端子导出不支持格式: {format_type}")
                return False
                
        except Exception as e:
            logger.error(f"端子导出失败: {e}")
            return False
    
    def export_connections_only(self,
                               table: VirtualTerminalTable,
                               output_path: str,
                               format_type: str = 'csv') -> bool:
        """
        仅导出连接信息
        
        Args:
            table: 虚端子表
            output_path: 输出路径
            format_type: 导出格式
            
        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_path)
            
            if format_type == 'csv':
                return self._export_connections_csv(table, output_path)
            elif format_type == 'excel':
                return self._export_connections_excel(table, output_path)
            else:
                logger.error(f"连接导出不支持格式: {format_type}")
                return False
                
        except Exception as e:
            logger.error(f"连接导出失败: {e}")
            return False
    
    def _export_json(self, 
                    table: VirtualTerminalTable,
                    output_path: Path,
                    options: Dict[str, Any]) -> bool:
        """导出为JSON格式"""
        try:
            data = table.to_dict()
            
            # 添加导出信息
            data['export_info'] = {
                'exported_at': datetime.now().isoformat(),
                'exported_by': 'VirtualTerminalExporter',
                'format': 'json',
                'options': options
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"JSON导出失败: {e}")
            return False
    
    def _export_csv(self,
                   table: VirtualTerminalTable,
                   output_path: Path,
                   options: Dict[str, Any]) -> bool:
        """导出为CSV格式"""
        try:
            # 导出端子和连接到不同的CSV文件
            base_path = output_path.with_suffix('')
            
            # 导出端子
            terminals_path = base_path.with_name(f"{base_path.name}_terminals.csv")
            self._export_terminals_csv(table, terminals_path)
            
            # 导出连接
            connections_path = base_path.with_name(f"{base_path.name}_connections.csv")
            self._export_connections_csv(table, connections_path)
            
            # 导出摘要信息
            summary_path = base_path.with_name(f"{base_path.name}_summary.csv")
            self._export_summary_csv(table, summary_path)
            
            return True
            
        except Exception as e:
            logger.error(f"CSV导出失败: {e}")
            return False
    
    def _export_terminals_csv(self,
                             table: VirtualTerminalTable,
                             output_path: Path) -> bool:
        """导出端子为CSV"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入标题行
                headers = [
                    '端子ID', '设备名称', '逻辑设备', '逻辑节点', '数据对象', '数据属性',
                    '端子类型', '信号类型', '数据类型', '功能约束', '描述', '单位',
                    '最小值', '最大值', '默认值', '是否关键', '标签'
                ]
                writer.writerow(headers)
                
                # 写入端子数据
                for terminal in table.terminals:
                    row = [
                        terminal.terminal_id,
                        terminal.device_name,
                        terminal.logical_device,
                        terminal.logical_node,
                        terminal.data_object,
                        terminal.data_attribute or '',
                        terminal.terminal_type.value,
                        terminal.signal_type.value,
                        terminal.data_type or '',
                        terminal.functional_constraint or '',
                        terminal.description or '',
                        terminal.unit or '',
                        terminal.range_min or '',
                        terminal.range_max or '',
                        terminal.default_value or '',
                        '是' if terminal.is_critical else '否',
                        ';'.join(terminal.tags) if terminal.tags else ''
                    ]
                    writer.writerow(row)
            
            return True
            
        except Exception as e:
            logger.error(f"端子CSV导出失败: {e}")
            return False
    
    def _export_connections_csv(self,
                               table: VirtualTerminalTable,
                               output_path: Path) -> bool:
        """导出连接为CSV"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入标题行
                headers = [
                    '连接ID', '源端子', '目标端子', '连接类型', '描述',
                    '是否激活', '优先级', '延迟(ms)', '质量因子', '创建时间'
                ]
                writer.writerow(headers)
                
                # 写入连接数据
                for connection in table.connections:
                    row = [
                        connection.connection_id,
                        connection.source_terminal.terminal_id,
                        connection.target_terminal.terminal_id,
                        connection.connection_type.value,
                        connection.description or '',
                        '是' if connection.is_active else '否',
                        connection.priority,
                        connection.delay_ms or '',
                        connection.quality_factor,
                        connection.created_time.isoformat()
                    ]
                    writer.writerow(row)
            
            return True
            
        except Exception as e:
            logger.error(f"连接CSV导出失败: {e}")
            return False
    
    def _export_summary_csv(self,
                           table: VirtualTerminalTable,
                           output_path: Path) -> bool:
        """导出摘要为CSV"""
        try:
            stats = table.get_statistics()
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 写入基本信息
                writer.writerow(['项目', '值'])
                writer.writerow(['表名称', table.name])
                writer.writerow(['表ID', table.table_id])
                writer.writerow(['版本', table.version])
                writer.writerow(['创建时间', table.created_time.isoformat()])
                writer.writerow(['修改时间', table.modified_time.isoformat()])
                writer.writerow([])
                
                # 写入统计信息
                writer.writerow(['统计项目', '数量'])
                writer.writerow(['总端子数', stats['total_terminals']])
                writer.writerow(['总连接数', stats['total_connections']])
                writer.writerow(['总设备数', stats['total_devices']])
                writer.writerow(['未连接端子数', stats['unconnected_terminals']])
                writer.writerow([])
                
                # 写入端子类型分布
                writer.writerow(['端子类型', '数量'])
                for terminal_type, count in stats['terminal_types'].items():
                    writer.writerow([terminal_type, count])
                writer.writerow([])
                
                # 写入连接类型分布
                writer.writerow(['连接类型', '数量'])
                for connection_type, count in stats['connection_types'].items():
                    writer.writerow([connection_type, count])
            
            return True
            
        except Exception as e:
            logger.error(f"摘要CSV导出失败: {e}")
            return False
    
    def _export_excel(self,
                     table: VirtualTerminalTable,
                     output_path: Path,
                     options: Dict[str, Any]) -> bool:
        """导出为Excel格式"""
        try:
            # 需要安装openpyxl库
            try:
                import openpyxl
                from openpyxl.styles import Font, PatternFill, Alignment
                from openpyxl.utils.dataframe import dataframe_to_rows
            except ImportError:
                logger.error("Excel导出需要安装openpyxl库")
                return False
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 创建端子工作表
            terminals_ws = wb.create_sheet("端子列表")
            self._populate_terminals_worksheet(terminals_ws, table)
            
            # 创建连接工作表
            connections_ws = wb.create_sheet("连接列表")
            self._populate_connections_worksheet(connections_ws, table)
            
            # 创建摘要工作表
            summary_ws = wb.create_sheet("摘要信息")
            self._populate_summary_worksheet(summary_ws, table)
            
            # 保存文件
            wb.save(output_path)
            return True
            
        except Exception as e:
            logger.error(f"Excel导出失败: {e}")
            return False
    
    def _export_xml(self,
                   table: VirtualTerminalTable,
                   output_path: Path,
                   options: Dict[str, Any]) -> bool:
        """导出为XML格式"""
        try:
            # 创建根元素
            root = ET.Element("VirtualTerminalTable")
            root.set("id", table.table_id)
            root.set("name", table.name)
            root.set("version", table.version)
            
            # 添加基本信息
            info = ET.SubElement(root, "Info")
            ET.SubElement(info, "Description").text = table.description or ""
            ET.SubElement(info, "CreatedTime").text = table.created_time.isoformat()
            ET.SubElement(info, "ModifiedTime").text = table.modified_time.isoformat()
            
            # 添加端子
            terminals = ET.SubElement(root, "Terminals")
            for terminal in table.terminals:
                terminal_elem = ET.SubElement(terminals, "Terminal")
                terminal_elem.set("id", terminal.terminal_id)
                
                ET.SubElement(terminal_elem, "DeviceName").text = terminal.device_name
                ET.SubElement(terminal_elem, "LogicalDevice").text = terminal.logical_device
                ET.SubElement(terminal_elem, "LogicalNode").text = terminal.logical_node
                ET.SubElement(terminal_elem, "DataObject").text = terminal.data_object
                
                if terminal.data_attribute:
                    ET.SubElement(terminal_elem, "DataAttribute").text = terminal.data_attribute
                
                ET.SubElement(terminal_elem, "TerminalType").text = terminal.terminal_type.value
                ET.SubElement(terminal_elem, "SignalType").text = terminal.signal_type.value
                
                if terminal.data_type:
                    ET.SubElement(terminal_elem, "DataType").text = terminal.data_type
                
                if terminal.functional_constraint:
                    ET.SubElement(terminal_elem, "FunctionalConstraint").text = terminal.functional_constraint
                
                if terminal.description:
                    ET.SubElement(terminal_elem, "Description").text = terminal.description
            
            # 添加连接
            connections = ET.SubElement(root, "Connections")
            for connection in table.connections:
                connection_elem = ET.SubElement(connections, "Connection")
                connection_elem.set("id", connection.connection_id)
                
                ET.SubElement(connection_elem, "SourceTerminal").text = connection.source_terminal.terminal_id
                ET.SubElement(connection_elem, "TargetTerminal").text = connection.target_terminal.terminal_id
                ET.SubElement(connection_elem, "ConnectionType").text = connection.connection_type.value
                
                if connection.description:
                    ET.SubElement(connection_elem, "Description").text = connection.description
                
                ET.SubElement(connection_elem, "IsActive").text = str(connection.is_active).lower()
                ET.SubElement(connection_elem, "Priority").text = str(connection.priority)
                ET.SubElement(connection_elem, "QualityFactor").text = str(connection.quality_factor)
            
            # 写入文件
            tree = ET.ElementTree(root)
            tree.write(output_path, encoding='utf-8', xml_declaration=True)
            
            return True
            
        except Exception as e:
            logger.error(f"XML导出失败: {e}")
            return False
    
    def _export_html(self,
                    table: VirtualTerminalTable,
                    output_path: Path,
                    options: Dict[str, Any]) -> bool:
        """导出为HTML格式"""
        try:
            html_content = self._generate_html_report(table, options)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return True
            
        except Exception as e:
            logger.error(f"HTML导出失败: {e}")
            return False
    
    def _export_pdf(self,
                   table: VirtualTerminalTable,
                   output_path: Path,
                   options: Dict[str, Any]) -> bool:
        """导出为PDF格式"""
        try:
            # 需要安装reportlab库
            try:
                from reportlab.lib.pagesizes import letter, A4
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib import colors
                from reportlab.lib.units import inch
            except ImportError:
                logger.error("PDF导出需要安装reportlab库")
                return False
            
            # 创建PDF文档
            doc = SimpleDocTemplate(str(output_path), pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # 标题
            title = Paragraph(f"虚端子表报告: {table.name}", styles['Title'])
            story.append(title)
            story.append(Spacer(1, 12))
            
            # 基本信息
            info_data = [
                ['表ID', table.table_id],
                ['表名称', table.name],
                ['版本', table.version],
                ['创建时间', table.created_time.strftime('%Y-%m-%d %H:%M:%S')],
                ['修改时间', table.modified_time.strftime('%Y-%m-%d %H:%M:%S')]
            ]
            
            info_table = Table(info_data)
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 12))
            
            # 统计信息
            stats = table.get_statistics()
            stats_title = Paragraph("统计信息", styles['Heading2'])
            story.append(stats_title)
            
            stats_data = [
                ['项目', '数量'],
                ['总端子数', str(stats['total_terminals'])],
                ['总连接数', str(stats['total_connections'])],
                ['总设备数', str(stats['total_devices'])],
                ['未连接端子数', str(stats['unconnected_terminals'])]
            ]
            
            stats_table = Table(stats_data)
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(stats_table)
            
            # 构建PDF
            doc.build(story)
            
            return True
            
        except Exception as e:
            logger.error(f"PDF导出失败: {e}")
            return False
    
    def _generate_html_report(self, 
                             table: VirtualTerminalTable,
                             options: Dict[str, Any]) -> str:
        """生成HTML报告"""
        stats = table.get_statistics()
        
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>虚端子表报告 - {table.name}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .stats {{ margin: 20px 0; }}
                .stats table {{ border-collapse: collapse; width: 100%; }}
                .stats th, .stats td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .stats th {{ background-color: #f2f2f2; }}
                .section {{ margin: 30px 0; }}
                .table-container {{ overflow-x: auto; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .terminal-input {{ background-color: #e8f5e8; }}
                .terminal-output {{ background-color: #f5e8e8; }}
                .connection-active {{ background-color: #e8f5e8; }}
                .connection-inactive {{ background-color: #f5f5f5; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>虚端子表报告</h1>
                <h2>{table.name}</h2>
                <p><strong>表ID:</strong> {table.table_id}</p>
                <p><strong>版本:</strong> {table.version}</p>
                <p><strong>创建时间:</strong> {table.created_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>修改时间:</strong> {table.modified_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
                {f'<p><strong>描述:</strong> {table.description}</p>' if table.description else ''}
            </div>
            
            <div class="stats">
                <h3>统计信息</h3>
                <table>
                    <tr><th>项目</th><th>数量</th></tr>
                    <tr><td>总端子数</td><td>{stats['total_terminals']}</td></tr>
                    <tr><td>总连接数</td><td>{stats['total_connections']}</td></tr>
                    <tr><td>总设备数</td><td>{stats['total_devices']}</td></tr>
                    <tr><td>未连接端子数</td><td>{stats['unconnected_terminals']}</td></tr>
                </table>
            </div>
        """
        
        # 添加端子类型分布
        if stats['terminal_types']:
            html += """
            <div class="section">
                <h3>端子类型分布</h3>
                <table>
                    <tr><th>端子类型</th><th>数量</th></tr>
            """
            for terminal_type, count in stats['terminal_types'].items():
                html += f"<tr><td>{terminal_type}</td><td>{count}</td></tr>"
            html += "</table></div>"
        
        # 添加连接类型分布
        if stats['connection_types']:
            html += """
            <div class="section">
                <h3>连接类型分布</h3>
                <table>
                    <tr><th>连接类型</th><th>数量</th></tr>
            """
            for connection_type, count in stats['connection_types'].items():
                html += f"<tr><td>{connection_type}</td><td>{count}</td></tr>"
            html += "</table></div>"
        
        # 添加设备列表
        if stats['devices']:
            html += """
            <div class="section">
                <h3>设备列表</h3>
                <ul>
            """
            for device in stats['devices']:
                html += f"<li>{device}</li>"
            html += "</ul></div>"
        
        html += """
            <div class="section">
                <p><em>报告生成时间: {}</em></p>
            </div>
        </body>
        </html>
        """.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        return html
    
    def _populate_terminals_worksheet(self, worksheet, table: VirtualTerminalTable):
        """填充端子工作表"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Alignment
        except ImportError:
            return
        
        # 设置标题行
        headers = [
            '端子ID', '设备名称', '逻辑设备', '逻辑节点', '数据对象', '数据属性',
            '端子类型', '信号类型', '数据类型', '功能约束', '描述', '单位',
            '最小值', '最大值', '默认值', '是否关键', '标签'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        for row, terminal in enumerate(table.terminals, 2):
            data = [
                terminal.terminal_id,
                terminal.device_name,
                terminal.logical_device,
                terminal.logical_node,
                terminal.data_object,
                terminal.data_attribute or '',
                terminal.terminal_type.value,
                terminal.signal_type.value,
                terminal.data_type or '',
                terminal.functional_constraint or '',
                terminal.description or '',
                terminal.unit or '',
                terminal.range_min or '',
                terminal.range_max or '',
                terminal.default_value or '',
                '是' if terminal.is_critical else '否',
                ';'.join(terminal.tags) if terminal.tags else ''
            ]
            
            for col, value in enumerate(data, 1):
                worksheet.cell(row=row, column=col, value=value)
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def _populate_connections_worksheet(self, worksheet, table: VirtualTerminalTable):
        """填充连接工作表"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            return
        
        # 设置标题行
        headers = [
            '连接ID', '源端子', '目标端子', '连接类型', '描述',
            '是否激活', '优先级', '延迟(ms)', '质量因子', '创建时间'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # 填充数据
        for row, connection in enumerate(table.connections, 2):
            data = [
                connection.connection_id,
                connection.source_terminal.terminal_id,
                connection.target_terminal.terminal_id,
                connection.connection_type.value,
                connection.description or '',
                '是' if connection.is_active else '否',
                connection.priority,
                connection.delay_ms or '',
                connection.quality_factor,
                connection.created_time.strftime('%Y-%m-%d %H:%M:%S')
            ]
            
            for col, value in enumerate(data, 1):
                worksheet.cell(row=row, column=col, value=value)
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def _populate_summary_worksheet(self, worksheet, table: VirtualTerminalTable):
        """填充摘要工作表"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill
        except ImportError:
            return
        
        stats = table.get_statistics()
        
        # 基本信息
        worksheet.cell(row=1, column=1, value="基本信息").font = Font(bold=True, size=14)
        
        basic_info = [
            ['表名称', table.name],
            ['表ID', table.table_id],
            ['版本', table.version],
            ['创建时间', table.created_time.strftime('%Y-%m-%d %H:%M:%S')],
            ['修改时间', table.modified_time.strftime('%Y-%m-%d %H:%M:%S')]
        ]
        
        for i, (key, value) in enumerate(basic_info, 2):
            worksheet.cell(row=i, column=1, value=key)
            worksheet.cell(row=i, column=2, value=value)
        
        # 统计信息
        start_row = len(basic_info) + 4
        worksheet.cell(row=start_row, column=1, value="统计信息").font = Font(bold=True, size=14)
        
        stats_info = [
            ['总端子数', stats['total_terminals']],
            ['总连接数', stats['total_connections']],
            ['总设备数', stats['total_devices']],
            ['未连接端子数', stats['unconnected_terminals']]
        ]
        
        for i, (key, value) in enumerate(stats_info, start_row + 1):
            worksheet.cell(row=i, column=1, value=key)
            worksheet.cell(row=i, column=2, value=value)
        
        # 自动调整列宽
        worksheet.column_dimensions['A'].width = 20
        worksheet.column_dimensions['B'].width = 30
