<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007" revision="B" xmlns="http://www.iec.ch/61850/2003/SCL">
    <Header id="SampleSCD" version="1.0" revision="A" toolID="IEC61850DesignChecker" nameStructure="IEDName">
        <Text>Sample SCD file for testing IEC61850 Design Checker</Text>
        <History>
            <Hitem version="1.0" revision="A" when="2024-01-01T00:00:00Z" who="TestUser" what="Initial version"/>
        </History>
    </Header>
    
    <Substation name="TestSubstation" desc="Test substation for validation">
        <VoltageLevel name="VL_110kV" desc="110kV voltage level">
            <Voltage unit="V" multiplier="k" value="110"/>
            <Bay name="Bay_Line1" desc="110kV transmission line bay">
                <ConductingEquipment name="CBR_Line1" type="CBR" desc="Line circuit breaker"/>
                <ConductingEquipment name="DIS_Line1_1" type="DIS" desc="Line disconnector 1"/>
                <ConductingEquipment name="DIS_Line1_2" type="DIS" desc="Line disconnector 2"/>
                <ConductingEquipment name="CTR_Line1" type="CTR" desc="Line current transformer"/>
                <ConductingEquipment name="VTR_Line1" type="VTR" desc="Line voltage transformer"/>
            </Bay>
            <Bay name="Bay_Trafo1" desc="Main transformer bay">
                <ConductingEquipment name="CBR_Trafo1" type="CBR" desc="Transformer circuit breaker"/>
                <ConductingEquipment name="DIS_Trafo1" type="DIS" desc="Transformer disconnector"/>
                <ConductingEquipment name="PTR_Main" type="PTR" desc="Main power transformer"/>
            </Bay>
        </VoltageLevel>
        
        <VoltageLevel name="VL_10kV" desc="10kV voltage level">
            <Voltage unit="V" multiplier="k" value="10"/>
            <Bay name="Bay_Feeder1" desc="10kV feeder bay 1">
                <ConductingEquipment name="CBR_Feeder1" type="CBR" desc="Feeder circuit breaker"/>
                <ConductingEquipment name="CTR_Feeder1" type="CTR" desc="Feeder current transformer"/>
            </Bay>
            <Bay name="Bay_Feeder2" desc="10kV feeder bay 2">
                <ConductingEquipment name="CBR_Feeder2" type="CBR" desc="Feeder circuit breaker"/>
                <ConductingEquipment name="CTR_Feeder2" type="CTR" desc="Feeder current transformer"/>
            </Bay>
        </VoltageLevel>
    </Substation>
    
    <Communication>
        <SubNetwork name="StationBus" type="8-MMS" desc="Station bus network">
            <BitRate unit="b/s" multiplier="M" value="100"/>
            <ConnectedAP iedName="IED_PROT_Line1" apName="AP1">
                <Address>
                    <P type="IP">***********0</P>
                    <P type="IP-SUBNET">*************</P>
                    <P type="IP-GATEWAY">***********</P>
                    <P type="MAC-Address">01-02-03-04-05-06</P>
                </Address>
            </ConnectedAP>
            <ConnectedAP iedName="IED_CTRL_Trafo1" apName="AP1">
                <Address>
                    <P type="IP">************</P>
                    <P type="IP-SUBNET">*************</P>
                    <P type="IP-GATEWAY">***********</P>
                    <P type="MAC-Address">01-02-03-04-05-07</P>
                </Address>
            </ConnectedAP>
            <ConnectedAP iedName="IED_MEAS_Feeder1" apName="AP1">
                <Address>
                    <P type="IP">************</P>
                    <P type="IP-SUBNET">*************</P>
                    <P type="IP-GATEWAY">***********</P>
                    <P type="MAC-Address">01-02-03-04-05-08</P>
                </Address>
            </ConnectedAP>
        </SubNetwork>
        
        <SubNetwork name="ProcessBus" type="8-GOOSE" desc="Process bus for GOOSE messages">
            <ConnectedAP iedName="IED_PROT_Line1" apName="AP2">
                <Address>
                    <P type="MAC-Address">01-02-03-04-05-10</P>
                    <P type="APPID">0x3000</P>
                    <P type="VLAN-ID">100</P>
                    <P type="VLAN-PRIORITY">4</P>
                </Address>
            </ConnectedAP>
            <ConnectedAP iedName="IED_CTRL_Trafo1" apName="AP2">
                <Address>
                    <P type="MAC-Address">01-02-03-04-05-11</P>
                    <P type="APPID">0x3001</P>
                    <P type="VLAN-ID">100</P>
                    <P type="VLAN-PRIORITY">4</P>
                </Address>
            </ConnectedAP>
        </SubNetwork>
    </Communication>
    
    <IED name="IED_PROT_Line1" type="Protection" manufacturer="ABB" configVersion="1.0" 
         originalScdVersion="2007" originalScdRevision="B" engRight="full" owner="Utility">
        <AccessPoint name="AP1" desc="MMS access point">
            <Server timeout="30">
                <Authentication none="true" password="false" weak="false" strong="false" certificate="false"/>
                <LDevice name="PROT" inst="PROT" desc="Protection logical device">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
                    <LN lnClass="PTOC" inst="1" lnType="PTOC_Type" prefix=""/>
                    <LN lnClass="PDIF" inst="1" lnType="PDIF_Type" prefix=""/>
                </LDevice>
            </Server>
            <Services>
                <DynAssociation/>
                <GetDirectory/>
                <GetDataObjectDefinition/>
                <DataObjectDirectory/>
                <GetDataSetValue/>
                <DataSetDirectory/>
                <ReadWrite/>
                <GetCBValues/>
                <ConfReportControl bufTime="2000" bufMode="both"/>
                <GOOSE max="8"/>
                <GSSE max="0"/>
                <FileHandling/>
            </Services>
        </AccessPoint>
        
        <AccessPoint name="AP2" desc="GOOSE access point">
            <Services>
                <GOOSE max="8"/>
            </Services>
        </AccessPoint>
    </IED>
    
    <IED name="IED_CTRL_Trafo1" type="Control" manufacturer="Siemens" configVersion="1.0">
        <AccessPoint name="AP1" desc="MMS access point">
            <Server timeout="30">
                <LDevice name="CTRL" inst="CTRL" desc="Control logical device">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
                    <LN lnClass="CSWI" inst="1" lnType="CSWI_Type" prefix=""/>
                    <LN lnClass="CILO" inst="1" lnType="CILO_Type" prefix=""/>
                </LDevice>
            </Server>
            <Services>
                <GetDirectory/>
                <GetDataObjectDefinition/>
                <DataObjectDirectory/>
                <GetDataSetValue/>
                <SetDataSetValue/>
                <DataSetDirectory/>
                <ReadWrite/>
                <GetCBValues/>
                <TimerActivatedControl/>
                <GOOSE max="4"/>
            </Services>
        </AccessPoint>
        
        <AccessPoint name="AP2" desc="GOOSE access point">
            <Services>
                <GOOSE max="4"/>
            </Services>
        </AccessPoint>
    </IED>
    
    <IED name="IED_MEAS_Feeder1" type="Measurement" manufacturer="Schneider" configVersion="1.0">
        <AccessPoint name="AP1" desc="MMS access point">
            <Server timeout="30">
                <LDevice name="MEAS" inst="MEAS" desc="Measurement logical device">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
                    <LN lnClass="MMXU" inst="1" lnType="MMXU_Type" prefix=""/>
                    <LN lnClass="MSQI" inst="1" lnType="MSQI_Type" prefix=""/>
                </LDevice>
            </Server>
            <Services>
                <GetDirectory/>
                <GetDataObjectDefinition/>
                <DataObjectDirectory/>
                <GetDataSetValue/>
                <DataSetDirectory/>
                <ReadWrite/>
                <GetCBValues/>
                <ConfReportControl bufTime="1000"/>
                <SMV max="8"/>
            </Services>
        </AccessPoint>
    </IED>
    
    <DataTypeTemplates>
        <LNodeType id="LLN0_Type" lnClass="LLN0">
            <DO name="Mod" type="INC_Type" transient="false"/>
            <DO name="Beh" type="INS_Type" transient="false"/>
            <DO name="Health" type="INS_Type" transient="false"/>
            <DO name="NamPlt" type="LPL_Type" transient="true"/>
        </LNodeType>
        
        <LNodeType id="PTOC_Type" lnClass="PTOC">
            <DO name="Mod" type="INC_Type"/>
            <DO name="Beh" type="INS_Type"/>
            <DO name="Str" type="ACD_Type"/>
            <DO name="Op" type="ACT_Type"/>
        </LNodeType>
        
        <LNodeType id="CSWI_Type" lnClass="CSWI">
            <DO name="Mod" type="INC_Type"/>
            <DO name="Beh" type="INS_Type"/>
            <DO name="Pos" type="DPC_Type"/>
        </LNodeType>
        
        <LNodeType id="MMXU_Type" lnClass="MMXU">
            <DO name="Mod" type="INC_Type"/>
            <DO name="Beh" type="INS_Type"/>
            <DO name="TotW" type="MV_Type"/>
            <DO name="TotVAr" type="MV_Type"/>
        </LNodeType>
        
        <DOType id="INC_Type" cdc="INC">
            <DA name="stVal" fc="ST" bType="INT32" dchg="true"/>
            <DA name="q" fc="ST" bType="Quality" qchg="true"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
            <DA name="ctlModel" fc="CF" bType="Enum" type="CtlModelEnum"/>
        </DOType>
        
        <DOType id="DPC_Type" cdc="DPC">
            <DA name="stVal" fc="ST" bType="Dbpos" dchg="true"/>
            <DA name="q" fc="ST" bType="Quality" qchg="true"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
            <DA name="ctlModel" fc="CF" bType="Enum" type="CtlModelEnum"/>
        </DOType>
        
        <DOType id="MV_Type" cdc="MV">
            <DA name="mag" fc="MX" bType="Struct" type="AnalogueValue_Type"/>
            <DA name="q" fc="MX" bType="Quality" qchg="true"/>
            <DA name="t" fc="MX" bType="Timestamp"/>
        </DOType>
        
        <DAType id="AnalogueValue_Type">
            <BDA name="f" bType="FLOAT32"/>
        </DAType>
        
        <EnumType id="CtlModelEnum">
            <EnumVal ord="0">status-only</EnumVal>
            <EnumVal ord="1">direct-with-normal-security</EnumVal>
            <EnumVal ord="2">sbo-with-normal-security</EnumVal>
            <EnumVal ord="3">direct-with-enhanced-security</EnumVal>
            <EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
        </EnumType>
    </DataTypeTemplates>
</SCL>
