"""
生成器模块
为智能变电站工程师提供各种实用的文档和表格生成功能

解决的实际问题：
1. 虚端子表手工制作耗时且容易出错
2. 缺乏标准化的文档模板
3. 设计文档与配置文件的一致性难以保证
4. 工程交付文档的标准化程度不高

提供的解决方案：
1. 自动生成虚端子连接表
2. 标准化的SCD文件生成
3. 专业的工程文档模板
4. 配置与文档的一致性检查
"""

from .virtual_terminal_generator import VirtualTerminalGenerator
from .scd_generator import SCDGenerator
from .document_generator import DocumentGenerator

__all__ = [
    'VirtualTerminalGenerator',
    'SCDGenerator', 
    'DocumentGenerator'
]
