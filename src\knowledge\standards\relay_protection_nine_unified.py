"""
继电保护九统一标准知识库
包含Q/GDW 1161-2014和Q/GDW 1175-2013标准的详细知识
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from ..base.knowledge_entity import (
    StandardEntity, RuleEntity, RequirementEntity,
    EntityType, ConfidenceLevel
)


logger = logging.getLogger(__name__)


class RelayProtectionNineUnified:
    """继电保护九统一标准知识库"""
    
    def __init__(self):
        """初始化继电保护九统一标准知识库"""
        self.standards = {}
        self.rules = {}
        self.requirements = {}
        
        self._initialize_knowledge()
        logger.info("继电保护九统一标准知识库初始化完成")
    
    def _initialize_knowledge(self):
        """初始化知识库内容"""
        self._init_nine_unified_standards()
        self._init_nine_unified_rules()
        self._init_nine_unified_requirements()
    
    def _init_nine_unified_standards(self):
        """初始化九统一标准"""
        
        # Q/GDW 1161-2014 线路保护及辅助装置标准化设计规范
        line_protection_standard = StandardEntity(
            standard_number="Q/GDW 1161-2014",
            standard_title="线路保护及辅助装置标准化设计规范",
            name="线路保护及辅助装置标准化设计规范",
            description="国家电网公司关于线路保护及辅助装置标准化设计的技术规范，实现功能、配置、原理、接口、结构、性能、外观、调试、运维的九统一",
            content="""
            Q/GDW 1161-2014《线路保护及辅助装置标准化设计规范》是国家电网公司为实现继电保护装置的标准化设计而制定的重要技术规范。
            
            九统一原则包括：
            1. 功能配置统一
            2. 端子排布置统一
            3. 原理接线统一
            4. 接口标准统一
            5. 屏柜压板统一
            6. 保护定值统一
            7. 报告格式统一
            8. 图形符号统一
            9. 模型规范统一
            
            主要技术要求：
            - 保护功能配置要求
            - 二次回路设计要求
            - 通信接口要求
            - 人机接口要求
            - 屏柜结构要求
            """,
            source="国家电网公司",
            issuing_organization="国家电网公司",
            effective_date=datetime(2014, 1, 1),
            category="electrical",
            domain="protection",
            scope="线路保护及辅助装置标准化设计",
            confidence=1.0,
            attributes={
                "classification": "Standard",
                "version": "2014版",
                "applicable_equipment": ["线路保护装置", "辅助装置"]
            }
        )
        self.standards["q_gdw_1161_2014"] = line_protection_standard
        
        # Q/GDW 1175-2013 变压器、高压并联电抗器和母线保护及辅助装置标准化设计规范
        transformer_protection_standard = StandardEntity(
            standard_number="Q/GDW 1175-2013",
            standard_title="变压器、高压并联电抗器和母线保护及辅助装置标准化设计规范",
            name="变压器、高压并联电抗器和母线保护及辅助装置标准化设计规范",
            description="国家电网公司关于变压器、高压并联电抗器和母线保护及辅助装置标准化设计的技术规范，实现九统一原则",
            content="""
            Q/GDW 1175-2013《变压器、高压并联电抗器和母线保护及辅助装置标准化设计规范》是国家电网公司为实现变压器、高压并联电抗器和母线保护装置的标准化设计而制定的重要技术规范。
            
            九统一原则包括：
            1. 功能配置统一
            2. 端子排布置统一
            3. 原理接线统一
            4. 接口标准统一
            5. 屏柜压板统一
            6. 保护定值统一
            7. 报告格式统一
            8. 图形符号统一
            9. 模型规范统一
            
            主要技术要求：
            - 变压器保护功能配置要求
            - 高压并联电抗器保护功能配置要求
            - 母线保护功能配置要求
            - 二次回路设计要求
            - 通信接口要求
            """,
            source="国家电网公司",
            issuing_organization="国家电网公司",
            effective_date=datetime(2013, 1, 1),
            category="electrical",
            domain="protection",
            scope="变压器、高压并联电抗器和母线保护及辅助装置标准化设计",
            confidence=1.0,
            attributes={
                "classification": "Standard",
                "version": "2013版",
                "applicable_equipment": ["变压器保护装置", "高压并联电抗器保护装置", "母线保护装置", "辅助装置"]
            }
        )
        self.standards["q_gdw_1175_2013"] = transformer_protection_standard
    
    def _init_nine_unified_rules(self):
        """初始化九统一规则"""
        
        # 功能配置统一规则
        function_config_rule = RuleEntity(
            name="Nine_Unified_Function_Configuration_Rule",
            description="继电保护装置功能配置应符合九统一要求",
            content="""
            继电保护装置功能配置应符合九统一要求：
            
            1. 保护功能配置应标准化
               - 线路保护应配置主保护和后备保护
               - 变压器保护应配置差动保护、后备保护等
               - 母线保护应配置差动保护
               
            2. 保护功能投入/退出应规范
               - 保护功能投退应通过软压板实现
               - 保护功能状态应能远方监视
               
            3. 保护定值管理应统一
               - 保护定值应按统一格式整定
               - 保护定值应支持远方修改
            """,
            source="Q/GDW 1161-2014, Q/GDW 1175-2013",
            rule_type="business",
            severity="high",
            category="protection_configuration",
            condition="继电保护装置功能配置",
            action="检查保护功能配置是否符合九统一要求",
            message_template="继电保护装置功能配置不符合九统一要求: {details}",
            applicable_standards=["Q/GDW 1161-2014", "Q/GDW 1175-2013"],
            applicable_devices=["line_protection", "transformer_protection", "bus_protection"],
            fix_suggestions=[
                "按照标准配置保护功能",
                "规范保护功能投退方式",
                "统一保护定值管理"
            ],
            confidence=0.95,
            keywords=["九统一", "功能配置", "保护配置"]
        )
        self.rules["nine_unified_function_config"] = function_config_rule
        
        # 端子排布置统一规则
        terminal_arrangement_rule = RuleEntity(
            name="Nine_Unified_Terminal_Arrangement_Rule",
            description="继电保护装置端子排布置应符合九统一要求",
            content="""
            继电保护装置端子排布置应符合九统一要求：
            
            1. 端子排分区应规范
               - 交流电流电压端子排
               - 交流电源端子排
               - 直流电源端子排
               - 信号端子排
               - 出口端子排
               
            2. 端子标识应统一
               - 端子标识应清晰明确
               - 端子标识应符合标准要求
               
            3. 端子排设计应便于维护
               - 端子排应留有备用端子
               - 端子排应便于接线和检查
            """,
            source="Q/GDW 1161-2014, Q/GDW 1175-2013",
            rule_type="design",
            severity="medium",
            category="terminal_arrangement",
            condition="继电保护装置端子排设计",
            action="检查端子排布置是否符合九统一要求",
            message_template="继电保护装置端子排布置不符合九统一要求: {details}",
            applicable_standards=["Q/GDW 1161-2014", "Q/GDW 1175-2013"],
            applicable_devices=["protection_device"],
            fix_suggestions=[
                "按照标准分区布置端子排",
                "统一端子标识",
                "优化端子排设计便于维护"
            ],
            confidence=0.9,
            keywords=["九统一", "端子排", "端子布置"]
        )
        self.rules["nine_unified_terminal_arrangement"] = terminal_arrangement_rule
        
        # 原理接线统一规则
        principle_wiring_rule = RuleEntity(
            name="Nine_Unified_Principle_Wiring_Rule",
            description="继电保护装置原理接线应符合九统一要求",
            content="""
            继电保护装置原理接线应符合九统一要求：
            
            1. 二次回路设计应标准化
               - 电流回路设计应符合标准
               - 电压回路设计应符合标准
               - 控制回路设计应符合标准
               
            2. 回路标识应统一
               - 回路编号应符合标准
               - 回路标识应清晰
               
            3. 回路设计应便于调试和维护
               - 回路应有测试点
               - 回路应便于隔离和检查
            """,
            source="Q/GDW 1161-2014, Q/GDW 1175-2013",
            rule_type="design",
            severity="high",
            category="principle_wiring",
            condition="继电保护装置原理接线设计",
            action="检查原理接线是否符合九统一要求",
            message_template="继电保护装置原理接线不符合九统一要求: {details}",
            applicable_standards=["Q/GDW 1161-2014", "Q/GDW 1175-2013"],
            applicable_devices=["protection_device"],
            fix_suggestions=[
                "按照标准设计二次回路",
                "统一回路标识",
                "优化回路设计便于调试维护"
            ],
            confidence=0.9,
            keywords=["九统一", "原理接线", "二次回路"]
        )
        self.rules["nine_unified_principle_wiring"] = principle_wiring_rule
    
    def _init_nine_unified_requirements(self):
        """初始化九统一要求"""
        
        # 功能配置统一要求
        function_config_req = RequirementEntity(
            name="Function_Configuration_Unified_Requirement",
            description="继电保护装置功能配置统一技术要求",
            content="""
            继电保护装置功能配置统一技术要求：
            
            1. 保护功能配置要求
               - 线路保护应配置纵联保护、距离保护、零序保护等
               - 变压器保护应配置差动保护、后备保护等
               - 母线保护应配置差动保护
               
            2. 保护功能投退要求
               - 保护功能投退应通过软压板实现
               - 保护功能状态应能远方监视和操作
               
            3. 保护定值管理要求
               - 保护定值应按统一格式整定
               - 保护定值应支持远方修改和固化
            """,
            source="Q/GDW 1161-2014, Q/GDW 1175-2013",
            requirement_type="design",
            priority="high",
            mandatory=True,
            verification_method="检查和测试",
            acceptance_criteria="满足九统一功能配置要求",
            source_standard="Q/GDW 1161-2014, Q/GDW 1175-2013",
            clause_reference="功能配置统一章节",
            confidence=0.95,
            attributes={
                "application_scope": "继电保护装置",
                "verification_method": "设计审查+现场检查"
            }
        )
        self.requirements["function_config_unified"] = function_config_req
        
        # 接口标准统一要求
        interface_standard_req = RequirementEntity(
            name="Interface_Standard_Unified_Requirement",
            description="继电保护装置接口标准统一技术要求",
            content="""
            继电保护装置接口标准统一技术要求：
            
            1. 通信接口要求
               - 应支持IEC 61850通信协议
               - 应支持GOOSE和SV通信
               - 应支持MMS服务
               
            2. 人机接口要求
               - 应有统一的人机界面
               - 应支持远方操作和监视
               - 应有统一的操作习惯
               
            3. 硬件接口要求
               - 应有统一的插件结构
               - 应有统一的连接器规格
               - 应有统一的指示灯布置
            """,
            source="Q/GDW 1161-2014, Q/GDW 1175-2013",
            requirement_type="design",
            priority="high",
            mandatory=True,
            verification_method="检查和测试",
            acceptance_criteria="满足九统一接口标准要求",
            source_standard="Q/GDW 1161-2014, Q/GDW 1175-2013",
            clause_reference="接口标准统一章节",
            confidence=0.95,
            attributes={
                "application_scope": "继电保护装置",
                "verification_method": "设计审查+型式试验"
            }
        )
        self.requirements["interface_standard_unified"] = interface_standard_req
    
    def get_all_standards(self) -> List[StandardEntity]:
        """获取所有标准"""
        return list(self.standards.values())
    
    def get_all_rules(self) -> List[RuleEntity]:
        """获取所有规则"""
        return list(self.rules.values())
    
    def get_all_requirements(self) -> List[RequirementEntity]:
        """获取所有技术要求"""
        return list(self.requirements.values())
    
    def get_rules_for_standard(self, standard_id: str) -> List[RuleEntity]:
        """获取特定标准的相关规则"""
        rules = []
        for rule in self.rules.values():
            if standard_id in rule.applicable_standards:
                rules.append(rule)
        return rules
    
    def get_applicable_rules(self, device_type: str = None, category: str = None) -> List[RuleEntity]:
        """获取适用的规则"""
        applicable_rules = []
        
        for rule in self.rules.values():
            # 检查设备类型
            if device_type and rule.applicable_devices:
                if device_type not in rule.applicable_devices:
                    continue
            
            # 检查类别
            if category and rule.category:
                if category != rule.category:
                    continue
            
            applicable_rules.append(rule)
        
        return applicable_rules