"""
关于对话框
显示应用程序信息和版本详情
"""

import logging
from datetime import datetime

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QTabWidget, QWidget, QDialogButtonBox, QScrollArea
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap, QFont

logger = logging.getLogger(__name__)


class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        """初始化关于对话框"""
        super().__init__(parent)
        
        self._init_ui()
        
        logger.debug("关于对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("关于 IEC61850智能设计检查器")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 应用程序图标和标题
        header_layout = QHBoxLayout()
        
        # 图标
        icon_label = QLabel()
        try:
            pixmap = QPixmap("resources/icons/app_icon.png")
            if not pixmap.isNull():
                icon_label.setPixmap(pixmap.scaled(64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            else:
                icon_label.setText("📋")
                icon_label.setStyleSheet("font-size: 48px;")
        except:
            icon_label.setText("📋")
            icon_label.setStyleSheet("font-size: 48px;")
        
        icon_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(icon_label)
        
        # 标题信息
        title_layout = QVBoxLayout()
        
        app_name_label = QLabel("IEC61850智能设计检查器")
        app_name_font = QFont()
        app_name_font.setPointSize(16)
        app_name_font.setBold(True)
        app_name_label.setFont(app_name_font)
        title_layout.addWidget(app_name_label)
        
        version_label = QLabel("版本 1.0.0")
        version_label.setStyleSheet("color: #666666;")
        title_layout.addWidget(version_label)
        
        build_date_label = QLabel(f"构建日期: {datetime.now().strftime('%Y-%m-%d')}")
        build_date_label.setStyleSheet("color: #666666; font-size: 10px;")
        title_layout.addWidget(build_date_label)
        
        title_layout.addStretch()
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # 标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 关于标签页
        about_tab = self._create_about_tab()
        tab_widget.addTab(about_tab, "关于")
        
        # 功能特性标签页
        features_tab = self._create_features_tab()
        tab_widget.addTab(features_tab, "功能特性")
        
        # 技术信息标签页
        tech_tab = self._create_tech_tab()
        tab_widget.addTab(tech_tab, "技术信息")
        
        # 许可证标签页
        license_tab = self._create_license_tab()
        tab_widget.addTab(license_tab, "许可证")
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)
    
    def _create_about_tab(self) -> QWidget:
        """创建关于标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        about_text = QTextEdit()
        about_text.setReadOnly(True)
        about_text.setHtml("""
        <h3>IEC61850智能设计检查器</h3>
        <p>一个专业的IEC61850标准配置文件验证和分析工具，集成了人工智能和知识库技术。</p>
        
        <h4>主要功能：</h4>
        <ul>
            <li>SCD/ICD/CID文件解析和验证</li>
            <li>智能规则推理和验证</li>
            <li>网络拓扑可视化</li>
            <li>设备配置分析</li>
            <li>虚端子表生成</li>
            <li>文件对比分析</li>
            <li>知识库管理</li>
        </ul>
        
        <h4>开发团队：</h4>
        <p>本软件由电力系统自动化专家团队开发，致力于提升IEC61850工程设计的质量和效率。</p>
        
        <h4>联系方式：</h4>
        <p>
            邮箱: <EMAIL><br>
            网站: https://www.iec61850checker.com<br>
            技术支持: https://support.iec61850checker.com
        </p>
        """)
        
        layout.addWidget(about_text)
        return widget
    
    def _create_features_tab(self) -> QWidget:
        """创建功能特性标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        features_text = QTextEdit()
        features_text.setReadOnly(True)
        features_text.setHtml("""
        <h3>核心功能特性</h3>
        
        <h4>🔍 智能验证引擎</h4>
        <ul>
            <li>基于IEC61850标准的全面验证</li>
            <li>语法、语义、业务规则三层验证</li>
            <li>智能推理和规则推荐</li>
            <li>自适应验证规则生成</li>
        </ul>
        
        <h4>📊 可视化分析</h4>
        <ul>
            <li>网络拓扑图形化显示</li>
            <li>设备配置树状结构</li>
            <li>验证结果统计图表</li>
            <li>交互式数据探索</li>
        </ul>
        
        <h4>🧠 知识库系统</h4>
        <ul>
            <li>内置IEC61850系列标准知识</li>
            <li>中国国家和行业标准集成</li>
            <li>专家经验知识库</li>
            <li>持续学习和更新机制</li>
        </ul>
        
        <h4>⚡ 高级功能</h4>
        <ul>
            <li>文件对比和差异分析</li>
            <li>虚端子表自动生成</li>
            <li>SCD文件智能生成</li>
            <li>互操作性检查</li>
            <li>批量文件处理</li>
        </ul>
        
        <h4>🎨 用户体验</h4>
        <ul>
            <li>现代化桌面界面</li>
            <li>多主题支持</li>
            <li>可定制的工作空间</li>
            <li>丰富的快捷键支持</li>
        </ul>
        """)
        
        layout.addWidget(features_text)
        return widget
    
    def _create_tech_tab(self) -> QWidget:
        """创建技术信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        tech_text = QTextEdit()
        tech_text.setReadOnly(True)
        tech_text.setHtml("""
        <h3>技术架构信息</h3>
        
        <h4>🏗️ 系统架构</h4>
        <ul>
            <li><strong>编程语言:</strong> Python 3.8+</li>
            <li><strong>GUI框架:</strong> PySide6 (Qt6)</li>
            <li><strong>数据库:</strong> SQLite</li>
            <li><strong>图形库:</strong> NetworkX</li>
            <li><strong>XML解析:</strong> lxml</li>
        </ul>
        
        <h4>📦 核心模块</h4>
        <ul>
            <li><strong>解析引擎:</strong> 支持SCD/ICD/CID文件解析</li>
            <li><strong>验证引擎:</strong> 多层次规则验证系统</li>
            <li><strong>知识库:</strong> 基于图数据库的知识管理</li>
            <li><strong>推理引擎:</strong> 智能规则推理和推荐</li>
            <li><strong>可视化:</strong> 交互式图形界面</li>
        </ul>
        
        <h4>🔧 支持的标准</h4>
        <ul>
            <li><strong>IEC 61850系列:</strong> 1, 6, 7-1, 7-4, 8-1, 9-2等</li>
            <li><strong>GB国家标准:</strong> GB/T 51072, GB/T 14285等</li>
            <li><strong>DL行业标准:</strong> DL/T 5136, DL/T 5218等</li>
            <li><strong>文件格式:</strong> SCD, ICD, CID, SCL</li>
        </ul>
        
        <h4>⚙️ 系统要求</h4>
        <ul>
            <li><strong>操作系统:</strong> Windows 10+, macOS 10.15+, Linux</li>
            <li><strong>内存:</strong> 最低4GB，推荐8GB</li>
            <li><strong>存储:</strong> 500MB可用空间</li>
            <li><strong>显示:</strong> 1280x720分辨率以上</li>
        </ul>
        
        <h4>🔄 版本历史</h4>
        <ul>
            <li><strong>v1.0.0:</strong> 初始发布版本</li>
            <li><strong>特性:</strong> 完整的验证和分析功能</li>
            <li><strong>发布日期:</strong> 2025年8月</li>
        </ul>
        """)
        
        layout.addWidget(tech_text)
        return widget
    
    def _create_license_tab(self) -> QWidget:
        """创建许可证标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        license_text = QTextEdit()
        license_text.setReadOnly(True)
        license_text.setPlainText("""
IEC61850智能设计检查器 软件许可协议

版权所有 (c) 2025 IEC61850检查器开发团队

本软件及其相关文档文件（以下简称"软件"）的使用受以下条款约束：

1. 许可授权
   本许可证授予您以下权利：
   - 在单台计算机上安装和使用本软件
   - 为备份目的制作软件副本
   - 在遵守本协议的前提下使用软件的所有功能

2. 使用限制
   您不得：
   - 对软件进行反向工程、反编译或反汇编
   - 修改、改编或创建基于软件的衍生作品
   - 分发、出租、租赁或转让软件
   - 移除或修改软件中的版权声明

3. 知识产权
   软件及其所有组件的知识产权归开发团队所有。
   本许可证不授予您任何商标、版权或专利权利。

4. 免责声明
   软件按"现状"提供，不提供任何明示或暗示的保证。
   开发团队不对因使用软件而产生的任何损害承担责任。

5. 技术支持
   我们提供有限的技术支持服务。
   详细信息请访问我们的支持网站。

6. 协议终止
   如果您违反本协议的任何条款，许可证将自动终止。
   协议终止后，您必须停止使用软件并删除所有副本。

7. 适用法律
   本协议受中华人民共和国法律管辖。

如果您不同意本协议的条款，请不要安装或使用本软件。

联系信息：
邮箱: <EMAIL>
地址: 中国北京市
        """)
        
        layout.addWidget(license_text)
        return widget
