"""
规范检查规则引擎
实现二次图纸的规范检查规则
"""

import logging
from typing import Dict, List, Any, Optional, Callable
from abc import ABC, abstractmethod

from .drawing_models import (
    DrawingDocument, DrawingElement, Line, Arc, Circle, Text, Dimension, Block,
    Point, ElementType, LineType, Color, ReviewIssue, ReviewSeverity
)
from .standards_knowledge import StandardsKnowledgeBase, StandardRule
from .drawing_analyzer import DrawingAnalyzer

logger = logging.getLogger(__name__)


class ComplianceRule(ABC):
    """规范检查规则基类"""
    
    def __init__(self, rule_id: str, standard_rule: StandardRule):
        """初始化规则"""
        self.rule_id = rule_id
        self.standard_rule = standard_rule
        self.knowledge_base = None
    
    @abstractmethod
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """执行规则检查"""
        pass
    
    def create_issue(self, 
                    severity: ReviewSeverity,
                    title: str,
                    description: str,
                    location: Optional[Point] = None,
                    affected_elements: List[str] = None,
                    suggestion: str = "",
                    auto_fixable: bool = False) -> ReviewIssue:
        """创建审查问题"""
        return ReviewIssue(
            severity=severity,
            category=self.standard_rule.category,
            rule_code=self.standard_rule.rule_code,
            title=title,
            description=description,
            location=location,
            affected_elements=affected_elements or [],
            suggestion=suggestion,
            auto_fixable=auto_fixable,
            standard_reference=self.standard_rule.standard_number,
            standard_clause=self.standard_rule.clause
        )


class LineTypeComplianceRule(ComplianceRule):
    """线型规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查线型规范"""
        issues = []
        
        for element in drawing.elements:
            if isinstance(element, Line):
                # 检查线型是否符合用途
                layer_name = element.layer
                expected_line_type = self._get_expected_line_type(layer_name)
                
                if expected_line_type and element.line_type != expected_line_type:
                    issue = self.create_issue(
                        severity=ReviewSeverity.WARNING,
                        title="线型不符合规范",
                        description=f"图层 '{layer_name}' 上的线型应为 {expected_line_type.value}，当前为 {element.line_type.value}",
                        affected_elements=[element.element_id],
                        suggestion=f"将线型修改为 {expected_line_type.value}",
                        auto_fixable=True
                    )
                    issues.append(issue)
        
        return issues
    
    def _get_expected_line_type(self, layer_name: str) -> Optional[LineType]:
        """获取图层期望的线型"""
        layer_line_type_map = {
            'CENTER': LineType.CENTER,
            'HIDDEN': LineType.DASHED,
            'OUTLINE': LineType.CONTINUOUS,
            'DIMENSION': LineType.CONTINUOUS
        }
        return layer_line_type_map.get(layer_name.upper())


class LineWidthComplianceRule(ComplianceRule):
    """线宽规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查线宽规范"""
        issues = []
        
        # 获取标准线宽
        standard_widths = self.standard_rule.parameters
        thick_width = standard_widths.get('thick_line_width', 0.7)
        medium_width = standard_widths.get('medium_line_width', 0.35)
        thin_width = standard_widths.get('thin_line_width', 0.18)
        
        for element in drawing.elements:
            if hasattr(element, 'line_weight'):
                current_width = element.line_weight
                expected_width = self._get_expected_line_width(element.layer)
                
                if expected_width and abs(current_width - expected_width) > 0.05:
                    issue = self.create_issue(
                        severity=ReviewSeverity.WARNING,
                        title="线宽不符合规范",
                        description=f"线宽 {current_width}mm 不符合标准，应为 {expected_width}mm",
                        affected_elements=[element.element_id],
                        suggestion=f"调整线宽为 {expected_width}mm",
                        auto_fixable=True
                    )
                    issues.append(issue)
        
        return issues
    
    def _get_expected_line_width(self, layer_name: str) -> Optional[float]:
        """获取图层期望的线宽"""
        layer_width_map = {
            'OUTLINE': 0.7,
            'CENTER': 0.35,
            'HIDDEN': 0.35,
            'DIMENSION': 0.18,
            'TEXT': 0.18
        }
        return layer_width_map.get(layer_name.upper())


class TextStandardComplianceRule(ComplianceRule):
    """文字标准规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查文字标准规范"""
        issues = []
        
        text_elements = drawing.get_text_elements()
        standard_heights = self.standard_rule.parameters.get('standard_heights', [2.5, 3.5, 5.0, 7.0, 10.0])
        min_height = self.standard_rule.parameters.get('min_height', 2.5)
        max_height = self.standard_rule.parameters.get('max_height', 10.0)
        
        for text in text_elements:
            # 检查文字高度
            if text.height < min_height:
                issue = self.create_issue(
                    severity=ReviewSeverity.ERROR,
                    title="文字高度过小",
                    description=f"文字高度 {text.height}mm 小于最小标准 {min_height}mm",
                    location=text.position,
                    affected_elements=[text.element_id],
                    suggestion=f"调整文字高度为 {min_height}mm 或以上",
                    auto_fixable=True
                )
                issues.append(issue)
            
            elif text.height > max_height:
                issue = self.create_issue(
                    severity=ReviewSeverity.WARNING,
                    title="文字高度过大",
                    description=f"文字高度 {text.height}mm 大于最大标准 {max_height}mm",
                    location=text.position,
                    affected_elements=[text.element_id],
                    suggestion=f"调整文字高度为 {max_height}mm 或以下",
                    auto_fixable=True
                )
                issues.append(issue)
            
            elif text.height not in standard_heights:
                # 找到最接近的标准高度
                closest_height = min(standard_heights, key=lambda x: abs(x - text.height))
                if abs(text.height - closest_height) > 0.5:
                    issue = self.create_issue(
                        severity=ReviewSeverity.INFO,
                        title="建议使用标准文字高度",
                        description=f"当前文字高度 {text.height}mm，建议使用标准高度 {closest_height}mm",
                        location=text.position,
                        affected_elements=[text.element_id],
                        suggestion=f"调整文字高度为 {closest_height}mm",
                        auto_fixable=True
                    )
                    issues.append(issue)
            
            # 检查空白文字
            if not text.content.strip():
                issue = self.create_issue(
                    severity=ReviewSeverity.WARNING,
                    title="空白文字",
                    description="发现空白文字对象",
                    location=text.position,
                    affected_elements=[text.element_id],
                    suggestion="删除空白文字或添加内容",
                    auto_fixable=True
                )
                issues.append(issue)
        
        return issues


class LayerStandardComplianceRule(ComplianceRule):
    """图层标准规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查图层标准规范"""
        issues = []
        
        # 检查图层命名规范
        for layer_name, layer in drawing.layers.items():
            if layer_name != "0" and not self._is_valid_layer_name(layer_name):
                issue = self.create_issue(
                    severity=ReviewSeverity.WARNING,
                    title="图层命名不规范",
                    description=f"图层名称 '{layer_name}' 不符合命名规范",
                    suggestion="使用大写字母和数字组合，如 'OUTLINE', 'CENTER', 'TEXT' 等",
                    auto_fixable=False
                )
                issues.append(issue)
            
            # 检查空图层
            elements_on_layer = drawing.get_elements_by_layer(layer_name)
            if not elements_on_layer and layer_name != "0":
                issue = self.create_issue(
                    severity=ReviewSeverity.INFO,
                    title="空图层",
                    description=f"图层 '{layer_name}' 没有任何图元",
                    suggestion="删除空图层或将相关图元移至该图层",
                    auto_fixable=True
                )
                issues.append(issue)
        
        # 检查是否使用了推荐的标准图层
        recommended_layers = ['OUTLINE', 'CENTER', 'HIDDEN', 'DIMENSION', 'TEXT', 'ELECTRICAL']
        used_layers = set(drawing.layers.keys())
        missing_layers = set(recommended_layers) - used_layers
        
        if missing_layers:
            issue = self.create_issue(
                severity=ReviewSeverity.INFO,
                title="建议使用标准图层",
                description=f"建议创建标准图层: {', '.join(missing_layers)}",
                suggestion="按照图元类型创建相应的标准图层",
                auto_fixable=False
            )
            issues.append(issue)
        
        return issues
    
    def _is_valid_layer_name(self, layer_name: str) -> bool:
        """检查图层名称是否有效"""
        import re
        # 图层名称应为大写字母、数字和下划线组合
        return re.match(r'^[A-Z][A-Z0-9_]*$', layer_name) is not None


class DimensionStandardComplianceRule(ComplianceRule):
    """尺寸标注标准规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查尺寸标注标准规范"""
        issues = []
        
        dimension_elements = drawing.get_dimension_elements()
        
        for dim in dimension_elements:
            # 检查尺寸标注是否有测量点
            if len(dim.measurement_points) < 2:
                issue = self.create_issue(
                    severity=ReviewSeverity.ERROR,
                    title="尺寸标注缺少测量点",
                    description="尺寸标注必须有至少两个测量点",
                    affected_elements=[dim.element_id],
                    suggestion="添加正确的测量点",
                    auto_fixable=False
                )
                issues.append(issue)
            
            # 检查尺寸标注图层
            if dim.layer != "DIMENSION":
                issue = self.create_issue(
                    severity=ReviewSeverity.WARNING,
                    title="尺寸标注图层不正确",
                    description=f"尺寸标注应在 'DIMENSION' 图层，当前在 '{dim.layer}' 图层",
                    affected_elements=[dim.element_id],
                    suggestion="将尺寸标注移至 'DIMENSION' 图层",
                    auto_fixable=True
                )
                issues.append(issue)
        
        return issues


class ElectricalSymbolComplianceRule(ComplianceRule):
    """电气符号规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查电气符号规范"""
        issues = []
        
        symbol_analysis = analysis.get('symbol_analysis', {})
        unknown_symbols = symbol_analysis.get('unknown_symbols', [])
        
        # 检查未识别的符号
        for symbol_name in unknown_symbols:
            issue = self.create_issue(
                severity=ReviewSeverity.WARNING,
                title="未识别的电气符号",
                description=f"符号 '{symbol_name}' 可能不是标准电气符号",
                suggestion="使用标准电气符号库中的符号",
                auto_fixable=False
            )
            issues.append(issue)
        
        return issues


class ConnectionComplianceRule(ComplianceRule):
    """连接规范检查规则"""
    
    def check(self, drawing: DrawingDocument, analysis: Dict[str, Any]) -> List[ReviewIssue]:
        """检查连接规范"""
        issues = []
        
        connection_analysis = analysis.get('connection_analysis', {})
        connection_issues = connection_analysis.get('connection_issues', [])
        
        # 转换连接问题为审查问题
        for conn_issue in connection_issues:
            if "悬空" in conn_issue:
                issue = self.create_issue(
                    severity=ReviewSeverity.ERROR,
                    title="线段悬空",
                    description=conn_issue,
                    suggestion="确保所有线段都正确连接",
                    auto_fixable=False
                )
                issues.append(issue)
        
        return issues


class ComplianceChecker:
    """规范检查器"""
    
    def __init__(self):
        """初始化规范检查器"""
        self.knowledge_base = StandardsKnowledgeBase()
        self.analyzer = DrawingAnalyzer()
        self.rules = {}
        
        # 注册检查规则
        self._register_rules()
        
        logger.info(f"规范检查器初始化完成，注册 {len(self.rules)} 个检查规则")
    
    def _register_rules(self):
        """注册检查规则"""
        # 获取所有标准规则
        standard_rules = self.knowledge_base.get_all_rules()
        
        for standard_rule in standard_rules:
            rule_class = self._get_rule_class(standard_rule.check_function)
            if rule_class:
                compliance_rule = rule_class(standard_rule.rule_id, standard_rule)
                compliance_rule.knowledge_base = self.knowledge_base
                self.rules[standard_rule.rule_id] = compliance_rule
    
    def _get_rule_class(self, check_function: str) -> Optional[type]:
        """根据检查函数名获取规则类"""
        rule_class_map = {
            'check_line_type_compliance': LineTypeComplianceRule,
            'check_line_width_compliance': LineWidthComplianceRule,
            'check_text_annotation_standard': TextStandardComplianceRule,
            'check_layer_standard': LayerStandardComplianceRule,
            'check_dimension_standard': DimensionStandardComplianceRule,
            'check_equipment_symbol_standard': ElectricalSymbolComplianceRule,
            'check_connection_standard': ConnectionComplianceRule
        }
        return rule_class_map.get(check_function)
    
    def check_compliance(self, drawing: DrawingDocument) -> List[ReviewIssue]:
        """执行规范检查"""
        try:
            logger.info(f"开始规范检查: {drawing.file_path}")
            
            # 首先分析图纸
            analysis = self.analyzer.analyze_drawing(drawing)
            
            # 执行所有规则检查
            all_issues = []
            
            for rule_id, rule in self.rules.items():
                try:
                    issues = rule.check(drawing, analysis)
                    all_issues.extend(issues)
                    logger.debug(f"规则 {rule_id} 检查完成，发现 {len(issues)} 个问题")
                except Exception as e:
                    logger.error(f"规则 {rule_id} 检查失败: {e}")
            
            logger.info(f"规范检查完成，共发现 {len(all_issues)} 个问题")
            return all_issues
            
        except Exception as e:
            logger.error(f"规范检查失败: {e}")
            return []
    
    def check_specific_rules(self, drawing: DrawingDocument, rule_ids: List[str]) -> List[ReviewIssue]:
        """检查指定规则"""
        analysis = self.analyzer.analyze_drawing(drawing)
        issues = []
        
        for rule_id in rule_ids:
            if rule_id in self.rules:
                rule_issues = self.rules[rule_id].check(drawing, analysis)
                issues.extend(rule_issues)
        
        return issues
    
    def get_available_rules(self) -> List[Dict[str, Any]]:
        """获取可用的检查规则"""
        rules_info = []
        
        for rule_id, rule in self.rules.items():
            rule_info = {
                'rule_id': rule_id,
                'title': rule.standard_rule.title,
                'description': rule.standard_rule.description,
                'category': rule.standard_rule.category,
                'severity': rule.standard_rule.severity.value,
                'standard': rule.standard_rule.standard_number,
                'auto_fixable': rule.standard_rule.auto_fixable
            }
            rules_info.append(rule_info)
        
        return rules_info
    
    def get_rules_by_category(self, category: str) -> List[str]:
        """按分类获取规则ID"""
        return [
            rule_id for rule_id, rule in self.rules.items()
            if rule.standard_rule.category == category
        ]
