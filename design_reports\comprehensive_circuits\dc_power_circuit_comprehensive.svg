<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .dc-positive { stroke: red; stroke-width: 4; fill: none; }
            .dc-negative { stroke: blue; stroke-width: 4; fill: none; }
            .dc-ground { stroke: green; stroke-width: 2; fill: none; }
            .ac-line { stroke: orange; stroke-width: 2; fill: none; }
        </style>
    </defs>

    <!-- 背景 -->
    <rect width="1200" height="900" fill="#f8f9fa" stroke="#ddd"/>

    <!-- 标题 -->
    <text x="600" y="30" text-anchor="middle" class="title-text">直流操作电源系统</text>
    <text x="600" y="50" text-anchor="middle" font-size="12" fill="#666">变电站二次回路的"心脏" - DC 220V/110V系统</text>

    <!-- 交流输入 -->
    <g transform="translate(50, 100)">
        <text x="0" y="0" font-size="12" font-weight="bold">交流输入电源:</text>
        <rect x="0" y="20" width="80" height="60" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="40" y="45" text-anchor="middle" font-size="10" font-weight="bold">AC 380V</text>
        <text x="40" y="60" text-anchor="middle" font-size="8">站用变</text>

        <!-- 输出端子 -->
        <circle cx="85" cy="35" r="2" fill="orange"/>
        <text x="90" y="30" font-size="8">L1</text>
        <circle cx="85" cy="50" r="2" fill="orange"/>
        <text x="90" y="45" font-size="8">L2</text>
        <circle cx="85" cy="65" r="2" fill="orange"/>
        <text x="90" y="60" font-size="8">L3</text>
    </g>

    <!-- 充电装置 -->
    <g transform="translate(250, 100)">
        <rect x="0" y="20" width="120" height="80" fill="lightsteelblue" stroke="black" stroke-width="2"/>
        <text x="60" y="40" text-anchor="middle" font-size="12" font-weight="bold">充电装置</text>
        <text x="60" y="55" text-anchor="middle" font-size="8">整流器/开关电源</text>
        <text x="60" y="70" text-anchor="middle" font-size="8">AC→DC转换</text>
        <text x="60" y="85" text-anchor="middle" font-size="8">稳压/限流</text>

        <!-- 输入端子 -->
        <circle cx="-5" cy="35" r="2" fill="orange"/>
        <circle cx="-5" cy="50" r="2" fill="orange"/>
        <circle cx="-5" cy="65" r="2" fill="orange"/>

        <!-- 输出端子 -->
        <circle cx="125" cy="40" r="2" fill="red"/>
        <text x="130" y="35" font-size="8">+220V</text>
        <circle cx="125" cy="60" r="2" fill="blue"/>
        <text x="130" y="55" font-size="8">-220V</text>
        <circle cx="125" cy="80" r="2" fill="green"/>
        <text x="130" y="75" font-size="8">地</text>
    </g>

    <!-- 蓄电池组 -->
    <g transform="translate(500, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">蓄电池组:</text>

        <!-- 电池组1 -->
        <rect x="0" y="20" width="100" height="40" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="50" y="35" text-anchor="middle" font-size="10" font-weight="bold">蓄电池组I</text>
        <text x="50" y="50" text-anchor="middle" font-size="8">DC 220V</text>

        <!-- 电池组2 -->
        <rect x="0" y="80" width="100" height="40" fill="lightblue" stroke="black" stroke-width="2"/>
        <text x="50" y="95" text-anchor="middle" font-size="10" font-weight="bold">蓄电池组II</text>
        <text x="50" y="110" text-anchor="middle" font-size="8">DC 220V</text>

        <!-- 端子 -->
        <circle cx="105" cy="30" r="2" fill="red"/>
        <text x="110" y="25" font-size="8">+</text>
        <circle cx="105" cy="50" r="2" fill="blue"/>
        <text x="110" y="45" font-size="8">-</text>

        <circle cx="105" cy="90" r="2" fill="red"/>
        <text x="110" y="85" font-size="8">+</text>
        <circle cx="105" cy="110" r="2" fill="blue"/>
        <text x="110" y="105" font-size="8">-</text>
    </g>

    <!-- 直流母线 -->
    <g transform="translate(50, 250)">
        <text x="0" y="0" font-size="12" font-weight="bold">直流母线系统:</text>

        <!-- +KM母线 -->
        <line x1="0" y1="30" x2="1000" y2="30" class="dc-positive"/>
        <text x="10" y="25" font-size="10" font-weight="bold" fill="red">+KM (+220V)</text>

        <!-- -KM母线 -->
        <line x1="0" y1="80" x2="1000" y2="80" class="dc-negative"/>
        <text x="10" y="75" font-size="10" font-weight="bold" fill="blue">-KM (-220V)</text>

        <!-- 接地母线 -->
        <line x1="0" y1="130" x2="1000" y2="130" class="dc-ground"/>
        <text x="10" y="125" font-size="10" font-weight="bold" fill="green">接地母线</text>
    </g>

    <!-- 直流馈线回路 -->
    <g transform="translate(100, 450)">
        <text x="0" y="0" font-size="12" font-weight="bold">直流馈线回路:</text>

        <!-- 控制回路馈线 -->
        <rect x="0" y="30" width="80" height="40" fill="lightgreen" stroke="black" stroke-width="2"/>
        <text x="40" y="45" text-anchor="middle" font-size="8">控制回路</text>
        <text x="40" y="60" text-anchor="middle" font-size="8">DC 220V</text>

        <!-- 保护回路馈线 -->
        <rect x="120" y="30" width="80" height="40" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="160" y="45" text-anchor="middle" font-size="8">保护回路</text>
        <text x="160" y="60" text-anchor="middle" font-size="8">DC 220V</text>

        <!-- 信号回路馈线 -->
        <rect x="240" y="30" width="80" height="40" fill="lightcyan" stroke="black" stroke-width="2"/>
        <text x="280" y="45" text-anchor="middle" font-size="8">信号回路</text>
        <text x="280" y="60" text-anchor="middle" font-size="8">DC 220V</text>

        <!-- 通信回路馈线 -->
        <rect x="360" y="30" width="80" height="40" fill="lavender" stroke="black" stroke-width="2"/>
        <text x="400" y="45" text-anchor="middle" font-size="8">通信回路</text>
        <text x="400" y="60" text-anchor="middle" font-size="8">DC 48V</text>

        <!-- 连接到母线 -->
        <line x1="40" y1="30" x2="40" y2="280" class="dc-positive"/>
        <line x1="160" y1="30" x2="160" y2="280" class="dc-positive"/>
        <line x1="280" y1="30" x2="280" y2="280" class="dc-positive"/>
        <line x1="400" y1="30" x2="400" y2="280" class="dc-positive"/>
    </g>

    <!-- 绝缘监测 -->
    <g transform="translate(700, 450)">
        <rect x="0" y="30" width="100" height="60" fill="lightpink" stroke="black" stroke-width="2"/>
        <text x="50" y="50" text-anchor="middle" font-size="10" font-weight="bold">绝缘监测</text>
        <text x="50" y="65" text-anchor="middle" font-size="8">接地检测</text>
        <text x="50" y="80" text-anchor="middle" font-size="8">报警装置</text>
    </g>

    <!-- 连接线 -->
    <!-- 交流到充电装置 -->
    <line x1="135" y1="135" x2="245" y2="135" class="ac-line"/>
    <line x1="135" y1="150" x2="245" y2="150" class="ac-line"/>
    <line x1="135" y1="165" x2="245" y2="165" class="ac-line"/>

    <!-- 充电装置到母线 -->
    <line x1="375" y1="140" x2="450" y2="140" class="dc-positive"/>
    <line x1="450" y1="140" x2="450" y2="280" class="dc-positive"/>
    <line x1="375" y1="160" x2="500" y2="160" class="dc-negative"/>
    <line x1="500" y1="160" x2="500" y2="330" class="dc-negative"/>

    <!-- 蓄电池到母线 -->
    <line x1="605" y1="110" x2="650" y2="110" class="dc-positive"/>
    <line x1="650" y1="110" x2="650" y2="280" class="dc-positive"/>
    <line x1="605" y1="130" x2="700" y2="130" class="dc-negative"/>
    <line x1="700" y1="130" x2="700" y2="330" class="dc-negative"/>

    <!-- 技术说明 -->
    <g transform="translate(50, 600)">
        <text x="0" y="0" font-size="14" font-weight="bold">直流电源系统技术要点:</text>

        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">系统配置:</text>
            <text x="0" y="20" font-size="10">• 双系统设计：I系统、II系统互为备用</text>
            <text x="0" y="35" font-size="10">• 电压等级：DC 220V(主)、110V(辅助)、48V(通信)</text>
            <text x="0" y="50" font-size="10">• 蓄电池：阀控式密封铅酸蓄电池</text>
            <text x="0" y="65" font-size="10">• 容量配置：满足2小时事故放电要求</text>
            <text x="0" y="80" font-size="10">• 充电方式：浮充电+均充电</text>
        </g>

        <g transform="translate(400, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">保护功能:</text>
            <text x="0" y="20" font-size="10">• 过压保护：防止过充电</text>
            <text x="0" y="35" font-size="10">• 欠压保护：低压告警和切负荷</text>
            <text x="0" y="50" font-size="10">• 绝缘监测：实时监测对地绝缘</text>
            <text x="0" y="65" font-size="10">• 接地选线：快速定位接地故障</text>
            <text x="0" y="80" font-size="10">• 温度监测：电池温度补偿</text>
        </g>

        <g transform="translate(800, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">重要性:</text>
            <text x="0" y="20" font-size="10">• 二次系统的"心脏"</text>
            <text x="0" y="35" font-size="10">• 保证控制保护可靠性</text>
            <text x="0" y="50" font-size="10">• 事故时独立供电</text>
            <text x="0" y="65" font-size="10">• 不间断电源特性</text>
            <text x="0" y="80" font-size="10">• 系统安全运行基础</text>
        </g>
    </g>

</svg>