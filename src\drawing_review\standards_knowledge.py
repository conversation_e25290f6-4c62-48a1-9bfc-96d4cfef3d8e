"""
二次图纸设计规范知识库
包含GB/T、DL/T等标准中的图纸设计规范
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .drawing_models import Color, LineType, ReviewSeverity

logger = logging.getLogger(__name__)


class StandardType(Enum):
    """标准类型"""
    GB_T = "GB/T"      # 国家标准
    DL_T = "DL/T"      # 电力行业标准
    IEC = "IEC"        # 国际电工委员会标准
    IEEE = "IEEE"      # 电气电子工程师学会标准
    COMPANY = "企业标准"


@dataclass
class StandardRule:
    """标准规则"""
    rule_id: str
    rule_code: str
    title: str
    description: str
    standard_type: StandardType
    standard_number: str
    clause: str
    severity: ReviewSeverity = ReviewSeverity.WARNING
    category: str = ""
    
    # 规则参数
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 检查函数名
    check_function: str = ""
    
    # 修改建议
    suggestion_template: str = ""
    auto_fixable: bool = False


class StandardsKnowledgeBase:
    """二次图纸设计规范知识库"""
    
    def __init__(self):
        """初始化知识库"""
        self.rules = {}
        self.layer_standards = {}
        self.line_type_standards = {}
        self.color_standards = {}
        self.text_standards = {}
        self.dimension_standards = {}
        
        # 加载标准规范
        self._load_gb_standards()
        self._load_dl_standards()
        self._load_drawing_standards()
        
        logger.info(f"标准知识库初始化完成，加载 {len(self.rules)} 条规则")
    
    def _load_gb_standards(self):
        """加载GB/T国家标准"""
        
        # GB/T 4457.4-2002 机械制图 图样画法 图线
        self.add_rule(StandardRule(
            rule_id="GB_T_4457_4_001",
            rule_code="LINE_TYPE_STANDARD",
            title="图线类型标准",
            description="图线应按照GB/T 4457.4标准使用正确的线型",
            standard_type=StandardType.GB_T,
            standard_number="GB/T 4457.4-2002",
            clause="4.1",
            severity=ReviewSeverity.ERROR,
            category="线型",
            parameters={
                "continuous_line_usage": ["外轮廓线", "可见轮廓线"],
                "dashed_line_usage": ["不可见轮廓线", "虚线"],
                "center_line_usage": ["中心线", "对称线"],
                "dimension_line_usage": ["尺寸线", "尺寸界线"]
            },
            check_function="check_line_type_compliance",
            suggestion_template="建议将{element_type}的线型修改为{correct_line_type}",
            auto_fixable=True
        ))
        
        # GB/T 4457.4-2002 图线宽度
        self.add_rule(StandardRule(
            rule_id="GB_T_4457_4_002",
            rule_code="LINE_WIDTH_STANDARD",
            title="图线宽度标准",
            description="图线宽度应符合标准比例关系",
            standard_type=StandardType.GB_T,
            standard_number="GB/T 4457.4-2002",
            clause="4.2",
            severity=ReviewSeverity.WARNING,
            category="线宽",
            parameters={
                "thick_line_width": 0.7,
                "medium_line_width": 0.35,
                "thin_line_width": 0.18,
                "width_ratio": 2.0  # 粗线:细线 = 2:1
            },
            check_function="check_line_width_compliance",
            suggestion_template="建议调整线宽为{recommended_width}mm",
            auto_fixable=True
        ))
        
        # GB/T 14665-2012 机械制图 图纸幅面和格式
        self.add_rule(StandardRule(
            rule_id="GB_T_14665_001",
            rule_code="PAPER_SIZE_STANDARD",
            title="图纸幅面标准",
            description="图纸幅面应符合GB/T 14665标准",
            standard_type=StandardType.GB_T,
            standard_number="GB/T 14665-2012",
            clause="5.1",
            severity=ReviewSeverity.INFO,
            category="图纸格式",
            parameters={
                "standard_sizes": {
                    "A0": (841, 1189),
                    "A1": (594, 841),
                    "A2": (420, 594),
                    "A3": (297, 420),
                    "A4": (210, 297)
                }
            },
            check_function="check_paper_size_compliance"
        ))
    
    def _load_dl_standards(self):
        """加载DL/T电力行业标准"""
        
        # DL/T 5136-2012 火力发电厂二次系统设计技术规程
        self.add_rule(StandardRule(
            rule_id="DL_T_5136_001",
            rule_code="SECONDARY_CIRCUIT_LAYOUT",
            title="二次回路布置标准",
            description="二次回路图应按照DL/T 5136标准进行布置",
            standard_type=StandardType.DL_T,
            standard_number="DL/T 5136-2012",
            clause="6.2",
            severity=ReviewSeverity.ERROR,
            category="回路布置",
            parameters={
                "min_spacing": 5.0,  # 最小间距(mm)
                "alignment_tolerance": 1.0,  # 对齐容差(mm)
                "connection_point_size": 2.0  # 连接点大小(mm)
            },
            check_function="check_secondary_circuit_layout",
            suggestion_template="二次回路布置不符合标准，建议{specific_suggestion}"
        ))
        
        # DL/T 5136-2012 文字标注标准
        self.add_rule(StandardRule(
            rule_id="DL_T_5136_002",
            rule_code="TEXT_ANNOTATION_STANDARD",
            title="文字标注标准",
            description="文字标注应符合电力行业标准",
            standard_type=StandardType.DL_T,
            standard_number="DL/T 5136-2012",
            clause="7.1",
            severity=ReviewSeverity.WARNING,
            category="文字标注",
            parameters={
                "min_text_height": 2.5,  # 最小文字高度(mm)
                "standard_fonts": ["宋体", "黑体", "Arial"],
                "text_spacing": 1.5,  # 行间距倍数
                "annotation_position": "上方或右侧"
            },
            check_function="check_text_annotation_standard",
            suggestion_template="文字标注不符合标准：{issue_description}，建议{correction}",
            auto_fixable=True
        ))
        
        # DL/T 5136-2012 设备符号标准
        self.add_rule(StandardRule(
            rule_id="DL_T_5136_003",
            rule_code="EQUIPMENT_SYMBOL_STANDARD",
            title="设备符号标准",
            description="电气设备符号应符合标准图形符号",
            standard_type=StandardType.DL_T,
            standard_number="DL/T 5136-2012",
            clause="8.1",
            severity=ReviewSeverity.ERROR,
            category="设备符号",
            parameters={
                "symbol_library": "standard_electrical_symbols",
                "symbol_scale": 1.0,
                "symbol_rotation": [0, 90, 180, 270]  # 允许的旋转角度
            },
            check_function="check_equipment_symbol_standard",
            suggestion_template="设备符号不标准，建议使用标准符号库中的{standard_symbol}"
        ))
    
    def _load_drawing_standards(self):
        """加载图纸绘制标准"""
        
        # 图层标准
        self.layer_standards = {
            "0": {
                "description": "默认图层",
                "color": Color(r=255, g=255, b=255, index=7),
                "line_type": LineType.CONTINUOUS,
                "line_weight": 0.25
            },
            "OUTLINE": {
                "description": "外轮廓线",
                "color": Color(r=255, g=255, b=255, index=7),
                "line_type": LineType.CONTINUOUS,
                "line_weight": 0.7
            },
            "CENTER": {
                "description": "中心线",
                "color": Color(r=0, g=255, b=255, index=4),
                "line_type": LineType.CENTER,
                "line_weight": 0.35
            },
            "HIDDEN": {
                "description": "隐藏线",
                "color": Color(r=255, g=0, b=0, index=1),
                "line_type": LineType.DASHED,
                "line_weight": 0.35
            },
            "DIMENSION": {
                "description": "尺寸标注",
                "color": Color(r=0, g=255, b=0, index=3),
                "line_type": LineType.CONTINUOUS,
                "line_weight": 0.18
            },
            "TEXT": {
                "description": "文字标注",
                "color": Color(r=255, g=255, b=0, index=2),
                "line_type": LineType.CONTINUOUS,
                "line_weight": 0.18
            },
            "ELECTRICAL": {
                "description": "电气线路",
                "color": Color(r=255, g=0, b=0, index=1),
                "line_type": LineType.CONTINUOUS,
                "line_weight": 0.5
            },
            "CONTROL": {
                "description": "控制回路",
                "color": Color(r=0, g=0, b=255, index=5),
                "line_type": LineType.CONTINUOUS,
                "line_weight": 0.35
            }
        }
        
        # 线型标准
        self.line_type_standards = {
            "外轮廓线": LineType.CONTINUOUS,
            "内轮廓线": LineType.CONTINUOUS,
            "中心线": LineType.CENTER,
            "对称线": LineType.CENTER,
            "隐藏线": LineType.DASHED,
            "虚线": LineType.DASHED,
            "尺寸线": LineType.CONTINUOUS,
            "尺寸界线": LineType.CONTINUOUS,
            "引出线": LineType.CONTINUOUS,
            "剖面线": LineType.CONTINUOUS
        }
        
        # 颜色标准
        self.color_standards = {
            "主要轮廓": Color(r=255, g=255, b=255, index=7),  # 白色
            "次要轮廓": Color(r=128, g=128, b=128, index=8),  # 灰色
            "中心线": Color(r=0, g=255, b=255, index=4),      # 青色
            "隐藏线": Color(r=255, g=0, b=0, index=1),        # 红色
            "尺寸标注": Color(r=0, g=255, b=0, index=3),      # 绿色
            "文字标注": Color(r=255, g=255, b=0, index=2),    # 黄色
            "电气线路": Color(r=255, g=0, b=0, index=1),      # 红色
            "控制回路": Color(r=0, g=0, b=255, index=5)       # 蓝色
        }
        
        # 文字标准
        self.text_standards = {
            "min_height": 2.5,
            "max_height": 10.0,
            "standard_heights": [2.5, 3.5, 5.0, 7.0, 10.0],
            "standard_fonts": ["宋体", "黑体", "Arial", "Times New Roman"],
            "line_spacing_factor": 1.5,
            "character_spacing_factor": 1.0
        }
        
        # 尺寸标注标准
        self.dimension_standards = {
            "arrow_size": 2.5,
            "text_height": 3.5,
            "extension_line_offset": 1.0,
            "dimension_line_spacing": 7.0,
            "tolerance_text_factor": 0.7
        }
    
    def add_rule(self, rule: StandardRule):
        """添加规则"""
        self.rules[rule.rule_id] = rule
    
    def get_rule(self, rule_id: str) -> Optional[StandardRule]:
        """获取规则"""
        return self.rules.get(rule_id)
    
    def get_rules_by_category(self, category: str) -> List[StandardRule]:
        """按分类获取规则"""
        return [rule for rule in self.rules.values() if rule.category == category]
    
    def get_rules_by_standard(self, standard_type: StandardType) -> List[StandardRule]:
        """按标准类型获取规则"""
        return [rule for rule in self.rules.values() if rule.standard_type == standard_type]
    
    def get_layer_standard(self, layer_name: str) -> Optional[Dict[str, Any]]:
        """获取图层标准"""
        return self.layer_standards.get(layer_name)
    
    def get_recommended_line_type(self, usage: str) -> Optional[LineType]:
        """获取推荐线型"""
        return self.line_type_standards.get(usage)
    
    def get_recommended_color(self, usage: str) -> Optional[Color]:
        """获取推荐颜色"""
        return self.color_standards.get(usage)
    
    def validate_text_height(self, height: float) -> Tuple[bool, str]:
        """验证文字高度"""
        min_height = self.text_standards["min_height"]
        max_height = self.text_standards["max_height"]
        standard_heights = self.text_standards["standard_heights"]
        
        if height < min_height:
            return False, f"文字高度{height}mm小于最小标准{min_height}mm"
        elif height > max_height:
            return False, f"文字高度{height}mm大于最大标准{max_height}mm"
        elif height not in standard_heights:
            # 找到最接近的标准高度
            closest = min(standard_heights, key=lambda x: abs(x - height))
            return False, f"建议使用标准文字高度{closest}mm"
        else:
            return True, "文字高度符合标准"
    
    def validate_font(self, font_name: str) -> Tuple[bool, str]:
        """验证字体"""
        standard_fonts = self.text_standards["standard_fonts"]
        
        if font_name in standard_fonts:
            return True, "字体符合标准"
        else:
            return False, f"建议使用标准字体：{', '.join(standard_fonts)}"
    
    def get_all_rules(self) -> List[StandardRule]:
        """获取所有规则"""
        return list(self.rules.values())
    
    def get_rules_summary(self) -> Dict[str, Any]:
        """获取规则摘要"""
        total_rules = len(self.rules)
        by_standard = {}
        by_category = {}
        by_severity = {}
        
        for rule in self.rules.values():
            # 按标准类型统计
            std_type = rule.standard_type.value
            by_standard[std_type] = by_standard.get(std_type, 0) + 1
            
            # 按分类统计
            category = rule.category or "其他"
            by_category[category] = by_category.get(category, 0) + 1
            
            # 按严重程度统计
            severity = rule.severity.value
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        return {
            "total_rules": total_rules,
            "by_standard": by_standard,
            "by_category": by_category,
            "by_severity": by_severity,
            "layer_standards_count": len(self.layer_standards),
            "line_type_standards_count": len(self.line_type_standards),
            "color_standards_count": len(self.color_standards)
        }
