{% extends "base.html" %}

{% block title %}网络拓扑可视化 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="fas fa-project-diagram fa-2x text-primary me-3"></i>
                    <div>
                        <h1 class="mb-1">网络拓扑可视化</h1>
                        <p class="text-muted mb-0">让虚端子连接变得直观可见</p>
                    </div>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="exportDiagram()">
                        <i class="fas fa-download me-2"></i>导出图表
                    </button>
                    <button class="btn btn-primary" onclick="loadSampleData()">
                        <i class="fas fa-eye me-2"></i>查看示例
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 使命说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-0">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="fas fa-lightbulb fa-2x text-warning"></i>
                    </div>
                    <div class="col-md-11">
                        <h6 class="mb-2">
                            <i class="fas fa-target me-2"></i>
                            解决虚端子连接的直观性问题
                        </h6>
                        <p class="mb-0">
                            传统的硬接线设计中，工程师可以直观地追踪每一根电缆的连接。
                            智能变电站的虚端子使这种直观性消失。本工具通过可视化技术，
                            将抽象的虚端子连接转化为直观的网络拓扑图。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 控制面板 -->
        <div class="col-lg-3">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        显示控制
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 视图选择 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">视图类型</label>
                        <select class="form-select form-select-sm" id="viewType" onchange="changeView()">
                            <option value="network">网络拓扑</option>
                            <option value="logical">逻辑连接</option>
                            <option value="physical">物理布局</option>
                            <option value="data-flow">数据流向</option>
                        </select>
                    </div>

                    <!-- 显示选项 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">显示元素</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showIEDs" checked onchange="toggleDisplay('ieds')">
                            <label class="form-check-label small" for="showIEDs">IED设备</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showSubnets" checked onchange="toggleDisplay('subnets')">
                            <label class="form-check-label small" for="showSubnets">子网</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showConnections" checked onchange="toggleDisplay('connections')">
                            <label class="form-check-label small" for="showConnections">连接线</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showLabels" checked onchange="toggleDisplay('labels')">
                            <label class="form-check-label small" for="showLabels">标签</label>
                        </div>
                    </div>

                    <!-- 过滤器 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">网络类型过滤</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filterMMS" checked onchange="filterNetwork('MMS')">
                            <label class="form-check-label small" for="filterMMS">MMS网络</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filterGOOSE" checked onchange="filterNetwork('GOOSE')">
                            <label class="form-check-label small" for="filterGOOSE">GOOSE网络</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filterSMV" checked onchange="filterNetwork('SMV')">
                            <label class="form-check-label small" for="filterSMV">SMV网络</label>
                        </div>
                    </div>

                    <!-- 布局控制 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">布局算法</label>
                        <select class="form-select form-select-sm" id="layoutType" onchange="changeLayout()">
                            <option value="force">力导向布局</option>
                            <option value="hierarchical">层次布局</option>
                            <option value="circular">环形布局</option>
                            <option value="grid">网格布局</option>
                        </select>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="resetView()">
                            <i class="fas fa-undo me-2"></i>重置视图
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="fitToScreen()">
                            <i class="fas fa-expand me-2"></i>适应屏幕
                        </button>
                    </div>
                </div>
            </div>

            <!-- 图例 -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        图例说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-primary rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>IED设备</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-success rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>MMS网络</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-warning rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>GOOSE网络</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-info rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>SMV网络</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div style="width: 16px; height: 2px; background: #6c757d;" class="me-2"></div>
                            <small>虚端子连接</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 选中元素信息 -->
            <div class="card shadow-sm border-0" id="selectionInfo" style="display: none;">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-mouse-pointer me-2"></i>
                        选中元素
                    </h6>
                </div>
                <div class="card-body">
                    <div id="selectionDetails">
                        <!-- 动态填充选中元素的详细信息 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 可视化区域 -->
        <div class="col-lg-9">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="mb-0">
                            <i class="fas fa-network-wired me-2"></i>
                            智能变电站网络拓扑
                        </h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 可视化画布 -->
                    <div id="networkCanvas" style="height: 600px; background: #f8f9fa;">
                        <!-- 这里将使用D3.js或其他图形库渲染网络拓扑 -->
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                                <h5 class="text-muted">网络拓扑可视化</h5>
                                <p class="text-muted mb-3">
                                    点击"查看示例"按钮查看智能变电站网络拓扑示例
                                </p>
                                <button class="btn btn-primary" onclick="loadSampleData()">
                                    <i class="fas fa-play me-2"></i>
                                    加载示例数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-primary mb-1" id="iedCount">-</div>
                            <small class="text-muted">IED设备</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-success mb-1" id="subnetCount">-</div>
                            <small class="text-muted">子网数量</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-warning mb-1" id="connectionCount">-</div>
                            <small class="text-muted">连接数量</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-info mb-1" id="virtualTerminalCount">-</div>
                            <small class="text-muted">虚端子</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- D3.js CSS -->
<style>
.node {
    cursor: pointer;
    stroke: #fff;
    stroke-width: 2px;
}

.node:hover {
    stroke-width: 3px;
}

.link {
    stroke: #999;
    stroke-opacity: 0.6;
    stroke-width: 2px;
}

.link:hover {
    stroke-opacity: 1;
    stroke-width: 3px;
}

.node-label {
    font-family: Arial, sans-serif;
    font-size: 12px;
    text-anchor: middle;
    pointer-events: none;
}

.subnet-group {
    fill: none;
    stroke: #ddd;
    stroke-width: 2px;
    stroke-dasharray: 5,5;
}

#networkCanvas {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.tooltip {
    position: absolute;
    text-align: center;
    padding: 8px;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
}
</style>
{% endblock %}

{% block extra_js %}
<!-- D3.js -->
<script src="https://d3js.org/d3.v7.min.js"></script>

<script>
let networkData = null;
let svg = null;
let simulation = null;
let currentView = 'network';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeVisualization();
});

// 初始化可视化
function initializeVisualization() {
    const canvas = document.getElementById('networkCanvas');
    const width = canvas.clientWidth;
    const height = canvas.clientHeight;
    
    // 创建SVG
    svg = d3.select('#networkCanvas')
        .html('') // 清空现有内容
        .append('svg')
        .attr('width', width)
        .attr('height', height);
    
    // 添加缩放功能
    const zoom = d3.zoom()
        .scaleExtent([0.1, 3])
        .on('zoom', function(event) {
            svg.select('g').attr('transform', event.transform);
        });
    
    svg.call(zoom);
    
    // 创建主容器
    const container = svg.append('g');
    
    // 添加工具提示
    d3.select('body').append('div')
        .attr('class', 'tooltip')
        .attr('id', 'networkTooltip');
}

// 加载示例数据
function loadSampleData() {
    // 模拟智能变电站网络数据
    networkData = {
        nodes: [
            // IED设备
            { id: 'IED_PROT_01', name: '保护IED-01', type: 'ied', category: 'protection', x: 100, y: 100 },
            { id: 'IED_PROT_02', name: '保护IED-02', type: 'ied', category: 'protection', x: 300, y: 100 },
            { id: 'IED_CTRL_01', name: '控制IED-01', type: 'ied', category: 'control', x: 200, y: 200 },
            { id: 'IED_MEAS_01', name: '测量IED-01', type: 'ied', category: 'measurement', x: 400, y: 200 },
            
            // 子网
            { id: 'SUBNET_MMS', name: 'MMS网络', type: 'subnet', category: 'mms', x: 250, y: 50 },
            { id: 'SUBNET_GOOSE', name: 'GOOSE网络', type: 'subnet', category: 'goose', x: 150, y: 300 },
            { id: 'SUBNET_SMV', name: 'SMV网络', type: 'subnet', category: 'smv', x: 350, y: 300 }
        ],
        links: [
            // IED到子网的连接
            { source: 'IED_PROT_01', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_PROT_02', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_CTRL_01', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_MEAS_01', target: 'SUBNET_MMS', type: 'mms' },
            
            { source: 'IED_PROT_01', target: 'SUBNET_GOOSE', type: 'goose' },
            { source: 'IED_PROT_02', target: 'SUBNET_GOOSE', type: 'goose' },
            { source: 'IED_CTRL_01', target: 'SUBNET_GOOSE', type: 'goose' },
            
            { source: 'IED_MEAS_01', target: 'SUBNET_SMV', type: 'smv' }
        ]
    };
    
    renderNetwork(networkData);
    updateStatistics(networkData);
    
    toast.success('示例加载成功', '已加载智能变电站网络拓扑示例');
}

// 渲染网络拓扑
function renderNetwork(data) {
    const width = svg.attr('width');
    const height = svg.attr('height');
    
    // 清空现有内容
    svg.select('g').selectAll('*').remove();
    const container = svg.select('g');
    
    // 创建力导向仿真
    simulation = d3.forceSimulation(data.nodes)
        .force('link', d3.forceLink(data.links).id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-300))
        .force('center', d3.forceCenter(width / 2, height / 2));
    
    // 绘制连接线
    const links = container.append('g')
        .attr('class', 'links')
        .selectAll('line')
        .data(data.links)
        .enter().append('line')
        .attr('class', 'link')
        .style('stroke', d => getLinkColor(d.type))
        .on('mouseover', showLinkTooltip)
        .on('mouseout', hideTooltip);
    
    // 绘制节点
    const nodes = container.append('g')
        .attr('class', 'nodes')
        .selectAll('circle')
        .data(data.nodes)
        .enter().append('circle')
        .attr('class', 'node')
        .attr('r', d => getNodeSize(d.type))
        .style('fill', d => getNodeColor(d.type, d.category))
        .on('mouseover', showNodeTooltip)
        .on('mouseout', hideTooltip)
        .on('click', selectNode)
        .call(d3.drag()
            .on('start', dragStarted)
            .on('drag', dragged)
            .on('end', dragEnded));
    
    // 添加节点标签
    const labels = container.append('g')
        .attr('class', 'labels')
        .selectAll('text')
        .data(data.nodes)
        .enter().append('text')
        .attr('class', 'node-label')
        .text(d => d.name)
        .style('font-size', '10px')
        .style('fill', '#333');
    
    // 更新仿真
    simulation.on('tick', () => {
        links
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);
        
        nodes
            .attr('cx', d => d.x)
            .attr('cy', d => d.y);
        
        labels
            .attr('x', d => d.x)
            .attr('y', d => d.y + 20);
    });
}

// 获取节点颜色
function getNodeColor(type, category) {
    if (type === 'ied') {
        const colors = {
            'protection': '#dc3545',
            'control': '#28a745',
            'measurement': '#17a2b8'
        };
        return colors[category] || '#007bff';
    } else if (type === 'subnet') {
        const colors = {
            'mms': '#28a745',
            'goose': '#ffc107',
            'smv': '#17a2b8'
        };
        return colors[category] || '#6c757d';
    }
    return '#6c757d';
}

// 获取连接线颜色
function getLinkColor(type) {
    const colors = {
        'mms': '#28a745',
        'goose': '#ffc107',
        'smv': '#17a2b8'
    };
    return colors[type] || '#6c757d';
}

// 获取节点大小
function getNodeSize(type) {
    return type === 'ied' ? 15 : 20;
}

// 显示节点工具提示
function showNodeTooltip(event, d) {
    const tooltip = d3.select('#networkTooltip');
    tooltip.transition().duration(200).style('opacity', 1);
    tooltip.html(`
        <strong>${d.name}</strong><br>
        类型: ${d.type === 'ied' ? 'IED设备' : '子网'}<br>
        类别: ${d.category}
    `)
    .style('left', (event.pageX + 10) + 'px')
    .style('top', (event.pageY - 10) + 'px');
}

// 显示连接线工具提示
function showLinkTooltip(event, d) {
    const tooltip = d3.select('#networkTooltip');
    tooltip.transition().duration(200).style('opacity', 1);
    tooltip.html(`
        <strong>虚端子连接</strong><br>
        从: ${d.source.name}<br>
        到: ${d.target.name}<br>
        类型: ${d.type.toUpperCase()}
    `)
    .style('left', (event.pageX + 10) + 'px')
    .style('top', (event.pageY - 10) + 'px');
}

// 隐藏工具提示
function hideTooltip() {
    d3.select('#networkTooltip').transition().duration(200).style('opacity', 0);
}

// 选择节点
function selectNode(event, d) {
    // 显示选中元素信息
    const infoPanel = document.getElementById('selectionInfo');
    const detailsDiv = document.getElementById('selectionDetails');
    
    detailsDiv.innerHTML = `
        <h6>${d.name}</h6>
        <div class="small">
            <div><strong>ID:</strong> ${d.id}</div>
            <div><strong>类型:</strong> ${d.type === 'ied' ? 'IED设备' : '子网'}</div>
            <div><strong>类别:</strong> ${d.category}</div>
        </div>
    `;
    
    infoPanel.style.display = 'block';
}

// 拖拽事件处理
function dragStarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
}

function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
}

function dragEnded(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
}

// 更新统计信息
function updateStatistics(data) {
    const iedCount = data.nodes.filter(n => n.type === 'ied').length;
    const subnetCount = data.nodes.filter(n => n.type === 'subnet').length;
    const connectionCount = data.links.length;
    const virtualTerminalCount = connectionCount * 2; // 每个连接有两个虚端子
    
    document.getElementById('iedCount').textContent = iedCount;
    document.getElementById('subnetCount').textContent = subnetCount;
    document.getElementById('connectionCount').textContent = connectionCount;
    document.getElementById('virtualTerminalCount').textContent = virtualTerminalCount;
}

// 控制函数
function changeView() {
    const viewType = document.getElementById('viewType').value;
    currentView = viewType;
    
    if (networkData) {
        renderNetwork(networkData);
    }
    
    toast.info('视图切换', `已切换到${viewType}视图`);
}

function changeLayout() {
    const layoutType = document.getElementById('layoutType').value;
    
    if (simulation && networkData) {
        // 根据布局类型调整力的参数
        switch (layoutType) {
            case 'hierarchical':
                simulation.force('charge', d3.forceManyBody().strength(-500));
                simulation.force('y', d3.forceY().strength(0.1));
                break;
            case 'circular':
                simulation.force('charge', d3.forceManyBody().strength(-100));
                simulation.force('radial', d3.forceRadial(150, svg.attr('width')/2, svg.attr('height')/2));
                break;
            case 'grid':
                simulation.force('charge', d3.forceManyBody().strength(-50));
                break;
            default: // force
                simulation.force('charge', d3.forceManyBody().strength(-300));
                break;
        }
        
        simulation.alpha(1).restart();
    }
}

function toggleDisplay(element) {
    // 实现显示/隐藏功能
    toast.info('显示控制', `已切换${element}的显示状态`);
}

function filterNetwork(type) {
    // 实现网络类型过滤
    toast.info('网络过滤', `已切换${type}网络的显示状态`);
}

function resetView() {
    if (networkData) {
        renderNetwork(networkData);
        toast.success('视图重置', '已重置到初始视图');
    }
}

function fitToScreen() {
    if (svg) {
        const bounds = svg.select('g').node().getBBox();
        const width = svg.attr('width');
        const height = svg.attr('height');
        
        const scale = Math.min(width / bounds.width, height / bounds.height) * 0.9;
        const translate = [
            width / 2 - scale * (bounds.x + bounds.width / 2),
            height / 2 - scale * (bounds.y + bounds.height / 2)
        ];
        
        svg.transition().duration(750).call(
            d3.zoom().transform,
            d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale)
        );
        
        toast.success('视图调整', '已适应屏幕大小');
    }
}

function zoomIn() {
    svg.transition().call(d3.zoom().scaleBy, 1.5);
}

function zoomOut() {
    svg.transition().call(d3.zoom().scaleBy, 1 / 1.5);
}

function toggleFullscreen() {
    const canvas = document.getElementById('networkCanvas');
    if (canvas.requestFullscreen) {
        canvas.requestFullscreen();
    }
}

function exportDiagram() {
    toast.info('导出功能', '图表导出功能开发中...');
}
</script>
{% endblock %}
{% endblock %}
