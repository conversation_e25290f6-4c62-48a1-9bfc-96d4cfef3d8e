{% extends "base.html" %}

{% block title %}网络拓扑可视化 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="fas fa-project-diagram fa-2x text-primary me-3"></i>
                    <div>
                        <h1 class="mb-1">网络拓扑可视化</h1>
                        <p class="text-muted mb-0">让虚端子连接变得直观可见</p>
                    </div>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="exportDiagram()">
                        <i class="fas fa-download me-2"></i>导出图表
                    </button>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="loadSampleData()">
                            <i class="fas fa-eye me-2"></i>基础示例
                        </button>
                        <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="visually-hidden">切换示例</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" onclick="load110kVExample()">110kV线路完整二次回路</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使命说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-0">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="fas fa-lightbulb fa-2x text-warning"></i>
                    </div>
                    <div class="col-md-11">
                        <h6 class="mb-2">
                            <i class="fas fa-target me-2"></i>
                            解决虚端子连接的直观性问题
                        </h6>
                        <p class="mb-0">
                            传统的硬接线设计中，工程师可以直观地追踪每一根电缆的连接。
                            智能变电站的虚端子使这种直观性消失。本工具通过可视化技术，
                            将抽象的虚端子连接转化为直观的网络拓扑图。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 控制面板 -->
        <div class="col-lg-3">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        显示控制
                    </h6>
                </div>
                <div class="card-body">
                    <!-- 视图选择 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">视图类型</label>
                        <select class="form-select form-select-sm" id="viewType" onchange="changeView()">
                            <option value="network">网络拓扑</option>
                            <option value="logical">逻辑连接</option>
                            <option value="physical">物理布局</option>
                            <option value="data-flow">数据流向</option>
                        </select>
                    </div>

                    <!-- 显示选项 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">显示元素</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showIEDs" checked onchange="toggleDisplay('ieds')">
                            <label class="form-check-label small" for="showIEDs">IED设备</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showSubnets" checked onchange="toggleDisplay('subnets')">
                            <label class="form-check-label small" for="showSubnets">子网</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showConnections" checked onchange="toggleDisplay('connections')">
                            <label class="form-check-label small" for="showConnections">连接线</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showLabels" checked onchange="toggleDisplay('labels')">
                            <label class="form-check-label small" for="showLabels">标签</label>
                        </div>
                    </div>

                    <!-- 过滤器 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">网络类型过滤</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filterMMS" checked onchange="filterNetwork('MMS')">
                            <label class="form-check-label small" for="filterMMS">MMS网络</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filterGOOSE" checked onchange="filterNetwork('GOOSE')">
                            <label class="form-check-label small" for="filterGOOSE">GOOSE网络</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="filterSMV" checked onchange="filterNetwork('SMV')">
                            <label class="form-check-label small" for="filterSMV">SMV网络</label>
                        </div>
                    </div>

                    <!-- 布局控制 -->
                    <div class="mb-3">
                        <label class="form-label small fw-bold">布局算法</label>
                        <select class="form-select form-select-sm" id="layoutType" onchange="changeLayout()">
                            <option value="force">力导向布局</option>
                            <option value="hierarchical">层次布局</option>
                            <option value="circular">环形布局</option>
                            <option value="grid">网格布局</option>
                            <option value="hflow">水平布局</option>
                            <option value="vflow">垂直布局</option>
                        </select>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="resetView()">
                            <i class="fas fa-undo me-2"></i>重置视图
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="fitToScreen()">
                            <i class="fas fa-expand me-2"></i>适应屏幕
                        </button>
                    </div>
                </div>
            </div>

            <!-- 图例 -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        图例说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-primary rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>IED设备</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-success rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>MMS网络</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-warning rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>GOOSE网络</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div class="legend-symbol bg-info rounded me-2" style="width: 16px; height: 16px;"></div>
                            <small>SMV网络</small>
                        </div>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="d-flex align-items-center">
                            <div style="width: 16px; height: 2px; background: #6c757d;" class="me-2"></div>
                            <small>虚端子连接</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 选中元素信息 -->
            <div class="card shadow-sm border-0" id="selectionInfo" style="display: none;">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-mouse-pointer me-2"></i>
                        选中元素
                    </h6>
                </div>
                <div class="card-body">
                    <div id="selectionDetails">
                        <!-- 动态填充选中元素的详细信息 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 可视化区域 -->
        <div class="col-lg-9">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white">
                    <div class="d-flex align-items-center justify-content-between">
                        <h6 class="mb-0">
                            <i class="fas fa-network-wired me-2"></i>
                            智能变电站网络拓扑
                        </h6>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="zoomIn()">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="zoomOut()">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- 可视化画布 -->
                    <div id="networkCanvas" style="height: 600px; background: #f8f9fa;">
                        <!-- 这里将使用D3.js或其他图形库渲染网络拓扑 -->
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <i class="fas fa-project-diagram fa-4x text-muted mb-3"></i>
                                <h5 class="text-muted">网络拓扑可视化</h5>
                                <p class="text-muted mb-3">
                                    点击"查看示例"按钮查看智能变电站网络拓扑示例
                                </p>
                                <div class="btn-group">
                                  <button class="btn btn-primary" onclick="loadSampleData()">
                                      <i class="fas fa-play me-2"></i>
                                      加载基础示例
                                  </button>
                                  <button class="btn btn-outline-primary" onclick="load110kVExample()">
                                      <i class="fas fa-bolt me-2"></i>
                                      加载110kV线路示例
                                  </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="row mt-4">
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-primary mb-1" id="iedCount">-</div>
                            <small class="text-muted">IED设备</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-success mb-1" id="subnetCount">-</div>
                            <small class="text-muted">子网数量</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-warning mb-1" id="connectionCount">-</div>
                            <small class="text-muted">连接数量</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card shadow-sm border-0 text-center">
                        <div class="card-body">
                            <div class="h4 text-info mb-1" id="virtualTerminalCount">-</div>
                            <small class="text-muted">虚端子</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- D3.js CSS -->
<style>
.node {
    cursor: pointer;
    stroke: #fff;
    stroke-width: 2px;
}

.node:hover {
    stroke-width: 3px;
}

.link {
    stroke: #999;
    stroke-opacity: 0.6;
    stroke-width: 2px;
}

.link:hover {
    stroke-opacity: 1;
    stroke-width: 3px;
}

.node-label {
    font-family: Arial, sans-serif;
    font-size: 12px;
    text-anchor: middle;
    pointer-events: none;
}

.subnet-group {
    fill: none;
    stroke: #ddd;
    stroke-width: 2px;
    stroke-dasharray: 5,5;
}

#networkCanvas {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.tooltip {
    position: absolute;
    text-align: center;
    padding: 8px;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s;
}
</style>
{% endblock %}

{% block extra_js %}
<!-- D3.js -->
<script src="https://d3js.org/d3.v7.min.js"></script>

<script>
let networkData = null;
let svg = null;
let simulation = null;
let currentView = 'network';
let validationResults = null; // 存储后端校验结果

// 调用后端API进行权威校验
async function validateCircuitLogic(data) {
    try {
        const response = await fetch('/api/validate_circuit_logic', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            validationResults = result.data;
            return result.data;
        } else {
            console.error('回路逻辑校验失败:', result.error);
            return null;
        }
    } catch (error) {
        console.error('调用校验API失败:', error);
        return null;
    }
}

// 标记违规连接（基于后端结果）
function markInvalidLinks(data) {
    if (!validationResults || !validationResults.invalid_links) {
        return;
    }
    
    // 创建违规连接映射
    const invalidLinkMap = new Map();
    validationResults.invalid_links.forEach(invalidLink => {
        const key = `${invalidLink.source}->${invalidLink.target}`;
        invalidLinkMap.set(key, invalidLink);
    });
    
    // 标记违规连接
    data.links.forEach(link => {
        const key = `${link.source}->${link.target}`;
        const invalidInfo = invalidLinkMap.get(key);
        if (invalidInfo) {
            link._invalid = true;
            link._reason = invalidInfo.reason;
        } else {
            link._invalid = false;
            link._reason = '';
        }
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeVisualization();
});

// 初始化可视化
function initializeVisualization() {
    const canvas = document.getElementById('networkCanvas');
    const width = canvas.clientWidth;
    const height = canvas.clientHeight;
    
    // 创建SVG
    svg = d3.select('#networkCanvas')
        .html('') // 清空现有内容
        .append('svg')
        .attr('width', width)
        .attr('height', height);
    
    // 添加缩放功能
    const zoom = d3.zoom()
        .scaleExtent([0.1, 3])
        .on('zoom', function(event) {
            svg.select('g').attr('transform', event.transform);
        });
    
    svg.call(zoom);
    
    // 创建主容器
    const container = svg.append('g');
    
    // 添加工具提示
    d3.select('body').append('div')
        .attr('class', 'tooltip')
        .attr('id', 'networkTooltip');
}

// 加载示例数据
function loadSampleData() {
    // 模拟智能变电站网络数据
    networkData = {
        nodes: [
            // IED设备
            { id: 'IED_PROT_01', name: '保护IED-01', type: 'ied', category: 'protection', x: 100, y: 100 },
            { id: 'IED_PROT_02', name: '保护IED-02', type: 'ied', category: 'protection', x: 300, y: 100 },
            { id: 'IED_CTRL_01', name: '控制IED-01', type: 'ied', category: 'control', x: 200, y: 200 },
            { id: 'IED_MEAS_01', name: '测量IED-01', type: 'ied', category: 'measurement', x: 400, y: 200 },
            
            // 子网
            { id: 'SUBNET_MMS', name: 'MMS网络', type: 'subnet', category: 'mms', x: 250, y: 50 },
            { id: 'SUBNET_GOOSE', name: 'GOOSE网络', type: 'subnet', category: 'goose', x: 150, y: 300 },
            { id: 'SUBNET_SMV', name: 'SMV网络', type: 'subnet', category: 'smv', x: 350, y: 300 }
        ],
        links: [
            // IED到子网的连接
            { source: 'IED_PROT_01', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_PROT_02', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_CTRL_01', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_MEAS_01', target: 'SUBNET_MMS', type: 'mms' },
            
            { source: 'IED_PROT_01', target: 'SUBNET_GOOSE', type: 'goose' },
            { source: 'IED_PROT_02', target: 'SUBNET_GOOSE', type: 'goose' },
            { source: 'IED_CTRL_01', target: 'SUBNET_GOOSE', type: 'goose' },
            
            { source: 'IED_MEAS_01', target: 'SUBNET_SMV', type: 'smv' }
        ]
    };
    
    renderNetwork(networkData);
    updateStatistics(networkData);
    
    toast.success('示例加载成功', '已加载智能变电站网络拓扑示例');
}

// 加载110kV线路完整二次回路示例
function load110kVExample() {
    // 该示例覆盖：SMV采样、距离保护/后备保护、开关故障、重合闸、遥信遥测、GOOSE跳闸/闭锁/联跳、MMS监控
    networkData = {
        nodes: [
            // IED设备
            { id: 'IED_PROT_DIST', name: '线路保护IED-距离', type: 'ied', category: 'protection' },
            { id: 'IED_PROT_BACK', name: '线路保护IED-后备', type: 'ied', category: 'protection' },
            { id: 'IED_BCU', name: '间隔层BCU', type: 'ied', category: 'control' },
            { id: 'IED_RECLOSE', name: '重合闸装置', type: 'ied', category: 'control' },
            { id: 'IED_BF', name: '断路器失灵(BF)', type: 'ied', category: 'protection' },
            { id: 'IED_TPL', name: '通道/联跳TPL', type: 'ied', category: 'control' },
            { id: 'IED_PMU', name: '测量/PMU', type: 'ied', category: 'measurement' },

            // 采样合并单元（MU）
            { id: 'MU_L1', name: '合并单元 MU-110kV-L1', type: 'ied', category: 'measurement' },

            // 物理一次设备（抽象为节点用于展示回路闭环）
            { id: 'BRK_Q0', name: '断路器 Q0', type: 'device', category: 'switchgear' },
            { id: 'DIS_Q1', name: '刀闸 Q1', type: 'device', category: 'switchgear' },
            { id: 'DIS_Q2', name: '刀闸 Q2', type: 'device', category: 'switchgear' },

            // 子网
            { id: 'SUBNET_MMS', name: 'MMS监控网', type: 'subnet', category: 'mms' },
            { id: 'SUBNET_GOOSE_A', name: 'GOOSE-A', type: 'subnet', category: 'goose' },
            { id: 'SUBNET_GOOSE_B', name: 'GOOSE-B', type: 'subnet', category: 'goose' },
            { id: 'SUBNET_SMV_L1', name: 'SMV-110kV-L1', type: 'subnet', category: 'smv' }
        ],
        links: [
            // SMV：MU 到 距离保护/后备保护/测量
            { source: 'MU_L1', target: 'SUBNET_SMV_L1', type: 'smv' },
            { source: 'IED_PROT_DIST', target: 'SUBNET_SMV_L1', type: 'smv' },
            { source: 'IED_PROT_BACK', target: 'SUBNET_SMV_L1', type: 'smv' },
            { source: 'IED_PMU', target: 'SUBNET_SMV_L1', type: 'smv' },

            // GOOSE：保护→BCU/断路器失灵/重合闸/联跳
            { source: 'IED_PROT_DIST', target: 'SUBNET_GOOSE_A', type: 'goose' },
            { source: 'IED_PROT_BACK', target: 'SUBNET_GOOSE_A', type: 'goose' },
            { source: 'IED_BCU', target: 'SUBNET_GOOSE_A', type: 'goose' },
            { source: 'IED_BF', target: 'SUBNET_GOOSE_A', type: 'goose' },
            { source: 'IED_RECLOSE', target: 'SUBNET_GOOSE_B', type: 'goose' },
            { source: 'IED_TPL', target: 'SUBNET_GOOSE_B', type: 'goose' },

            // MMS：所有IED上送监控
            { source: 'IED_PROT_DIST', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_PROT_BACK', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_BCU', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_BF', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_RECLOSE', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_TPL', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'IED_PMU', target: 'SUBNET_MMS', type: 'mms' },
            { source: 'MU_L1', target: 'SUBNET_MMS', type: 'mms' },

            // 控制/跳闸回路（抽象为虚端子连接，展示逻辑闭环）
            { source: 'IED_PROT_DIST', target: 'IED_BCU', type: 'trip' },
            { source: 'IED_PROT_BACK', target: 'IED_BCU', type: 'trip' },
            { source: 'IED_BCU', target: 'BRK_Q0', type: 'ctl' },
            { source: 'IED_BF', target: 'IED_TPL', type: 'interlock' },
            { source: 'IED_TPL', target: 'IED_BCU', type: 'trip' },
            { source: 'IED_RECLOSE', target: 'IED_BCU', type: 'ctl' },

            // 一次接线抽象连接
            { source: 'DIS_Q1', target: 'BRK_Q0', type: 'primary' },
            { source: 'BRK_Q0', target: 'DIS_Q2', type: 'primary' },

            // 测控辅助
            { source: 'IED_BCU', target: 'DIS_Q1', type: 'ctl' },
            { source: 'IED_BCU', target: 'DIS_Q2', type: 'ctl' }
        ]
    };

    // 调用后端校验
    validateCircuitLogic(networkData).then(validationData => {
        if (validationData) {
            // 标记违规连接
            markInvalidLinks(networkData);
            
            // 渲染网络
            renderNetwork(networkData);
            updateStatistics(networkData);
            
            // 显示校验结果
            const nInvalid = validationData.invalid_links_count || 0;
            if (nInvalid > 0) {
                toast.error('逻辑校核', `发现${nInvalid}条违规连接，已以红色虚线标注`);
            } else {
                toast.success('示例加载成功', '已加载110kV线路完整二次回路示例，逻辑校验通过');
            }
        } else {
            // 校验失败，仍渲染网络但不标记违规
            renderNetwork(networkData);
            updateStatistics(networkData);
            toast.warning('示例加载成功', '已加载110kV线路完整二次回路示例（校验服务暂时不可用）');
        }
    });
}

// 渲染网络拓扑
function renderNetwork(data) {
    const width = svg.attr('width');
    const height = svg.attr('height');
    
    // 清空现有内容
    svg.select('g').selectAll('*').remove();
    const container = svg.select('g');
    
    // 为节点添加分组信息
    data.nodes.forEach(node => {
        node.group = getNodeGroup(node);
    });
    
    // 创建层次化布局
    const layout = createHierarchicalLayout(data.nodes, width, height);
    
    // 绘制分组背景
    drawGroupBackgrounds(container, layout);
    
    // 创建力导向仿真（用于微调）
    simulation = d3.forceSimulation(data.nodes)
        .force('link', d3.forceLink(data.links).id(d => d.id).distance(80))
        .force('charge', d3.forceManyBody().strength(-200))
        .force('collision', d3.forceCollide().radius(30))
        .force('x', d3.forceX().x(d => layout.groups[d.group].centerX).strength(0.3))
        .force('y', d3.forceY().y(d => layout.groups[d.group].centerY).strength(0.3));
    
    // 绘制连接线（按类型分层）
    const linkGroups = container.append('g').attr('class', 'links');
    
    // 先绘制背景连接线
    const backgroundLinks = linkGroups.append('g')
        .attr('class', 'background-links')
        .selectAll('line')
        .data(data.links.filter(d => d.type === 'primary'))
        .enter().append('line')
        .attr('class', 'link background-link')
        .style('stroke', '#e9ecef')
        .style('stroke-width', '1px')
        .style('stroke-dasharray', '5,5');
    
    // 绘制主要连接线
    const mainLinks = linkGroups.append('g')
        .attr('class', 'main-links')
        .selectAll('line')
        .data(data.links.filter(d => d.type !== 'primary'))
        .enter().append('line')
        .attr('class', 'link main-link')
        .style('stroke', d => d._invalid ? '#dc3545' : getLinkColor(d.type))
        .style('stroke-width', d => getLinkWidth(d.type))
        .style('stroke-opacity', 0.9)
        .style('stroke-dasharray', d => d._invalid ? '6,4' : 'none')
        .on('mouseover', showLinkTooltip)
        .on('mouseout', hideTooltip);
    
    // 绘制节点
    const nodes = container.append('g')
        .attr('class', 'nodes')
        .selectAll('g')
        .data(data.nodes)
        .enter().append('g')
        .attr('class', 'node-group')
        .on('mouseover', showNodeTooltip)
        .on('mouseout', hideTooltip)
        .on('click', selectNode)
        .call(d3.drag()
            .on('start', dragStarted)
            .on('drag', dragged)
            .on('end', dragEnded));
    
    // 节点圆圈
    nodes.append('circle')
        .attr('class', 'node')
        .attr('r', d => getNodeSize(d.type))
        .style('fill', d => getNodeColor(d.type, d.category))
        .style('stroke', '#fff')
        .style('stroke-width', '2px');
    
    // 节点图标
    nodes.append('text')
        .attr('class', 'node-icon')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.35em')
        .style('font-size', '12px')
        .style('fill', '#fff')
        .style('pointer-events', 'none')
        .text(d => getNodeIcon(d.type, d.category));
    
    // 节点标签
    const labels = container.append('g')
        .attr('class', 'labels')
        .selectAll('text')
        .data(data.nodes)
        .enter().append('text')
        .attr('class', 'node-label')
        .text(d => d.name)
        .style('font-size', '10px')
        .style('fill', '#333')
        .style('text-anchor', 'middle')
        .style('pointer-events', 'none');
    
    // 更新仿真
    simulation.on('tick', () => {
        backgroundLinks
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);
        
        mainLinks
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);
        
        nodes
            .attr('transform', d => `translate(${d.x},${d.y})`);
        
        labels
            .attr('x', d => d.x)
            .attr('y', d => d.y + 25);
    });
}

// 获取节点分组
function getNodeGroup(node) {
    if (node.type === 'subnet') return 'networks';
    if (node.type === 'device') return 'primary';
    if (node.category === 'protection') return 'protection';
    if (node.category === 'control') return 'control';
    if (node.category === 'measurement') return 'measurement';
    return 'other';
}

// 创建层次化布局
function createHierarchicalLayout(nodes, width, height) {
    const groups = {
        'networks': { centerX: width * 0.5, centerY: height * 0.1, name: '通信网络' },
        'protection': { centerX: width * 0.2, centerY: height * 0.3, name: '保护设备' },
        'control': { centerX: width * 0.8, centerY: height * 0.3, name: '控制设备' },
        'measurement': { centerX: width * 0.2, centerY: height * 0.6, name: '测量设备' },
        'primary': { centerX: width * 0.8, centerY: height * 0.6, name: '一次设备' },
        'other': { centerX: width * 0.5, centerY: height * 0.8, name: '其他设备' }
    };
    
    // 为每个分组计算边界
    Object.keys(groups).forEach(groupKey => {
        const groupNodes = nodes.filter(n => n.group === groupKey);
        if (groupNodes.length > 0) {
            const radius = Math.max(80, groupNodes.length * 20);
            groups[groupKey].radius = radius;
        }
    });
    
    return { groups };
}

// 绘制分组背景
function drawGroupBackgrounds(container, layout) {
    Object.keys(layout.groups).forEach(groupKey => {
        const group = layout.groups[groupKey];
        if (group.radius) {
            // 分组背景圆
            container.append('circle')
                .attr('cx', group.centerX)
                .attr('cy', group.centerY)
                .attr('r', group.radius)
                .style('fill', getGroupColor(groupKey))
                .style('opacity', 0.1)
                .style('stroke', getGroupColor(groupKey))
                .style('stroke-width', '1px')
                .style('stroke-opacity', 0.3);
            
            // 分组标签
            container.append('text')
                .attr('x', group.centerX)
                .attr('y', group.centerY - group.radius - 10)
                .style('text-anchor', 'middle')
                .style('font-size', '12px')
                .style('font-weight', 'bold')
                .style('fill', getGroupColor(groupKey))
                .text(group.name);
        }
    });
}

// 获取分组颜色
function getGroupColor(group) {
    const colors = {
        'networks': '#28a745',
        'protection': '#dc3545',
        'control': '#0d6efd',
        'measurement': '#17a2b8',
        'primary': '#6c757d',
        'other': '#6f42c1'
    };
    return colors[group] || '#6c757d';
}

// 获取节点图标
function getNodeIcon(type, category) {
    if (type === 'subnet') return '🌐';
    if (type === 'device') return '⚡';
    if (category === 'protection') return '🛡️';
    if (category === 'control') return '🎛️';
    if (category === 'measurement') return '📊';
    return '🔧';
}

// 获取连接线宽度
function getLinkWidth(type) {
    const widths = {
        'trip': '3px',
        'ctl': '2px',
        'interlock': '2px',
        'mms': '2px',
        'goose': '2px',
        'smv': '2px',
        'primary': '1px'
    };
    return widths[type] || '1px';
}

// 获取节点颜色
function getNodeColor(type, category) {
    if (type === 'ied') {
        const colors = {
            'protection': '#dc3545',
            'control': '#28a745',
            'measurement': '#17a2b8'
        };
        return colors[category] || '#007bff';
    } else if (type === 'subnet') {
        const colors = {
            'mms': '#28a745',
            'goose': '#ffc107',
            'smv': '#17a2b8'
        };
        return colors[category] || '#6c757d';
    }
    return '#6c757d';
}

// 获取连接线颜色
function getLinkColor(type) {
    const colors = {
        'mms': '#28a745',
        'goose': '#ffc107',
        'smv': '#17a2b8',
        'trip': '#dc3545',
        'ctl': '#0d6efd',
        'interlock': '#6f42c1',
        'primary': '#6c757d'
    };
    return colors[type] || '#6c757d';
}

// 获取节点大小
function getNodeSize(type) {
    return type === 'ied' ? 18 : type === 'subnet' ? 25 : 20;
}

// 显示节点工具提示
function showNodeTooltip(event, d) {
    const tooltip = d3.select('#networkTooltip');
    tooltip.transition().duration(200).style('opacity', 1);
    tooltip.html(`
        <strong>${d.name}</strong><br>
        类型: ${d.type === 'ied' ? 'IED设备' : '子网'}<br>
        类别: ${d.category}
    `)
    .style('left', (event.pageX + 10) + 'px')
    .style('top', (event.pageY - 10) + 'px');
}

// 显示连接线工具提示
function showLinkTooltip(event, d) {
    const tooltip = d3.select('#networkTooltip');
    tooltip.transition().duration(200).style('opacity', 1);
    
    let tooltipContent = `
        <strong>虚端子连接</strong><br>
        从: ${d.source.name}<br>
        到: ${d.target.name}<br>
        类型: ${d.type.toUpperCase()}`;
    
    if (d._invalid) {
        tooltipContent += `<br><span style="color:#dc3545">⚠️ 违规: ${d._reason}</span>`;
    }
    
    tooltip.html(tooltipContent)
    .style('left', (event.pageX + 10) + 'px')
    .style('top', (event.pageY - 10) + 'px');
}

// 隐藏工具提示
function hideTooltip() {
    d3.select('#networkTooltip').transition().duration(200).style('opacity', 0);
}

// 选择节点
function selectNode(event, d) {
    // 显示选中元素信息
    const infoPanel = document.getElementById('selectionInfo');
    const detailsDiv = document.getElementById('selectionDetails');
    
    detailsDiv.innerHTML = `
        <h6>${d.name}</h6>
        <div class="small">
            <div><strong>ID:</strong> ${d.id}</div>
            <div><strong>类型:</strong> ${d.type === 'ied' ? 'IED设备' : '子网'}</div>
            <div><strong>类别:</strong> ${d.category}</div>
        </div>
    `;
    
    infoPanel.style.display = 'block';
}

// 拖拽事件处理
function dragStarted(event, d) {
    if (!event.active) simulation.alphaTarget(0.3).restart();
    d.fx = d.x;
    d.fy = d.y;
}

function dragged(event, d) {
    d.fx = event.x;
    d.fy = event.y;
}

function dragEnded(event, d) {
    if (!event.active) simulation.alphaTarget(0);
    d.fx = null;
    d.fy = null;
}

// 更新统计信息
function updateStatistics(data) {
    const iedCount = data.nodes.filter(n => n.type === 'ied').length;
    const subnetCount = data.nodes.filter(n => n.type === 'subnet').length;
    const connectionCount = data.links.length;
    const virtualTerminalCount = connectionCount * 2; // 每个连接有两个虚端子
    
    document.getElementById('iedCount').textContent = iedCount;
    document.getElementById('subnetCount').textContent = subnetCount;
    document.getElementById('connectionCount').textContent = connectionCount;
    document.getElementById('virtualTerminalCount').textContent = virtualTerminalCount;
}

// 控制函数
function changeView() {
    const viewType = document.getElementById('viewType').value;
    currentView = viewType;
    
    if (networkData) {
        renderNetwork(networkData);
    }
    
    toast.info('视图切换', `已切换到${viewType}视图`);
}

function changeLayout() {
    const layoutType = document.getElementById('layoutType').value;
    
    if (networkData) {
        if (layoutType === 'hflow' || layoutType === 'vflow') {
            applyFlowLayout(layoutType);
            toast.success('布局切换', layoutType === 'hflow' ? '已应用水平布局' : '已应用垂直布局');
            return;
        }
    }
    
    if (simulation && networkData) {
        // 根据布局类型调整力的参数
        switch (layoutType) {
            case 'hierarchical':
                simulation.force('charge', d3.forceManyBody().strength(-500));
                simulation.force('y', d3.forceY().strength(0.1));
                break;
            case 'circular':
                simulation.force('charge', d3.forceManyBody().strength(-100));
                simulation.force('radial', d3.forceRadial(150, svg.attr('width')/2, svg.attr('height')/2));
                break;
            case 'grid':
                simulation.force('charge', d3.forceManyBody().strength(-50));
                break;
            default: // force
                simulation.force('charge', d3.forceManyBody().strength(-300));
                break;
        }
        simulation.alpha(1).restart();
    }
}

// 确定性水平/垂直排布（按分组分行/分列）
function applyFlowLayout(direction) {
    const width = +svg.attr('width');
    const height = +svg.attr('height');
    const groups = ['networks','protection','control','measurement','primary','other'];
    const paddingOuter = 60;
    const paddingInner = 80;
    const nodeGap = 70;

    // 计算每组锚点
    const anchors = {};
    if (direction === 'hflow') {
        // 垂直分层，水平均匀
        const layerY = (idx) => paddingOuter + idx * ((height - 2*paddingOuter) / (groups.length - 1));
        groups.forEach((g, i) => {
            anchors[g] = { x: width/2, y: layerY(i) };
        });
        // 同组节点沿水平方向均匀排布
        groups.forEach(g => {
            const nodes = networkData.nodes.filter(n => getNodeGroup(n) === g);
            const n = nodes.length;
            if (n === 0) return;
            const startX = paddingOuter;
            nodes.forEach((node, idx) => {
                node.x = startX + idx * nodeGap;
                node.y = anchors[g].y;
            });
        });
    } else { // vflow
        // 水平分层，垂直均匀
        const layerX = (idx) => paddingOuter + idx * ((width - 2*paddingOuter) / (groups.length - 1));
        groups.forEach((g, i) => {
            anchors[g] = { x: layerX(i), y: height/2 };
        });
        // 同组节点沿垂直方向均匀排布
        groups.forEach(g => {
            const nodes = networkData.nodes.filter(n => getNodeGroup(n) === g);
            const n = nodes.length;
            if (n === 0) return;
            const startY = paddingOuter;
            nodes.forEach((node, idx) => {
                node.x = anchors[g].x;
                node.y = startY + idx * nodeGap;
            });
        });
    }

    // 停止力导向并直接渲染
    if (simulation) simulation.stop();
    renderNetwork(networkData);
    // 渲染后锁定节点位置，避免抖动
    svg.selectAll('.node-group').each(function(d){ d.fx = d.x; d.fy = d.y; });
}

function toggleDisplay(element) {
    // 实现显示/隐藏功能
    toast.info('显示控制', `已切换${element}的显示状态`);
}

function filterNetwork(type) {
    // 实现网络类型过滤
    toast.info('网络过滤', `已切换${type}网络的显示状态`);
}

function resetView() {
    if (networkData) {
        renderNetwork(networkData);
        toast.success('视图重置', '已重置到初始视图');
    }
}

function fitToScreen() {
    if (svg) {
        const bounds = svg.select('g').node().getBBox();
        const width = svg.attr('width');
        const height = svg.attr('height');
        
        const scale = Math.min(width / bounds.width, height / bounds.height) * 0.9;
        const translate = [
            width / 2 - scale * (bounds.x + bounds.width / 2),
            height / 2 - scale * (bounds.y + bounds.height / 2)
        ];
        
        svg.transition().duration(750).call(
            d3.zoom().transform,
            d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale)
        );
        
        toast.success('视图调整', '已适应屏幕大小');
    }
}

function zoomIn() {
    svg.transition().call(d3.zoom().scaleBy, 1.5);
}

function zoomOut() {
    svg.transition().call(d3.zoom().scaleBy, 1 / 1.5);
}

function toggleFullscreen() {
    const canvas = document.getElementById('networkCanvas');
    if (canvas.requestFullscreen) {
        canvas.requestFullscreen();
    }
}

function exportDiagram() {
    toast.info('导出功能', '图表导出功能开发中...');
}
</script>
{% endblock %}
