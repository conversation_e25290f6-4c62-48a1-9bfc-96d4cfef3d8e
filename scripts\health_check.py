#!/usr/bin/env python3
"""
系统健康检查脚本
检查系统各组件的健康状态
"""

import sys
import os
import time
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import subprocess
import socket
import psutil
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


class HealthChecker:
    """健康检查器"""
    
    def __init__(self, verbose: bool = False):
        """初始化健康检查器"""
        self.verbose = verbose
        self.checks = {}
        self.overall_status = True
        
        # 设置日志
        self._setup_logging()
        
        logger.info("健康检查器初始化完成")
    
    def run_all_checks(self, full_check: bool = False) -> Dict[str, Any]:
        """运行所有健康检查"""
        logger.info("开始系统健康检查")
        
        # 基础检查
        self.checks['python_environment'] = self._check_python_environment()
        self.checks['dependencies'] = self._check_dependencies()
        self.checks['file_system'] = self._check_file_system()
        self.checks['system_resources'] = self._check_system_resources()
        
        if full_check:
            # 完整检查
            self.checks['database'] = self._check_database()
            self.checks['network'] = self._check_network()
            self.checks['services'] = self._check_services()
            self.checks['application'] = self._check_application()
        
        # 生成总体状态
        self._generate_overall_status()
        
        # 生成报告
        report = self._generate_report()
        
        logger.info(f"健康检查完成 - 总体状态: {'健康' if self.overall_status else '异常'}")
        
        return report
    
    def _check_python_environment(self) -> Dict[str, Any]:
        """检查Python环境"""
        check_result = {
            'name': 'Python环境检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查Python版本
            python_version = sys.version_info
            check_result['details']['python_version'] = f"{python_version.major}.{python_version.minor}.{python_version.micro}"
            
            if python_version < (3, 8):
                check_result['status'] = 'fail'
                check_result['issues'].append(f"Python版本过低: {python_version}")
                check_result['recommendations'].append("升级到Python 3.8或更高版本")
            
            # 检查虚拟环境
            in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
            check_result['details']['virtual_environment'] = in_venv
            
            if not in_venv:
                check_result['status'] = 'warning'
                check_result['issues'].append("未使用虚拟环境")
                check_result['recommendations'].append("建议使用虚拟环境隔离依赖")
            
            # 检查pip版本
            try:
                import pip
                check_result['details']['pip_version'] = pip.__version__
            except ImportError:
                check_result['status'] = 'fail'
                check_result['issues'].append("pip未安装")
                check_result['recommendations'].append("安装pip包管理器")
            
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"Python环境检查失败: {e}")
        
        return check_result
    
    def _check_dependencies(self) -> Dict[str, Any]:
        """检查依赖包"""
        check_result = {
            'name': '依赖包检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 读取requirements.txt
            requirements_file = Path('requirements.txt')
            if not requirements_file.exists():
                check_result['status'] = 'fail'
                check_result['issues'].append("requirements.txt文件不存在")
                return check_result
            
            # 检查关键依赖
            critical_packages = [
                'pydantic', 'lxml', 'PySide6', 'pandas', 'numpy',
                'neo4j', 'networkx', 'flask', 'psutil'
            ]
            
            missing_packages = []
            installed_packages = {}
            
            for package in critical_packages:
                try:
                    __import__(package)
                    # 获取版本信息
                    try:
                        module = __import__(package)
                        version = getattr(module, '__version__', 'unknown')
                        installed_packages[package] = version
                    except:
                        installed_packages[package] = 'installed'
                except ImportError:
                    missing_packages.append(package)
            
            check_result['details']['installed_packages'] = installed_packages
            check_result['details']['missing_packages'] = missing_packages
            
            if missing_packages:
                check_result['status'] = 'fail'
                check_result['issues'].append(f"缺少关键依赖包: {', '.join(missing_packages)}")
                check_result['recommendations'].append("运行 pip install -r requirements.txt 安装依赖")
            
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"依赖检查失败: {e}")
        
        return check_result
    
    def _check_file_system(self) -> Dict[str, Any]:
        """检查文件系统"""
        check_result = {
            'name': '文件系统检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查关键目录
            required_dirs = ['src', 'tests', 'config', 'logs', 'uploads', 'reports']
            missing_dirs = []
            
            for dir_name in required_dirs:
                dir_path = Path(dir_name)
                if not dir_path.exists():
                    missing_dirs.append(dir_name)
                    # 尝试创建目录
                    try:
                        dir_path.mkdir(parents=True, exist_ok=True)
                        logger.info(f"创建缺失目录: {dir_name}")
                    except Exception as e:
                        check_result['issues'].append(f"无法创建目录 {dir_name}: {e}")
            
            check_result['details']['missing_directories'] = missing_dirs
            
            # 检查磁盘空间
            disk_usage = psutil.disk_usage('.')
            free_space_gb = disk_usage.free / (1024**3)
            check_result['details']['free_space_gb'] = round(free_space_gb, 2)
            
            if free_space_gb < 1:
                check_result['status'] = 'fail'
                check_result['issues'].append(f"磁盘空间不足: {free_space_gb:.2f}GB")
                check_result['recommendations'].append("清理磁盘空间或扩展存储")
            elif free_space_gb < 5:
                check_result['status'] = 'warning'
                check_result['issues'].append(f"磁盘空间较少: {free_space_gb:.2f}GB")
                check_result['recommendations'].append("建议清理不必要的文件")
            
            # 检查文件权限
            test_file = Path('test_write_permission.tmp')
            try:
                test_file.write_text('test')
                test_file.unlink()
                check_result['details']['write_permission'] = True
            except Exception as e:
                check_result['status'] = 'fail'
                check_result['issues'].append(f"写入权限检查失败: {e}")
                check_result['details']['write_permission'] = False
        
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"文件系统检查失败: {e}")
        
        return check_result
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        check_result = {
            'name': '系统资源检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查内存
            memory = psutil.virtual_memory()
            memory_gb = memory.total / (1024**3)
            memory_usage_percent = memory.percent
            
            check_result['details']['total_memory_gb'] = round(memory_gb, 2)
            check_result['details']['memory_usage_percent'] = memory_usage_percent
            
            if memory_gb < 4:
                check_result['status'] = 'warning'
                check_result['issues'].append(f"系统内存较少: {memory_gb:.2f}GB")
                check_result['recommendations'].append("建议增加系统内存到8GB或更多")
            
            if memory_usage_percent > 90:
                check_result['status'] = 'warning'
                check_result['issues'].append(f"内存使用率过高: {memory_usage_percent:.1f}%")
                check_result['recommendations'].append("关闭不必要的程序释放内存")
            
            # 检查CPU
            cpu_count = psutil.cpu_count()
            cpu_usage = psutil.cpu_percent(interval=1)
            
            check_result['details']['cpu_count'] = cpu_count
            check_result['details']['cpu_usage_percent'] = cpu_usage
            
            if cpu_count < 2:
                check_result['status'] = 'warning'
                check_result['issues'].append(f"CPU核心数较少: {cpu_count}")
                check_result['recommendations'].append("建议使用多核CPU提升性能")
            
            if cpu_usage > 80:
                check_result['status'] = 'warning'
                check_result['issues'].append(f"CPU使用率过高: {cpu_usage:.1f}%")
                check_result['recommendations'].append("检查是否有高CPU使用的进程")
            
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"系统资源检查失败: {e}")
        
        return check_result
    
    def _check_database(self) -> Dict[str, Any]:
        """检查数据库连接"""
        check_result = {
            'name': '数据库检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查Neo4j连接
            try:
                from neo4j import GraphDatabase
                
                # 尝试连接Neo4j
                uri = "bolt://localhost:7687"
                driver = GraphDatabase.driver(uri, auth=("neo4j", "password"))
                
                with driver.session() as session:
                    result = session.run("RETURN 1 as test")
                    test_value = result.single()["test"]
                    
                    if test_value == 1:
                        check_result['details']['neo4j_connection'] = 'success'
                    else:
                        check_result['status'] = 'fail'
                        check_result['issues'].append("Neo4j连接测试失败")
                
                driver.close()
                
            except Exception as e:
                check_result['status'] = 'warning'
                check_result['issues'].append(f"Neo4j连接失败: {e}")
                check_result['recommendations'].append("检查Neo4j服务是否启动，确认连接配置")
                check_result['details']['neo4j_connection'] = 'failed'
        
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"数据库检查失败: {e}")
        
        return check_result
    
    def _check_network(self) -> Dict[str, Any]:
        """检查网络连接"""
        check_result = {
            'name': '网络检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查端口占用
            ports_to_check = [8080, 7687, 7474]  # Web服务、Neo4j bolt、Neo4j http
            port_status = {}
            
            for port in ports_to_check:
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex(('localhost', port))
                    sock.close()
                    
                    port_status[port] = 'open' if result == 0 else 'closed'
                except Exception as e:
                    port_status[port] = f'error: {e}'
            
            check_result['details']['port_status'] = port_status
            
            # 检查网络接口
            network_interfaces = psutil.net_if_addrs()
            check_result['details']['network_interfaces'] = list(network_interfaces.keys())
            
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"网络检查失败: {e}")
        
        return check_result
    
    def _check_services(self) -> Dict[str, Any]:
        """检查服务状态"""
        check_result = {
            'name': '服务检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查Neo4j服务（如果安装）
            try:
                if os.name == 'nt':  # Windows
                    result = subprocess.run(['sc', 'query', 'neo4j'], 
                                          capture_output=True, text=True, timeout=5)
                    neo4j_running = 'RUNNING' in result.stdout
                else:  # Linux/macOS
                    result = subprocess.run(['systemctl', 'is-active', 'neo4j'], 
                                          capture_output=True, text=True, timeout=5)
                    neo4j_running = result.stdout.strip() == 'active'
                
                check_result['details']['neo4j_service'] = 'running' if neo4j_running else 'stopped'
                
                if not neo4j_running:
                    check_result['status'] = 'warning'
                    check_result['issues'].append("Neo4j服务未运行")
                    check_result['recommendations'].append("启动Neo4j服务")
                
            except subprocess.TimeoutExpired:
                check_result['details']['neo4j_service'] = 'timeout'
            except Exception as e:
                check_result['details']['neo4j_service'] = f'error: {e}'
        
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"服务检查失败: {e}")
        
        return check_result
    
    def _check_application(self) -> Dict[str, Any]:
        """检查应用程序"""
        check_result = {
            'name': '应用程序检查',
            'status': 'pass',
            'details': {},
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 检查主要模块是否可以导入
            modules_to_check = [
                'src.core.models',
                'src.core.parsers',
                'src.core.rules',
                'src.gui.main_window',
                'src.web.app'
            ]
            
            import_status = {}
            
            for module in modules_to_check:
                try:
                    __import__(module)
                    import_status[module] = 'success'
                except ImportError as e:
                    import_status[module] = f'failed: {e}'
                    check_result['status'] = 'fail'
                    check_result['issues'].append(f"模块导入失败: {module}")
            
            check_result['details']['module_imports'] = import_status
            
            # 检查配置文件
            config_files = ['requirements.txt', 'main.py', 'run_web.py']
            missing_files = []
            
            for config_file in config_files:
                if not Path(config_file).exists():
                    missing_files.append(config_file)
            
            if missing_files:
                check_result['status'] = 'fail'
                check_result['issues'].append(f"缺少关键文件: {', '.join(missing_files)}")
                check_result['recommendations'].append("确保所有必要文件存在")
            
            check_result['details']['missing_files'] = missing_files
        
        except Exception as e:
            check_result['status'] = 'error'
            check_result['issues'].append(f"应用程序检查失败: {e}")
        
        return check_result
    
    def _generate_overall_status(self):
        """生成总体状态"""
        for check_name, check_result in self.checks.items():
            if check_result['status'] in ['fail', 'error']:
                self.overall_status = False
                break
    
    def _generate_report(self) -> Dict[str, Any]:
        """生成健康检查报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy' if self.overall_status else 'unhealthy',
            'checks': self.checks,
            'summary': {
                'total_checks': len(self.checks),
                'passed': len([c for c in self.checks.values() if c['status'] == 'pass']),
                'warnings': len([c for c in self.checks.values() if c['status'] == 'warning']),
                'failed': len([c for c in self.checks.values() if c['status'] == 'fail']),
                'errors': len([c for c in self.checks.values() if c['status'] == 'error'])
            }
        }
        
        return report
    
    def _setup_logging(self):
        """设置日志"""
        global logger
        
        # 确保日志目录存在
        Path('logs').mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO if self.verbose else logging.WARNING,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/health_check.log', encoding='utf-8')
            ]
        )
        
        logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='IEC61850智能设计检查器健康检查')
    parser.add_argument('--full', action='store_true', help='执行完整检查')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--output', type=str, help='输出报告文件')
    
    args = parser.parse_args()
    
    # 创建健康检查器
    checker = HealthChecker(verbose=args.verbose)
    
    # 运行检查
    report = checker.run_all_checks(full_check=args.full)
    
    # 输出结果
    print(f"\n{'='*60}")
    print(f"IEC61850智能设计检查器 - 健康检查报告")
    print(f"{'='*60}")
    print(f"检查时间: {report['timestamp']}")
    print(f"总体状态: {report['overall_status'].upper()}")
    print(f"检查项目: {report['summary']['total_checks']}")
    print(f"通过: {report['summary']['passed']}")
    print(f"警告: {report['summary']['warnings']}")
    print(f"失败: {report['summary']['failed']}")
    print(f"错误: {report['summary']['errors']}")
    
    # 显示详细结果
    if args.verbose:
        print(f"\n详细检查结果:")
        for check_name, check_result in report['checks'].items():
            status_symbol = {
                'pass': '✅',
                'warning': '⚠️',
                'fail': '❌',
                'error': '💥'
            }.get(check_result['status'], '❓')
            
            print(f"\n{status_symbol} {check_result['name']}: {check_result['status'].upper()}")
            
            if check_result['issues']:
                print("  问题:")
                for issue in check_result['issues']:
                    print(f"    - {issue}")
            
            if check_result['recommendations']:
                print("  建议:")
                for rec in check_result['recommendations']:
                    print(f"    - {rec}")
    
    # 保存报告
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\n报告已保存到: {args.output}")
        except Exception as e:
            print(f"\n保存报告失败: {e}")
    
    # 返回适当的退出码
    sys.exit(0 if report['overall_status'] == 'healthy' else 1)


if __name__ == "__main__":
    main()
