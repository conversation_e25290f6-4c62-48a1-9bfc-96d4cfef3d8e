"""
规则引擎模块
提供IEC61850设计验证规则的定义、执行和管理功能
"""

from .base import (
    BaseRule, RuleResult, RuleContext, RuleSeverity, RuleCategory,
    ValidationError, ValidationWarning, ValidationInfo
)
from .engine import RuleEngine, RuleExecutor, ExecutionConfig
from .registry import RuleRegistry, rule_registry
from .config import RuleConfig, RuleConfigManager
from .report import ValidationReport, ReportGenerator

# 规则类别 - 使用简化版本进行测试
from .standard_simple import register_standard_rules
# from .device import DeviceRules
# from .communication import CommunicationRules
# from .datatype import DataTypeRules

__all__ = [
    # 基础框架
    'BaseRule', 'RuleResult', 'RuleContext', 'RuleSeverity', 'RuleCategory',
    'ValidationError', 'ValidationWarning', 'ValidationInfo',
    
    # 执行引擎
    'RuleEngine', 'RuleExecutor', 'ExecutionConfig',
    
    # 注册管理
    'RuleRegistry', 'rule_registry',
    
    # 配置管理
    'RuleConfig', 'RuleConfigManager',
    
    # 报告生成
    'ValidationReport', 'ReportGenerator',
    
    # 规则类别
    'register_standard_rules'
    # 'StandardRules', 'DeviceRules', 'CommunicationRules', 'DataTypeRules'
]
