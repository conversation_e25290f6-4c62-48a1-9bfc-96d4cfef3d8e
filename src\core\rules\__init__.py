"""
规则引擎模块
提供基于IEC61850标准和电力系统规程的规则验证功能
"""

from .base import (
    BaseRule, RuleContext, RuleResult, RuleSeverity, RuleCategory,
    ValidationIssue, ValidationError, ValidationWarning
)
from .engine import RuleEngine, RuleExecutor, ExecutionConfig, ExecutionResult
from .registry import RuleRegistry, rule_registry
from .circuit_logic import register_circuit_logic_rules

__all__ = [
    'BaseRule', 'RuleContext', 'RuleResult', 'RuleSeverity', 'RuleCategory',
    'ValidationIssue', 'ValidationError', 'ValidationWarning',
    'RuleEngine', 'RuleExecutor', 'ExecutionConfig', 'ExecutionResult',
    'RuleRegistry', 'rule_registry',
    'register_circuit_logic_rules'
]
