#!/usr/bin/env python3
"""
详细设计检查器
专门检查IEC61850配置文件和图纸中的具体设计问题
"""

import xml.etree.ElementTree as ET
from typing import List, Dict, Set, Optional
from pathlib import Path
import re

# 避免循环导入，在运行时导入
# from .unified_review_engine import UnifiedReviewIssue


class DesignChecker:
    """详细设计检查器"""
    
    def __init__(self):
        self.namespace = {'scl': 'http://www.iec.ch/61850/2003/SCL'}
    
    def check_config_design(self, file_path: str) -> List:
        """检查配置文件设计问题"""
        issues = []
        
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # 检查1: 断路器缺少Terminal连接
            issues.extend(self._check_missing_terminals(root))
            
            # 检查2: 缺少必要的互感器
            issues.extend(self._check_missing_transformers(root))
            
            # 检查3: 电流互感器二次侧连接
            issues.extend(self._check_ct_secondary_connections(root))
            
            # 检查4: 电压等级设备配置完整性
            issues.extend(self._check_voltage_level_completeness(root))
            
            # 检查5: 通信网络配置问题
            issues.extend(self._check_communication_issues(root))
            
            # 检查6: IP地址冲突
            issues.extend(self._check_ip_conflicts(root))
            
            # 检查7: 数据集引用完整性
            issues.extend(self._check_dataset_references(root))
            
            # 检查8: 逻辑节点数据对象完整性
            issues.extend(self._check_logical_node_completeness(root))
            
            # 检查9: IED配置完整性
            issues.extend(self._check_ied_completeness(root))
            
            # 检查10: 数据类型定义完整性
            issues.extend(self._check_datatype_completeness(root))
            
        except Exception as e:
            # 动态导入避免循环导入
            from .unified_review_engine import UnifiedReviewIssue
            issues.append(UnifiedReviewIssue(
                title="配置文件解析错误",
                description=f"解析配置文件时发生错误: {str(e)}",
                severity="critical",
                category="解析错误",
                source_type="config",
                suggestion="检查XML文件格式是否正确"
            ))
        
        return issues
    
    def _check_missing_terminals(self, root: ET.Element) -> List:
        """检查断路器缺少Terminal连接"""
        issues = []
        # 动态导入避免循环导入
        from .unified_review_engine import UnifiedReviewIssue
        
        # 查找所有断路器
        breakers = root.findall(".//ConductingEquipment[@type='CBR']")
        
        for breaker in breakers:
            breaker_name = breaker.get('name', '未知断路器')
            terminals = breaker.findall("Terminal")
            
            if len(terminals) == 0:
                issues.append(UnifiedReviewIssue(
                    title=f"断路器{breaker_name}缺少Terminal连接",
                    description=f"断路器{breaker_name}没有定义任何Terminal连接，导致回路不通",
                    severity="critical",
                    category="回路连接",
                    source_type="config",
                    location=f"断路器: {breaker_name}",
                    suggestion=f"为断路器{breaker_name}添加至少两个Terminal连接，分别连接到母线和线路侧",
                    standard_reference="IEC61850-6: 配置描述语言",
                    affected_elements=[breaker_name]
                ))
            elif len(terminals) == 1:
                issues.append(UnifiedReviewIssue(
                    title=f"断路器{breaker_name}Terminal连接不完整",
                    description=f"断路器{breaker_name}只有一个Terminal连接，无法形成完整回路",
                    severity="error",
                    category="回路连接",
                    source_type="config",
                    location=f"断路器: {breaker_name}",
                    suggestion=f"为断路器{breaker_name}添加第二个Terminal连接",
                    standard_reference="IEC61850-6: 配置描述语言",
                    affected_elements=[breaker_name]
                ))
        
        return issues
    
    def _check_missing_transformers(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查缺少必要的互感器"""
        issues = []
        
        # 查找所有间隔
        bays = root.findall(".//Bay")
        
        for bay in bays:
            bay_name = bay.get('name', '未知间隔')
            
            # 检查是否有断路器
            breakers = bay.findall(".//ConductingEquipment[@type='CBR']")
            if breakers:
                # 检查是否有电流互感器
                current_transformers = bay.findall(".//ConductingEquipment[@type='CTR']")
                if not current_transformers:
                    issues.append(UnifiedReviewIssue(
                        title=f"间隔{bay_name}缺少电流互感器",
                        description=f"间隔{bay_name}有断路器但缺少电流互感器，无法进行电流测量和保护",
                        severity="error",
                        category="设备配置",
                        source_type="config",
                        location=f"间隔: {bay_name}",
                        suggestion=f"在间隔{bay_name}中添加电流互感器(CTR)用于电流测量",
                        standard_reference="IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        affected_elements=[bay_name]
                    ))
                
                # 检查是否有电压互感器（对于重要间隔）
                if "220kV" in bay_name or "110kV" in bay_name:
                    voltage_transformers = bay.findall(".//ConductingEquipment[@type='VTR']")
                    if not voltage_transformers:
                        issues.append(UnifiedReviewIssue(
                            title=f"高压间隔{bay_name}缺少电压互感器",
                            description=f"高压间隔{bay_name}缺少电压互感器，无法进行电压测量",
                            severity="warning",
                            category="设备配置",
                            source_type="config",
                            location=f"间隔: {bay_name}",
                            suggestion=f"在间隔{bay_name}中添加电压互感器(VTR)用于电压测量",
                            standard_reference="IEC61850-7-4: 兼容的逻辑节点类和数据类",
                            affected_elements=[bay_name]
                        ))
        
        return issues
    
    def _check_ct_secondary_connections(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查电流互感器二次侧连接"""
        issues = []
        
        # 查找所有电流互感器
        current_transformers = root.findall(".//ConductingEquipment[@type='CTR']")
        
        for ct in current_transformers:
            ct_name = ct.get('name', '未知电流互感器')
            terminals = ct.findall("Terminal")
            
            if len(terminals) < 2:
                issues.append(UnifiedReviewIssue(
                    title=f"电流互感器{ct_name}缺少二次侧连接",
                    description=f"电流互感器{ct_name}缺少二次侧Terminal连接，无法向保护和测量装置提供电流信号",
                    severity="error",
                    category="虚端子连接",
                    source_type="config",
                    location=f"电流互感器: {ct_name}",
                    suggestion=f"为电流互感器{ct_name}添加二次侧Terminal连接，连接到保护和测量装置",
                    standard_reference="IEC61850-9-2: 采样值传输的特殊通信服务映射",
                    affected_elements=[ct_name]
                ))
        
        return issues
    
    def _check_voltage_level_completeness(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查电压等级设备配置完整性"""
        issues = []
        
        # 查找所有电压等级
        voltage_levels = root.findall(".//VoltageLevel")
        
        for vl in voltage_levels:
            vl_name = vl.get('name', '未知电压等级')
            
            # 检查是否有间隔
            bays = vl.findall("Bay")
            if not bays:
                issues.append(UnifiedReviewIssue(
                    title=f"电压等级{vl_name}没有配置任何间隔",
                    description=f"电压等级{vl_name}下没有定义任何间隔，这是不完整的配置",
                    severity="error",
                    category="配置完整性",
                    source_type="config",
                    location=f"电压等级: {vl_name}",
                    suggestion=f"在电压等级{vl_name}下添加相应的间隔配置，如出线间隔、母联间隔等",
                    standard_reference="IEC61850-6: 配置描述语言",
                    affected_elements=[vl_name]
                ))
            
            # 检查间隔中是否有设备
            for bay in bays:
                bay_name = bay.get('name', '未知间隔')
                equipment = bay.findall(".//ConductingEquipment")
                if not equipment:
                    issues.append(UnifiedReviewIssue(
                        title=f"间隔{bay_name}没有配置任何设备",
                        description=f"间隔{bay_name}下没有定义任何导电设备",
                        severity="warning",
                        category="配置完整性",
                        source_type="config",
                        location=f"间隔: {bay_name}",
                        suggestion=f"在间隔{bay_name}中添加相应的导电设备，如断路器、隔离开关等",
                        standard_reference="IEC61850-6: 配置描述语言",
                        affected_elements=[bay_name]
                    ))
        
        return issues
    
    def _check_communication_issues(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查通信网络配置问题"""
        issues = []
        
        # 查找通信配置
        communication = root.find("Communication")
        if communication is None:
            issues.append(UnifiedReviewIssue(
                title="缺少通信网络配置",
                description="配置文件中没有定义Communication部分，无法进行网络通信",
                severity="critical",
                category="通信配置",
                source_type="config",
                location="根节点",
                suggestion="添加Communication部分，定义子网络和IED连接",
                standard_reference="IEC61850-8-1: 特定通信服务映射",
                affected_elements=["Communication"]
            ))
            return issues
        
        # 检查网关配置
        connected_aps = communication.findall(".//ConnectedAP")
        for ap in connected_aps:
            ied_name = ap.get('iedName', '未知IED')
            address = ap.find("Address")
            if address is not None:
                gateway = address.find("P[@type='IP-GATEWAY']")
                if gateway is None:
                    issues.append(UnifiedReviewIssue(
                        title=f"IED {ied_name}缺少网关配置",
                        description=f"IED {ied_name}的网络配置中缺少IP-GATEWAY设置",
                        severity="warning",
                        category="通信配置",
                        source_type="config",
                        location=f"IED: {ied_name}",
                        suggestion=f"为IED {ied_name}添加IP-GATEWAY配置",
                        standard_reference="IEC61850-8-1: 特定通信服务映射",
                        affected_elements=[ied_name]
                    ))
        
        return issues

    def _check_ip_conflicts(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查IP地址冲突"""
        issues = []
        ip_addresses = {}

        # 收集所有IP地址
        connected_aps = root.findall(".//ConnectedAP")
        for ap in connected_aps:
            ied_name = ap.get('iedName', '未知IED')
            address = ap.find("Address")
            if address is not None:
                ip_elem = address.find("P[@type='IP']")
                if ip_elem is not None:
                    ip_addr = ip_elem.text
                    if ip_addr in ip_addresses:
                        issues.append(UnifiedReviewIssue(
                            title=f"IP地址冲突: {ip_addr}",
                            description=f"IP地址{ip_addr}被多个IED使用: {ip_addresses[ip_addr]} 和 {ied_name}",
                            severity="critical",
                            category="通信配置",
                            source_type="config",
                            location=f"IED: {ied_name}",
                            suggestion=f"为IED {ied_name}分配唯一的IP地址",
                            standard_reference="IEC61850-8-1: 特定通信服务映射",
                            affected_elements=[ip_addresses[ip_addr], ied_name]
                        ))
                    else:
                        ip_addresses[ip_addr] = ied_name

        return issues

    def _check_dataset_references(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查数据集引用完整性"""
        issues = []

        # 查找所有数据集
        datasets = root.findall(".//DataSet")
        for dataset in datasets:
            dataset_name = dataset.get('name', '未知数据集')

            # 检查数据集中的FCDA引用
            fcdas = dataset.findall("FCDA")
            for fcda in fcdas:
                ln_class = fcda.get('lnClass')
                ln_inst = fcda.get('lnInst')
                ld_inst = fcda.get('ldInst')

                # 查找对应的逻辑节点
                ln_xpath = f".//LN[@lnClass='{ln_class}'][@inst='{ln_inst}']"
                logical_nodes = root.findall(ln_xpath)

                if not logical_nodes:
                    issues.append(UnifiedReviewIssue(
                        title=f"数据集{dataset_name}引用不存在的逻辑节点",
                        description=f"数据集{dataset_name}中引用了不存在的逻辑节点: {ln_class}{ln_inst}",
                        severity="error",
                        category="数据集配置",
                        source_type="config",
                        location=f"数据集: {dataset_name}",
                        suggestion=f"检查逻辑节点{ln_class}{ln_inst}是否正确定义，或修正数据集中的引用",
                        standard_reference="IEC61850-7-2: 抽象通信服务接口",
                        affected_elements=[dataset_name, f"{ln_class}{ln_inst}"]
                    ))

        return issues

    def _check_logical_node_completeness(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查逻辑节点数据对象完整性"""
        issues = []

        # 查找所有保护逻辑节点
        protection_lns = root.findall(".//LN[@lnClass='PTRC']")
        for ln in protection_lns:
            ln_inst = ln.get('inst', '未知实例')
            ln_type = ln.get('lnType')

            # 检查逻辑节点类型是否存在
            if ln_type:
                type_xpath = f".//LNodeType[@id='{ln_type}']"
                type_def = root.find(type_xpath)
                if type_def is None:
                    issues.append(UnifiedReviewIssue(
                        title=f"保护逻辑节点PTRC{ln_inst}引用不存在的类型",
                        description=f"保护逻辑节点PTRC{ln_inst}引用了不存在的类型定义: {ln_type}",
                        severity="error",
                        category="逻辑节点配置",
                        source_type="config",
                        location=f"逻辑节点: PTRC{ln_inst}",
                        suggestion=f"检查类型定义{ln_type}是否存在，或修正逻辑节点的类型引用",
                        standard_reference="IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        affected_elements=[f"PTRC{ln_inst}", ln_type]
                    ))

        return issues

    def _check_ied_completeness(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查IED配置完整性"""
        issues = []

        # 查找所有IED
        ieds = root.findall(".//IED")
        for ied in ieds:
            ied_name = ied.get('name', '未知IED')
            ied_type = ied.get('type', '未知类型')

            # 检查保护IED是否有保护逻辑节点
            if ied_type == 'Protection':
                protection_lns = ied.findall(".//LN[@lnClass='PTRC']")
                if not protection_lns:
                    issues.append(UnifiedReviewIssue(
                        title=f"保护IED {ied_name}缺少保护逻辑节点",
                        description=f"保护类型的IED {ied_name}没有定义任何保护逻辑节点(PTRC)",
                        severity="error",
                        category="IED配置",
                        source_type="config",
                        location=f"IED: {ied_name}",
                        suggestion=f"为保护IED {ied_name}添加相应的保护逻辑节点(PTRC)",
                        standard_reference="IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        affected_elements=[ied_name]
                    ))

        return issues

    def _check_datatype_completeness(self, root: ET.Element) -> List[UnifiedReviewIssue]:
        """检查数据类型定义完整性"""
        issues = []

        # 查找数据类型模板部分
        templates = root.find("DataTypeTemplates")
        if templates is None:
            issues.append(UnifiedReviewIssue(
                title="缺少数据类型模板定义",
                description="配置文件中没有DataTypeTemplates部分，无法定义数据类型",
                severity="critical",
                category="数据类型定义",
                source_type="config",
                location="根节点",
                suggestion="添加DataTypeTemplates部分，定义必要的数据类型",
                standard_reference="IEC61850-6: 配置描述语言",
                affected_elements=["DataTypeTemplates"]
            ))
            return issues

        # 检查LLN0类型是否有NamPlt
        lln0_types = templates.findall("LNodeType[@lnClass='LLN0']")
        for lln0_type in lln0_types:
            type_id = lln0_type.get('id', '未知类型')
            namPlt_do = lln0_type.find("DO[@name='NamPlt']")
            if namPlt_do is None:
                issues.append(UnifiedReviewIssue(
                    title=f"LLN0类型{type_id}缺少NamPlt数据对象",
                    description=f"LLN0类型{type_id}缺少必要的NamPlt(名牌)数据对象",
                    severity="error",
                    category="数据类型定义",
                    source_type="config",
                    location=f"LNodeType: {type_id}",
                    suggestion=f"为LLN0类型{type_id}添加NamPlt数据对象",
                    standard_reference="IEC61850-7-4: 兼容的逻辑节点类和数据类",
                    affected_elements=[type_id]
                ))

        return issues

    def check_drawing_design(self, file_path: str) -> List[UnifiedReviewIssue]:
        """检查图纸设计问题"""
        issues = []

        try:
            # 解析DXF文件
            dxf_data = self._parse_dxf_file(file_path)

            # 检查1: 回路连通性
            issues.extend(self._check_circuit_continuity(dxf_data))

            # 检查2: 线型规范
            issues.extend(self._check_line_types(dxf_data))

            # 检查3: 线宽规范
            issues.extend(self._check_line_weights(dxf_data))

            # 检查4: 文字标注规范
            issues.extend(self._check_text_standards(dxf_data))

            # 检查5: 图层使用规范
            issues.extend(self._check_layer_standards(dxf_data))

            # 检查6: 设备符号完整性
            issues.extend(self._check_symbol_completeness(dxf_data))

        except Exception as e:
            issues.append(UnifiedReviewIssue(
                title="图纸文件解析错误",
                description=f"解析图纸文件时发生错误: {str(e)}",
                severity="critical",
                category="解析错误",
                source_type="drawing",
                suggestion="检查DXF文件格式是否正确"
            ))

        return issues

    def _parse_dxf_file(self, file_path: str) -> Dict:
        """解析DXF文件"""
        dxf_data = {
            'layers': {},
            'lines': [],
            'circles': [],
            'texts': [],
            'entities': []
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 简单的DXF解析
            lines = content.split('\n')
            i = 0
            current_entity = {}

            while i < len(lines):
                line = lines[i].strip()

                if line == '0' and i + 1 < len(lines):
                    entity_type = lines[i + 1].strip()

                    if entity_type == 'LAYER':
                        # 解析图层
                        layer_info = self._parse_layer(lines, i)
                        if layer_info:
                            dxf_data['layers'][layer_info['name']] = layer_info

                    elif entity_type == 'LINE':
                        # 解析直线
                        line_info = self._parse_line(lines, i)
                        if line_info:
                            dxf_data['lines'].append(line_info)
                            dxf_data['entities'].append(line_info)

                    elif entity_type == 'CIRCLE':
                        # 解析圆
                        circle_info = self._parse_circle(lines, i)
                        if circle_info:
                            dxf_data['circles'].append(circle_info)
                            dxf_data['entities'].append(circle_info)

                    elif entity_type == 'TEXT':
                        # 解析文字
                        text_info = self._parse_text(lines, i)
                        if text_info:
                            dxf_data['texts'].append(text_info)
                            dxf_data['entities'].append(text_info)

                i += 1

        except Exception as e:
            print(f"DXF解析错误: {e}")

        return dxf_data

    def _parse_layer(self, lines: List[str], start_idx: int) -> Optional[Dict]:
        """解析图层信息"""
        layer_info = {'type': 'LAYER'}
        i = start_idx + 2

        while i < len(lines) and lines[i].strip() != '0':
            code = lines[i].strip()
            if i + 1 < len(lines):
                value = lines[i + 1].strip()

                if code == '2':  # 图层名
                    layer_info['name'] = value
                elif code == '62':  # 颜色
                    layer_info['color'] = value
                elif code == '6':  # 线型
                    layer_info['linetype'] = value
                elif code == '370':  # 线宽
                    layer_info['lineweight'] = value

            i += 2

        return layer_info if 'name' in layer_info else None

    def _parse_line(self, lines: List[str], start_idx: int) -> Optional[Dict]:
        """解析直线信息"""
        line_info = {'type': 'LINE'}
        i = start_idx + 2

        while i < len(lines) and lines[i].strip() != '0':
            code = lines[i].strip()
            if i + 1 < len(lines):
                value = lines[i + 1].strip()

                if code == '8':  # 图层
                    line_info['layer'] = value
                elif code == '10':  # 起点X
                    line_info['start_x'] = float(value)
                elif code == '20':  # 起点Y
                    line_info['start_y'] = float(value)
                elif code == '11':  # 终点X
                    line_info['end_x'] = float(value)
                elif code == '21':  # 终点Y
                    line_info['end_y'] = float(value)
                elif code == '6':  # 线型
                    line_info['linetype'] = value
                elif code == '370':  # 线宽
                    line_info['lineweight'] = value

            i += 2

        return line_info if 'layer' in line_info else None

    def _parse_circle(self, lines: List[str], start_idx: int) -> Optional[Dict]:
        """解析圆信息"""
        circle_info = {'type': 'CIRCLE'}
        i = start_idx + 2

        while i < len(lines) and lines[i].strip() != '0':
            code = lines[i].strip()
            if i + 1 < len(lines):
                value = lines[i + 1].strip()

                if code == '8':  # 图层
                    circle_info['layer'] = value
                elif code == '10':  # 圆心X
                    circle_info['center_x'] = float(value)
                elif code == '20':  # 圆心Y
                    circle_info['center_y'] = float(value)
                elif code == '40':  # 半径
                    circle_info['radius'] = float(value)

            i += 2

        return circle_info if 'layer' in circle_info else None

    def _parse_text(self, lines: List[str], start_idx: int) -> Optional[Dict]:
        """解析文字信息"""
        text_info = {'type': 'TEXT'}
        i = start_idx + 2

        while i < len(lines) and lines[i].strip() != '0':
            code = lines[i].strip()
            if i + 1 < len(lines):
                value = lines[i + 1].strip()

                if code == '8':  # 图层
                    text_info['layer'] = value
                elif code == '1':  # 文字内容
                    text_info['text'] = value
                elif code == '10':  # 位置X
                    text_info['x'] = float(value)
                elif code == '20':  # 位置Y
                    text_info['y'] = float(value)
                elif code == '40':  # 文字高度
                    text_info['height'] = float(value)

            i += 2

        return text_info if 'layer' in text_info else None

    def _check_circuit_continuity(self, dxf_data: Dict) -> List[UnifiedReviewIssue]:
        """检查回路连通性"""
        issues = []

        # 检查主回路连通性
        main_circuit_lines = [line for line in dxf_data['lines'] if line.get('layer') == '主回路']

        if len(main_circuit_lines) < 2:
            issues.append(UnifiedReviewIssue(
                title="主回路连接不完整",
                description="主回路图层中的连线数量不足，可能存在回路不通的问题",
                severity="error",
                category="回路连接",
                source_type="drawing",
                location="主回路图层",
                suggestion="检查主回路连接，确保所有设备之间有完整的电气连接",
                standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                affected_elements=["主回路"]
            ))

        # 检查控制回路连通性
        control_circuit_lines = [line for line in dxf_data['lines'] if line.get('layer') == '控制回路']

        # 检查控制回路是否有断点
        if control_circuit_lines:
            # 简单检查：控制回路线段之间是否有明显的间隙
            for i, line in enumerate(control_circuit_lines):
                if i > 0:
                    prev_line = control_circuit_lines[i-1]
                    # 检查线段之间的连接
                    gap_x = abs(line.get('start_x', 0) - prev_line.get('end_x', 0))
                    gap_y = abs(line.get('start_y', 0) - prev_line.get('end_y', 0))

                    if gap_x > 50 or gap_y > 50:  # 间隙过大
                        issues.append(UnifiedReviewIssue(
                            title=f"控制回路第{i+1}段存在断点",
                            description=f"控制回路在坐标({line.get('start_x', 0)}, {line.get('start_y', 0)})附近存在断点",
                            severity="warning",
                            category="回路连接",
                            source_type="drawing",
                            location=f"控制回路第{i+1}段",
                            suggestion="检查控制回路连接，消除断点确保回路连通",
                            standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                            affected_elements=["控制回路"]
                        ))

        return issues

    def _check_line_types(self, dxf_data: Dict) -> List[UnifiedReviewIssue]:
        """检查线型规范"""
        issues = []

        # 检查主回路线型
        for line in dxf_data['lines']:
            layer = line.get('layer', '')
            linetype = line.get('linetype', 'CONTINUOUS')

            if layer == '主回路' and linetype != 'CONTINUOUS':
                issues.append(UnifiedReviewIssue(
                    title="主回路线型不规范",
                    description=f"主回路应使用连续线型(CONTINUOUS)，当前使用: {linetype}",
                    severity="warning",
                    category="线型规范",
                    source_type="drawing",
                    location="主回路图层",
                    suggestion="将主回路的线型改为连续线型(CONTINUOUS)",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=["主回路"]
                ))

            elif layer == '控制回路' and linetype not in ['DASHED', 'DOTTED']:
                issues.append(UnifiedReviewIssue(
                    title="控制回路线型不规范",
                    description=f"控制回路应使用虚线型(DASHED)或点线型(DOTTED)，当前使用: {linetype}",
                    severity="info",
                    category="线型规范",
                    source_type="drawing",
                    location="控制回路图层",
                    suggestion="将控制回路的线型改为虚线型(DASHED)或点线型(DOTTED)",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=["控制回路"]
                ))

        return issues

    def _check_line_weights(self, dxf_data: Dict) -> List[UnifiedReviewIssue]:
        """检查线宽规范"""
        issues = []

        for line in dxf_data['lines']:
            layer = line.get('layer', '')
            lineweight = line.get('lineweight', '25')  # 默认线宽

            try:
                weight_value = int(lineweight)

                if layer == '主回路' and weight_value < 50:
                    issues.append(UnifiedReviewIssue(
                        title="主回路线宽过细",
                        description=f"主回路线宽应不小于0.5mm(50)，当前线宽: {weight_value}",
                        severity="warning",
                        category="线宽规范",
                        source_type="drawing",
                        location="主回路图层",
                        suggestion="将主回路线宽调整为0.5mm或更粗",
                        standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                        affected_elements=["主回路"]
                    ))

                elif layer == '控制回路' and weight_value > 25:
                    issues.append(UnifiedReviewIssue(
                        title="控制回路线宽过粗",
                        description=f"控制回路线宽应不大于0.25mm(25)，当前线宽: {weight_value}",
                        severity="info",
                        category="线宽规范",
                        source_type="drawing",
                        location="控制回路图层",
                        suggestion="将控制回路线宽调整为0.25mm或更细",
                        standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                        affected_elements=["控制回路"]
                    ))

            except ValueError:
                pass  # 忽略无效的线宽值

        return issues

    def _check_text_standards(self, dxf_data: Dict) -> List[UnifiedReviewIssue]:
        """检查文字标注规范"""
        issues = []

        for text in dxf_data['texts']:
            text_content = text.get('text', '')
            text_height = text.get('height', 0)
            layer = text.get('layer', '')

            # 检查文字高度
            if layer == '文字标注' and text_height < 1.5:
                issues.append(UnifiedReviewIssue(
                    title=f"文字标注'{text_content}'高度过小",
                    description=f"文字标注'{text_content}'高度为{text_height}，小于最小要求1.5mm",
                    severity="warning",
                    category="文字标注",
                    source_type="drawing",
                    location=f"坐标({text.get('x', 0)}, {text.get('y', 0)})",
                    suggestion="将文字标注高度调整为1.5mm或更大",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[text_content]
                ))

            elif layer == '文字标注' and text_height > 8.0:
                issues.append(UnifiedReviewIssue(
                    title=f"文字标注'{text_content}'高度过大",
                    description=f"文字标注'{text_content}'高度为{text_height}，超过建议最大值8.0mm",
                    severity="info",
                    category="文字标注",
                    source_type="drawing",
                    location=f"坐标({text.get('x', 0)}, {text.get('y', 0)})",
                    suggestion="将文字标注高度调整为8.0mm或更小",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[text_content]
                ))

            # 检查设备编号规范
            if re.match(r'^Q[SF]\d+$', text_content):  # 断路器或隔离开关编号
                # 检查编号是否在正确的图层
                if layer != '文字标注':
                    issues.append(UnifiedReviewIssue(
                        title=f"设备编号'{text_content}'图层错误",
                        description=f"设备编号'{text_content}'应在'文字标注'图层，当前在'{layer}'图层",
                        severity="warning",
                        category="文字标注",
                        source_type="drawing",
                        location=f"坐标({text.get('x', 0)}, {text.get('y', 0)})",
                        suggestion=f"将设备编号'{text_content}'移动到'文字标注'图层",
                        standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                        affected_elements=[text_content]
                    ))

        return issues

    def _check_layer_standards(self, dxf_data: Dict) -> List[UnifiedReviewIssue]:
        """检查图层使用规范"""
        issues = []

        # 检查是否有错误图层
        for layer_name, layer_info in dxf_data['layers'].items():
            if '错误' in layer_name or 'error' in layer_name.lower():
                issues.append(UnifiedReviewIssue(
                    title=f"存在错误图层: {layer_name}",
                    description=f"图纸中存在名为'{layer_name}'的错误图层，可能包含错误内容",
                    severity="error",
                    category="图层规范",
                    source_type="drawing",
                    location=f"图层: {layer_name}",
                    suggestion=f"检查并删除错误图层'{layer_name}'，或将其内容移动到正确的图层",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[layer_name]
                ))

        # 检查标准图层是否存在
        required_layers = ['主回路', '控制回路', '文字标注']
        existing_layers = set(dxf_data['layers'].keys())

        for required_layer in required_layers:
            if required_layer not in existing_layers:
                issues.append(UnifiedReviewIssue(
                    title=f"缺少标准图层: {required_layer}",
                    description=f"图纸中缺少标准图层'{required_layer}'",
                    severity="warning",
                    category="图层规范",
                    source_type="drawing",
                    location="图层定义",
                    suggestion=f"添加标准图层'{required_layer}'",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[required_layer]
                ))

        # 检查图层中的内容是否在错误图层
        for entity in dxf_data['entities']:
            layer = entity.get('layer', '')
            if '错误' in layer or 'error' in layer.lower():
                entity_type = entity.get('type', '未知')
                issues.append(UnifiedReviewIssue(
                    title=f"图元在错误图层中",
                    description=f"{entity_type}类型的图元位于错误图层'{layer}'中",
                    severity="warning",
                    category="图层规范",
                    source_type="drawing",
                    location=f"图层: {layer}",
                    suggestion=f"将{entity_type}图元移动到正确的图层",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[layer]
                ))

        return issues

    def _check_symbol_completeness(self, dxf_data: Dict) -> List[UnifiedReviewIssue]:
        """检查设备符号完整性"""
        issues = []

        # 检查断路器符号
        qf_texts = [text for text in dxf_data['texts'] if text.get('text', '').startswith('QF')]
        qf_circles = [circle for circle in dxf_data['circles'] if circle.get('layer') == '主回路']

        if len(qf_texts) > len(qf_circles):
            issues.append(UnifiedReviewIssue(
                title="断路器符号不完整",
                description=f"发现{len(qf_texts)}个断路器标注，但只有{len(qf_circles)}个圆形符号",
                severity="error",
                category="设备符号",
                source_type="drawing",
                location="主回路图层",
                suggestion="为每个断路器标注添加对应的圆形符号",
                standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                affected_elements=["断路器符号"]
            ))

        # 检查隔离开关符号
        qs_texts = [text for text in dxf_data['texts'] if text.get('text', '').startswith('QS')]

        for qs_text in qs_texts:
            qs_name = qs_text.get('text', '')
            qs_x = qs_text.get('x', 0)
            qs_y = qs_text.get('y', 0)

            # 查找附近是否有对应的符号（简单检查：附近是否有线段）
            nearby_lines = [
                line for line in dxf_data['lines']
                if line.get('layer') == '主回路' and
                abs(line.get('start_x', 0) - qs_x) < 50 and
                abs(line.get('start_y', 0) - qs_y) < 50
            ]

            if not nearby_lines:
                issues.append(UnifiedReviewIssue(
                    title=f"隔离开关{qs_name}缺少符号",
                    description=f"隔离开关{qs_name}有文字标注但缺少对应的图形符号",
                    severity="warning",
                    category="设备符号",
                    source_type="drawing",
                    location=f"坐标({qs_x}, {qs_y})",
                    suggestion=f"为隔离开关{qs_name}添加对应的图形符号",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[qs_name]
                ))

        # 检查文字标注与符号的对应关系
        device_texts = [text for text in dxf_data['texts'] if re.match(r'^Q[SF]\d+$', text.get('text', ''))]

        for device_text in device_texts:
            device_name = device_text.get('text', '')
            text_x = device_text.get('x', 0)
            text_y = device_text.get('y', 0)

            # 检查文字标注附近是否有设备符号
            nearby_symbols = []

            # 检查附近的圆形（断路器）
            for circle in dxf_data['circles']:
                if (abs(circle.get('center_x', 0) - text_x) < 30 and
                    abs(circle.get('center_y', 0) - text_y) < 30):
                    nearby_symbols.append('circle')

            # 检查附近的线段（隔离开关等）
            for line in dxf_data['lines']:
                if (line.get('layer') == '主回路' and
                    abs(line.get('start_x', 0) - text_x) < 30 and
                    abs(line.get('start_y', 0) - text_y) < 30):
                    nearby_symbols.append('line')

            if not nearby_symbols:
                issues.append(UnifiedReviewIssue(
                    title=f"设备{device_name}标注与符号不匹配",
                    description=f"设备{device_name}有文字标注但附近没有找到对应的设备符号",
                    severity="warning",
                    category="设备符号",
                    source_type="drawing",
                    location=f"坐标({text_x}, {text_y})",
                    suggestion=f"检查设备{device_name}的符号位置，确保文字标注与符号正确对应",
                    standard_reference="GB/T 4728.1-2005 电气简图用图形符号",
                    affected_elements=[device_name]
                ))

        return issues
