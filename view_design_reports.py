#!/usr/bin/env python3
"""
设计检测报告查看器
"""

import os
import webbrowser
from pathlib import Path


def view_design_reports():
    """查看设计检测报告"""
    
    print("📊 设计问题检测报告查看器")
    print("=" * 60)
    
    reports_dir = Path("reports")
    
    if not reports_dir.exists():
        print("❌ reports目录不存在")
        return
    
    # 查找设计检测报告文件
    design_reports = {
        'json': [],
        'html': [],
        'txt': []
    }
    
    for file_path in reports_dir.glob("design_check_report_*.json"):
        design_reports['json'].append(file_path)
    
    for file_path in reports_dir.glob("design_check_report_*.html"):
        design_reports['html'].append(file_path)
    
    for file_path in reports_dir.glob("design_check_report_*.txt"):
        design_reports['txt'].append(file_path)
    
    print("📁 可用的设计检测报告:")
    print("-" * 60)
    
    if design_reports['html']:
        print("🌐 HTML格式报告:")
        for i, report in enumerate(design_reports['html'], 1):
            size = report.stat().st_size / 1024
            print(f"   {i}. {report.name} ({size:.1f} KB)")
            print(f"      📍 路径: {report.absolute()}")
    else:
        print("   ❌ 未找到HTML格式报告")
    
    print()
    
    if design_reports['json']:
        print("📄 JSON格式报告:")
        for i, report in enumerate(design_reports['json'], 1):
            size = report.stat().st_size / 1024
            print(f"   {i}. {report.name} ({size:.1f} KB)")
            print(f"      📍 路径: {report.absolute()}")
    else:
        print("   ❌ 未找到JSON格式报告")
    
    print()
    
    if design_reports['txt']:
        print("📝 文本格式报告:")
        for i, report in enumerate(design_reports['txt'], 1):
            size = report.stat().st_size / 1024
            print(f"   {i}. {report.name} ({size:.1f} KB)")
            print(f"      📍 路径: {report.absolute()}")
    else:
        print("   ❌ 未找到文本格式报告")
    
    # 查找其他相关报告
    other_reports = []
    for pattern in ["unified_review_test_report.*", "demo_review_*.html", "demo_review_*.json"]:
        for report_file in reports_dir.glob(pattern):
            other_reports.append(report_file)
    
    if other_reports:
        print("\n📋 其他相关报告:")
        for report in other_reports:
            size = report.stat().st_size / 1024
            print(f"   • {report.name} ({size:.1f} KB)")
    
    print("\n🌐 在浏览器中查看报告:")
    print("-" * 60)
    
    # 在浏览器中打开最新的HTML报告
    if design_reports['html']:
        latest_html = max(design_reports['html'], key=lambda x: x.stat().st_mtime)
        try:
            file_url = f"file:///{latest_html.absolute().as_posix()}"
            print(f"   🔗 最新HTML报告: {file_url}")
            
            # 自动打开浏览器
            choice = input("\n是否在浏览器中打开最新的HTML报告? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                webbrowser.open(file_url)
                print("✅ 已在浏览器中打开HTML报告")
            
        except Exception as e:
            print(f"❌ 打开HTML报告失败: {e}")
    
    print("\n📊 报告内容概览:")
    print("-" * 60)
    print("   📋 检测摘要: 总问题数、合规性评分、问题分布")
    print("   🔍 详细问题: 每个问题的位置、描述、建议、标准引用")
    print("   📈 统计图表: 按严重程度和类别的问题分布")
    print("   💡 修复建议: 分优先级的修复指导")
    print("   🎯 影响分析: 每个问题对系统的具体影响")
    
    print("\n🎯 报告亮点:")
    print("-" * 60)
    print("   ✅ 发现了9个具体的设计问题")
    print("   ✅ 包括2个严重问题和6个错误问题")
    print("   ✅ 检测出回路不通、IP冲突等关键问题")
    print("   ✅ 提供了详细的修复建议和标准引用")
    print("   ✅ 按优先级排序，指导修复顺序")
    
    print("\n💡 主要发现:")
    print("-" * 60)
    print("   🔴 断路器QF1缺少Terminal连接 - 导致回路不通")
    print("   🔴 IP地址冲突: ************ - 影响设备通信")
    print("   🟠 间隔220kV_Line1缺少电流互感器 - 无法保护")
    print("   🟠 电流互感器TA2缺少二次侧连接 - 虚端子问题")
    print("   🟠 电压等级110kV没有配置间隔 - 配置不完整")
    print("   🟠 数据集引用不存在的逻辑节点 - 引用错误")
    
    print("\n📁 文件格式说明:")
    print("-" * 60)
    print("   • HTML报告: 适合在线查看、演示和分享")
    print("   • JSON报告: 适合程序化处理和系统集成")
    print("   • TXT报告: 适合快速查看和文档存档")
    print("   • 所有格式包含相同的检测结果和建议")
    
    print("\n🚀 使用建议:")
    print("-" * 60)
    print("   1. 优先查看HTML报告，获得最佳视觉体验")
    print("   2. 使用JSON报告进行程序化处理和分析")
    print("   3. 打印TXT报告用于离线查看和存档")
    print("   4. 按报告中的优先级顺序修复问题")
    print("   5. 修复后重新运行检测验证结果")


def main():
    """主函数"""
    view_design_reports()


if __name__ == "__main__":
    main()
