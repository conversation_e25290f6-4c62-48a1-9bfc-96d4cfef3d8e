"""
虚端子表生成器
从SCD文件自动生成虚端子连接关系表
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from datetime import datetime

from .vt_models import (
    VirtualTerminal, VirtualConnection, VirtualTerminalTable,
    TerminalType, ConnectionType, SignalType
)


logger = logging.getLogger(__name__)


class VirtualTerminalGenerator:
    """虚端子表生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.data_type_mapping = self._load_data_type_mapping()
        self.signal_type_mapping = self._load_signal_type_mapping()
        self.fc_terminal_type_mapping = self._load_fc_terminal_type_mapping()
        
        logger.info("虚端子表生成器初始化完成")
    
    def generate_from_scd(self, scd_data: Dict[str, Any], 
                         table_name: Optional[str] = None) -> VirtualTerminalTable:
        """
        从SCD文件生成虚端子表
        
        Args:
            scd_data: SCD文件解析数据
            table_name: 表名称
            
        Returns:
            VirtualTerminalTable: 生成的虚端子表
        """
        try:
            logger.info("开始从SCD文件生成虚端子表")
            
            # 创建虚端子表
            if not table_name:
                table_name = f"VT_Table_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            table = VirtualTerminalTable(
                table_id=f"vt_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                name=table_name,
                description="从SCD文件自动生成的虚端子表"
            )
            
            # 提取虚端子
            terminals = self._extract_terminals_from_scd(scd_data)
            for terminal in terminals:
                table.add_terminal(terminal)
            
            # 提取虚连接
            connections = self._extract_connections_from_scd(scd_data, table)
            for connection in connections:
                table.add_connection(connection)
            
            # 添加元数据
            table.metadata = {
                'source': 'SCD',
                'generation_time': datetime.now().isoformat(),
                'scd_info': self._extract_scd_metadata(scd_data)
            }
            
            logger.info(f"虚端子表生成完成: {len(terminals)} 个端子, {len(connections)} 个连接")
            return table
            
        except Exception as e:
            logger.error(f"虚端子表生成失败: {e}")
            raise
    
    def generate_from_devices(self, devices: List[Dict[str, Any]], 
                            table_name: Optional[str] = None) -> VirtualTerminalTable:
        """
        从设备列表生成虚端子表
        
        Args:
            devices: 设备列表
            table_name: 表名称
            
        Returns:
            VirtualTerminalTable: 生成的虚端子表
        """
        try:
            logger.info(f"开始从 {len(devices)} 个设备生成虚端子表")
            
            if not table_name:
                table_name = f"VT_Table_Devices_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            table = VirtualTerminalTable(
                table_id=f"vt_dev_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                name=table_name,
                description="从设备配置自动生成的虚端子表"
            )
            
            # 从每个设备提取端子
            for device in devices:
                device_terminals = self._extract_terminals_from_device(device)
                for terminal in device_terminals:
                    table.add_terminal(terminal)
            
            # 添加元数据
            table.metadata = {
                'source': 'devices',
                'generation_time': datetime.now().isoformat(),
                'device_count': len(devices),
                'device_names': [d.get('name', 'Unknown') for d in devices]
            }
            
            logger.info(f"虚端子表生成完成: {len(table.terminals)} 个端子")
            return table
            
        except Exception as e:
            logger.error(f"从设备生成虚端子表失败: {e}")
            raise
    
    def _extract_terminals_from_scd(self, scd_data: Dict[str, Any]) -> List[VirtualTerminal]:
        """从SCD数据提取虚端子"""
        terminals = []
        
        try:
            # 获取IED列表
            ieds = scd_data.get('ieds', [])
            
            for ied in ieds:
                ied_name = ied.get('name', 'Unknown_IED')
                
                # 获取逻辑设备
                logical_devices = ied.get('logical_devices', [])
                for ld in logical_devices:
                    ld_name = ld.get('name', 'Unknown_LD')
                    
                    # 获取逻辑节点
                    logical_nodes = ld.get('logical_nodes', [])
                    for ln in logical_nodes:
                        ln_name = ln.get('name', 'Unknown_LN')
                        
                        # 获取数据对象
                        data_objects = ln.get('data_objects', [])
                        for do in data_objects:
                            do_name = do.get('name', 'Unknown_DO')
                            
                            # 创建虚端子
                            terminal = self._create_terminal_from_data_object(
                                ied_name, ld_name, ln_name, do_name, do
                            )
                            if terminal:
                                terminals.append(terminal)
                                
                                # 如果数据对象有子属性，也创建端子
                                data_attributes = do.get('data_attributes', [])
                                for da in data_attributes:
                                    da_name = da.get('name', 'Unknown_DA')
                                    da_terminal = self._create_terminal_from_data_attribute(
                                        ied_name, ld_name, ln_name, do_name, da_name, da
                                    )
                                    if da_terminal:
                                        terminals.append(da_terminal)
            
            logger.debug(f"从SCD提取到 {len(terminals)} 个虚端子")
            return terminals
            
        except Exception as e:
            logger.error(f"从SCD提取虚端子失败: {e}")
            return []
    
    def _extract_terminals_from_device(self, device: Dict[str, Any]) -> List[VirtualTerminal]:
        """从单个设备提取虚端子"""
        terminals = []
        
        try:
            device_name = device.get('name', 'Unknown_Device')
            
            # 获取逻辑设备
            logical_devices = device.get('logical_devices', [])
            for ld in logical_devices:
                ld_name = ld.get('name', 'Unknown_LD')
                
                # 获取逻辑节点
                logical_nodes = ld.get('logical_nodes', [])
                for ln in logical_nodes:
                    ln_name = ln.get('name', 'Unknown_LN')
                    
                    # 获取数据对象
                    data_objects = ln.get('data_objects', [])
                    for do in data_objects:
                        do_name = do.get('name', 'Unknown_DO')
                        
                        # 创建虚端子
                        terminal = self._create_terminal_from_data_object(
                            device_name, ld_name, ln_name, do_name, do
                        )
                        if terminal:
                            terminals.append(terminal)
            
            return terminals
            
        except Exception as e:
            logger.error(f"从设备 {device.get('name')} 提取虚端子失败: {e}")
            return []
    
    def _create_terminal_from_data_object(self, 
                                        device_name: str,
                                        ld_name: str,
                                        ln_name: str,
                                        do_name: str,
                                        do_data: Dict[str, Any]) -> Optional[VirtualTerminal]:
        """从数据对象创建虚端子"""
        try:
            # 获取基本信息
            data_type = do_data.get('type', 'Unknown')
            fc = do_data.get('fc', 'Unknown')
            description = do_data.get('desc', '')
            
            # 确定端子类型
            terminal_type = self._determine_terminal_type(fc, do_data)
            
            # 确定信号类型
            signal_type = self._determine_signal_type(data_type, do_name)
            
            # 创建端子
            terminal = VirtualTerminal(
                terminal_id="",  # 将自动生成
                device_name=device_name,
                logical_device=ld_name,
                logical_node=ln_name,
                data_object=do_name,
                terminal_type=terminal_type,
                signal_type=signal_type,
                data_type=data_type,
                functional_constraint=fc,
                description=description
            )
            
            # 设置其他属性
            self._set_terminal_properties(terminal, do_data)
            
            return terminal
            
        except Exception as e:
            logger.error(f"创建虚端子失败: {e}")
            return None
    
    def _create_terminal_from_data_attribute(self,
                                           device_name: str,
                                           ld_name: str,
                                           ln_name: str,
                                           do_name: str,
                                           da_name: str,
                                           da_data: Dict[str, Any]) -> Optional[VirtualTerminal]:
        """从数据属性创建虚端子"""
        try:
            # 获取基本信息
            data_type = da_data.get('type', 'Unknown')
            fc = da_data.get('fc', 'Unknown')
            description = da_data.get('desc', '')
            
            # 确定端子类型
            terminal_type = self._determine_terminal_type(fc, da_data)
            
            # 确定信号类型
            signal_type = self._determine_signal_type(data_type, da_name)
            
            # 创建端子
            terminal = VirtualTerminal(
                terminal_id="",  # 将自动生成
                device_name=device_name,
                logical_device=ld_name,
                logical_node=ln_name,
                data_object=do_name,
                data_attribute=da_name,
                terminal_type=terminal_type,
                signal_type=signal_type,
                data_type=data_type,
                functional_constraint=fc,
                description=description
            )
            
            # 设置其他属性
            self._set_terminal_properties(terminal, da_data)
            
            return terminal
            
        except Exception as e:
            logger.error(f"创建数据属性虚端子失败: {e}")
            return None
    
    def _extract_connections_from_scd(self, 
                                    scd_data: Dict[str, Any],
                                    table: VirtualTerminalTable) -> List[VirtualConnection]:
        """从SCD数据提取虚连接"""
        connections = []
        
        try:
            # 提取GOOSE连接
            goose_connections = self._extract_goose_connections(scd_data, table)
            connections.extend(goose_connections)
            
            # 提取SMV连接
            smv_connections = self._extract_smv_connections(scd_data, table)
            connections.extend(smv_connections)
            
            # 提取报告连接
            report_connections = self._extract_report_connections(scd_data, table)
            connections.extend(report_connections)
            
            logger.debug(f"从SCD提取到 {len(connections)} 个虚连接")
            return connections
            
        except Exception as e:
            logger.error(f"从SCD提取虚连接失败: {e}")
            return []
    
    def _extract_goose_connections(self, 
                                 scd_data: Dict[str, Any],
                                 table: VirtualTerminalTable) -> List[VirtualConnection]:
        """提取GOOSE连接"""
        connections = []
        
        try:
            # 从通信部分获取GOOSE配置
            communication = scd_data.get('communication', {})
            goose_configs = communication.get('goose', [])
            
            for goose_config in goose_configs:
                # 解析GOOSE发布者和订阅者
                publisher = goose_config.get('publisher')
                subscribers = goose_config.get('subscribers', [])
                
                if publisher:
                    # 找到发布端子
                    pub_terminal = self._find_terminal_by_reference(publisher, table)
                    
                    if pub_terminal:
                        # 为每个订阅者创建连接
                        for subscriber in subscribers:
                            sub_terminal = self._find_terminal_by_reference(subscriber, table)
                            
                            if sub_terminal:
                                connection = VirtualConnection(
                                    connection_id="",  # 将自动生成
                                    source_terminal=pub_terminal,
                                    target_terminal=sub_terminal,
                                    connection_type=ConnectionType.GOOSE,
                                    description=f"GOOSE: {goose_config.get('name', 'Unknown')}",
                                    configuration=goose_config
                                )
                                connections.append(connection)
            
            return connections
            
        except Exception as e:
            logger.error(f"提取GOOSE连接失败: {e}")
            return []
    
    def _extract_smv_connections(self, 
                               scd_data: Dict[str, Any],
                               table: VirtualTerminalTable) -> List[VirtualConnection]:
        """提取SMV连接"""
        connections = []
        
        try:
            # 从通信部分获取SMV配置
            communication = scd_data.get('communication', {})
            smv_configs = communication.get('smv', [])
            
            for smv_config in smv_configs:
                # 解析SMV发布者和订阅者
                publisher = smv_config.get('publisher')
                subscribers = smv_config.get('subscribers', [])
                
                if publisher:
                    # 找到发布端子
                    pub_terminal = self._find_terminal_by_reference(publisher, table)
                    
                    if pub_terminal:
                        # 为每个订阅者创建连接
                        for subscriber in subscribers:
                            sub_terminal = self._find_terminal_by_reference(subscriber, table)
                            
                            if sub_terminal:
                                connection = VirtualConnection(
                                    connection_id="",  # 将自动生成
                                    source_terminal=pub_terminal,
                                    target_terminal=sub_terminal,
                                    connection_type=ConnectionType.SMV,
                                    description=f"SMV: {smv_config.get('name', 'Unknown')}",
                                    configuration=smv_config
                                )
                                connections.append(connection)
            
            return connections
            
        except Exception as e:
            logger.error(f"提取SMV连接失败: {e}")
            return []
    
    def _extract_report_connections(self, 
                                  scd_data: Dict[str, Any],
                                  table: VirtualTerminalTable) -> List[VirtualConnection]:
        """提取报告连接"""
        connections = []
        
        try:
            # 从IED配置中获取报告控制块
            ieds = scd_data.get('ieds', [])
            
            for ied in ieds:
                logical_devices = ied.get('logical_devices', [])
                for ld in logical_devices:
                    logical_nodes = ld.get('logical_nodes', [])
                    for ln in logical_nodes:
                        # 获取报告控制块
                        report_controls = ln.get('report_controls', [])
                        for rcb in report_controls:
                            # 解析报告数据集和客户端
                            dataset = rcb.get('dataset')
                            clients = rcb.get('clients', [])
                            
                            if dataset:
                                # 找到数据集中的端子
                                dataset_terminals = self._find_terminals_in_dataset(dataset, table)
                                
                                # 为每个客户端创建连接
                                for client in clients:
                                    client_terminal = self._find_terminal_by_reference(client, table)
                                    
                                    if client_terminal:
                                        for source_terminal in dataset_terminals:
                                            connection = VirtualConnection(
                                                connection_id="",  # 将自动生成
                                                source_terminal=source_terminal,
                                                target_terminal=client_terminal,
                                                connection_type=ConnectionType.MMS,
                                                description=f"Report: {rcb.get('name', 'Unknown')}",
                                                configuration=rcb
                                            )
                                            connections.append(connection)
            
            return connections
            
        except Exception as e:
            logger.error(f"提取报告连接失败: {e}")
            return []
    
    def _determine_terminal_type(self, fc: str, data: Dict[str, Any]) -> TerminalType:
        """确定端子类型"""
        # 基于功能约束确定端子类型
        output_fcs = ['ST', 'MX', 'CO']  # 状态、测量、控制输出
        input_fcs = ['SP', 'SV', 'CF']   # 设定点、设定值、配置
        
        if fc in output_fcs:
            return TerminalType.OUTPUT
        elif fc in input_fcs:
            return TerminalType.INPUT
        else:
            return TerminalType.BIDIRECTIONAL
    
    def _determine_signal_type(self, data_type: str, name: str) -> SignalType:
        """确定信号类型"""
        # 基于数据类型和名称确定信号类型
        if data_type in ['BOOLEAN', 'Dbpos', 'Tcmd']:
            return SignalType.DIGITAL
        elif data_type in ['FLOAT32', 'FLOAT64', 'INT32', 'AnalogueValue']:
            if any(keyword in name.lower() for keyword in ['cmd', 'ctrl', 'set']):
                return SignalType.COMMAND
            else:
                return SignalType.MEASUREMENT
        elif data_type in ['Quality', 'Timestamp']:
            return SignalType.STATUS
        else:
            return SignalType.DIGITAL
    
    def _set_terminal_properties(self, terminal: VirtualTerminal, data: Dict[str, Any]):
        """设置端子属性"""
        # 设置单位
        if 'unit' in data:
            terminal.unit = data['unit']
        
        # 设置范围
        if 'min' in data:
            terminal.range_min = data['min']
        if 'max' in data:
            terminal.range_max = data['max']
        
        # 设置默认值
        if 'default' in data:
            terminal.default_value = data['default']
        
        # 设置关键性
        if 'critical' in data:
            terminal.is_critical = data['critical']
        
        # 设置标签
        if 'tags' in data:
            terminal.tags = data['tags']
    
    def _find_terminal_by_reference(self, 
                                   reference: Dict[str, Any],
                                   table: VirtualTerminalTable) -> Optional[VirtualTerminal]:
        """根据引用查找端子"""
        try:
            # 构建端子ID
            device_name = reference.get('ied_name', '')
            ld_name = reference.get('ld_name', '')
            ln_name = reference.get('ln_name', '')
            do_name = reference.get('do_name', '')
            da_name = reference.get('da_name')
            
            parts = [device_name, ld_name, ln_name, do_name]
            if da_name:
                parts.append(da_name)
            
            terminal_id = "/".join(parts)
            return table.get_terminal_by_id(terminal_id)
            
        except Exception as e:
            logger.error(f"查找端子失败: {e}")
            return None
    
    def _find_terminals_in_dataset(self, 
                                 dataset: Dict[str, Any],
                                 table: VirtualTerminalTable) -> List[VirtualTerminal]:
        """查找数据集中的端子"""
        terminals = []
        
        try:
            # 获取数据集中的数据引用
            data_refs = dataset.get('data_refs', [])
            
            for data_ref in data_refs:
                terminal = self._find_terminal_by_reference(data_ref, table)
                if terminal:
                    terminals.append(terminal)
            
            return terminals
            
        except Exception as e:
            logger.error(f"查找数据集端子失败: {e}")
            return []
    
    def _extract_scd_metadata(self, scd_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取SCD元数据"""
        metadata = {}
        
        try:
            header = scd_data.get('header', {})
            metadata.update({
                'version': header.get('version', ''),
                'revision': header.get('revision', ''),
                'toolID': header.get('toolID', ''),
                'nameStructure': header.get('nameStructure', '')
            })
            
            # 统计信息
            ieds = scd_data.get('ieds', [])
            metadata.update({
                'ied_count': len(ieds),
                'ied_names': [ied.get('name', 'Unknown') for ied in ieds]
            })
            
        except Exception as e:
            logger.error(f"提取SCD元数据失败: {e}")
        
        return metadata
    
    def _load_data_type_mapping(self) -> Dict[str, str]:
        """加载数据类型映射"""
        return {
            'BOOLEAN': 'BOOLEAN',
            'INT8': 'INT8',
            'INT16': 'INT16', 
            'INT32': 'INT32',
            'FLOAT32': 'FLOAT32',
            'FLOAT64': 'FLOAT64',
            'VISIBLE_STRING': 'STRING',
            'TIMESTAMP': 'TIMESTAMP',
            'QUALITY': 'QUALITY'
        }
    
    def _load_signal_type_mapping(self) -> Dict[str, SignalType]:
        """加载信号类型映射"""
        return {
            'BOOLEAN': SignalType.DIGITAL,
            'INT32': SignalType.ANALOG,
            'FLOAT32': SignalType.ANALOG,
            'QUALITY': SignalType.STATUS,
            'TIMESTAMP': SignalType.STATUS
        }
    
    def _load_fc_terminal_type_mapping(self) -> Dict[str, TerminalType]:
        """加载功能约束到端子类型的映射"""
        return {
            'ST': TerminalType.OUTPUT,  # 状态信息
            'MX': TerminalType.OUTPUT,  # 测量值
            'CO': TerminalType.OUTPUT,  # 控制输出
            'SP': TerminalType.INPUT,   # 设定点
            'SV': TerminalType.INPUT,   # 设定值
            'CF': TerminalType.INPUT,   # 配置
            'DC': TerminalType.BIDIRECTIONAL,  # 描述
            'EX': TerminalType.BIDIRECTIONAL   # 扩展定义
        }
