"""
知识图谱核心类
使用NetworkX构建电力标准的语义网络
"""

import logging
from typing import Dict, List, Optional, Any, Tuple, Set
import networkx as nx
import json
from datetime import datetime

from ..base.knowledge_entity import (
    KnowledgeEntity, EntityRelationship, EntityType, RelationshipType
)


logger = logging.getLogger(__name__)


class KnowledgeGraph:
    """知识图谱核心类"""
    
    def __init__(self):
        """初始化知识图谱"""
        # 使用有向图存储知识
        self.graph = nx.DiGraph()
        
        # 实体索引
        self.entity_index = {}  # entity_id -> entity
        self.type_index = {}    # entity_type -> [entity_ids]
        self.name_index = {}    # entity_name -> entity_id
        
        # 关系索引
        self.relationship_index = {}  # relationship_id -> relationship
        self.relation_type_index = {} # relation_type -> [relationship_ids]
        
        # 统计信息
        self.stats = {
            'total_entities': 0,
            'total_relationships': 0,
            'entity_types': {},
            'relationship_types': {},
            'last_updated': datetime.now()
        }
        
        logger.info("知识图谱初始化完成")
    
    def add_entity(self, entity: KnowledgeEntity) -> bool:
        """
        添加实体到知识图谱
        
        Args:
            entity: 知识实体
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 添加节点到图中
            self.graph.add_node(
                entity.id,
                entity_type=entity.entity_type.value,
                name=entity.name,
                description=entity.description,
                confidence=entity.confidence,
                attributes=entity.attributes
            )
            
            # 更新索引
            self.entity_index[entity.id] = entity
            self.name_index[entity.name] = entity.id
            
            # 更新类型索引
            entity_type = entity.entity_type.value
            if entity_type not in self.type_index:
                self.type_index[entity_type] = []
            self.type_index[entity_type].append(entity.id)
            
            # 更新统计信息
            self.stats['total_entities'] += 1
            if entity_type not in self.stats['entity_types']:
                self.stats['entity_types'][entity_type] = 0
            self.stats['entity_types'][entity_type] += 1
            self.stats['last_updated'] = datetime.now()
            
            logger.debug(f"添加实体到知识图谱: {entity.name} ({entity.entity_type})")
            return True
            
        except Exception as e:
            logger.error(f"添加实体失败: {e}")
            return False
    
    def add_relationship(self, relationship: EntityRelationship) -> bool:
        """
        添加关系到知识图谱
        
        Args:
            relationship: 实体关系
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 检查源实体和目标实体是否存在
            if (relationship.source_entity_id not in self.entity_index or
                relationship.target_entity_id not in self.entity_index):
                logger.warning(f"关系的源实体或目标实体不存在: {relationship.id}")
                return False
            
            # 添加边到图中
            self.graph.add_edge(
                relationship.source_entity_id,
                relationship.target_entity_id,
                relationship_id=relationship.id,
                relationship_type=relationship.relationship_type.value,
                strength=relationship.strength,
                confidence=relationship.confidence,
                attributes=relationship.attributes
            )
            
            # 更新关系索引
            self.relationship_index[relationship.id] = relationship
            
            # 更新关系类型索引
            rel_type = relationship.relationship_type.value
            if rel_type not in self.relation_type_index:
                self.relation_type_index[rel_type] = []
            self.relation_type_index[rel_type].append(relationship.id)
            
            # 更新统计信息
            self.stats['total_relationships'] += 1
            if rel_type not in self.stats['relationship_types']:
                self.stats['relationship_types'][rel_type] = 0
            self.stats['relationship_types'][rel_type] += 1
            self.stats['last_updated'] = datetime.now()
            
            logger.debug(f"添加关系到知识图谱: {relationship.relationship_type}")
            return True
            
        except Exception as e:
            logger.error(f"添加关系失败: {e}")
            return False
    
    def get_entity(self, entity_id: str) -> Optional[KnowledgeEntity]:
        """获取实体"""
        return self.entity_index.get(entity_id)
    
    def get_entity_by_name(self, name: str) -> Optional[KnowledgeEntity]:
        """根据名称获取实体"""
        entity_id = self.name_index.get(name)
        if entity_id:
            return self.entity_index.get(entity_id)
        return None
    
    def get_entities_by_type(self, entity_type: EntityType) -> List[KnowledgeEntity]:
        """根据类型获取实体"""
        entity_ids = self.type_index.get(entity_type.value, [])
        return [self.entity_index[eid] for eid in entity_ids if eid in self.entity_index]
    
    def get_neighbors(self, entity_id: str, direction: str = "both") -> List[str]:
        """
        获取邻居节点
        
        Args:
            entity_id: 实体ID
            direction: 方向 ("in", "out", "both")
            
        Returns:
            List[str]: 邻居节点ID列表
        """
        if entity_id not in self.graph:
            return []
        
        if direction == "in":
            return list(self.graph.predecessors(entity_id))
        elif direction == "out":
            return list(self.graph.successors(entity_id))
        else:  # both
            return list(self.graph.neighbors(entity_id))
    
    def get_related_entities(self, 
                           entity_id: str,
                           relationship_type: str = None,
                           max_depth: int = 2) -> List[Tuple[str, int]]:
        """
        获取相关实体
        
        Args:
            entity_id: 起始实体ID
            relationship_type: 关系类型过滤
            max_depth: 最大搜索深度
            
        Returns:
            List[Tuple[str, int]]: (实体ID, 距离)列表
        """
        if entity_id not in self.graph:
            return []
        
        related = []
        visited = {entity_id}
        queue = [(entity_id, 0)]
        
        while queue:
            current_id, depth = queue.pop(0)
            
            if depth >= max_depth:
                continue
            
            # 获取邻居
            for neighbor_id in self.graph.neighbors(current_id):
                if neighbor_id in visited:
                    continue
                
                # 检查关系类型过滤
                if relationship_type:
                    edge_data = self.graph.get_edge_data(current_id, neighbor_id)
                    if not edge_data or edge_data.get('relationship_type') != relationship_type:
                        continue
                
                visited.add(neighbor_id)
                related.append((neighbor_id, depth + 1))
                queue.append((neighbor_id, depth + 1))
        
        return related
    
    def find_path(self, source_id: str, target_id: str) -> Optional[List[str]]:
        """
        查找两个实体间的路径
        
        Args:
            source_id: 源实体ID
            target_id: 目标实体ID
            
        Returns:
            Optional[List[str]]: 路径节点ID列表
        """
        try:
            if source_id not in self.graph or target_id not in self.graph:
                return None
            
            return nx.shortest_path(self.graph, source_id, target_id)
        except nx.NetworkXNoPath:
            return None
        except Exception as e:
            logger.error(f"路径查找失败: {e}")
            return None
    
    def get_subgraph(self, entity_ids: List[str]) -> 'KnowledgeGraph':
        """
        获取子图
        
        Args:
            entity_ids: 实体ID列表
            
        Returns:
            KnowledgeGraph: 子图
        """
        subgraph = KnowledgeGraph()
        
        # 添加实体
        for entity_id in entity_ids:
            if entity_id in self.entity_index:
                subgraph.add_entity(self.entity_index[entity_id])
        
        # 添加关系
        for edge in self.graph.edges(data=True):
            source, target, data = edge
            if source in entity_ids and target in entity_ids:
                rel_id = data.get('relationship_id')
                if rel_id and rel_id in self.relationship_index:
                    subgraph.add_relationship(self.relationship_index[rel_id])
        
        return subgraph
    
    def search_entities(self, 
                       query: str,
                       entity_types: List[EntityType] = None,
                       limit: int = 10) -> List[Tuple[KnowledgeEntity, float]]:
        """
        搜索实体
        
        Args:
            query: 搜索查询
            entity_types: 实体类型过滤
            limit: 结果数量限制
            
        Returns:
            List[Tuple[KnowledgeEntity, float]]: (实体, 相关性分数)列表
        """
        results = []
        query_lower = query.lower()
        
        for entity in self.entity_index.values():
            # 类型过滤
            if entity_types and entity.entity_type not in entity_types:
                continue
            
            # 计算相关性分数
            score = 0.0
            
            # 名称匹配
            if query_lower in entity.name.lower():
                score += 1.0
            
            # 描述匹配
            if query_lower in entity.description.lower():
                score += 0.5
            
            # 关键词匹配
            for keyword in entity.keywords:
                if query_lower in keyword.lower():
                    score += 0.3
            
            # 标签匹配
            for tag in entity.tags:
                if query_lower in tag.lower():
                    score += 0.2
            
            if score > 0:
                results.append((entity, score))
        
        # 按分数排序
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:limit]
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取图统计信息"""
        # 更新图统计
        self.stats.update({
            'nodes': self.graph.number_of_nodes(),
            'edges': self.graph.number_of_edges(),
            'density': nx.density(self.graph),
            'is_connected': nx.is_weakly_connected(self.graph),
            'components': nx.number_weakly_connected_components(self.graph)
        })
        
        return self.stats.copy()
    
    def export_graph(self, format: str = "json") -> str:
        """
        导出图数据
        
        Args:
            format: 导出格式 ("json", "gexf", "graphml")
            
        Returns:
            str: 导出的数据
        """
        try:
            if format == "json":
                data = {
                    'entities': [entity.dict() for entity in self.entity_index.values()],
                    'relationships': [rel.dict() for rel in self.relationship_index.values()],
                    'statistics': self.get_statistics()
                }
                return json.dumps(data, indent=2, default=str)
            
            elif format == "gexf":
                return '\n'.join(nx.generate_gexf(self.graph))
            
            elif format == "graphml":
                return '\n'.join(nx.generate_graphml(self.graph))
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            logger.error(f"图数据导出失败: {e}")
            return ""
    
    def import_graph(self, data: str, format: str = "json") -> bool:
        """
        导入图数据
        
        Args:
            data: 导入的数据
            format: 数据格式
            
        Returns:
            bool: 是否导入成功
        """
        try:
            if format == "json":
                imported_data = json.loads(data)
                
                # 导入实体
                for entity_data in imported_data.get('entities', []):
                    entity = KnowledgeEntity(**entity_data)
                    self.add_entity(entity)
                
                # 导入关系
                for rel_data in imported_data.get('relationships', []):
                    relationship = EntityRelationship(**rel_data)
                    self.add_relationship(relationship)
                
                return True
            
            else:
                raise ValueError(f"不支持的导入格式: {format}")
                
        except Exception as e:
            logger.error(f"图数据导入失败: {e}")
            return False
