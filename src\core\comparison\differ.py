"""
差异分析器
提供结构化和语义化的配置文件差异分析
"""

import difflib
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod

from .comparator import Change, ChangeType, ChangeLevel


@dataclass
class DiffNode:
    """差异节点"""
    path: str
    node_type: str
    name: str
    old_value: Any = None
    new_value: Any = None
    children: List['DiffNode'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


class BaseDiffer(ABC):
    """差异分析器基类"""
    
    @abstractmethod
    def diff(self, source: Any, target: Any, path: str = "") -> List[Change]:
        """执行差异分析"""
        pass


class StructuralDiffer(BaseDiffer):
    """
    结构化差异分析器
    
    专门分析IEC61850配置文件的结构差异：
    - 元素的增删改
    - 层次结构的变化
    - 属性值的变更
    """
    
    def __init__(self):
        self.change_handlers = {
            'IED': self._analyze_ied_change,
            'SubStation': self._analyze_substation_change,
            'VoltageLevel': self._analyze_voltage_level_change,
            'Bay': self._analyze_bay_change,
            'ConductingEquipment': self._analyze_equipment_change,
            'SubNetwork': self._analyze_subnet_change,
            'ConnectedAP': self._analyze_connected_ap_change
        }
    
    def diff(self, source: Any, target: Any, path: str = "") -> List[Change]:
        """执行结构化差异分析"""
        changes = []
        
        # 如果是字典类型，按键值对比
        if isinstance(source, dict) and isinstance(target, dict):
            changes.extend(self._diff_dict(source, target, path))
        
        # 如果是列表类型，按元素对比
        elif isinstance(source, list) and isinstance(target, list):
            changes.extend(self._diff_list(source, target, path))
        
        # 如果是对象类型，按属性对比
        elif hasattr(source, '__dict__') and hasattr(target, '__dict__'):
            changes.extend(self._diff_object(source, target, path))
        
        # 如果是基本类型，直接对比值
        elif source != target:
            changes.append(self._create_value_change(source, target, path))
        
        return changes
    
    def _diff_dict(self, source: Dict, target: Dict, path: str) -> List[Change]:
        """对比字典"""
        changes = []
        
        # 查找新增的键
        for key in target.keys() - source.keys():
            changes.append(Change(
                change_type=ChangeType.ADDED,
                change_level=self._assess_change_level(key, None, target[key]),
                path=f"{path}.{key}" if path else key,
                element_type=type(target[key]).__name__,
                element_name=str(key),
                new_value=target[key],
                description=f"新增属性: {key}",
                impact_analysis=self._analyze_impact(key, None, target[key]),
                recommendation=self._get_recommendation(key, None, target[key])
            ))
        
        # 查找删除的键
        for key in source.keys() - target.keys():
            changes.append(Change(
                change_type=ChangeType.REMOVED,
                change_level=self._assess_change_level(key, source[key], None),
                path=f"{path}.{key}" if path else key,
                element_type=type(source[key]).__name__,
                element_name=str(key),
                old_value=source[key],
                description=f"删除属性: {key}",
                impact_analysis=self._analyze_impact(key, source[key], None),
                recommendation=self._get_recommendation(key, source[key], None)
            ))
        
        # 对比共同的键
        for key in source.keys() & target.keys():
            sub_path = f"{path}.{key}" if path else key
            changes.extend(self.diff(source[key], target[key], sub_path))
        
        return changes
    
    def _diff_list(self, source: List, target: List, path: str) -> List[Change]:
        """对比列表"""
        changes = []
        
        # 简单的长度对比
        if len(source) != len(target):
            changes.append(Change(
                change_type=ChangeType.MODIFIED,
                change_level=ChangeLevel.MINOR,
                path=path,
                element_type="List",
                element_name=path.split('.')[-1] if '.' in path else path,
                old_value=len(source),
                new_value=len(target),
                description=f"列表长度从 {len(source)} 变更为 {len(target)}",
                impact_analysis="列表长度变更可能表示元素的增删",
                recommendation="检查具体的元素变更"
            ))
        
        # 对比相同位置的元素
        min_len = min(len(source), len(target))
        for i in range(min_len):
            item_path = f"{path}[{i}]"
            changes.extend(self.diff(source[i], target[i], item_path))
        
        return changes
    
    def _diff_object(self, source: Any, target: Any, path: str) -> List[Change]:
        """对比对象"""
        changes = []
        
        # 获取对象的属性字典
        source_dict = source.__dict__ if hasattr(source, '__dict__') else {}
        target_dict = target.__dict__ if hasattr(target, '__dict__') else {}
        
        # 使用字典对比方法
        changes.extend(self._diff_dict(source_dict, target_dict, path))
        
        return changes
    
    def _create_value_change(self, old_value: Any, new_value: Any, path: str) -> Change:
        """创建值变更记录"""
        element_name = path.split('.')[-1] if '.' in path else path
        
        return Change(
            change_type=ChangeType.MODIFIED,
            change_level=self._assess_change_level(element_name, old_value, new_value),
            path=path,
            element_type=type(new_value).__name__,
            element_name=element_name,
            old_value=old_value,
            new_value=new_value,
            description=f"{element_name} 从 '{old_value}' 变更为 '{new_value}'",
            impact_analysis=self._analyze_impact(element_name, old_value, new_value),
            recommendation=self._get_recommendation(element_name, old_value, new_value)
        )
    
    def _assess_change_level(self, element_name: str, old_value: Any, new_value: Any) -> ChangeLevel:
        """评估变更级别"""
        # 关键属性变更
        critical_attributes = {
            'name', 'id', 'type', 'ip', 'subnet_mask', 'gateway',
            'mac_address', 'vlan_id', 'app_id', 'conf_rev'
        }
        
        # 重要属性变更
        major_attributes = {
            'manufacturer', 'model', 'version', 'desc',
            'logical_nodes', 'access_points', 'services'
        }
        
        element_lower = element_name.lower()
        
        if any(attr in element_lower for attr in critical_attributes):
            return ChangeLevel.CRITICAL
        elif any(attr in element_lower for attr in major_attributes):
            return ChangeLevel.MAJOR
        else:
            return ChangeLevel.MINOR
    
    def _analyze_impact(self, element_name: str, old_value: Any, new_value: Any) -> str:
        """分析变更影响"""
        element_lower = element_name.lower()
        
        if 'ip' in element_lower or 'address' in element_lower:
            return "IP地址变更可能影响设备通信，需要更新网络配置"
        elif 'name' in element_lower or 'id' in element_lower:
            return "名称或ID变更可能影响引用关系，需要检查相关配置"
        elif 'type' in element_lower:
            return "类型变更可能影响功能兼容性，需要验证新类型的支持"
        elif 'version' in element_lower:
            return "版本变更可能影响兼容性，需要检查版本兼容性要求"
        else:
            return "此变更的具体影响需要根据实际情况评估"
    
    def _get_recommendation(self, element_name: str, old_value: Any, new_value: Any) -> str:
        """获取修复建议"""
        element_lower = element_name.lower()
        
        if 'ip' in element_lower:
            return "请确认新IP地址的可达性，并更新相关的网络配置文档"
        elif 'name' in element_lower:
            return "请检查所有引用此名称的配置，确保引用关系正确"
        elif 'type' in element_lower:
            return "请验证新类型的功能是否满足设计要求"
        elif old_value is None:
            return "请验证新增配置的正确性和必要性"
        elif new_value is None:
            return "请确认删除此配置不会影响系统功能"
        else:
            return "请仔细评估此变更的必要性和影响范围"
    
    def _analyze_ied_change(self, change: Change) -> Change:
        """分析IED变更"""
        if change.change_type == ChangeType.ADDED:
            change.impact_analysis = "新增IED设备需要验证网络配置、通信参数和功能配置"
            change.recommendation = "请确认新IED的配置完整性，包括网络地址、逻辑节点和服务配置"
        elif change.change_type == ChangeType.REMOVED:
            change.change_level = ChangeLevel.CRITICAL
            change.impact_analysis = "删除IED设备可能影响保护、测量或控制功能的正常运行"
            change.recommendation = "请确认删除此IED不会影响系统安全运行，并更新相关的系统文档"
        
        return change
    
    def _analyze_substation_change(self, change: Change) -> Change:
        """分析变电站变更"""
        change.change_level = ChangeLevel.MAJOR
        change.impact_analysis = "变电站配置变更可能影响整个系统的拓扑结构"
        change.recommendation = "请仔细检查变电站配置的完整性和正确性"
        return change
    
    def _analyze_voltage_level_change(self, change: Change) -> Change:
        """分析电压等级变更"""
        change.change_level = ChangeLevel.MAJOR
        change.impact_analysis = "电压等级变更可能影响设备的电气连接和保护配置"
        change.recommendation = "请验证电压等级变更的合理性和相关设备配置"
        return change
    
    def _analyze_bay_change(self, change: Change) -> Change:
        """分析间隔变更"""
        change.change_level = ChangeLevel.MAJOR
        change.impact_analysis = "间隔配置变更可能影响设备的逻辑分组和操作"
        change.recommendation = "请检查间隔内设备的配置和连接关系"
        return change
    
    def _analyze_equipment_change(self, change: Change) -> Change:
        """分析设备变更"""
        change.change_level = ChangeLevel.MAJOR
        change.impact_analysis = "设备配置变更可能影响电气连接和控制逻辑"
        change.recommendation = "请验证设备配置的正确性和完整性"
        return change
    
    def _analyze_subnet_change(self, change: Change) -> Change:
        """分析子网变更"""
        change.change_level = ChangeLevel.CRITICAL
        change.impact_analysis = "子网配置变更可能影响设备间的通信"
        change.recommendation = "请验证网络拓扑的正确性和设备的网络配置"
        return change
    
    def _analyze_connected_ap_change(self, change: Change) -> Change:
        """分析连接访问点变更"""
        change.change_level = ChangeLevel.MAJOR
        change.impact_analysis = "访问点配置变更可能影响设备的网络连接"
        change.recommendation = "请检查访问点的网络参数和连接状态"
        return change


class SemanticDiffer(BaseDiffer):
    """
    语义化差异分析器
    
    理解IEC61850配置的语义含义，提供更智能的差异分析：
    - 功能等价性分析
    - 配置合理性检查
    - 业务影响评估
    """
    
    def __init__(self):
        self.semantic_rules = {
            'ip_conflict': self._check_ip_conflicts,
            'name_consistency': self._check_name_consistency,
            'reference_integrity': self._check_reference_integrity,
            'functional_equivalence': self._check_functional_equivalence
        }
    
    def diff(self, source: Any, target: Any, path: str = "") -> List[Change]:
        """执行语义化差异分析"""
        changes = []
        
        # 执行各种语义检查
        for rule_name, rule_func in self.semantic_rules.items():
            try:
                rule_changes = rule_func(source, target, path)
                changes.extend(rule_changes)
            except Exception as e:
                # 记录规则执行错误，但不中断整个分析过程
                print(f"语义规则 {rule_name} 执行失败: {e}")
        
        return changes
    
    def _check_ip_conflicts(self, source: Any, target: Any, path: str) -> List[Change]:
        """检查IP地址冲突"""
        changes = []
        
        # 提取IP地址信息
        source_ips = self._extract_ip_addresses(source)
        target_ips = self._extract_ip_addresses(target)
        
        # 检查新增的IP是否与现有IP冲突
        for ip in target_ips - source_ips:
            if self._is_ip_conflict(ip, target_ips):
                changes.append(Change(
                    change_type=ChangeType.ADDED,
                    change_level=ChangeLevel.CRITICAL,
                    path=f"{path}.ip_conflict",
                    element_type="IPAddress",
                    element_name=ip,
                    new_value=ip,
                    description=f"检测到IP地址冲突: {ip}",
                    impact_analysis="IP地址冲突将导致网络通信异常",
                    recommendation="请分配唯一的IP地址"
                ))
        
        return changes
    
    def _check_name_consistency(self, source: Any, target: Any, path: str) -> List[Change]:
        """检查命名一致性"""
        changes = []
        
        # 检查命名规范
        source_names = self._extract_names(source)
        target_names = self._extract_names(target)
        
        for name in target_names - source_names:
            if not self._is_valid_name(name):
                changes.append(Change(
                    change_type=ChangeType.ADDED,
                    change_level=ChangeLevel.MINOR,
                    path=f"{path}.naming",
                    element_type="Name",
                    element_name=name,
                    new_value=name,
                    description=f"名称不符合命名规范: {name}",
                    impact_analysis="不规范的命名可能影响可读性和维护性",
                    recommendation="请使用符合IEC61850标准的命名规范"
                ))
        
        return changes
    
    def _check_reference_integrity(self, source: Any, target: Any, path: str) -> List[Change]:
        """检查引用完整性"""
        changes = []
        
        # 检查引用关系是否完整
        source_refs = self._extract_references(source)
        target_refs = self._extract_references(target)
        
        # 查找断开的引用
        for ref in source_refs:
            if ref not in target_refs and not self._reference_exists(ref, target):
                changes.append(Change(
                    change_type=ChangeType.REMOVED,
                    change_level=ChangeLevel.MAJOR,
                    path=f"{path}.reference",
                    element_type="Reference",
                    element_name=ref,
                    old_value=ref,
                    description=f"引用关系断开: {ref}",
                    impact_analysis="断开的引用可能导致配置错误",
                    recommendation="请修复或删除无效的引用"
                ))
        
        return changes
    
    def _check_functional_equivalence(self, source: Any, target: Any, path: str) -> List[Change]:
        """检查功能等价性"""
        changes = []
        
        # 分析功能变更
        source_functions = self._extract_functions(source)
        target_functions = self._extract_functions(target)
        
        if source_functions != target_functions:
            changes.append(Change(
                change_type=ChangeType.MODIFIED,
                change_level=ChangeLevel.MAJOR,
                path=f"{path}.functions",
                element_type="Function",
                element_name="system_functions",
                old_value=list(source_functions),
                new_value=list(target_functions),
                description="系统功能发生变更",
                impact_analysis="功能变更可能影响系统的运行逻辑",
                recommendation="请验证新功能配置的正确性"
            ))
        
        return changes
    
    def _extract_ip_addresses(self, obj: Any) -> set:
        """提取IP地址"""
        ips = set()
        # 简化实现，实际应该递归遍历对象结构
        if hasattr(obj, 'ip'):
            ips.add(obj.ip)
        return ips
    
    def _extract_names(self, obj: Any) -> set:
        """提取名称"""
        names = set()
        if hasattr(obj, 'name') and obj.name:
            names.add(obj.name)
        return names
    
    def _extract_references(self, obj: Any) -> set:
        """提取引用"""
        refs = set()
        # 简化实现
        return refs
    
    def _extract_functions(self, obj: Any) -> set:
        """提取功能"""
        functions = set()
        # 简化实现
        return functions
    
    def _is_ip_conflict(self, ip: str, all_ips: set) -> bool:
        """检查IP冲突"""
        # 简化实现
        return False
    
    def _is_valid_name(self, name: str) -> bool:
        """检查名称有效性"""
        # 简化的命名规范检查
        return name.isalnum() and len(name) <= 64
    
    def _reference_exists(self, ref: str, obj: Any) -> bool:
        """检查引用是否存在"""
        # 简化实现
        return True
