#!/usr/bin/env python3
"""
220kV线路完整逻辑回路实例
使用具体厂家设备和型号

工程实例：某220kV变电站出线间隔
线路名称：220kV某某线
设备配置：采用国内主流厂家产品
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class Real220kVLineCircuit:
    """220kV线路真实设备配置"""
    
    def __init__(self):
        # 真实设备配置
        self.equipment_config = {
            '一次设备': {
                '断路器': {
                    '厂家': '西安西电开关电气有限公司',
                    '型号': 'LW36-252',
                    '规格': '252kV/4000A/50kA',
                    '操动机构': 'CD17弹簧操动机构',
                    '跳闸线圈': 'YT1(主)、YT2(备)',
                    '合闸线圈': 'YC',
                    '辅助触点': '6常开+6常闭'
                },
                '隔离开关': {
                    '厂家': '平高集团有限公司',
                    '型号': 'GW4-252D/4000',
                    '规格': '252kV/4000A',
                    '操动机构': 'CS6-I电动操动机构',
                    '辅助触点': '4常开+4常闭'
                },
                '电流互感器': {
                    '厂家': '大连北方互感器集团',
                    '型号': 'LZZBJ9-252',
                    '变比': '2000/5A',
                    '准确度': '保护5P20，测量0.5级，计量0.2S级',
                    '二次绕组': '1S1-1S2(保护A)，2S1-2S2(保护B)，3S1-3S2(测量)，4S1-4S2(计量)'
                },
                '电压互感器': {
                    '厂家': '大连北方互感器集团',
                    '型号': 'JDZ10-252',
                    '变比': '220000/100V',
                    '准确度': '保护3P，测量0.5级，计量0.2级',
                    '二次绕组': 'A相a-x，B相b-y，C相c-z，开口三角形d-n'
                }
            },
            '二次设备': {
                '线路保护A': {
                    '厂家': '南京南瑞继保电气有限公司',
                    '型号': 'RCS-978A',
                    '功能': '220kV线路成套保护装置',
                    '保护功能': [
                        '距离保护(三段)',
                        '零序电流保护(四段)',
                        '重合闸',
                        '失灵保护',
                        '过负荷保护'
                    ],
                    '通信接口': 'IEC 61850，GOOSE/SV',
                    '跳闸出口': '硬接点+GOOSE'
                },
                '线路保护B': {
                    '厂家': '北京四方继保自动化股份有限公司',
                    '型号': 'CSC-278A',
                    '功能': '220kV线路成套保护装置',
                    '保护功能': [
                        '距离保护(三段)',
                        '零序电流保护(四段)',
                        '重合闸',
                        '失灵保护',
                        '过负荷保护'
                    ],
                    '通信接口': 'IEC 61850，GOOSE/SV',
                    '跳闸出口': '硬接点+GOOSE'
                },
                '测控装置': {
                    '厂家': '国电南瑞科技股份有限公司',
                    '型号': 'RCS-9611CS',
                    '功能': '220kV线路测控装置',
                    '测量功能': [
                        '三相电流、电压',
                        '有功、无功功率',
                        '频率、功率因数',
                        '正反向电能'
                    ],
                    '控制功能': [
                        '断路器遥控',
                        '隔离开关遥控',
                        '重合闸投退'
                    ]
                },
                '合并单元': {
                    '厂家': '国电南瑞科技股份有限公司',
                    '型号': 'RCS-9611MU',
                    '功能': '过程层合并单元',
                    '采样频率': '4000Hz',
                    '精度': '0.2级',
                    '同步方式': 'IEEE 1588v2',
                    '输出': 'SV数据流'
                },
                '智能终端': {
                    '厂家': '国电南瑞科技股份有限公司',
                    '型号': 'RCS-9611IT',
                    '功能': '智能操作箱',
                    'GOOSE接收': '保护跳闸信号',
                    '硬接点输出': '断路器控制',
                    '状态采集': '设备位置、压力、SF6密度'
                }
            },
            '直流系统': {
                '直流电源': {
                    '厂家': '许继电气股份有限公司',
                    '型号': 'XJZG-220/110',
                    '电压等级': 'DC 220V/110V',
                    '容量': '600Ah',
                    '系统配置': 'I系统、II系统双套独立'
                },
                '直流分电屏': {
                    '保护A电源': 'DC 220V-I系统',
                    '保护B电源': 'DC 220V-II系统',
                    '控制电源': 'DC 220V-I/II系统',
                    '信号电源': 'DC 110V'
                }
            }
        }
        
        # 回路连接关系
        self.circuit_connections = {
            '保护回路A': {
                'CT输入': 'LZZBJ9-252 1S1-1S2 → RCS-978A',
                'PT输入': 'JDZ10-252 a-x,b-y,c-z → RCS-978A',
                '跳闸出口': 'RCS-978A → LW36-252 YT1',
                '电源': 'DC 220V-I → RCS-978A',
                'GOOSE发送': 'RCS-978A → 过程层网络'
            },
            '保护回路B': {
                'CT输入': 'LZZBJ9-252 2S1-2S2 → CSC-278A',
                'PT输入': 'JDZ10-252 a-x,b-y,c-z → CSC-278A',
                '跳闸出口': 'CSC-278A → LW36-252 YT2',
                '电源': 'DC 220V-II → CSC-278A',
                'GOOSE发送': 'CSC-278A → 过程层网络'
            },
            '测量回路': {
                'CT输入': 'LZZBJ9-252 3S1-3S2 → RCS-9611CS',
                'PT输入': 'JDZ10-252 a-x,b-y,c-z → RCS-9611CS',
                '电源': 'DC 220V → RCS-9611CS',
                '通信': 'RCS-9611CS → 站控层网络'
            },
            '控制回路': {
                '合闸回路': 'RCS-9611CS → RCS-9611IT → LW36-252 YC',
                '分闸回路': 'RCS-9611CS → RCS-9611IT → LW36-252 YT1/YT2',
                '位置信号': 'LW36-252辅助触点 → RCS-9611IT → RCS-9611CS',
                '电源': 'DC 220V → 控制回路'
            }
        }
    
    def generate_real_220kv_circuit_svg(self) -> str:
        """生成真实220kV线路回路图"""
        
        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #333; }
            .device-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
            .model-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
            .protection-a { stroke: red; stroke-width: 3; fill: none; }
            .protection-b { stroke: blue; stroke-width: 3; fill: none; }
            .measurement { stroke: green; stroke-width: 2; fill: none; }
            .control { stroke: purple; stroke-width: 2; fill: none; }
            .goose { stroke: orange; stroke-width: 2; fill: none; stroke-dasharray: 8,4; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1800" height="1400" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="900" y="40" text-anchor="middle" class="title-text">220kV某某线完整逻辑回路</text>
    <text x="900" y="65" text-anchor="middle" font-size="14" fill="#666">真实厂家设备配置实例</text>
    
    <!-- 一次设备 -->
    <g transform="translate(100, 120)">
        <text x="0" y="0" font-size="16" font-weight="bold">一次设备配置:</text>
        
        <!-- 线路 -->
        <line x1="0" y1="40" x2="400" y2="40" stroke="black" stroke-width="8"/>
        <text x="200" y="30" text-anchor="middle" font-size="12" font-weight="bold">220kV某某线</text>
        
        <!-- 电流互感器 -->
        <g transform="translate(80, 20)">
            <circle cx="0" cy="20" r="18" fill="none" stroke="red" stroke-width="3"/>
            <text x="0" y="50" text-anchor="middle" class="device-text" font-weight="bold">TA</text>
            <text x="0" y="65" text-anchor="middle" class="model-text">大连北方</text>
            <text x="0" y="75" text-anchor="middle" class="model-text">LZZBJ9-252</text>
            <text x="0" y="85" text-anchor="middle" class="model-text">2000/5A</text>
            
            <!-- 二次绕组 -->
            <circle cx="-25" cy="95" r="3" fill="red"/>
            <text x="-35" y="90" class="model-text">1S1-1S2</text>
            <text x="-35" y="100" class="model-text">保护A</text>
            
            <circle cx="-8" cy="95" r="3" fill="blue"/>
            <text x="-18" y="90" class="model-text">2S1-2S2</text>
            <text x="-18" y="100" class="model-text">保护B</text>
            
            <circle cx="8" cy="95" r="3" fill="green"/>
            <text x="18" y="90" class="model-text">3S1-3S2</text>
            <text x="18" y="100" class="model-text">测量</text>
            
            <circle cx="25" cy="95" r="3" fill="orange"/>
            <text x="35" y="90" class="model-text">4S1-4S2</text>
            <text x="35" y="100" class="model-text">计量</text>
        </g>
        
        <!-- 断路器 -->
        <g transform="translate(300, 20)">
            <rect x="0" y="0" width="40" height="40" fill="none" stroke="black" stroke-width="4"/>
            <line x1="5" y1="5" x2="35" y2="35" stroke="black" stroke-width="6"/>
            <text x="20" y="55" text-anchor="middle" class="device-text" font-weight="bold">QF</text>
            <text x="20" y="70" text-anchor="middle" class="model-text">西安西电</text>
            <text x="20" y="80" text-anchor="middle" class="model-text">LW36-252</text>
            <text x="20" y="90" text-anchor="middle" class="model-text">252kV/4000A</text>
            
            <!-- 跳闸线圈 -->
            <rect x="-15" y="100" width="20" height="15" fill="red" stroke="black"/>
            <text x="-5" y="110" text-anchor="middle" class="model-text" fill="white">YT1</text>
            <text x="-5" y="125" text-anchor="middle" class="model-text">主跳</text>
            
            <rect x="35" y="100" width="20" height="15" fill="blue" stroke="black"/>
            <text x="45" y="110" text-anchor="middle" class="model-text" fill="white">YT2</text>
            <text x="45" y="125" text-anchor="middle" class="model-text">备跳</text>
            
            <!-- 合闸线圈 -->
            <rect x="10" y="130" width="20" height="15" fill="green" stroke="black"/>
            <text x="20" y="140" text-anchor="middle" class="model-text" fill="white">YC</text>
            <text x="20" y="155" text-anchor="middle" class="model-text">合闸</text>
        </g>
        
        <!-- 电压互感器 -->
        <g transform="translate(500, 20)">
            <rect x="0" y="0" width="30" height="40" fill="none" stroke="blue" stroke-width="3"/>
            <text x="15" y="25" text-anchor="middle" class="device-text" font-weight="bold">TV</text>
            <text x="15" y="55" text-anchor="middle" class="device-text" font-weight="bold">PT</text>
            <text x="15" y="70" text-anchor="middle" class="model-text">大连北方</text>
            <text x="15" y="80" text-anchor="middle" class="model-text">JDZ10-252</text>
            <text x="15" y="90" text-anchor="middle" class="model-text">220kV/100V</text>
        </g>
    </g>
    
    <!-- 保护装置A -->
    <g transform="translate(100, 350)">
        <rect x="0" y="0" width="200" height="120" fill="lightcoral" stroke="black" stroke-width="3"/>
        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">线路保护A</text>
        <text x="100" y="45" text-anchor="middle" class="device-text" font-weight="bold">南京南瑞继保</text>
        <text x="100" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-978A</text>
        <text x="100" y="75" text-anchor="middle" class="model-text">220kV线路成套保护</text>
        <text x="100" y="90" text-anchor="middle" class="model-text">距离+零序+重合闸</text>
        <text x="100" y="105" text-anchor="middle" class="model-text">IEC 61850通信</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="30" r="4" fill="red"/>
        <text x="-25" y="25" class="model-text">CT-A</text>
        <circle cx="-10" cy="50" r="4" fill="blue"/>
        <text x="-25" y="45" class="model-text">PT</text>
        <circle cx="-10" cy="70" r="4" fill="black"/>
        <text x="-30" y="65" class="model-text">DC220V-I</text>
        
        <circle cx="210" cy="30" r="4" fill="red"/>
        <text x="220" y="25" class="model-text">跳闸YT1</text>
        <circle cx="210" cy="50" r="4" fill="orange"/>
        <text x="220" y="45" class="model-text">GOOSE</text>
    </g>
    
    <!-- 保护装置B -->
    <g transform="translate(100, 520)">
        <rect x="0" y="0" width="200" height="120" fill="lightblue" stroke="black" stroke-width="3"/>
        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">线路保护B</text>
        <text x="100" y="45" text-anchor="middle" class="device-text" font-weight="bold">北京四方继保</text>
        <text x="100" y="60" text-anchor="middle" class="device-text" font-weight="bold">CSC-278A</text>
        <text x="100" y="75" text-anchor="middle" class="model-text">220kV线路成套保护</text>
        <text x="100" y="90" text-anchor="middle" class="model-text">距离+零序+重合闸</text>
        <text x="100" y="105" text-anchor="middle" class="model-text">IEC 61850通信</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="30" r="4" fill="blue"/>
        <text x="-25" y="25" class="model-text">CT-B</text>
        <circle cx="-10" cy="50" r="4" fill="blue"/>
        <text x="-25" y="45" class="model-text">PT</text>
        <circle cx="-10" cy="70" r="4" fill="black"/>
        <text x="-30" y="65" class="model-text">DC220V-II</text>
        
        <circle cx="210" cy="30" r="4" fill="blue"/>
        <text x="220" y="25" class="model-text">跳闸YT2</text>
        <circle cx="210" cy="50" r="4" fill="orange"/>
        <text x="220" y="45" class="model-text">GOOSE</text>
    </g>
    
    <!-- 测控装置 -->
    <g transform="translate(400, 350)">
        <rect x="0" y="0" width="180" height="100" fill="lightgreen" stroke="black" stroke-width="3"/>
        <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold">测控装置</text>
        <text x="90" y="45" text-anchor="middle" class="device-text" font-weight="bold">国电南瑞</text>
        <text x="90" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-9611CS</text>
        <text x="90" y="75" text-anchor="middle" class="model-text">测量+控制+通信</text>
        <text x="90" y="90" text-anchor="middle" class="model-text">遥测+遥控+遥信</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="30" r="4" fill="green"/>
        <text x="-30" y="25" class="model-text">CT测量</text>
        <circle cx="-10" cy="50" r="4" fill="blue"/>
        <text x="-25" y="45" class="model-text">PT</text>
        
        <circle cx="190" cy="40" r="4" fill="purple"/>
        <text x="200" y="35" class="model-text">控制</text>
    </g>
    
    <!-- 智能终端 -->
    <g transform="translate(400, 500)">
        <rect x="0" y="0" width="180" height="80" fill="lightyellow" stroke="black" stroke-width="3"/>
        <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold">智能终端</text>
        <text x="90" y="45" text-anchor="middle" class="device-text" font-weight="bold">国电南瑞</text>
        <text x="90" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-9611IT</text>
        <text x="90" y="75" text-anchor="middle" class="model-text">智能操作箱</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="40" r="4" fill="orange"/>
        <text x="-30" y="35" class="model-text">GOOSE</text>
        
        <circle cx="190" cy="30" r="4" fill="green"/>
        <text x="200" y="25" class="model-text">合闸</text>
        <circle cx="190" cy="50" r="4" fill="red"/>
        <text x="200" y="45" class="model-text">跳闸</text>
    </g>
    
    <!-- 合并单元 -->
    <g transform="translate(700, 350)">
        <rect x="0" y="0" width="160" height="100" fill="lightcyan" stroke="black" stroke-width="3"/>
        <text x="80" y="25" text-anchor="middle" font-size="14" font-weight="bold">合并单元</text>
        <text x="80" y="45" text-anchor="middle" class="device-text" font-weight="bold">国电南瑞</text>
        <text x="80" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-9611MU</text>
        <text x="80" y="75" text-anchor="middle" class="model-text">4000Hz采样</text>
        <text x="80" y="90" text-anchor="middle" class="model-text">SV输出</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="40" r="4" fill="red"/>
        <text x="-25" y="35" class="model-text">CT</text>
        <circle cx="-10" cy="60" r="4" fill="blue"/>
        <text x="-25" y="55" class="model-text">PT</text>
        
        <circle cx="170" cy="50" r="4" fill="orange"/>
        <text x="180" y="45" class="model-text">SV</text>
    </g>
    
    <!-- 直流电源系统 -->
    <g transform="translate(100, 750)">
        <text x="0" y="0" font-size="16" font-weight="bold">直流电源系统:</text>
        
        <!-- 直流电源I -->
        <rect x="0" y="20" width="150" height="80" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="75" y="40" text-anchor="middle" font-size="12" font-weight="bold">直流电源I系统</text>
        <text x="75" y="55" text-anchor="middle" class="device-text">许继电气</text>
        <text x="75" y="70" text-anchor="middle" class="device-text">XJZG-220/110</text>
        <text x="75" y="85" text-anchor="middle" class="model-text">DC 220V/600Ah</text>
        
        <!-- 直流电源II -->
        <rect x="200" y="20" width="150" height="80" fill="lightblue" stroke="black" stroke-width="2"/>
        <text x="275" y="40" text-anchor="middle" font-size="12" font-weight="bold">直流电源II系统</text>
        <text x="275" y="55" text-anchor="middle" class="device-text">许继电气</text>
        <text x="275" y="70" text-anchor="middle" class="device-text">XJZG-220/110</text>
        <text x="275" y="85" text-anchor="middle" class="model-text">DC 220V/600Ah</text>
        
        <!-- 分电屏 -->
        <rect x="400" y="20" width="200" height="80" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="500" y="35" text-anchor="middle" font-size="12" font-weight="bold">直流分电屏</text>
        <text x="420" y="55" class="model-text">保护A: DC220V-I</text>
        <text x="420" y="70" class="model-text">保护B: DC220V-II</text>
        <text x="420" y="85" class="model-text">控制: DC220V-I/II</text>
        <text x="520" y="55" class="model-text">测控: DC220V</text>
        <text x="520" y="70" class="model-text">信号: DC110V</text>
        <text x="520" y="85" class="model-text">通信: DC48V</text>
    </g>
    
    <!-- 连接线 -->
    <!-- CT到保护装置 -->
    <line x1="155" y1="215" x2="90" y2="380" class="protection-a"/>
    <line x1="172" y1="215" x2="90" y2="550" class="protection-b"/>
    <line x1="188" y1="215" x2="390" y2="380" class="measurement"/>
    
    <!-- PT到各装置 -->
    <line x1="615" y1="160" x2="90" y2="400" stroke="blue" stroke-width="2"/>
    <line x1="615" y1="160" x2="90" y2="570" stroke="blue" stroke-width="2"/>
    <line x1="615" y1="160" x2="390" y2="400" stroke="blue" stroke-width="2"/>
    
    <!-- 保护装置到断路器跳闸线圈 -->
    <line x1="310" y1="380" x2="385" y2="220" class="protection-a"/>
    <line x1="310" y1="550" x2="445" y2="220" class="protection-b"/>
    
    <!-- 测控到智能终端 -->
    <line x1="590" y1="390" x2="390" y2="540" class="control"/>
    
    <!-- 智能终端到断路器 -->
    <line x1="590" y1="530" x2="410" y2="250" stroke="green" stroke-width="3"/>
    <line x1="590" y1="550" x2="420" y2="220" stroke="red" stroke-width="3"/>
    
    <!-- GOOSE连接 -->
    <line x1="310" y1="400" x2="390" y2="540" class="goose"/>
    <line x1="310" y1="570" x2="390" y2="540" class="goose"/>
    
    <!-- 直流电源连接 -->
    <line x1="75" y1="750" x2="90" y2="420" stroke="red" stroke-width="2"/>
    <line x1="275" y1="750" x2="90" y2="590" stroke="blue" stroke-width="2"/>
    <line x1="500" y1="750" x2="490" y2="450" stroke="black" stroke-width="2"/>
    
    <!-- 技术参数说明 -->
    <g transform="translate(1000, 200)">
        <text x="0" y="0" font-size="16" font-weight="bold">设备技术参数:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">保护装置:</text>
            <text x="0" y="20" class="device-text">• RCS-978A: 距离保护≤20ms</text>
            <text x="0" y="35" class="device-text">• CSC-278A: 零序保护≤30ms</text>
            <text x="0" y="50" class="device-text">• 重合闸: 0.5-300s可调</text>
            <text x="0" y="65" class="device-text">• 通信: IEC 61850</text>
        </g>
        
        <g transform="translate(0, 120)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">断路器:</text>
            <text x="0" y="20" class="device-text">• LW36-252: SF6断路器</text>
            <text x="0" y="35" class="device-text">• 分闸时间: ≤40ms</text>
            <text x="0" y="50" class="device-text">• 合闸时间: ≤100ms</text>
            <text x="0" y="65" class="device-text">• 机械寿命: 10000次</text>
        </g>
        
        <g transform="translate(0, 210)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">互感器:</text>
            <text x="0" y="20" class="device-text">• CT: 2000/5A, 5P20</text>
            <text x="0" y="35" class="device-text">• PT: 220kV/100V, 3P</text>
            <text x="0" y="50" class="device-text">• 热稳定: 4s/80kA</text>
            <text x="0" y="65" class="device-text">• 动稳定: 200kA</text>
        </g>
        
        <g transform="translate(0, 300)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#6f42c1">通信性能:</text>
            <text x="0" y="20" class="device-text">• GOOSE: ≤4ms</text>
            <text x="0" y="35" class="device-text">• SV: 4000Hz, ≤3ms</text>
            <text x="0" y="50" class="device-text">• 同步精度: ±1μs</text>
            <text x="0" y="65" class="device-text">• 网络: 双网冗余</text>
        </g>
    </g>
    
    <!-- 保护逻辑说明 -->
    <g transform="translate(1000, 650)">
        <text x="0" y="0" font-size="16" font-weight="bold">保护配置逻辑:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">主保护:</text>
            <text x="0" y="20" class="device-text">• 距离I段: 瞬时, 85%线路</text>
            <text x="0" y="35" class="device-text">• 距离II段: 0.5s, 120%线路</text>
            <text x="0" y="50" class="device-text">• 零序I段: 瞬时, 85%线路</text>
            <text x="0" y="65" class="device-text">• 纵差保护: 光纤通道</text>
        </g>
        
        <g transform="translate(0, 120)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">后备保护:</text>
            <text x="0" y="20" class="device-text">• 距离III段: 1.2s, 相邻线路</text>
            <text x="0" y="35" class="device-text">• 零序II段: 0.5s, 相邻线路</text>
            <text x="0" y="50" class="device-text">• 过负荷: 告警+跳闸</text>
            <text x="0" y="65" class="device-text">• 失灵保护: 150ms</text>
        </g>
        
        <g transform="translate(0, 210)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">自动装置:</text>
            <text x="0" y="20" class="device-text">• 重合闸: 单相+三相</text>
            <text x="0" y="35" class="device-text">• 重合时间: 1s可调</text>
            <text x="0" y="50" class="device-text">• 重合次数: 1次</text>
            <text x="0" y="65" class="device-text">• 检同期: 电压+频率</text>
        </g>
    </g>
    
</svg>'''
        
        return svg_content


def main():
    """主函数"""
    
    print("🏗️ 220kV线路完整逻辑回路实例")
    print("=" * 80)
    print("使用具体厂家设备和型号")
    print("=" * 80)
    
    # 创建220kV线路回路
    circuit = Real220kVLineCircuit()
    
    # 输出目录
    output_dir = "design_reports/real_220kv_line"
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print("\n🏗️ 生成220kV线路完整回路图...")
    
    # 生成回路图
    svg_content = circuit.generate_real_220kv_circuit_svg()
    svg_file = output_path / "real_220kv_line_circuit.svg"
    with open(svg_file, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    # 生成设备清单
    equipment_file = output_path / "equipment_list.json"
    with open(equipment_file, 'w', encoding='utf-8') as f:
        json.dump(circuit.equipment_config, f, ensure_ascii=False, indent=2)
    
    # 生成连接关系
    connection_file = output_path / "circuit_connections.json"
    with open(connection_file, 'w', encoding='utf-8') as f:
        json.dump(circuit.circuit_connections, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 220kV线路回路图已保存: {svg_file}")
    print(f"✅ 设备清单已保存: {equipment_file}")
    print(f"✅ 连接关系已保存: {connection_file}")
    
    print("\n📋 主要设备配置:")
    print("   🔴 保护A: 南瑞继保 RCS-978A")
    print("   🔵 保护B: 四方继保 CSC-278A")
    print("   ⚡ 断路器: 西安西电 LW36-252")
    print("   📊 测控: 国电南瑞 RCS-9611CS")
    print("   🔧 智能终端: 国电南瑞 RCS-9611IT")
    print("   🔋 直流电源: 许继电气 XJZG-220/110")
    
    print("\n🎯 技术特点:")
    print("   ✅ 双重化保护完全独立")
    print("   ✅ 硬接线跳闸+GOOSE信息")
    print("   ✅ CT/PT专用绕组配置")
    print("   ✅ IEC 61850标准通信")
    print("   ✅ 符合实际工程配置")


if __name__ == "__main__":
    main()
