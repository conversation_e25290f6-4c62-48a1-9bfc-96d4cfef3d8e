#!/usr/bin/env python3
"""
220kV线路完整逻辑回路实例
使用具体厂家设备和型号

工程实例：某220kV变电站出线间隔
线路名称：220kV某某线
设备配置：采用国内主流厂家产品
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class Real220kVLineCircuit:
    """220kV线路真实设备配置"""
    
    def __init__(self):
        # 真实设备配置
        self.equipment_config = {
            '一次设备': {
                '断路器': {
                    '厂家': '西安西电开关电气有限公司',
                    '型号': 'LW36-252',
                    '规格': '252kV/4000A/50kA',
                    '操动机构': 'CD17弹簧操动机构',
                    '跳闸线圈': 'YT1(主)、YT2(备)',
                    '合闸线圈': 'YC',
                    '辅助触点': '6常开+6常闭'
                },
                '隔离开关': {
                    '厂家': '平高集团有限公司',
                    '型号': 'GW4-252D/4000',
                    '规格': '252kV/4000A',
                    '操动机构': 'CS6-I电动操动机构',
                    '辅助触点': '4常开+4常闭'
                },
                '电流互感器': {
                    '厂家': '大连北方互感器集团',
                    '型号': 'LZZBJ9-252',
                    '变比': '2000/5A',
                    '准确度': '保护5P20，测量0.5级，计量0.2S级',
                    '二次绕组': '1S1-1S2(保护A)，2S1-2S2(保护B)，3S1-3S2(测量)，4S1-4S2(计量)'
                },
                '电压互感器': {
                    '厂家': '大连北方互感器集团',
                    '型号': 'JDZ10-252',
                    '变比': '220000/100V',
                    '准确度': '保护3P，测量0.5级，计量0.2级',
                    '二次绕组': 'A相a-x，B相b-y，C相c-z，开口三角形d-n'
                }
            },
            '二次设备': {
                '线路保护A': {
                    '厂家': '南京南瑞继保电气有限公司',
                    '型号': 'RCS-978A',
                    '功能': '220kV线路成套保护装置',
                    '保护功能': [
                        '距离保护(三段)',
                        '零序电流保护(四段)',
                        '重合闸',
                        '失灵保护',
                        '过负荷保护'
                    ],
                    '通信接口': 'IEC 61850，GOOSE/SV',
                    '跳闸出口': '硬接点+GOOSE'
                },
                '线路保护B': {
                    '厂家': '北京四方继保自动化股份有限公司',
                    '型号': 'CSC-278A',
                    '功能': '220kV线路成套保护装置',
                    '保护功能': [
                        '距离保护(三段)',
                        '零序电流保护(四段)',
                        '重合闸',
                        '失灵保护',
                        '过负荷保护'
                    ],
                    '通信接口': 'IEC 61850，GOOSE/SV',
                    '跳闸出口': '硬接点+GOOSE'
                },
                '测控装置': {
                    '厂家': '国电南瑞科技股份有限公司',
                    '型号': 'RCS-9611CS',
                    '功能': '220kV线路测控装置',
                    '测量功能': [
                        '三相电流、电压',
                        '有功、无功功率',
                        '频率、功率因数',
                        '正反向电能'
                    ],
                    '控制功能': [
                        '断路器遥控',
                        '隔离开关遥控',
                        '重合闸投退'
                    ]
                },
                '合并单元': {
                    '厂家': '国电南瑞科技股份有限公司',
                    '型号': 'RCS-9611MU',
                    '功能': '过程层合并单元',
                    '采样频率': '4000Hz',
                    '精度': '0.2级',
                    '同步方式': 'IEEE 1588v2',
                    '输出': 'SV数据流'
                },
                '智能终端': {
                    '厂家': '国电南瑞科技股份有限公司',
                    '型号': 'RCS-9611IT',
                    '功能': '智能操作箱',
                    'GOOSE接收': '保护跳闸信号',
                    '硬接点输出': '断路器控制',
                    '状态采集': '设备位置、压力、SF6密度'
                }
            },
            '直流系统': {
                '直流电源': {
                    '厂家': '许继电气股份有限公司',
                    '型号': 'XJZG-220/110',
                    '电压等级': 'DC 220V/110V',
                    '容量': '600Ah',
                    '系统配置': 'I系统、II系统双套独立'
                },
                '直流分电屏': {
                    '保护A电源': 'DC 220V-I系统',
                    '保护B电源': 'DC 220V-II系统',
                    '控制电源': 'DC 220V-I/II系统',
                    '信号电源': 'DC 110V'
                }
            }
        }
        
        # 回路连接关系
        self.circuit_connections = {
            '保护回路A': {
                'CT输入': 'LZZBJ9-252 1S1-1S2 → RCS-978A',
                'PT输入': 'JDZ10-252 a-x,b-y,c-z → RCS-978A',
                '跳闸出口': 'RCS-978A → LW36-252 YT1',
                '电源': 'DC 220V-I → RCS-978A',
                'GOOSE发送': 'RCS-978A → 过程层网络'
            },
            '保护回路B': {
                'CT输入': 'LZZBJ9-252 2S1-2S2 → CSC-278A',
                'PT输入': 'JDZ10-252 a-x,b-y,c-z → CSC-278A',
                '跳闸出口': 'CSC-278A → LW36-252 YT2',
                '电源': 'DC 220V-II → CSC-278A',
                'GOOSE发送': 'CSC-278A → 过程层网络'
            },
            '测量回路': {
                'CT输入': 'LZZBJ9-252 3S1-3S2 → RCS-9611CS',
                'PT输入': 'JDZ10-252 a-x,b-y,c-z → RCS-9611CS',
                '电源': 'DC 220V → RCS-9611CS',
                '通信': 'RCS-9611CS → 站控层网络'
            },
            '控制回路': {
                '合闸回路': 'RCS-9611CS → RCS-9611IT → LW36-252 YC',
                '分闸回路': 'RCS-9611CS → RCS-9611IT → LW36-252 YT1/YT2',
                '位置信号': 'LW36-252辅助触点 → RCS-9611IT → RCS-9611CS',
                '电源': 'DC 220V → 控制回路'
            }
        }
    
    def generate_real_220kv_circuit_svg(self) -> str:
        """生成真实220kV线路回路图（清晰布局版）"""

        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .section-text { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #333; }
            .device-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
            .model-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
            .protection-a { stroke: red; stroke-width: 2; fill: none; }
            .protection-b { stroke: blue; stroke-width: 2; fill: none; }
            .measurement { stroke: green; stroke-width: 2; fill: none; }
            .control { stroke: purple; stroke-width: 2; fill: none; }
            .power { stroke: black; stroke-width: 2; fill: none; }
            .goose { stroke: orange; stroke-width: 1.5; fill: none; stroke-dasharray: 6,3; }
            .section-box { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 1; }
        </style>
    </defs>

    <!-- 背景 -->
    <rect width="1600" height="1200" fill="white" stroke="#ddd"/>

    <!-- 标题 -->
    <text x="800" y="30" text-anchor="middle" class="title-text">220kV某某线完整逻辑回路</text>
    <text x="800" y="50" text-anchor="middle" font-size="12" fill="#666">真实厂家设备配置实例</text>

    <!-- 一次设备区域 -->
    <rect x="50" y="80" width="1500" height="120" class="section-box"/>
    <text x="60" y="100" class="section-text">一次设备配置</text>

    <!-- 线路 -->
    <line x1="100" y1="140" x2="1450" y2="140" stroke="black" stroke-width="6"/>
    <text x="775" y="130" text-anchor="middle" font-size="12" font-weight="bold">220kV某某线</text>

    <!-- 电流互感器 -->
    <g transform="translate(300, 120)">
        <circle cx="0" cy="20" r="15" fill="none" stroke="red" stroke-width="2"/>
        <text x="0" y="45" text-anchor="middle" class="device-text" font-weight="bold">TA</text>
        <text x="0" y="58" text-anchor="middle" class="model-text">大连北方 LZZBJ9-252</text>
        <text x="0" y="70" text-anchor="middle" class="model-text">2000/5A</text>

        <!-- 二次绕组接线端子 -->
        <rect x="-40" y="80" width="15" height="8" fill="red" stroke="black"/>
        <text x="-32" y="86" text-anchor="middle" class="model-text" fill="white">1S</text>
        <text x="-32" y="98" text-anchor="middle" class="model-text">保护A</text>

        <rect x="-20" y="80" width="15" height="8" fill="blue" stroke="black"/>
        <text x="-12" y="86" text-anchor="middle" class="model-text" fill="white">2S</text>
        <text x="-12" y="98" text-anchor="middle" class="model-text">保护B</text>

        <rect x="5" y="80" width="15" height="8" fill="green" stroke="black"/>
        <text x="12" y="86" text-anchor="middle" class="model-text" fill="white">3S</text>
        <text x="12" y="98" text-anchor="middle" class="model-text">测量</text>

        <rect x="25" y="80" width="15" height="8" fill="orange" stroke="black"/>
        <text x="32" y="86" text-anchor="middle" class="model-text" fill="white">4S</text>
        <text x="32" y="98" text-anchor="middle" class="model-text">计量</text>
    </g>

    <!-- 断路器 -->
    <g transform="translate(750, 120)">
        <rect x="0" y="0" width="50" height="40" fill="none" stroke="black" stroke-width="3"/>
        <line x1="8" y1="8" x2="42" y2="32" stroke="black" stroke-width="4"/>
        <text x="25" y="55" text-anchor="middle" class="device-text" font-weight="bold">QF</text>
        <text x="25" y="68" text-anchor="middle" class="model-text">西安西电 LW36-252</text>
        <text x="25" y="80" text-anchor="middle" class="model-text">252kV/4000A/50kA</text>

        <!-- 操作机构 -->
        <rect x="-20" y="90" width="15" height="10" fill="red" stroke="black"/>
        <text x="-12" y="98" text-anchor="middle" class="model-text" fill="white">YT1</text>

        <rect x="55" y="90" width="15" height="10" fill="blue" stroke="black"/>
        <text x="62" y="98" text-anchor="middle" class="model-text" fill="white">YT2</text>

        <rect x="17" y="110" width="15" height="10" fill="green" stroke="black"/>
        <text x="25" y="118" text-anchor="middle" class="model-text" fill="white">YC</text>
    </g>

    <!-- 电压互感器 -->
    <g transform="translate(1200, 120)">
        <rect x="0" y="0" width="40" height="40" fill="none" stroke="blue" stroke-width="2"/>
        <text x="20" y="25" text-anchor="middle" class="device-text" font-weight="bold">PT</text>
        <text x="20" y="55" text-anchor="middle" class="model-text">大连北方 JDZ10-252</text>
        <text x="20" y="68" text-anchor="middle" class="model-text">220kV/100V</text>

        <!-- 二次绕组 -->
        <rect x="-10" y="80" width="60" height="8" fill="blue" stroke="black"/>
        <text x="20" y="86" text-anchor="middle" class="model-text" fill="white">a-x, b-y, c-z</text>
    </g>

    <!-- 保护装置区域 -->
    <rect x="50" y="250" width="700" height="200" class="section-box"/>
    <text x="60" y="270" class="section-text">保护装置</text>

    <!-- 保护装置A -->
    <g transform="translate(100, 290)">
        <rect x="0" y="0" width="250" height="80" fill="#ffe6e6" stroke="red" stroke-width="2"/>
        <text x="125" y="20" text-anchor="middle" font-size="12" font-weight="bold">线路保护A</text>
        <text x="125" y="35" text-anchor="middle" class="device-text">南京南瑞继保 RCS-978A</text>
        <text x="125" y="50" text-anchor="middle" class="model-text">距离+零序+重合闸+失灵</text>
        <text x="125" y="65" text-anchor="middle" class="model-text">IEC 61850通信</text>

        <!-- 输入端子 -->
        <rect x="-15" y="10" width="10" height="8" fill="red" stroke="black"/>
        <text x="-25" y="5" class="model-text">CT-1S</text>

        <rect x="-15" y="25" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="20" class="model-text">PT</text>

        <rect x="-15" y="40" width="10" height="8" fill="black" stroke="black"/>
        <text x="-35" y="35" class="model-text">DC220V-I</text>

        <!-- 输出端子 -->
        <rect x="255" y="15" width="10" height="8" fill="red" stroke="black"/>
        <text x="275" y="10" class="model-text">跳闸YT1</text>

        <rect x="255" y="35" width="10" height="8" fill="orange" stroke="black"/>
        <text x="275" y="30" class="model-text">GOOSE</text>
    </g>

    <!-- 保护装置B -->
    <g transform="translate(400, 290)">
        <rect x="0" y="0" width="250" height="80" fill="#e6f3ff" stroke="blue" stroke-width="2"/>
        <text x="125" y="20" text-anchor="middle" font-size="12" font-weight="bold">线路保护B</text>
        <text x="125" y="35" text-anchor="middle" class="device-text">北京四方继保 CSC-278A</text>
        <text x="125" y="50" text-anchor="middle" class="model-text">距离+零序+重合闸+失灵</text>
        <text x="125" y="65" text-anchor="middle" class="model-text">IEC 61850通信</text>

        <!-- 输入端子 -->
        <rect x="-15" y="10" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="5" class="model-text">CT-2S</text>

        <rect x="-15" y="25" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="20" class="model-text">PT</text>

        <rect x="-15" y="40" width="10" height="8" fill="black" stroke="black"/>
        <text x="-35" y="35" class="model-text">DC220V-II</text>

        <!-- 输出端子 -->
        <rect x="255" y="15" width="10" height="8" fill="blue" stroke="black"/>
        <text x="275" y="10" class="model-text">跳闸YT2</text>

        <rect x="255" y="35" width="10" height="8" fill="orange" stroke="black"/>
        <text x="275" y="30" class="model-text">GOOSE</text>
    </g>

    <!-- 测控通信区域 -->
    <rect x="800" y="250" width="750" height="200" class="section-box"/>
    <text x="810" y="270" class="section-text">测控通信装置</text>

    <!-- 测控装置 -->
    <g transform="translate(850, 290)">
        <rect x="0" y="0" width="200" height="80" fill="#e6ffe6" stroke="green" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">测控装置</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">国电南瑞 RCS-9611CS</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">测量+控制+通信</text>
        <text x="100" y="65" text-anchor="middle" class="model-text">遥测+遥控+遥信</text>

        <!-- 输入端子 -->
        <rect x="-15" y="15" width="10" height="8" fill="green" stroke="black"/>
        <text x="-30" y="10" class="model-text">CT-3S</text>

        <rect x="-15" y="30" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="25" class="model-text">PT</text>

        <!-- 输出端子 -->
        <rect x="205" y="25" width="10" height="8" fill="purple" stroke="black"/>
        <text x="225" y="20" class="model-text">控制</text>
    </g>

    <!-- 智能终端 -->
    <g transform="translate(1100, 290)">
        <rect x="0" y="0" width="200" height="80" fill="#fff9e6" stroke="#ffc107" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">智能终端</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">国电南瑞 RCS-9611IT</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">智能操作箱</text>
        <text x="100" y="65" text-anchor="middle" class="model-text">GOOSE接收+硬接点输出</text>

        <!-- 输入端子 -->
        <rect x="-15" y="20" width="10" height="8" fill="orange" stroke="black"/>
        <text x="-35" y="15" class="model-text">GOOSE</text>

        <rect x="-15" y="35" width="10" height="8" fill="purple" stroke="black"/>
        <text x="-30" y="30" class="model-text">控制</text>

        <!-- 输出端子 -->
        <rect x="205" y="15" width="10" height="8" fill="green" stroke="black"/>
        <text x="225" y="10" class="model-text">合闸</text>

        <rect x="205" y="35" width="10" height="8" fill="red" stroke="black"/>
        <text x="225" y="30" class="model-text">跳闸</text>
    </g>

    <!-- 合并单元 -->
    <g transform="translate(850, 390)">
        <rect x="0" y="0" width="200" height="50" fill="#e6ffff" stroke="cyan" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">合并单元</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">国电南瑞 RCS-9611MU (4000Hz采样)</text>

        <!-- 输入端子 -->
        <rect x="-15" y="15" width="10" height="8" fill="red" stroke="black"/>
        <text x="-25" y="10" class="model-text">CT</text>

        <rect x="-15" y="30" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="25" class="model-text">PT</text>

        <!-- 输出端子 -->
        <rect x="205" y="20" width="10" height="8" fill="orange" stroke="black"/>
        <text x="225" y="15" class="model-text">SV</text>
    </g>

    <!-- 直流电源系统 -->
    <rect x="50" y="500" width="1500" height="120" class="section-box"/>
    <text x="60" y="520" class="section-text">直流电源系统</text>

    <!-- 直流电源I -->
    <g transform="translate(150, 540)">
        <rect x="0" y="0" width="200" height="60" fill="#ffe6e6" stroke="red" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">直流电源I系统</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">许继电气 XJZG-220/110</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">DC 220V/600Ah</text>

        <!-- 输出端子 -->
        <rect x="205" y="20" width="10" height="8" fill="red" stroke="black"/>
        <text x="225" y="15" class="model-text">+220V-I</text>

        <rect x="205" y="35" width="10" height="8" fill="black" stroke="black"/>
        <text x="225" y="30" class="model-text">-220V-I</text>
    </g>

    <!-- 直流电源II -->
    <g transform="translate(450, 540)">
        <rect x="0" y="0" width="200" height="60" fill="#e6f3ff" stroke="blue" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">直流电源II系统</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">许继电气 XJZG-220/110</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">DC 220V/600Ah</text>

        <!-- 输出端子 -->
        <rect x="205" y="20" width="10" height="8" fill="blue" stroke="black"/>
        <text x="225" y="15" class="model-text">+220V-II</text>

        <rect x="205" y="35" width="10" height="8" fill="black" stroke="black"/>
        <text x="225" y="30" class="model-text">-220V-II</text>
    </g>

    <!-- 直流分电屏 -->
    <g transform="translate(800, 540)">
        <rect x="0" y="0" width="300" height="60" fill="#fff9e6" stroke="#ffc107" stroke-width="2"/>
        <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold">直流分电屏</text>
        <text x="80" y="35" text-anchor="middle" class="model-text">保护A: DC220V-I</text>
        <text x="220" y="35" text-anchor="middle" class="model-text">保护B: DC220V-II</text>
        <text x="80" y="50" text-anchor="middle" class="model-text">测控: DC220V</text>
        <text x="220" y="50" text-anchor="middle" class="model-text">信号: DC110V</text>
    </g>

    <!-- 连接线 - 垂直和水平布局 -->

    <!-- CT二次绕组到各装置 -->
    <!-- 1S绕组到保护A -->
    <line x1="268" y1="208" x2="268" y2="290" class="protection-a"/>
    <line x1="268" y1="290" x2="85" y2="290" class="protection-a"/>
    <line x1="85" y1="290" x2="85" y2="300" class="protection-a"/>

    <!-- 2S绕组到保护B -->
    <line x1="288" y1="208" x2="288" y2="240" class="protection-b"/>
    <line x1="288" y1="240" x2="385" y2="240" class="protection-b"/>
    <line x1="385" y1="240" x2="385" y2="300" class="protection-b"/>

    <!-- 3S绕组到测控装置 -->
    <line x1="312" y1="208" x2="312" y2="230" class="measurement"/>
    <line x1="312" y1="230" x2="835" y2="230" class="measurement"/>
    <line x1="835" y1="230" x2="835" y2="305" class="measurement"/>

    <!-- PT到各装置 -->
    <line x1="1220" y1="208" x2="1220" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="1220" y1="220" x2="85" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="85" y1="220" x2="85" y2="315" stroke="blue" stroke-width="2"/>

    <line x1="1220" y1="220" x2="385" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="385" y1="220" x2="385" y2="315" stroke="blue" stroke-width="2"/>

    <line x1="1220" y1="220" x2="835" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="835" y1="220" x2="835" y2="320" stroke="blue" stroke-width="2"/>

    <!-- 保护装置到断路器跳闸线圈 -->
    <line x1="365" y1="305" x2="730" y2="305" class="protection-a"/>
    <line x1="730" y1="305" x2="730" y2="210" class="protection-a"/>

    <line x1="665" y1="305" x2="770" y2="305" class="protection-b"/>
    <line x1="770" y1="305" x2="770" y2="210" class="protection-b"/>

    <!-- 测控到智能终端 -->
    <line x1="1065" y1="315" x2="1085" y2="315" class="control"/>

    <!-- 智能终端到断路器 -->
    <line x1="1315" y1="305" x2="1400" y2="305" stroke="green" stroke-width="2"/>
    <line x1="1400" y1="305" x2="1400" y2="250" stroke="green" stroke-width="2"/>
    <line x1="1400" y1="250" x2="775" y2="250" stroke="green" stroke-width="2"/>
    <line x1="775" y1="250" x2="775" y2="230" stroke="green" stroke-width="2"/>

    <line x1="1315" y1="325" x2="1420" y2="325" stroke="red" stroke-width="2"/>
    <line x1="1420" y1="325" x2="1420" y2="260" stroke="red" stroke-width="2"/>
    <line x1="1420" y1="260" x2="730" y2="260" stroke="red" stroke-width="2"/>
    <line x1="730" y1="260" x2="730" y2="210" stroke="red" stroke-width="2"/>

    <!-- GOOSE连接 -->
    <line x1="365" y1="325" x2="1085" y2="325" class="goose"/>
    <line x1="665" y1="325" x2="1085" y2="325" class="goose"/>

    <!-- 直流电源连接 -->
    <line x1="365" y1="330" x2="365" y2="540" class="power"/>
    <line x1="365" y1="540" x2="365" y2="560" class="power"/>

    <line x1="665" y1="330" x2="665" y2="540" class="power"/>
    <line x1="665" y1="540" x2="665" y2="560" class="power"/>

    <line x1="1065" y1="330" x2="1065" y2="540" class="power"/>
    <line x1="1065" y1="540" x2="950" y2="540" class="power"/>
    <line x1="950" y1="540" x2="950" y2="560" class="power"/>

    <!-- 技术参数说明 -->
    <rect x="50" y="680" width="750" height="200" class="section-box"/>
    <text x="60" y="700" class="section-text">设备技术参数</text>

    <g transform="translate(80, 720)">
        <text x="0" y="0" font-size="11" font-weight="bold" fill="#dc3545">保护装置:</text>
        <text x="0" y="15" class="model-text">• RCS-978A: 距离保护≤20ms, 零序保护≤30ms</text>
        <text x="0" y="30" class="model-text">• CSC-278A: 距离保护≤20ms, 零序保护≤30ms</text>
        <text x="0" y="45" class="model-text">• 重合闸: 0.5-300s可调, 单相+三相</text>
        <text x="0" y="60" class="model-text">• 通信: IEC 61850, GOOSE≤4ms</text>

        <text x="300" y="0" font-size="11" font-weight="bold" fill="#198754">断路器:</text>
        <text x="300" y="15" class="model-text">• LW36-252: SF6断路器, 252kV/4000A/50kA</text>
        <text x="300" y="30" class="model-text">• 分闸时间: ≤40ms, 合闸时间: ≤100ms</text>
        <text x="300" y="45" class="model-text">• 机械寿命: 10000次</text>
        <text x="300" y="60" class="model-text">• 操动机构: CD17弹簧操动</text>

        <text x="0" y="90" font-size="11" font-weight="bold" fill="#0d6efd">互感器:</text>
        <text x="0" y="105" class="model-text">• CT: LZZBJ9-252, 2000/5A, 保护5P20</text>
        <text x="0" y="120" class="model-text">• PT: JDZ10-252, 220kV/100V, 保护3P</text>
        <text x="0" y="135" class="model-text">• 热稳定: 4s/80kA, 动稳定: 200kA</text>

        <text x="300" y="90" font-size="11" font-weight="bold" fill="#6f42c1">通信性能:</text>
        <text x="300" y="105" class="model-text">• GOOSE: ≤4ms, SV: 4000Hz采样≤3ms</text>
        <text x="300" y="120" class="model-text">• 同步精度: ±1μs (IEEE 1588v2)</text>
        <text x="300" y="135" class="model-text">• 网络: 双网冗余, 光纤连接</text>
    </g>

    <!-- 保护逻辑说明 -->
    <rect x="850" y="680" width="700" height="200" class="section-box"/>
    <text x="860" y="700" class="section-text">保护配置逻辑</text>

    <g transform="translate(880, 720)">
        <text x="0" y="0" font-size="11" font-weight="bold" fill="#dc3545">主保护:</text>
        <text x="0" y="15" class="model-text">• 距离I段: 瞬时, 85%线路长度</text>
        <text x="0" y="30" class="model-text">• 距离II段: 0.5s, 120%线路长度</text>
        <text x="0" y="45" class="model-text">• 零序I段: 瞬时, 85%线路长度</text>
        <text x="0" y="60" class="model-text">• 纵差保护: 光纤通道, ≤10ms</text>

        <text x="300" y="0" font-size="11" font-weight="bold" fill="#198754">后备保护:</text>
        <text x="300" y="15" class="model-text">• 距离III段: 1.2s, 覆盖相邻线路</text>
        <text x="300" y="30" class="model-text">• 零序II段: 0.5s, 覆盖相邻线路</text>
        <text x="300" y="45" class="model-text">• 过负荷: 告警+延时跳闸</text>
        <text x="300" y="60" class="model-text">• 失灵保护: 150ms启动</text>

        <text x="0" y="90" font-size="11" font-weight="bold" fill="#0d6efd">自动装置:</text>
        <text x="0" y="105" class="model-text">• 重合闸: 单相0.5s, 三相1.0s</text>
        <text x="0" y="120" class="model-text">• 重合次数: 1次, 检同期功能</text>
        <text x="0" y="135" class="model-text">• 检无压: 电压<30V, 频率±0.5Hz</text>

        <text x="300" y="90" font-size="11" font-weight="bold" fill="#ffc107">回路特点:</text>
        <text x="300" y="105" class="model-text">• 双重化保护完全独立</text>
        <text x="300" y="120" class="model-text">• 硬接线跳闸+GOOSE信息</text>
        <text x="300" y="135" class="model-text">• CT/PT专用绕组配置</text>
    </g>
    
</svg>'''
        
        return svg_content


def main():
    """主函数"""
    
    print("🏗️ 220kV线路完整逻辑回路实例")
    print("=" * 80)
    print("使用具体厂家设备和型号")
    print("=" * 80)
    
    # 创建220kV线路回路
    circuit = Real220kVLineCircuit()
    
    # 输出目录
    output_dir = "design_reports/real_220kv_line"
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print("\n🏗️ 生成220kV线路完整回路图...")
    
    # 生成回路图
    svg_content = circuit.generate_real_220kv_circuit_svg()
    svg_file = output_path / "real_220kv_line_circuit.svg"
    with open(svg_file, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    # 生成设备清单
    equipment_file = output_path / "equipment_list.json"
    with open(equipment_file, 'w', encoding='utf-8') as f:
        json.dump(circuit.equipment_config, f, ensure_ascii=False, indent=2)
    
    # 生成连接关系
    connection_file = output_path / "circuit_connections.json"
    with open(connection_file, 'w', encoding='utf-8') as f:
        json.dump(circuit.circuit_connections, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 220kV线路回路图已保存: {svg_file}")
    print(f"✅ 设备清单已保存: {equipment_file}")
    print(f"✅ 连接关系已保存: {connection_file}")
    
    print("\n📋 主要设备配置:")
    print("   🔴 保护A: 南瑞继保 RCS-978A")
    print("   🔵 保护B: 四方继保 CSC-278A")
    print("   ⚡ 断路器: 西安西电 LW36-252")
    print("   📊 测控: 国电南瑞 RCS-9611CS")
    print("   🔧 智能终端: 国电南瑞 RCS-9611IT")
    print("   🔋 直流电源: 许继电气 XJZG-220/110")
    
    print("\n🎯 技术特点:")
    print("   ✅ 双重化保护完全独立")
    print("   ✅ 硬接线跳闸+GOOSE信息")
    print("   ✅ CT/PT专用绕组配置")
    print("   ✅ IEC 61850标准通信")
    print("   ✅ 符合实际工程配置")


if __name__ == "__main__":
    main()
