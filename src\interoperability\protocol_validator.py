"""
协议验证器
验证IEC61850协议的实现和兼容性
"""

import logging
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from ..core.rules.base_rule import ValidationResult


logger = logging.getLogger(__name__)


class ProtocolType(Enum):
    """协议类型"""
    MMS = "MMS"
    GOOSE = "GOOSE"
    SMV = "SMV"
    HTTP = "HTTP"
    FTP = "FTP"


class ProtocolVersion(Enum):
    """协议版本"""
    IEC61850_2003 = "2003"
    IEC61850_2004 = "2004"
    IEC61850_2011 = "2011"


@dataclass
class ProtocolCapability:
    """协议能力"""
    protocol_type: ProtocolType
    version: str
    supported_services: List[str]
    optional_features: List[str]
    limitations: List[str]
    configuration: Dict[str, Any]


@dataclass
class ProtocolValidationResult:
    """协议验证结果"""
    protocol_type: ProtocolType
    is_valid: bool
    compliance_level: str  # full, partial, minimal, non_compliant
    issues: List[ValidationResult]
    supported_features: List[str]
    missing_features: List[str]
    recommendations: List[str]


class ProtocolValidator:
    """协议验证器"""
    
    def __init__(self):
        """初始化协议验证器"""
        self.protocol_standards = self._load_protocol_standards()
        self.compliance_rules = self._load_compliance_rules()
        
        logger.info("协议验证器初始化完成")
    
    def validate_protocol_implementation(self, 
                                       device_config: Dict[str, Any],
                                       protocol_type: ProtocolType) -> ProtocolValidationResult:
        """
        验证设备的协议实现
        
        Args:
            device_config: 设备配置
            protocol_type: 协议类型
            
        Returns:
            ProtocolValidationResult: 协议验证结果
        """
        try:
            logger.info(f"开始验证 {protocol_type.value} 协议实现")
            
            issues = []
            supported_features = []
            missing_features = []
            
            # 获取协议标准要求
            standard_requirements = self.protocol_standards.get(protocol_type, {})
            
            # 验证必需服务
            service_issues, supported_services, missing_services = self._validate_required_services(
                device_config, protocol_type, standard_requirements
            )
            issues.extend(service_issues)
            supported_features.extend(supported_services)
            missing_features.extend(missing_services)
            
            # 验证协议版本
            version_issues = self._validate_protocol_version(device_config, protocol_type)
            issues.extend(version_issues)
            
            # 验证配置参数
            config_issues = self._validate_protocol_configuration(
                device_config, protocol_type, standard_requirements
            )
            issues.extend(config_issues)
            
            # 验证数据类型支持
            datatype_issues, supported_types, missing_types = self._validate_data_types(
                device_config, protocol_type, standard_requirements
            )
            issues.extend(datatype_issues)
            supported_features.extend([f"DataType:{t}" for t in supported_types])
            missing_features.extend([f"DataType:{t}" for t in missing_types])
            
            # 计算合规级别
            compliance_level = self._calculate_compliance_level(issues, supported_features, missing_features)
            
            # 生成推荐
            recommendations = self._generate_protocol_recommendations(issues, missing_features)
            
            result = ProtocolValidationResult(
                protocol_type=protocol_type,
                is_valid=len([i for i in issues if i.severity == "error"]) == 0,
                compliance_level=compliance_level,
                issues=issues,
                supported_features=supported_features,
                missing_features=missing_features,
                recommendations=recommendations
            )
            
            logger.info(f"{protocol_type.value} 协议验证完成: {compliance_level}")
            return result
            
        except Exception as e:
            logger.error(f"协议验证失败: {e}")
            return ProtocolValidationResult(
                protocol_type=protocol_type,
                is_valid=False,
                compliance_level="non_compliant",
                issues=[],
                supported_features=[],
                missing_features=[],
                recommendations=[]
            )
    
    def validate_protocol_interoperability(self,
                                         device1_config: Dict[str, Any],
                                         device2_config: Dict[str, Any],
                                         protocol_type: ProtocolType) -> List[ValidationResult]:
        """
        验证两个设备间的协议互操作性
        
        Args:
            device1_config: 设备1配置
            device2_config: 设备2配置
            protocol_type: 协议类型
            
        Returns:
            List[ValidationResult]: 验证结果列表
        """
        issues = []
        
        try:
            logger.info(f"验证 {protocol_type.value} 协议互操作性")
            
            # 验证协议版本兼容性
            version_issues = self._validate_version_compatibility(
                device1_config, device2_config, protocol_type
            )
            issues.extend(version_issues)
            
            # 验证服务兼容性
            service_issues = self._validate_service_compatibility(
                device1_config, device2_config, protocol_type
            )
            issues.extend(service_issues)
            
            # 验证参数兼容性
            param_issues = self._validate_parameter_compatibility(
                device1_config, device2_config, protocol_type
            )
            issues.extend(param_issues)
            
            # 协议特定的验证
            if protocol_type == ProtocolType.GOOSE:
                goose_issues = self._validate_goose_interoperability(device1_config, device2_config)
                issues.extend(goose_issues)
            elif protocol_type == ProtocolType.SMV:
                smv_issues = self._validate_smv_interoperability(device1_config, device2_config)
                issues.extend(smv_issues)
            elif protocol_type == ProtocolType.MMS:
                mms_issues = self._validate_mms_interoperability(device1_config, device2_config)
                issues.extend(mms_issues)
            
            logger.info(f"{protocol_type.value} 互操作性验证完成，发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"协议互操作性验证失败: {e}")
            return []
    
    def analyze_protocol_capabilities(self, device_config: Dict[str, Any]) -> List[ProtocolCapability]:
        """
        分析设备的协议能力
        
        Args:
            device_config: 设备配置
            
        Returns:
            List[ProtocolCapability]: 协议能力列表
        """
        capabilities = []
        
        try:
            # 分析每种协议的能力
            for protocol_type in ProtocolType:
                capability = self._analyze_single_protocol_capability(device_config, protocol_type)
                if capability:
                    capabilities.append(capability)
            
            logger.info(f"协议能力分析完成，支持 {len(capabilities)} 种协议")
            return capabilities
            
        except Exception as e:
            logger.error(f"协议能力分析失败: {e}")
            return []
    
    def _validate_required_services(self,
                                  device_config: Dict[str, Any],
                                  protocol_type: ProtocolType,
                                  standard_requirements: Dict[str, Any]) -> tuple:
        """验证必需服务"""
        issues = []
        supported_services = []
        missing_services = []
        
        required_services = standard_requirements.get('required_services', [])
        device_services = device_config.get('supported_services', [])
        
        for service in required_services:
            if service in device_services:
                supported_services.append(service)
            else:
                missing_services.append(service)
                
                issue = ValidationResult(
                    rule_id=f"PROTOCOL_{protocol_type.value}_SERVICE",
                    passed=False,
                    message=f"缺少必需的{protocol_type.value}服务: {service}",
                    severity="error",
                    details={
                        'protocol': protocol_type.value,
                        'missing_service': service,
                        'required_services': required_services,
                        'device_services': device_services
                    }
                )
                issues.append(issue)
        
        return issues, supported_services, missing_services
    
    def _validate_protocol_version(self,
                                 device_config: Dict[str, Any],
                                 protocol_type: ProtocolType) -> List[ValidationResult]:
        """验证协议版本"""
        issues = []
        
        device_version = device_config.get('protocol_versions', {}).get(protocol_type.value)
        if not device_version:
            issue = ValidationResult(
                rule_id=f"PROTOCOL_{protocol_type.value}_VERSION",
                passed=False,
                message=f"未指定{protocol_type.value}协议版本",
                severity="warning",
                details={
                    'protocol': protocol_type.value,
                    'available_versions': [v.value for v in ProtocolVersion]
                }
            )
            issues.append(issue)
        else:
            # 检查版本是否受支持
            supported_versions = [v.value for v in ProtocolVersion]
            if device_version not in supported_versions:
                issue = ValidationResult(
                    rule_id=f"PROTOCOL_{protocol_type.value}_VERSION",
                    passed=False,
                    message=f"不支持的{protocol_type.value}协议版本: {device_version}",
                    severity="error",
                    details={
                        'protocol': protocol_type.value,
                        'device_version': device_version,
                        'supported_versions': supported_versions
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_protocol_configuration(self,
                                       device_config: Dict[str, Any],
                                       protocol_type: ProtocolType,
                                       standard_requirements: Dict[str, Any]) -> List[ValidationResult]:
        """验证协议配置"""
        issues = []
        
        required_params = standard_requirements.get('required_parameters', {})
        device_params = device_config.get('protocol_parameters', {}).get(protocol_type.value, {})
        
        for param_name, param_requirements in required_params.items():
            device_value = device_params.get(param_name)
            
            if device_value is None:
                issue = ValidationResult(
                    rule_id=f"PROTOCOL_{protocol_type.value}_CONFIG",
                    passed=False,
                    message=f"缺少{protocol_type.value}协议参数: {param_name}",
                    severity="error",
                    details={
                        'protocol': protocol_type.value,
                        'missing_parameter': param_name,
                        'requirements': param_requirements
                    }
                )
                issues.append(issue)
            else:
                # 验证参数值
                if 'range' in param_requirements:
                    min_val, max_val = param_requirements['range']
                    if not (min_val <= device_value <= max_val):
                        issue = ValidationResult(
                            rule_id=f"PROTOCOL_{protocol_type.value}_CONFIG",
                            passed=False,
                            message=f"{protocol_type.value}协议参数{param_name}超出范围: {device_value}",
                            severity="warning",
                            details={
                                'protocol': protocol_type.value,
                                'parameter': param_name,
                                'value': device_value,
                                'valid_range': param_requirements['range']
                            }
                        )
                        issues.append(issue)
        
        return issues
    
    def _validate_data_types(self,
                           device_config: Dict[str, Any],
                           protocol_type: ProtocolType,
                           standard_requirements: Dict[str, Any]) -> tuple:
        """验证数据类型支持"""
        issues = []
        supported_types = []
        missing_types = []
        
        required_types = standard_requirements.get('required_data_types', [])
        device_types = device_config.get('supported_data_types', [])
        
        for data_type in required_types:
            if data_type in device_types:
                supported_types.append(data_type)
            else:
                missing_types.append(data_type)
                
                issue = ValidationResult(
                    rule_id=f"PROTOCOL_{protocol_type.value}_DATATYPE",
                    passed=False,
                    message=f"不支持{protocol_type.value}协议要求的数据类型: {data_type}",
                    severity="warning",
                    details={
                        'protocol': protocol_type.value,
                        'missing_data_type': data_type,
                        'required_types': required_types,
                        'device_types': device_types
                    }
                )
                issues.append(issue)
        
        return issues, supported_types, missing_types
    
    def _validate_version_compatibility(self,
                                      device1_config: Dict[str, Any],
                                      device2_config: Dict[str, Any],
                                      protocol_type: ProtocolType) -> List[ValidationResult]:
        """验证版本兼容性"""
        issues = []
        
        version1 = device1_config.get('protocol_versions', {}).get(protocol_type.value)
        version2 = device2_config.get('protocol_versions', {}).get(protocol_type.value)
        
        if version1 and version2 and version1 != version2:
            # 检查版本兼容性
            if not self._are_versions_compatible(version1, version2):
                issue = ValidationResult(
                    rule_id=f"PROTOCOL_{protocol_type.value}_VERSION_COMPAT",
                    passed=False,
                    message=f"{protocol_type.value}协议版本不兼容: {version1} vs {version2}",
                    severity="error",
                    details={
                        'protocol': protocol_type.value,
                        'version1': version1,
                        'version2': version2
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_service_compatibility(self,
                                      device1_config: Dict[str, Any],
                                      device2_config: Dict[str, Any],
                                      protocol_type: ProtocolType) -> List[ValidationResult]:
        """验证服务兼容性"""
        issues = []
        
        services1 = set(device1_config.get('supported_services', []))
        services2 = set(device2_config.get('supported_services', []))
        
        # 检查关键服务的互操作性
        critical_services = self._get_critical_services(protocol_type)
        
        for service in critical_services:
            if service in services1 and service not in services2:
                issue = ValidationResult(
                    rule_id=f"PROTOCOL_{protocol_type.value}_SERVICE_COMPAT",
                    passed=False,
                    message=f"设备2不支持关键{protocol_type.value}服务: {service}",
                    severity="error",
                    details={
                        'protocol': protocol_type.value,
                        'service': service,
                        'device1_services': list(services1),
                        'device2_services': list(services2)
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_parameter_compatibility(self,
                                        device1_config: Dict[str, Any],
                                        device2_config: Dict[str, Any],
                                        protocol_type: ProtocolType) -> List[ValidationResult]:
        """验证参数兼容性"""
        issues = []
        
        params1 = device1_config.get('protocol_parameters', {}).get(protocol_type.value, {})
        params2 = device2_config.get('protocol_parameters', {}).get(protocol_type.value, {})
        
        # 检查关键参数的兼容性
        critical_params = self._get_critical_parameters(protocol_type)
        
        for param in critical_params:
            value1 = params1.get(param)
            value2 = params2.get(param)
            
            if value1 is not None and value2 is not None:
                if not self._are_parameters_compatible(param, value1, value2, protocol_type):
                    issue = ValidationResult(
                        rule_id=f"PROTOCOL_{protocol_type.value}_PARAM_COMPAT",
                        passed=False,
                        message=f"{protocol_type.value}协议参数不兼容: {param} ({value1} vs {value2})",
                        severity="warning",
                        details={
                            'protocol': protocol_type.value,
                            'parameter': param,
                            'value1': value1,
                            'value2': value2
                        }
                    )
                    issues.append(issue)
        
        return issues
    
    def _validate_goose_interoperability(self,
                                       device1_config: Dict[str, Any],
                                       device2_config: Dict[str, Any]) -> List[ValidationResult]:
        """验证GOOSE互操作性"""
        issues = []
        
        # 检查GOOSE特定的兼容性
        goose1 = device1_config.get('goose_config', {})
        goose2 = device2_config.get('goose_config', {})
        
        # 检查发布间隔和超时时间
        publish_interval = goose1.get('publish_interval', 0)
        timeout = goose2.get('timeout', 0)
        
        if publish_interval > 0 and timeout > 0 and publish_interval > timeout:
            issue = ValidationResult(
                rule_id="GOOSE_TIMING_COMPAT",
                passed=False,
                message=f"GOOSE发布间隔({publish_interval}ms)超过超时时间({timeout}ms)",
                severity="error",
                details={
                    'publish_interval': publish_interval,
                    'timeout': timeout
                }
            )
            issues.append(issue)
        
        return issues
    
    def _validate_smv_interoperability(self,
                                     device1_config: Dict[str, Any],
                                     device2_config: Dict[str, Any]) -> List[ValidationResult]:
        """验证SMV互操作性"""
        issues = []
        
        # 检查SMV特定的兼容性
        smv1 = device1_config.get('smv_config', {})
        smv2 = device2_config.get('smv_config', {})
        
        # 检查采样率
        sample_rate1 = smv1.get('sample_rate', 0)
        sample_rate2 = smv2.get('sample_rate', 0)
        
        if sample_rate1 > 0 and sample_rate2 > 0 and sample_rate1 != sample_rate2:
            issue = ValidationResult(
                rule_id="SMV_SAMPLE_RATE_COMPAT",
                passed=False,
                message=f"SMV采样率不匹配: {sample_rate1} vs {sample_rate2}",
                severity="warning",
                details={
                    'sample_rate1': sample_rate1,
                    'sample_rate2': sample_rate2
                }
            )
            issues.append(issue)
        
        return issues
    
    def _validate_mms_interoperability(self,
                                     device1_config: Dict[str, Any],
                                     device2_config: Dict[str, Any]) -> List[ValidationResult]:
        """验证MMS互操作性"""
        issues = []
        
        # 检查MMS特定的兼容性
        mms1 = device1_config.get('mms_config', {})
        mms2 = device2_config.get('mms_config', {})
        
        # 检查最大PDU大小
        max_pdu1 = mms1.get('max_pdu_size', 0)
        max_pdu2 = mms2.get('max_pdu_size', 0)
        
        if max_pdu1 > 0 and max_pdu2 > 0:
            min_pdu = min(max_pdu1, max_pdu2)
            if min_pdu < 512:  # 最小推荐PDU大小
                issue = ValidationResult(
                    rule_id="MMS_PDU_SIZE_COMPAT",
                    passed=False,
                    message=f"MMS PDU大小过小: {min_pdu} bytes",
                    severity="warning",
                    details={
                        'max_pdu1': max_pdu1,
                        'max_pdu2': max_pdu2,
                        'effective_size': min_pdu
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _analyze_single_protocol_capability(self,
                                          device_config: Dict[str, Any],
                                          protocol_type: ProtocolType) -> Optional[ProtocolCapability]:
        """分析单个协议能力"""
        # 检查设备是否支持该协议
        supported_protocols = device_config.get('supported_protocols', [])
        if protocol_type.value not in supported_protocols:
            return None
        
        # 获取协议信息
        version = device_config.get('protocol_versions', {}).get(protocol_type.value, 'unknown')
        services = device_config.get('supported_services', [])
        
        # 过滤该协议相关的服务
        protocol_services = [s for s in services if self._is_protocol_service(s, protocol_type)]
        
        # 获取可选特性
        optional_features = device_config.get('optional_features', {}).get(protocol_type.value, [])
        
        # 获取限制
        limitations = device_config.get('limitations', {}).get(protocol_type.value, [])
        
        # 获取配置
        configuration = device_config.get('protocol_parameters', {}).get(protocol_type.value, {})
        
        return ProtocolCapability(
            protocol_type=protocol_type,
            version=version,
            supported_services=protocol_services,
            optional_features=optional_features,
            limitations=limitations,
            configuration=configuration
        )
    
    def _calculate_compliance_level(self,
                                  issues: List[ValidationResult],
                                  supported_features: List[str],
                                  missing_features: List[str]) -> str:
        """计算合规级别"""
        error_count = len([i for i in issues if i.severity == "error"])
        warning_count = len([i for i in issues if i.severity == "warning"])
        
        if error_count == 0 and warning_count == 0:
            return "full"
        elif error_count == 0 and warning_count <= 2:
            return "partial"
        elif error_count <= 1:
            return "minimal"
        else:
            return "non_compliant"
    
    def _generate_protocol_recommendations(self,
                                         issues: List[ValidationResult],
                                         missing_features: List[str]) -> List[str]:
        """生成协议推荐"""
        recommendations = []
        
        # 基于问题生成推荐
        for issue in issues:
            if issue.severity == "error":
                recommendations.append(f"修复错误: {issue.message}")
        
        # 基于缺失特性生成推荐
        for feature in missing_features[:3]:  # 限制数量
            recommendations.append(f"实现缺失特性: {feature}")
        
        return recommendations[:5]
    
    def _load_protocol_standards(self) -> Dict[ProtocolType, Dict[str, Any]]:
        """加载协议标准"""
        return {
            ProtocolType.MMS: {
                'required_services': ['GetNameList', 'Read', 'Write', 'GetVariableAccessAttributes'],
                'required_data_types': ['BOOLEAN', 'INT32', 'FLOAT32', 'VISIBLE_STRING'],
                'required_parameters': {
                    'max_pdu_size': {'range': (512, 65535)},
                    'timeout': {'range': (1000, 60000)}
                }
            },
            ProtocolType.GOOSE: {
                'required_services': ['Publish', 'Subscribe'],
                'required_data_types': ['BOOLEAN', 'INT32', 'FLOAT32', 'Timestamp', 'Quality'],
                'required_parameters': {
                    'publish_interval': {'range': (4, 60000)},
                    'timeout': {'range': (100, 60000)}
                }
            },
            ProtocolType.SMV: {
                'required_services': ['Publish', 'Subscribe'],
                'required_data_types': ['INT32', 'FLOAT32', 'Timestamp', 'Quality'],
                'required_parameters': {
                    'sample_rate': {'range': (80, 14400)},
                    'samples_per_cycle': {'range': (80, 256)}
                }
            }
        }
    
    def _load_compliance_rules(self) -> Dict[str, Any]:
        """加载合规规则"""
        return {
            'version_compatibility': {
                '2003': ['2003'],
                '2004': ['2003', '2004'],
                '2011': ['2003', '2004', '2011']
            }
        }
    
    def _get_critical_services(self, protocol_type: ProtocolType) -> List[str]:
        """获取关键服务"""
        critical_services = {
            ProtocolType.MMS: ['Read', 'Write'],
            ProtocolType.GOOSE: ['Publish', 'Subscribe'],
            ProtocolType.SMV: ['Publish', 'Subscribe']
        }
        return critical_services.get(protocol_type, [])
    
    def _get_critical_parameters(self, protocol_type: ProtocolType) -> List[str]:
        """获取关键参数"""
        critical_params = {
            ProtocolType.MMS: ['max_pdu_size'],
            ProtocolType.GOOSE: ['publish_interval', 'timeout'],
            ProtocolType.SMV: ['sample_rate']
        }
        return critical_params.get(protocol_type, [])
    
    def _are_versions_compatible(self, version1: str, version2: str) -> bool:
        """检查版本兼容性"""
        compatibility_matrix = self.compliance_rules.get('version_compatibility', {})
        
        # 检查双向兼容性
        compatible_with_v1 = compatibility_matrix.get(version1, [])
        compatible_with_v2 = compatibility_matrix.get(version2, [])
        
        return version2 in compatible_with_v1 or version1 in compatible_with_v2
    
    def _are_parameters_compatible(self, param_name: str, value1: Any, value2: Any, protocol_type: ProtocolType) -> bool:
        """检查参数兼容性"""
        # 简化的参数兼容性检查
        if param_name in ['sample_rate', 'samples_per_cycle']:
            return value1 == value2
        elif param_name in ['max_pdu_size']:
            return min(value1, value2) >= 512
        else:
            return True
    
    def _is_protocol_service(self, service: str, protocol_type: ProtocolType) -> bool:
        """检查服务是否属于指定协议"""
        protocol_service_map = {
            ProtocolType.MMS: ['GetNameList', 'Read', 'Write', 'GetVariableAccessAttributes'],
            ProtocolType.GOOSE: ['Publish', 'Subscribe', 'GOOSE'],
            ProtocolType.SMV: ['Publish', 'Subscribe', 'SMV']
        }
        
        protocol_services = protocol_service_map.get(protocol_type, [])
        return any(ps in service for ps in protocol_services)
