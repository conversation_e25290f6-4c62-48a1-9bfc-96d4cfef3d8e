{% extends "base.html" %}

{% block title %}配置文件对比 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center">
                <i class="fas fa-code-branch fa-2x text-primary me-3"></i>
                <div>
                    <h1 class="mb-1">配置文件对比</h1>
                    <p class="text-muted mb-0">智能分析配置文件版本差异，识别关键变更</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 使命说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info border-0">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="fas fa-lightbulb fa-2x text-warning"></i>
                    </div>
                    <div class="col-md-11">
                        <h6 class="mb-2">
                            <i class="fas fa-target me-2"></i>
                            解决版本管理和变更追踪难题
                        </h6>
                        <p class="mb-0">
                            智能变电站配置文件版本间的差异难以直观发现，手工对比XML文件容易遗漏关键变更。
                            本工具提供智能的结构化对比，帮助工程师快速识别和评估配置变更的影响。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 文件选择区域 -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-import me-2"></i>
                        选择对比文件
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 源文件选择 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file me-2"></i>
                                        源文件（旧版本）
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="sourceFileDropZone" class="drop-zone border-2 border-dashed rounded p-4 text-center mb-3">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-2">拖拽文件到此处或点击选择</p>
                                        <input type="file" id="sourceFileInput" class="d-none" accept=".scd,.icd,.cid,.xml">
                                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('sourceFileInput').click()">
                                            选择源文件
                                        </button>
                                    </div>
                                    <div id="sourceFileInfo" class="d-none">
                                        <div class="alert alert-success border-0 py-2">
                                            <div id="sourceFileDetails"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file me-2"></i>
                                        目标文件（新版本）
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div id="targetFileDropZone" class="drop-zone border-2 border-dashed rounded p-4 text-center mb-3">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-2">拖拽文件到此处或点击选择</p>
                                        <input type="file" id="targetFileInput" class="d-none" accept=".scd,.icd,.cid,.xml">
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="document.getElementById('targetFileInput').click()">
                                            选择目标文件
                                        </button>
                                    </div>
                                    <div id="targetFileInfo" class="d-none">
                                        <div class="alert alert-success border-0 py-2">
                                            <div id="targetFileDetails"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 对比选项 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="mb-3">对比选项</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="structuralDiff" checked>
                                        <label class="form-check-label" for="structuralDiff">
                                            结构化对比
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="semanticDiff" checked>
                                        <label class="form-check-label" for="semanticDiff">
                                            语义化分析
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="impactAnalysis" checked>
                                        <label class="form-check-label" for="impactAnalysis">
                                            影响分析
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="riskAssessment" checked>
                                        <label class="form-check-label" for="riskAssessment">
                                            风险评估
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 对比按钮 -->
                    <div class="text-center">
                        <button type="button" class="btn btn-primary btn-lg" onclick="startComparison()" disabled id="compareBtn">
                            <i class="fas fa-search me-2"></i>
                            开始对比
                        </button>
                    </div>

                    <!-- 对比进度 -->
                    <div id="comparisonProgress" class="d-none mt-4">
                        <div class="alert alert-info border-0">
                            <h6 class="mb-3">
                                <i class="fas fa-cogs me-2"></i>
                                正在对比配置文件...
                            </h6>
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="comparisonProgressText">准备开始对比...</div>
                        </div>
                    </div>

                    <!-- 对比结果 -->
                    <div id="comparisonResult" class="d-none mt-4">
                        <!-- 结果将通过JavaScript动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="col-lg-4">
            <!-- 对比功能说明 -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        对比功能
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            智能结构化对比
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            IED设备变更检测
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            网络配置差异分析
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            变更影响评估
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            风险等级评定
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            修复建议生成
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 变更类型说明 -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        变更类型
                    </h6>
                </div>
                <div class="card-body">
                    <div class="change-type-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">新增</span>
                            <small>新增的配置元素</small>
                        </div>
                    </div>
                    <div class="change-type-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2">删除</span>
                            <small>删除的配置元素</small>
                        </div>
                    </div>
                    <div class="change-type-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning me-2">修改</span>
                            <small>修改的配置元素</small>
                        </div>
                    </div>
                    <div class="change-type-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">移动</span>
                            <small>位置变更的元素</small>
                        </div>
                    </div>
                    <div class="change-type-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-secondary me-2">重命名</span>
                            <small>名称变更的元素</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 风险等级说明 -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        风险等级
                    </h6>
                </div>
                <div class="card-body">
                    <div class="risk-level-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2">关键</span>
                            <small>可能影响系统运行</small>
                        </div>
                    </div>
                    <div class="risk-level-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning me-2">重要</span>
                            <small>需要仔细评估</small>
                        </div>
                    </div>
                    <div class="risk-level-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">次要</span>
                            <small>影响较小</small>
                        </div>
                    </div>
                    <div class="risk-level-item mb-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-secondary me-2">外观</span>
                            <small>不影响功能</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let sourceFileId = null;
let targetFileId = null;
let comparisonResult = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    setupFileDropZones();
});

// 设置文件拖拽区域
function setupFileDropZones() {
    setupDropZone('sourceFileDropZone', 'sourceFileInput', handleSourceFile);
    setupDropZone('targetFileDropZone', 'targetFileInput', handleTargetFile);
}

function setupDropZone(dropZoneId, inputId, handler) {
    const dropZone = document.getElementById(dropZoneId);
    const fileInput = document.getElementById(inputId);
    
    // 拖拽事件
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handler(files[0]);
        }
    });
    
    // 文件选择事件
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handler(e.target.files[0]);
        }
    });
}

// 处理源文件
async function handleSourceFile(file) {
    if (!validateFile(file)) return;
    
    try {
        const uploadResult = await uploadFile(file);
        if (uploadResult.success) {
            sourceFileId = uploadResult.data.file_id;
            showFileInfo('source', file, uploadResult.data);
            checkCompareReady();
        } else {
            showToast('上传失败', uploadResult.error, 'error');
        }
    } catch (error) {
        showToast('上传失败', error.message, 'error');
    }
}

// 处理目标文件
async function handleTargetFile(file) {
    if (!validateFile(file)) return;
    
    try {
        const uploadResult = await uploadFile(file);
        if (uploadResult.success) {
            targetFileId = uploadResult.data.file_id;
            showFileInfo('target', file, uploadResult.data);
            checkCompareReady();
        } else {
            showToast('上传失败', uploadResult.error, 'error');
        }
    } catch (error) {
        showToast('上传失败', error.message, 'error');
    }
}

// 验证文件
function validateFile(file) {
    const allowedTypes = ['.scd', '.icd', '.cid', '.xml'];
    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExt)) {
        showToast('错误', '不支持的文件类型', 'error');
        return false;
    }
    
    if (file.size > 50 * 1024 * 1024) {
        showToast('错误', '文件大小不能超过50MB', 'error');
        return false;
    }
    
    return true;
}

// 上传文件
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 显示文件信息
function showFileInfo(type, file, uploadData) {
    const infoDiv = document.getElementById(`${type}FileInfo`);
    const detailsDiv = document.getElementById(`${type}FileDetails`);
    
    detailsDiv.innerHTML = `
        <div class="small">
            <div><strong>文件名:</strong> ${file.name}</div>
            <div><strong>大小:</strong> ${formatFileSize(file.size)}</div>
            <div><strong>上传ID:</strong> ${uploadData.file_id}</div>
        </div>
    `;
    
    infoDiv.classList.remove('d-none');
}

// 检查是否可以开始对比
function checkCompareReady() {
    const compareBtn = document.getElementById('compareBtn');
    if (sourceFileId && targetFileId) {
        compareBtn.disabled = false;
    }
}

// 开始对比
async function startComparison() {
    if (!sourceFileId || !targetFileId) {
        showToast('错误', '请先选择两个文件', 'error');
        return;
    }
    
    try {
        showProgress();
        updateProgress(10, '准备对比文件...');
        
        const response = await fetch('/api/compare', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                source_file_id: sourceFileId,
                target_file_id: targetFileId,
                options: {
                    structural_diff: document.getElementById('structuralDiff').checked,
                    semantic_diff: document.getElementById('semanticDiff').checked,
                    impact_analysis: document.getElementById('impactAnalysis').checked,
                    risk_assessment: document.getElementById('riskAssessment').checked
                }
            })
        });
        
        updateProgress(50, '正在分析差异...');
        
        const result = await response.json();
        
        updateProgress(100, '对比完成');
        
        setTimeout(() => {
            hideProgress();
            if (result.success) {
                showComparisonResult(result);
            } else {
                showToast('对比失败', result.error, 'error');
            }
        }, 1000);
        
    } catch (error) {
        hideProgress();
        showToast('对比失败', error.message, 'error');
    }
}

// 显示对比结果
function showComparisonResult(result) {
    const resultDiv = document.getElementById('comparisonResult');
    const data = result.data;
    
    const criticalClass = data.has_critical_changes ? 'danger' : 'success';
    const criticalIcon = data.has_critical_changes ? 'exclamation-triangle' : 'check-circle';
    
    resultDiv.innerHTML = `
        <div class="alert alert-${criticalClass} border-0">
            <h5 class="mb-3">
                <i class="fas fa-${criticalIcon} me-2"></i>
                对比完成
            </h5>
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4">${data.total_changes}</div>
                        <small>总变更数</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-danger">${data.statistics.by_level?.critical || 0}</div>
                        <small>关键变更</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-warning">${data.statistics.by_level?.major || 0}</div>
                        <small>重要变更</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-info">${data.statistics.by_level?.minor || 0}</div>
                        <small>次要变更</small>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <h6>对比摘要</h6>
                <p class="mb-0">${data.summary}</p>
            </div>
            <div class="d-flex gap-2">
                <a href="/api/report/${data.report_id}/download" class="btn btn-primary">
                    <i class="fas fa-download me-2"></i>下载对比报告
                </a>
                <button class="btn btn-outline-primary" onclick="viewDetailedComparison('${data.report_id}')">
                    <i class="fas fa-eye me-2"></i>查看详细对比
                </button>
            </div>
        </div>
    `;
    
    resultDiv.classList.remove('d-none');
    comparisonResult = result;
}

// 查看详细对比
function viewDetailedComparison(reportId) {
    window.open(`/validate?report_id=${reportId}`, '_blank');
}

// 工具函数
function showProgress() {
    document.getElementById('comparisonProgress').classList.remove('d-none');
}

function hideProgress() {
    document.getElementById('comparisonProgress').classList.add('d-none');
}

function updateProgress(percent, text) {
    const progressBar = document.querySelector('#comparisonProgress .progress-bar');
    const progressText = document.getElementById('comparisonProgressText');
    
    progressBar.style.width = percent + '%';
    progressText.textContent = text;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function showToast(title, message, type = 'info') {
    // 使用全局toast函数
    if (window.toast) {
        window.toast[type](title, message);
    } else {
        alert(`${title}: ${message}`);
    }
}
</script>

<style>
.drop-zone {
    transition: all 0.3s ease;
    cursor: pointer;
}

.drop-zone:hover,
.drop-zone.drag-over {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}

.change-type-item,
.risk-level-item {
    padding: 4px 0;
}
</style>
{% endblock %}
{% endblock %}
