#!/usr/bin/env python3
"""
安全的IEC61850设计检查器 - 精简版
重构后的主程序，专注于核心功能，提升安全性
"""

import sys
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import argparse
import json

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from core.secure_scd_parser import SecureSCDParser
    from core.secure_validator import SecureValidator
except ImportError as e:
    print(f"❌ 导入核心模块失败: {e}")
    print("请确保src/core目录下的模块文件存在")
    sys.exit(1)


class SecureIEC61850Checker:
    """安全的IEC61850设计检查器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化检查器"""
        self.config = config or {}
        self.setup_logging()
        
        # 初始化核心组件
        self.parser = SecureSCDParser(self.config.get('parser', {}))
        self.validator = SecureValidator(self.config.get('validator', {}))
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("安全IEC61850检查器初始化完成")
    
    def setup_logging(self):
        """设置日志"""
        log_level = self.config.get('log_level', 'INFO')
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('iec61850_checker.log', encoding='utf-8')
            ]
        )
    
    def check_file(self, file_path: str) -> Dict[str, Any]:
        """
        检查SCD文件
        
        Args:
            file_path: SCD文件路径
            
        Returns:
            Dict: 检查结果
        """
        try:
            self.logger.info(f"开始检查文件: {file_path}")
            
            # 1. 解析文件
            parse_result = self.parser.parse_file(file_path)
            
            if not parse_result.success:
                return {
                    'success': False,
                    'stage': 'parsing',
                    'errors': parse_result.errors,
                    'warnings': parse_result.warnings
                }
            
            # 2. 验证数据
            validation_result = self.validator.validate(parse_result.data)
            
            # 3. 生成检查结果
            result = {
                'success': validation_result.success,
                'stage': 'validation',
                'file_info': parse_result.file_info,
                'validation': {
                    'score': validation_result.score,
                    'issues_count': len(validation_result.issues),
                    'summary': validation_result.summary
                },
                'issues': [
                    {
                        'type': issue.type,
                        'severity': issue.severity.value,
                        'description': issue.description,
                        'location': issue.location,
                        'recommendation': issue.recommendation
                    }
                    for issue in validation_result.issues
                ]
            }
            
            self.logger.info(f"文件检查完成，得分: {validation_result.score:.1f}")
            return result
            
        except Exception as e:
            self.logger.error(f"检查文件时发生错误: {e}")
            return {
                'success': False,
                'stage': 'error',
                'errors': [f"检查过程发生错误: {str(e)}"]
            }
    
    def generate_report(self, result: Dict[str, Any], output_file: Optional[str] = None) -> str:
        """
        生成检查报告
        
        Args:
            result: 检查结果
            output_file: 输出文件路径
            
        Returns:
            str: 报告内容
        """
        report_lines = []
        
        # 报告头部
        report_lines.extend([
            "IEC61850设计检查报告",
            "=" * 50,
            f"检查时间: {self.get_current_time()}",
            ""
        ])
        
        # 文件信息
        if 'file_info' in result:
            file_info = result['file_info']
            report_lines.extend([
                "文件信息:",
                f"  文件路径: {file_info.get('file_path', 'N/A')}",
                f"  文件大小: {file_info.get('file_size', 0)} bytes",
                f"  根元素: {file_info.get('root_tag', 'N/A')}",
                ""
            ])
        
        # 检查结果
        if result['success']:
            validation = result.get('validation', {})
            report_lines.extend([
                "检查结果: ✅ 通过",
                f"验证得分: {validation.get('score', 0):.1f}/100",
                f"发现问题: {validation.get('issues_count', 0)} 个",
                ""
            ])
        else:
            report_lines.extend([
                "检查结果: ❌ 失败",
                f"失败阶段: {result.get('stage', 'unknown')}",
                ""
            ])
        
        # 错误信息
        if 'errors' in result and result['errors']:
            report_lines.extend([
                "错误信息:",
                *[f"  • {error}" for error in result['errors']],
                ""
            ])
        
        # 问题详情
        if 'issues' in result and result['issues']:
            report_lines.extend([
                "发现的问题:",
                ""
            ])
            
            # 按严重程度分组
            severity_order = ['critical', 'high', 'medium', 'low', 'info']
            issues_by_severity = {}
            
            for issue in result['issues']:
                severity = issue['severity']
                if severity not in issues_by_severity:
                    issues_by_severity[severity] = []
                issues_by_severity[severity].append(issue)
            
            for severity in severity_order:
                if severity in issues_by_severity:
                    issues = issues_by_severity[severity]
                    severity_icon = {
                        'critical': '🔴',
                        'high': '🟠', 
                        'medium': '🟡',
                        'low': '🔵',
                        'info': '⚪'
                    }.get(severity, '⚪')
                    
                    report_lines.extend([
                        f"{severity_icon} {severity.upper()} ({len(issues)}个):",
                        ""
                    ])
                    
                    for i, issue in enumerate(issues, 1):
                        report_lines.extend([
                            f"{i}. {issue['description']}",
                            f"   位置: {issue['location']}",
                            f"   类型: {issue['type']}"
                        ])
                        
                        if issue.get('recommendation'):
                            report_lines.append(f"   建议: {issue['recommendation']}")
                        
                        report_lines.append("")
        
        # 总结
        if result['success']:
            validation = result.get('validation', {})
            score = validation.get('score', 0)
            
            if score >= 90:
                grade = "优秀"
                advice = "配置质量很高，可以直接使用"
            elif score >= 80:
                grade = "良好"
                advice = "配置基本符合要求，建议修复发现的问题"
            elif score >= 70:
                grade = "合格"
                advice = "配置存在一些问题，需要进行改进"
            else:
                grade = "需要改进"
                advice = "配置存在较多问题，建议重新检查"
            
            report_lines.extend([
                "总结:",
                f"  质量等级: {grade}",
                f"  建议: {advice}",
                ""
            ])
        
        report_lines.extend([
            "=" * 50,
            "报告生成完成"
        ])
        
        report_content = "\n".join(report_lines)
        
        # 保存报告
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                self.logger.info(f"报告已保存到: {output_file}")
            except Exception as e:
                self.logger.error(f"保存报告失败: {e}")
        
        return report_content
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        'log_level': 'INFO',
        'parser': {
            'strict_mode': True,
            'max_file_size': 50 * 1024 * 1024,  # 50MB
            'validate_schema': True
        },
        'validator': {
            'max_validation_time': 30,
            'max_issues': 1000
        }
    }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="安全的IEC61850设计检查器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python secure_main.py test.scd                    # 检查文件
  python secure_main.py test.scd -o report.txt     # 检查并生成报告
  python secure_main.py test.scd --json result.json # 输出JSON格式结果
        """
    )
    
    parser.add_argument('file', help='要检查的SCD文件路径')
    parser.add_argument('-o', '--output', help='报告输出文件路径')
    parser.add_argument('--json', help='JSON格式结果输出文件路径')
    parser.add_argument('-c', '--config', help='配置文件路径')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--version', action='version', version='IEC61850检查器 v2.0.0')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = create_default_config()
        
        if args.config and Path(args.config).exists():
            try:
                with open(args.config, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    config.update(user_config)
            except Exception as e:
                print(f"⚠️ 加载配置文件失败: {e}")
        
        if args.verbose:
            config['log_level'] = 'DEBUG'
        
        # 创建检查器
        checker = SecureIEC61850Checker(config)
        
        # 检查文件
        print(f"🔍 正在检查文件: {args.file}")
        result = checker.check_file(args.file)
        
        # 输出结果
        if result['success']:
            validation = result.get('validation', {})
            print(f"✅ 检查完成")
            print(f"   得分: {validation.get('score', 0):.1f}/100")
            print(f"   问题: {validation.get('issues_count', 0)} 个")
        else:
            print(f"❌ 检查失败")
            if 'errors' in result:
                for error in result['errors']:
                    print(f"   错误: {error}")
        
        # 生成报告
        if args.output:
            report = checker.generate_report(result, args.output)
            print(f"📄 报告已保存到: {args.output}")
        
        # 输出JSON结果
        if args.json:
            try:
                with open(args.json, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"📊 JSON结果已保存到: {args.json}")
            except Exception as e:
                print(f"⚠️ 保存JSON结果失败: {e}")
        
        # 如果没有指定输出文件，显示简要报告
        if not args.output and not args.json:
            if result['success'] and result.get('issues'):
                print(f"\n📋 发现的主要问题:")
                for issue in result['issues'][:5]:  # 显示前5个问题
                    severity_icon = {
                        'critical': '🔴',
                        'high': '🟠',
                        'medium': '🟡',
                        'low': '🔵',
                        'info': '⚪'
                    }.get(issue['severity'], '⚪')
                    print(f"   {severity_icon} {issue['description']}")
        
        # 设置退出码
        if result['success']:
            validation = result.get('validation', {})
            if validation.get('score', 0) < 70:
                sys.exit(2)  # 质量不达标
            else:
                sys.exit(0)  # 成功
        else:
            sys.exit(1)  # 失败
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(130)
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()