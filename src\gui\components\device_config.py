"""
设备配置组件
显示IED设备的详细配置信息
"""

import logging
from typing import Dict, Any, List, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QTableWidget, QTableWidgetItem, QTextEdit, QSplitter, QGroupBox,
    QLabel, QPushButton, QComboBox, QHeaderView, QTabWidget,
    QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QFont

logger = logging.getLogger(__name__)


class DeviceConfigWidget(QWidget):
    """设备配置组件"""
    
    # 信号
    device_selected = Signal(dict)  # 设备选择信号
    
    def __init__(self, parent=None):
        """初始化设备配置组件"""
        super().__init__(parent)
        
        self.file_info = None
        self.current_device = None
        
        self._init_ui()
        
        logger.debug("设备配置组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 设备选择区域
        device_select_group = QGroupBox("设备选择")
        device_select_layout = QHBoxLayout(device_select_group)
        
        device_label = QLabel("设备:")
        device_select_layout.addWidget(device_label)
        
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self._on_device_changed)
        device_select_layout.addWidget(self.device_combo)
        
        device_select_layout.addStretch()
        
        layout.addWidget(device_select_group)
        
        # 主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧：设备树结构
        left_panel = self._create_device_tree_panel()
        main_splitter.addWidget(left_panel)
        
        # 右侧：详细信息标签页
        right_panel = self._create_details_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([300, 500])
    
    def _create_device_tree_panel(self) -> QWidget:
        """创建设备树面板"""
        panel = QGroupBox("设备结构")
        layout = QVBoxLayout(panel)
        
        self.device_tree = QTreeWidget()
        self.device_tree.setHeaderLabel("设备组件")
        self.device_tree.itemClicked.connect(self._on_tree_item_clicked)
        layout.addWidget(self.device_tree)
        
        return panel
    
    def _create_details_panel(self) -> QWidget:
        """创建详细信息面板"""
        self.details_tabs = QTabWidget()
        
        # 基本信息标签页
        self.basic_info_tab = self._create_basic_info_tab()
        self.details_tabs.addTab(self.basic_info_tab, "基本信息")
        
        # 逻辑节点标签页
        self.logical_nodes_tab = self._create_logical_nodes_tab()
        self.details_tabs.addTab(self.logical_nodes_tab, "逻辑节点")
        
        # 数据对象标签页
        self.data_objects_tab = self._create_data_objects_tab()
        self.details_tabs.addTab(self.data_objects_tab, "数据对象")
        
        # 通信配置标签页
        self.communication_tab = self._create_communication_tab()
        self.details_tabs.addTab(self.communication_tab, "通信配置")
        
        return self.details_tabs
    
    def _create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本属性表格
        self.basic_info_table = QTableWidget(0, 2)
        self.basic_info_table.setHorizontalHeaderLabels(["属性", "值"])
        self.basic_info_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.basic_info_table)
        
        # 描述文本
        desc_group = QGroupBox("描述")
        desc_layout = QVBoxLayout(desc_group)
        
        self.description_text = QTextEdit()
        self.description_text.setReadOnly(True)
        self.description_text.setMaximumHeight(100)
        desc_layout.addWidget(self.description_text)
        
        layout.addWidget(desc_group)
        
        return widget
    
    def _create_logical_nodes_tab(self) -> QWidget:
        """创建逻辑节点标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 逻辑节点表格
        self.logical_nodes_table = QTableWidget(0, 4)
        self.logical_nodes_table.setHorizontalHeaderLabels([
            "名称", "类型", "前缀", "实例"
        ])
        self.logical_nodes_table.horizontalHeader().setStretchLastSection(True)
        self.logical_nodes_table.itemSelectionChanged.connect(self._on_ln_selected)
        layout.addWidget(self.logical_nodes_table)
        
        return widget
    
    def _create_data_objects_tab(self) -> QWidget:
        """创建数据对象标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据对象表格
        self.data_objects_table = QTableWidget(0, 5)
        self.data_objects_table.setHorizontalHeaderLabels([
            "名称", "类型", "功能约束", "描述", "值"
        ])
        self.data_objects_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.data_objects_table)
        
        return widget
    
    def _create_communication_tab(self) -> QWidget:
        """创建通信配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 通信参数表格
        self.communication_table = QTableWidget(0, 2)
        self.communication_table.setHorizontalHeaderLabels(["参数", "值"])
        self.communication_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.communication_table)
        
        return widget
    
    def show_file_info(self, file_info: Dict[str, Any]):
        """显示文件信息"""
        try:
            self.file_info = file_info
            
            # 更新设备列表
            self._update_device_list()
            
            # 如果有设备，选择第一个
            if self.device_combo.count() > 0:
                self.device_combo.setCurrentIndex(0)
            
            logger.debug("设备配置信息更新完成")
            
        except Exception as e:
            logger.error(f"显示设备配置信息失败: {e}")
    
    def _update_device_list(self):
        """更新设备列表"""
        self.device_combo.clear()
        
        if not self.file_info:
            return
        
        parsed_data = self.file_info.get('parsed_data')
        if not parsed_data or not hasattr(parsed_data, 'ieds'):
            return
        
        # 添加IED设备到下拉列表
        for ied in parsed_data.ieds:
            ied_name = getattr(ied, 'name', 'Unknown IED')
            self.device_combo.addItem(ied_name, ied)
    
    def _on_device_changed(self, device_name: str):
        """设备改变事件"""
        if not device_name:
            return
        
        # 获取选中的设备数据
        current_index = self.device_combo.currentIndex()
        if current_index >= 0:
            device_data = self.device_combo.itemData(current_index)
            self.current_device = device_data
            
            # 更新显示
            self._update_device_display()
    
    def _update_device_display(self):
        """更新设备显示"""
        if not self.current_device:
            return
        
        # 更新设备树
        self._update_device_tree()
        
        # 更新基本信息
        self._update_basic_info()
        
        # 更新逻辑节点
        self._update_logical_nodes()
        
        # 更新通信配置
        self._update_communication_config()
    
    def _update_device_tree(self):
        """更新设备树"""
        self.device_tree.clear()
        
        if not self.current_device:
            return
        
        # 创建根节点
        device_name = getattr(self.current_device, 'name', 'Unknown Device')
        root = QTreeWidgetItem(self.device_tree)
        root.setText(0, device_name)
        root.setIcon(0, QIcon("resources/icons/device.png"))
        root.setData(0, Qt.UserRole, self.current_device)
        
        # 添加逻辑设备
        if hasattr(self.current_device, 'logical_devices'):
            for ld in self.current_device.logical_devices:
                ld_item = QTreeWidgetItem(root)
                ld_name = getattr(ld, 'name', 'Unknown LD')
                ld_item.setText(0, f"LD: {ld_name}")
                ld_item.setIcon(0, QIcon("resources/icons/logical_device.png"))
                ld_item.setData(0, Qt.UserRole, ld)
                
                # 添加逻辑节点
                if hasattr(ld, 'logical_nodes'):
                    for ln in ld.logical_nodes:
                        ln_item = QTreeWidgetItem(ld_item)
                        ln_name = getattr(ln, 'name', 'Unknown LN')
                        ln_type = getattr(ln, 'type', '')
                        ln_item.setText(0, f"LN: {ln_name} ({ln_type})")
                        ln_item.setIcon(0, QIcon("resources/icons/logical_node.png"))
                        ln_item.setData(0, Qt.UserRole, ln)
                        
                        # 添加数据对象
                        if hasattr(ln, 'data_objects'):
                            for do in ln.data_objects:
                                do_item = QTreeWidgetItem(ln_item)
                                do_name = getattr(do, 'name', 'Unknown DO')
                                do_item.setText(0, f"DO: {do_name}")
                                do_item.setIcon(0, QIcon("resources/icons/data_object.png"))
                                do_item.setData(0, Qt.UserRole, do)
        
        # 展开根节点
        root.setExpanded(True)
    
    def _update_basic_info(self):
        """更新基本信息"""
        self.basic_info_table.setRowCount(0)
        
        if not self.current_device:
            return
        
        # 收集基本信息
        info_items = [
            ("名称", getattr(self.current_device, 'name', '')),
            ("类型", getattr(self.current_device, 'type', '')),
            ("制造商", getattr(self.current_device, 'manufacturer', '')),
            ("配置版本", getattr(self.current_device, 'configVersion', '')),
            ("原始配置版本", getattr(self.current_device, 'originalSclVersion', '')),
            ("原始配置修订", getattr(self.current_device, 'originalSclRevision', '')),
            ("工程ID", getattr(self.current_device, 'engRight', '')),
            ("所有者", getattr(self.current_device, 'owner', ''))
        ]
        
        # 填充表格
        for i, (key, value) in enumerate(info_items):
            if value:  # 只显示有值的项
                self.basic_info_table.insertRow(i)
                self.basic_info_table.setItem(i, 0, QTableWidgetItem(key))
                self.basic_info_table.setItem(i, 1, QTableWidgetItem(str(value)))
        
        # 更新描述
        description = getattr(self.current_device, 'desc', '')
        self.description_text.setPlainText(description)
    
    def _update_logical_nodes(self):
        """更新逻辑节点表格"""
        self.logical_nodes_table.setRowCount(0)
        
        if not self.current_device:
            return
        
        # 收集所有逻辑节点
        logical_nodes = []
        if hasattr(self.current_device, 'logical_devices'):
            for ld in self.current_device.logical_devices:
                if hasattr(ld, 'logical_nodes'):
                    logical_nodes.extend(ld.logical_nodes)
        
        # 填充表格
        for i, ln in enumerate(logical_nodes):
            self.logical_nodes_table.insertRow(i)
            
            name = getattr(ln, 'name', '')
            ln_type = getattr(ln, 'type', '')
            prefix = getattr(ln, 'prefix', '')
            inst = getattr(ln, 'inst', '')
            
            self.logical_nodes_table.setItem(i, 0, QTableWidgetItem(name))
            self.logical_nodes_table.setItem(i, 1, QTableWidgetItem(ln_type))
            self.logical_nodes_table.setItem(i, 2, QTableWidgetItem(prefix))
            self.logical_nodes_table.setItem(i, 3, QTableWidgetItem(str(inst)))
            
            # 存储逻辑节点数据
            name_item = self.logical_nodes_table.item(i, 0)
            name_item.setData(Qt.UserRole, ln)
    
    def _update_communication_config(self):
        """更新通信配置"""
        self.communication_table.setRowCount(0)
        
        if not self.current_device:
            return
        
        # 收集通信相关信息
        comm_info = []
        
        # 从设备中提取通信参数
        if hasattr(self.current_device, 'communication'):
            comm = self.current_device.communication
            # 这里可以根据实际的通信配置结构来提取信息
            comm_info.append(("通信类型", getattr(comm, 'type', '')))
        
        # 从文件的通信部分查找相关配置
        if self.file_info:
            parsed_data = self.file_info.get('parsed_data')
            if parsed_data and hasattr(parsed_data, 'communication'):
                # 查找与当前设备相关的通信配置
                device_name = getattr(self.current_device, 'name', '')
                # 这里需要根据实际的SCD结构来查找设备的通信配置
                comm_info.append(("设备名称", device_name))
        
        # 填充表格
        for i, (key, value) in enumerate(comm_info):
            if value:
                self.communication_table.insertRow(i)
                self.communication_table.setItem(i, 0, QTableWidgetItem(key))
                self.communication_table.setItem(i, 1, QTableWidgetItem(str(value)))
    
    def _on_tree_item_clicked(self, item: QTreeWidgetItem, column: int):
        """树节点点击事件"""
        data = item.data(0, Qt.UserRole)
        if data:
            # 根据数据类型更新相应的显示
            if hasattr(data, 'logical_nodes'):  # 逻辑设备
                self._show_logical_device_details(data)
            elif hasattr(data, 'data_objects'):  # 逻辑节点
                self._show_logical_node_details(data)
            elif hasattr(data, 'data_attributes'):  # 数据对象
                self._show_data_object_details(data)
    
    def _show_logical_device_details(self, ld_data):
        """显示逻辑设备详情"""
        # 切换到逻辑节点标签页并更新内容
        self.details_tabs.setCurrentIndex(1)
        # 这里可以过滤显示特定逻辑设备的逻辑节点
    
    def _show_logical_node_details(self, ln_data):
        """显示逻辑节点详情"""
        # 切换到数据对象标签页并显示该逻辑节点的数据对象
        self.details_tabs.setCurrentIndex(2)
        self._update_data_objects_for_ln(ln_data)
    
    def _show_data_object_details(self, do_data):
        """显示数据对象详情"""
        # 在数据对象标签页中高亮显示该数据对象
        self.details_tabs.setCurrentIndex(2)
    
    def _update_data_objects_for_ln(self, ln_data):
        """更新特定逻辑节点的数据对象"""
        self.data_objects_table.setRowCount(0)
        
        if not hasattr(ln_data, 'data_objects'):
            return
        
        # 填充数据对象表格
        for i, do in enumerate(ln_data.data_objects):
            self.data_objects_table.insertRow(i)
            
            name = getattr(do, 'name', '')
            do_type = getattr(do, 'type', '')
            fc = getattr(do, 'fc', '')
            desc = getattr(do, 'desc', '')
            value = getattr(do, 'value', '')
            
            self.data_objects_table.setItem(i, 0, QTableWidgetItem(name))
            self.data_objects_table.setItem(i, 1, QTableWidgetItem(do_type))
            self.data_objects_table.setItem(i, 2, QTableWidgetItem(fc))
            self.data_objects_table.setItem(i, 3, QTableWidgetItem(desc))
            self.data_objects_table.setItem(i, 4, QTableWidgetItem(str(value)))
    
    def _on_ln_selected(self):
        """逻辑节点选择事件"""
        current_row = self.logical_nodes_table.currentRow()
        if current_row >= 0:
            name_item = self.logical_nodes_table.item(current_row, 0)
            ln_data = name_item.data(Qt.UserRole)
            
            if ln_data:
                self._update_data_objects_for_ln(ln_data)
