"""
IEC61850智能电子设备(IED)数据模型
定义IED、访问点、服务器、逻辑设备等实体
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum

from .base import (
    BaseModel, NamedModel, ValidationError,
    validate_required, validate_range, validate_enum,
    validate_list_not_empty, validate_unique_names
)


class IEDType(Enum):
    """IED类型枚举"""
    PROTECTION = "Protection"      # 保护装置
    CONTROL = "Control"           # 控制装置
    MEASUREMENT = "Measurement"   # 测量装置
    MONITORING = "Monitoring"     # 监控装置
    RECORDING = "Recording"       # 录波装置
    GATEWAY = "Gateway"           # 网关
    HMI = "HMI"                  # 人机界面
    OTHER = "Other"              # 其他


class ProtocolType(Enum):
    """通信协议类型"""
    MMS = "MMS"                  # Manufacturing Message Specification
    GOOSE = "GOOSE"             # Generic Object Oriented Substation Event
    SV = "SV"                   # Sampled Values
    HTTP = "HTTP"               # Hypertext Transfer Protocol
    FTP = "FTP"                 # File Transfer Protocol
    SNTP = "SNTP"               # Simple Network Time Protocol


@dataclass
class Services(BaseModel):
    """IED服务配置"""
    
    # 基本服务
    dyn_association: bool = False        # 动态关联
    setting_groups: bool = False         # 定值组
    get_directory: bool = True           # 获取目录
    get_data_object_definition: bool = True  # 获取数据对象定义
    data_object_directory: bool = True   # 数据对象目录
    get_data_set_value: bool = True     # 获取数据集值
    set_data_set_value: bool = False    # 设置数据集值
    data_set_directory: bool = True     # 数据集目录
    conf_data_set: bool = False         # 配置数据集
    dyn_data_set: bool = False          # 动态数据集
    read_write: bool = True             # 读写
    timer_activated_control: bool = False # 定时激活控制
    conf_report_control: bool = False   # 配置报告控制
    get_cb_values: bool = True          # 获取控制块值
    conf_log_control: bool = False      # 配置日志控制
    report_settings: bool = False       # 报告设置
    log_settings: bool = False          # 日志设置
    gse_settings: bool = False          # GSE设置
    smv_settings: bool = False          # SMV设置
    gse_dir: bool = False               # GSE目录
    goose: bool = False                 # GOOSE
    gsse: bool = False                  # GSSE
    sv: bool = False                    # 采样值
    sup_subscription: bool = False      # 支持订阅
    conf_sig_ref: bool = False          # 配置信号引用
    
    def validate(self) -> None:
        """验证服务配置"""
        # 基本验证，确保关键服务的依赖关系
        if self.conf_data_set and not self.data_set_directory:
            raise ValidationError(
                "配置数据集服务需要数据集目录服务支持",
                "conf_data_set"
            )
        
        if self.conf_report_control and not self.get_cb_values:
            raise ValidationError(
                "配置报告控制服务需要获取控制块值服务支持",
                "conf_report_control"
            )


@dataclass
class AccessPoint(NamedModel):
    """访问点"""
    
    # IEC61850标准属性
    router: bool = False             # 是否为路由器
    clock: bool = False              # 是否有时钟
    
    # 网络配置
    server: Optional['Server'] = None
    services: Services = field(default_factory=Services)
    
    def validate(self) -> None:
        """验证访问点"""
        super().validate()
        
        # 验证服务配置
        if self.services:
            self.services.validate()


@dataclass
class Authentication(BaseModel):
    """认证配置"""
    
    none: bool = True               # 无认证
    password: bool = False          # 密码认证
    weak: bool = False              # 弱认证
    strong: bool = False            # 强认证
    certificate: bool = False       # 证书认证
    
    def validate(self) -> None:
        """验证认证配置"""
        # 至少要有一种认证方式
        auth_methods = [self.none, self.password, self.weak, self.strong, self.certificate]
        if not any(auth_methods):
            raise ValidationError("至少需要配置一种认证方式")


@dataclass
class LDevice(NamedModel):
    """逻辑设备"""
    
    # IEC61850标准属性
    inst: str = ""                  # 实例名
    ldname: Optional[str] = None    # 逻辑设备名称

    # 逻辑节点
    logical_nodes: List['LogicalNode'] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证逻辑设备"""
        super().validate()
        validate_required(self.inst, "inst")
        
        # 验证实例名格式
        if not self._is_valid_iec_name(self.inst):
            raise ValidationError(
                "实例名必须符合IEC61850命名规范",
                "inst",
                self.inst
            )


@dataclass
class Server(BaseModel):
    """服务器"""
    
    # IEC61850标准属性
    timeout: int = 30               # 超时时间(秒)
    
    # 认证和安全
    authentication: Authentication = field(default_factory=Authentication)
    
    # 逻辑设备
    ldevices: List[LDevice] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证服务器"""
        validate_range(self.timeout, 1, 3600, "timeout")
        
        # 验证认证配置
        if self.authentication:
            self.authentication.validate()
        
        # 验证逻辑设备名称唯一性
        if self.ldevices:
            validate_unique_names(self.ldevices, "ldevices")
    
    def add_ldevice(self, ldevice: LDevice) -> None:
        """添加逻辑设备"""
        # 检查名称唯一性
        existing_names = [ld.name for ld in self.ldevices]
        if ldevice.name in existing_names:
            raise ValidationError(
                f"逻辑设备名称 '{ldevice.name}' 已存在",
                "ldevices"
            )
        
        self.ldevices.append(ldevice)
    
    def remove_ldevice(self, ldevice_name: str) -> bool:
        """移除逻辑设备"""
        for i, ld in enumerate(self.ldevices):
            if ld.name == ldevice_name:
                del self.ldevices[i]
                return True
        return False
    
    def get_ldevice(self, ldevice_name: str) -> Optional[LDevice]:
        """获取指定逻辑设备"""
        for ld in self.ldevices:
            if ld.name == ldevice_name:
                return ld
        return None


@dataclass
class IED(NamedModel):
    """智能电子设备"""
    
    # IEC61850标准属性
    type: str = IEDType.OTHER.value     # IED类型
    manufacturer: str = ""               # 制造商
    config_version: str = "1.0"         # 配置版本
    original_scd_version: str = "1.0"   # 原始SCD版本
    original_scd_revision: str = "A"    # 原始SCD修订版
    eng_right: str = "full"             # 工程权限
    owner: str = ""                     # 所有者
    
    # 访问点
    access_points: List[AccessPoint] = field(default_factory=list)

    # 逻辑设备
    logical_devices: List['LDevice'] = field(default_factory=list)

    # 扩展属性
    model: Optional[str] = None         # 型号
    serial_number: Optional[str] = None # 序列号
    firmware_version: Optional[str] = None  # 固件版本
    hardware_version: Optional[str] = None  # 硬件版本
    
    def validate(self) -> None:
        """验证IED"""
        super().validate()
        
        # 验证必填字段
        validate_required(self.manufacturer, "manufacturer")
        validate_required(self.config_version, "config_version")
        
        # 验证IED类型
        try:
            IEDType(self.type)
        except ValueError:
            # 允许自定义类型，但需要符合命名规范
            if not self._is_valid_iec_name(self.type):
                raise ValidationError(
                    "IED类型必须符合IEC61850命名规范",
                    "type",
                    self.type
                )
        
        # 验证工程权限
        valid_rights = ["full", "fix", "dataflow"]
        if self.eng_right not in valid_rights:
            raise ValidationError(
                f"工程权限必须是以下之一: {valid_rights}",
                "eng_right",
                self.eng_right
            )
        
        # 验证访问点名称唯一性
        if self.access_points:
            validate_unique_names(self.access_points, "access_points")
    
    def add_access_point(self, access_point: AccessPoint) -> None:
        """添加访问点"""
        # 检查名称唯一性
        existing_names = [ap.name for ap in self.access_points]
        if access_point.name in existing_names:
            raise ValidationError(
                f"访问点名称 '{access_point.name}' 已存在",
                "access_points"
            )
        
        self.access_points.append(access_point)
        self.update_timestamp()
    
    def remove_access_point(self, access_point_name: str) -> bool:
        """移除访问点"""
        for i, ap in enumerate(self.access_points):
            if ap.name == access_point_name:
                del self.access_points[i]
                self.update_timestamp()
                return True
        return False
    
    def get_access_point(self, access_point_name: str) -> Optional[AccessPoint]:
        """获取指定访问点"""
        for ap in self.access_points:
            if ap.name == access_point_name:
                return ap
        return None
    
    def get_all_ldevices(self) -> List[LDevice]:
        """获取所有逻辑设备"""
        ldevices = []
        for ap in self.access_points:
            if ap.server:
                ldevices.extend(ap.server.ldevices)
        return ldevices
    
    def get_ldevice_by_path(self, path: str) -> Optional[LDevice]:
        """根据路径获取逻辑设备
        
        Args:
            path: 逻辑设备路径，格式为 "access_point/ldevice"
        
        Returns:
            逻辑设备对象或None
        """
        parts = path.split('/')
        if len(parts) != 2:
            return None
        
        ap_name, ld_name = parts
        
        ap = self.get_access_point(ap_name)
        if not ap or not ap.server:
            return None
        
        return ap.server.get_ldevice(ld_name)
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取IED能力信息"""
        capabilities = {
            "type": self.type,
            "manufacturer": self.manufacturer,
            "model": self.model,
            "access_points_count": len(self.access_points),
            "ldevices_count": len(self.get_all_ldevices()),
            "supported_protocols": [],
            "supported_services": {}
        }
        
        # 收集支持的协议和服务
        for ap in self.access_points:
            if ap.services:
                services_dict = ap.services.to_dict()
                for service, supported in services_dict.items():
                    if supported and service not in ["id", "created_at", "updated_at", "desc"]:
                        if service not in capabilities["supported_services"]:
                            capabilities["supported_services"][service] = 0
                        capabilities["supported_services"][service] += 1
        
        return capabilities
    
    def is_compatible_with(self, other: 'IED') -> bool:
        """检查与另一个IED的兼容性"""
        # 基本兼容性检查
        if self.manufacturer != other.manufacturer:
            return False
        
        # 检查是否有共同的通信协议
        self_protocols = set()
        other_protocols = set()
        
        for ap in self.access_points:
            if ap.services:
                if ap.services.goose:
                    self_protocols.add("GOOSE")
                if ap.services.sv:
                    self_protocols.add("SV")
                # 添加其他协议检查
        
        for ap in other.access_points:
            if ap.services:
                if ap.services.goose:
                    other_protocols.add("GOOSE")
                if ap.services.sv:
                    other_protocols.add("SV")
                # 添加其他协议检查
        
        return bool(self_protocols & other_protocols)
