"""
知识库基类
提供知识管理的核心功能
"""

from typing import Dict, List, Optional, Any, Union, Type
from abc import ABC, abstractmethod
import logging
from datetime import datetime

from .knowledge_entity import (
    KnowledgeEntity, EntityType, EntityRelationship,
    StandardEntity, RuleEntity, DeviceEntity, ProtocolEntity, RequirementEntity
)
from .storage_engine import StorageEngine


logger = logging.getLogger(__name__)


class KnowledgeBase:
    """知识库主类"""
    
    def __init__(self, storage_engine: StorageEngine):
        """
        初始化知识库
        
        Args:
            storage_engine: 存储引擎实例
        """
        self.storage = storage_engine
        self._entity_types = {
            EntityType.STANDARD: StandardEntity,
            EntityType.RULE: RuleEntity,
            EntityType.DEVICE: DeviceEntity,
            EntityType.PROTOCOL: ProtocolEntity,
            EntityType.REQUIREMENT: RequirementEntity,
        }
        
        # 统计信息
        self._stats = {
            'total_entities': 0,
            'total_relationships': 0,
            'last_updated': datetime.now()
        }
        
        logger.info("知识库初始化完成")
    
    def add_entity(self, entity: KnowledgeEntity) -> bool:
        """
        添加知识实体
        
        Args:
            entity: 知识实体
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 验证实体
            if not self._validate_entity(entity):
                return False
            
            # 存储实体
            success = self.storage.store_entity(entity)
            if success:
                self._stats['total_entities'] += 1
                self._stats['last_updated'] = datetime.now()
                logger.info(f"成功添加实体: {entity.name} ({entity.entity_type})")
            
            return success
            
        except Exception as e:
            logger.error(f"添加实体失败: {e}")
            return False
    
    def get_entity(self, entity_id: str) -> Optional[KnowledgeEntity]:
        """
        获取知识实体
        
        Args:
            entity_id: 实体ID
            
        Returns:
            KnowledgeEntity: 知识实体，如果不存在返回None
        """
        try:
            return self.storage.get_entity(entity_id)
        except Exception as e:
            logger.error(f"获取实体失败: {e}")
            return None
    
    def update_entity(self, entity: KnowledgeEntity) -> bool:
        """
        更新知识实体
        
        Args:
            entity: 更新后的知识实体
            
        Returns:
            bool: 是否更新成功
        """
        try:
            entity.updated_at = datetime.now()
            success = self.storage.update_entity(entity)
            if success:
                self._stats['last_updated'] = datetime.now()
                logger.info(f"成功更新实体: {entity.name}")
            return success
            
        except Exception as e:
            logger.error(f"更新实体失败: {e}")
            return False
    
    def delete_entity(self, entity_id: str) -> bool:
        """
        删除知识实体
        
        Args:
            entity_id: 实体ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            success = self.storage.delete_entity(entity_id)
            if success:
                self._stats['total_entities'] -= 1
                self._stats['last_updated'] = datetime.now()
                logger.info(f"成功删除实体: {entity_id}")
            return success
            
        except Exception as e:
            logger.error(f"删除实体失败: {e}")
            return False
    
    def search_entities(self, 
                       query: str = None,
                       entity_type: EntityType = None,
                       tags: List[str] = None,
                       limit: int = 100) -> List[KnowledgeEntity]:
        """
        搜索知识实体
        
        Args:
            query: 搜索查询
            entity_type: 实体类型过滤
            tags: 标签过滤
            limit: 结果数量限制
            
        Returns:
            List[KnowledgeEntity]: 搜索结果
        """
        try:
            return self.storage.search_entities(
                query=query,
                entity_type=entity_type,
                tags=tags,
                limit=limit
            )
        except Exception as e:
            logger.error(f"搜索实体失败: {e}")
            return []
    
    def add_relationship(self, relationship: EntityRelationship) -> bool:
        """
        添加实体关系
        
        Args:
            relationship: 实体关系
            
        Returns:
            bool: 是否添加成功
        """
        try:
            success = self.storage.store_relationship(relationship)
            if success:
                self._stats['total_relationships'] += 1
                self._stats['last_updated'] = datetime.now()
                logger.info(f"成功添加关系: {relationship.relationship_type}")
            return success
            
        except Exception as e:
            logger.error(f"添加关系失败: {e}")
            return False
    
    def get_related_entities(self, 
                           entity_id: str,
                           relationship_type: str = None,
                           depth: int = 1) -> List[KnowledgeEntity]:
        """
        获取相关实体
        
        Args:
            entity_id: 实体ID
            relationship_type: 关系类型过滤
            depth: 搜索深度
            
        Returns:
            List[KnowledgeEntity]: 相关实体列表
        """
        try:
            return self.storage.get_related_entities(
                entity_id=entity_id,
                relationship_type=relationship_type,
                depth=depth
            )
        except Exception as e:
            logger.error(f"获取相关实体失败: {e}")
            return []
    
    def get_standards_by_domain(self, domain: str) -> List[StandardEntity]:
        """
        根据领域获取标准
        
        Args:
            domain: 应用领域
            
        Returns:
            List[StandardEntity]: 标准列表
        """
        entities = self.search_entities(entity_type=EntityType.STANDARD)
        return [e for e in entities if isinstance(e, StandardEntity) and e.domain == domain]
    
    def get_rules_by_standard(self, standard_id: str) -> List[RuleEntity]:
        """
        根据标准获取相关规则
        
        Args:
            standard_id: 标准ID
            
        Returns:
            List[RuleEntity]: 规则列表
        """
        related = self.get_related_entities(standard_id, "derived_from")
        return [e for e in related if isinstance(e, RuleEntity)]
    
    def get_applicable_rules(self, 
                           device_type: str = None,
                           protocol: str = None,
                           standard: str = None) -> List[RuleEntity]:
        """
        获取适用的验证规则
        
        Args:
            device_type: 设备类型
            protocol: 通信协议
            standard: 标准编号
            
        Returns:
            List[RuleEntity]: 适用的规则列表
        """
        rules = self.search_entities(entity_type=EntityType.RULE)
        applicable_rules = []
        
        for rule in rules:
            if not isinstance(rule, RuleEntity):
                continue
                
            # 检查设备类型匹配
            if device_type and rule.applicable_devices:
                if device_type not in rule.applicable_devices:
                    continue
            
            # 检查协议匹配
            if protocol and rule.applicable_protocols:
                if protocol not in rule.applicable_protocols:
                    continue
            
            # 检查标准匹配
            if standard and rule.applicable_standards:
                if standard not in rule.applicable_standards:
                    continue
            
            applicable_rules.append(rule)
        
        return applicable_rules
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        # 更新统计信息
        entity_counts = {}
        for entity_type in EntityType:
            count = len(self.search_entities(entity_type=entity_type))
            entity_counts[entity_type.value] = count
        
        self._stats.update({
            'entity_counts': entity_counts,
            'total_entities': sum(entity_counts.values())
        })
        
        return self._stats.copy()
    
    def _validate_entity(self, entity: KnowledgeEntity) -> bool:
        """
        验证实体有效性
        
        Args:
            entity: 知识实体
            
        Returns:
            bool: 是否有效
        """
        if not entity.name or not entity.description:
            logger.error("实体名称和描述不能为空")
            return False
        
        if entity.confidence < 0 or entity.confidence > 1:
            logger.error("置信度必须在0-1之间")
            return False
        
        return True
    
    def export_knowledge(self, format: str = "json") -> str:
        """
        导出知识库
        
        Args:
            format: 导出格式 (json, xml, rdf)
            
        Returns:
            str: 导出的数据
        """
        try:
            return self.storage.export_data(format)
        except Exception as e:
            logger.error(f"导出知识库失败: {e}")
            return ""
    
    def import_knowledge(self, data: str, format: str = "json") -> bool:
        """
        导入知识库
        
        Args:
            data: 导入的数据
            format: 数据格式
            
        Returns:
            bool: 是否导入成功
        """
        try:
            return self.storage.import_data(data, format)
        except Exception as e:
            logger.error(f"导入知识库失败: {e}")
            return False
