"""
知识图谱查询引擎
提供复杂的图查询功能
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from collections import defaultdict

from .knowledge_graph import KnowledgeGraph
from ..base.knowledge_entity import (
    KnowledgeEntity, EntityType, RelationshipType
)


logger = logging.getLogger(__name__)


class GraphQueryEngine:
    """知识图谱查询引擎"""
    
    def __init__(self, knowledge_graph: KnowledgeGraph):
        """
        初始化查询引擎
        
        Args:
            knowledge_graph: 知识图谱实例
        """
        self.graph = knowledge_graph
        logger.info("知识图谱查询引擎初始化完成")
    
    def find_applicable_rules(self, 
                            device_type: str = None,
                            protocol: str = None,
                            standard: str = None) -> List[KnowledgeEntity]:
        """
        查找适用的验证规则
        
        Args:
            device_type: 设备类型
            protocol: 通信协议
            standard: 标准编号
            
        Returns:
            List[KnowledgeEntity]: 适用的规则列表
        """
        applicable_rules = []
        
        try:
            # 获取所有规则
            rules = self.graph.get_entities_by_type(EntityType.RULE)
            
            for rule in rules:
                is_applicable = True
                
                # 检查设备类型
                if device_type:
                    if hasattr(rule, 'applicable_devices') and rule.applicable_devices:
                        if device_type not in rule.applicable_devices:
                            is_applicable = False
                
                # 检查协议
                if protocol and is_applicable:
                    if hasattr(rule, 'applicable_protocols') and rule.applicable_protocols:
                        if protocol not in rule.applicable_protocols:
                            is_applicable = False
                
                # 检查标准
                if standard and is_applicable:
                    if hasattr(rule, 'applicable_standards') and rule.applicable_standards:
                        if standard not in rule.applicable_standards:
                            is_applicable = False
                
                if is_applicable:
                    applicable_rules.append(rule)
            
            logger.info(f"找到 {len(applicable_rules)} 条适用规则")
            return applicable_rules
            
        except Exception as e:
            logger.error(f"查找适用规则失败: {e}")
            return []
    
    def find_related_standards(self, entity_id: str, max_depth: int = 2) -> List[KnowledgeEntity]:
        """
        查找相关标准
        
        Args:
            entity_id: 实体ID
            max_depth: 最大搜索深度
            
        Returns:
            List[KnowledgeEntity]: 相关标准列表
        """
        try:
            related_entities = self.graph.get_related_entities(entity_id, max_depth=max_depth)
            standards = []
            
            for related_id, distance in related_entities:
                entity = self.graph.get_entity(related_id)
                if entity and entity.entity_type == EntityType.STANDARD:
                    standards.append(entity)
            
            return standards
            
        except Exception as e:
            logger.error(f"查找相关标准失败: {e}")
            return []
    
    def find_compliance_path(self, source_entity_id: str, target_standard_id: str) -> Optional[List[str]]:
        """
        查找合规路径
        
        Args:
            source_entity_id: 源实体ID
            target_standard_id: 目标标准ID
            
        Returns:
            Optional[List[str]]: 合规路径
        """
        try:
            path = self.graph.find_path(source_entity_id, target_standard_id)
            return path
            
        except Exception as e:
            logger.error(f"查找合规路径失败: {e}")
            return None
    
    def analyze_dependencies(self, entity_id: str) -> Dict[str, List[KnowledgeEntity]]:
        """
        分析实体依赖关系
        
        Args:
            entity_id: 实体ID
            
        Returns:
            Dict[str, List[KnowledgeEntity]]: 依赖关系分析结果
        """
        dependencies = {
            'depends_on': [],      # 依赖的实体
            'depended_by': [],     # 被依赖的实体
            'references': [],      # 引用的实体
            'referenced_by': [],   # 被引用的实体
            'implements': [],      # 实现的实体
            'implemented_by': []   # 被实现的实体
        }
        
        try:
            # 获取出边（当前实体指向其他实体）
            outgoing_neighbors = self.graph.get_neighbors(entity_id, direction="out")
            for neighbor_id in outgoing_neighbors:
                edge_data = self.graph.graph.get_edge_data(entity_id, neighbor_id)
                if edge_data:
                    rel_type = edge_data.get('relationship_type')
                    neighbor_entity = self.graph.get_entity(neighbor_id)
                    
                    if neighbor_entity:
                        if rel_type == RelationshipType.DEPENDS_ON.value:
                            dependencies['depends_on'].append(neighbor_entity)
                        elif rel_type == RelationshipType.REFERENCES.value:
                            dependencies['references'].append(neighbor_entity)
                        elif rel_type == RelationshipType.IMPLEMENTS.value:
                            dependencies['implements'].append(neighbor_entity)
            
            # 获取入边（其他实体指向当前实体）
            incoming_neighbors = self.graph.get_neighbors(entity_id, direction="in")
            for neighbor_id in incoming_neighbors:
                edge_data = self.graph.graph.get_edge_data(neighbor_id, entity_id)
                if edge_data:
                    rel_type = edge_data.get('relationship_type')
                    neighbor_entity = self.graph.get_entity(neighbor_id)
                    
                    if neighbor_entity:
                        if rel_type == RelationshipType.DEPENDS_ON.value:
                            dependencies['depended_by'].append(neighbor_entity)
                        elif rel_type == RelationshipType.REFERENCES.value:
                            dependencies['referenced_by'].append(neighbor_entity)
                        elif rel_type == RelationshipType.IMPLEMENTS.value:
                            dependencies['implemented_by'].append(neighbor_entity)
            
            return dependencies
            
        except Exception as e:
            logger.error(f"依赖关系分析失败: {e}")
            return dependencies
    
    def find_conflicting_rules(self) -> List[Tuple[KnowledgeEntity, KnowledgeEntity, str]]:
        """
        查找冲突规则
        
        Returns:
            List[Tuple[KnowledgeEntity, KnowledgeEntity, str]]: (规则1, 规则2, 冲突原因)
        """
        conflicts = []
        
        try:
            rules = self.graph.get_entities_by_type(EntityType.RULE)
            
            for i, rule1 in enumerate(rules):
                for j, rule2 in enumerate(rules[i+1:], i+1):
                    conflict_reason = self._check_rule_conflict(rule1, rule2)
                    if conflict_reason:
                        conflicts.append((rule1, rule2, conflict_reason))
            
            logger.info(f"发现 {len(conflicts)} 个规则冲突")
            return conflicts
            
        except Exception as e:
            logger.error(f"查找冲突规则失败: {e}")
            return []
    
    def _check_rule_conflict(self, rule1: KnowledgeEntity, rule2: KnowledgeEntity) -> Optional[str]:
        """检查两个规则是否冲突"""
        try:
            # 检查是否有明确的冲突关系
            if self.graph.graph.has_edge(rule1.id, rule2.id):
                edge_data = self.graph.graph.get_edge_data(rule1.id, rule2.id)
                if edge_data.get('relationship_type') == RelationshipType.CONFLICTS_WITH.value:
                    return "明确的冲突关系"
            
            # 检查适用范围冲突
            if (hasattr(rule1, 'applicable_devices') and hasattr(rule2, 'applicable_devices') and
                rule1.applicable_devices and rule2.applicable_devices):
                
                common_devices = set(rule1.applicable_devices) & set(rule2.applicable_devices)
                if common_devices:
                    # 检查规则内容是否矛盾
                    if self._are_rules_contradictory(rule1, rule2):
                        return f"在设备 {common_devices} 上的规则矛盾"
            
            return None
            
        except Exception as e:
            logger.warning(f"规则冲突检查失败: {e}")
            return None
    
    def _are_rules_contradictory(self, rule1: KnowledgeEntity, rule2: KnowledgeEntity) -> bool:
        """检查规则内容是否矛盾"""
        # 简化的矛盾检测逻辑
        contradictory_pairs = [
            ('必须', '不得'),
            ('应当', '禁止'),
            ('启用', '禁用'),
            ('允许', '禁止')
        ]
        
        for pos, neg in contradictory_pairs:
            if pos in rule1.content and neg in rule2.content:
                return True
            if neg in rule1.content and pos in rule2.content:
                return True
        
        return False
    
    def get_entity_impact_analysis(self, entity_id: str) -> Dict[str, Any]:
        """
        获取实体影响分析
        
        Args:
            entity_id: 实体ID
            
        Returns:
            Dict[str, Any]: 影响分析结果
        """
        analysis = {
            'direct_impact': 0,      # 直接影响的实体数量
            'indirect_impact': 0,    # 间接影响的实体数量
            'affected_entities': [], # 受影响的实体
            'impact_score': 0.0      # 影响分数
        }
        
        try:
            # 获取直接相关的实体
            direct_neighbors = self.graph.get_neighbors(entity_id, direction="out")
            analysis['direct_impact'] = len(direct_neighbors)
            
            # 获取间接相关的实体
            all_related = self.graph.get_related_entities(entity_id, max_depth=3)
            analysis['indirect_impact'] = len(all_related)
            
            # 收集受影响的实体
            for related_id, distance in all_related:
                entity = self.graph.get_entity(related_id)
                if entity:
                    analysis['affected_entities'].append({
                        'entity': entity,
                        'distance': distance,
                        'impact_weight': 1.0 / (distance + 1)
                    })
            
            # 计算影响分数
            total_weight = sum(item['impact_weight'] for item in analysis['affected_entities'])
            analysis['impact_score'] = total_weight
            
            return analysis
            
        except Exception as e:
            logger.error(f"实体影响分析失败: {e}")
            return analysis
    
    def search_by_keywords(self, keywords: List[str], entity_types: List[EntityType] = None) -> List[KnowledgeEntity]:
        """
        基于关键词搜索实体
        
        Args:
            keywords: 关键词列表
            entity_types: 实体类型过滤
            
        Returns:
            List[KnowledgeEntity]: 搜索结果
        """
        results = []
        
        try:
            for keyword in keywords:
                search_results = self.graph.search_entities(keyword, entity_types)
                for entity, score in search_results:
                    if entity not in results:
                        results.append(entity)
            
            return results
            
        except Exception as e:
            logger.error(f"关键词搜索失败: {e}")
            return []
    
    def get_recommendation(self, entity_id: str, recommendation_type: str = "related") -> List[KnowledgeEntity]:
        """
        获取推荐实体
        
        Args:
            entity_id: 实体ID
            recommendation_type: 推荐类型 ("related", "similar", "complementary")
            
        Returns:
            List[KnowledgeEntity]: 推荐实体列表
        """
        recommendations = []
        
        try:
            if recommendation_type == "related":
                # 基于关系的推荐
                related_entities = self.graph.get_related_entities(entity_id, max_depth=2)
                for related_id, distance in related_entities[:10]:  # 限制数量
                    entity = self.graph.get_entity(related_id)
                    if entity:
                        recommendations.append(entity)
            
            elif recommendation_type == "similar":
                # 基于相似性的推荐
                source_entity = self.graph.get_entity(entity_id)
                if source_entity:
                    same_type_entities = self.graph.get_entities_by_type(source_entity.entity_type)
                    # 简化的相似性计算
                    for entity in same_type_entities[:5]:
                        if entity.id != entity_id:
                            recommendations.append(entity)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"获取推荐失败: {e}")
            return []
