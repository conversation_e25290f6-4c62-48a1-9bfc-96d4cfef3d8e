#!/usr/bin/env python3
"""
完全重新设计的智能变电站二次回路图生成系统
严格按照权威标准进行技术修正

权威标准依据：
- 防止电力生产事故的二十五项重点要求（2023版）- 国家能源局
- Q/GDW 441-2010: 智能变电站继电保护技术规范 - 国网公司
- GB/T 50976-2014: 继电保护及二次回路安装及验收规范
- DL/T 995-2006: 继电保护和安全自动装置技术规程

重要修正：
1. 保护装置直接硬接线跳闸，不通过GOOSE
2. GOOSE仅用于信息共享和联跳
3. 双重化保护完全独立
4. 硬接线回路不依赖通信网络
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class CorrectedStandardCircuitGenerator:
    """修正后的标准回路图生成器"""
    
    def __init__(self):
        # 权威标准要求
        self.authoritative_requirements = {
            '跳闸回路设计原则': {
                '基本原则': '保护装置应有独立可靠的硬接线跳闸回路',
                '双重化要求': '两套保护装置的跳闸回路应分别对应断路器的两个跳闸线圈',
                '独立性要求': '双重化配置的两套保护装置之间不应有电气联系',
                '可靠性要求': '跳闸回路不应依赖通信网络',
                '标准依据': '防止电力生产事故的二十五项重点要求（2023版）'
            },
            'GOOSE应用原则': {
                '主要功能': '信息交换、状态监视、联跳闭锁',
                '应用限制': 'GOOSE不应作为唯一的跳闸手段',
                '备用要求': '重要保护功能应保留硬接线备用',
                '性能要求': '传输延时≤4ms',
                '标准依据': 'Q/GDW 441-2010智能变电站继电保护技术规范'
            },
            '电源独立性': {
                '基本要求': '每套保护装置应由不同的电源供电',
                '配置要求': '分别设有专用的直流空气开关',
                '目的': '防止电源故障导致双重化保护同时失效',
                '标准依据': '防止电力生产事故的二十五项重点要求'
            }
        }
        
        # 正确的技术架构
        self.correct_technical_architecture = {
            '保护系统': {
                '主保护A': {
                    '电源': 'DC 220V-I',
                    '跳闸回路': '硬接线→跳闸线圈A',
                    'CT输入': 'CT-A组或CT独立绕组',
                    'PT输入': 'PT-A组或PT独立绕组',
                    'GOOSE': '仅用于信息发送',
                    '独立性': '与主保护B完全独立'
                },
                '主保护B': {
                    '电源': 'DC 220V-II',
                    '跳闸回路': '硬接线→跳闸线圈B', 
                    'CT输入': 'CT-B组或CT独立绕组',
                    'PT输入': 'PT-B组或PT独立绕组',
                    'GOOSE': '仅用于信息发送',
                    '独立性': '与主保护A完全独立'
                }
            },
            '通信系统': {
                'SV网络': {
                    '功能': '传输数字化采样值',
                    '路径': '合并单元→保护装置',
                    '性能': '4000Hz采样，≤3ms延时',
                    '冗余': '双网设计，自动切换'
                },
                'GOOSE网络': {
                    '功能': '快速信息交换',
                    '应用': '保护动作信息、设备状态、联跳信号',
                    '限制': '不作为主跳闸手段',
                    '性能': '≤4ms传输延时'
                }
            },
            '控制系统': {
                '断路器控制': {
                    '跳闸控制': '保护装置→硬接线→跳闸线圈',
                    '合闸控制': '控制系统→硬接线→合闸线圈',
                    '远程控制': 'SCADA→智能终端→硬接线',
                    '特点': '关键控制不依赖通信网络'
                }
            }
        }
    
    def generate_corrected_protection_circuit_svg(self) -> str:
        """生成修正后的保护回路图"""
        
        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #333; }
            .hardwire-line { stroke: red; stroke-width: 4; fill: none; }
            .goose-line { stroke: purple; stroke-width: 2; fill: none; stroke-dasharray: 8,4; }
            .sv-line { stroke: orange; stroke-width: 3; fill: none; }
            .power-line { stroke: blue; stroke-width: 3; fill: none; }
            .correction-box { fill: #d4edda; stroke: #28a745; stroke-width: 3; }
            .error-box { fill: #f8d7da; stroke: #dc3545; stroke-width: 2; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1600" height="1200" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="800" y="40" text-anchor="middle" class="title-text">智能变电站继电保护回路图（权威标准修正版）</text>
    <text x="800" y="65" text-anchor="middle" font-size="14" fill="#666">严格按照国家能源局和国网公司权威标准设计</text>
    
    <!-- 重要修正说明 -->
    <g transform="translate(50, 90)">
        <rect x="0" y="0" width="1500" height="100" class="correction-box"/>
        <text x="750" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#155724">✅ 重要技术修正（基于权威标准）</text>
        <text x="30" y="50" font-size="12" fill="#155724">1. 保护装置直接硬接线跳闸，符合《防止电力生产事故的二十五项重点要求》</text>
        <text x="30" y="70" font-size="12" fill="#155724">2. GOOSE仅用于信息共享，不作为主跳闸手段，符合Q/GDW 441-2010</text>
        <text x="30" y="90" font-size="12" fill="#155724">3. 双重化保护完全独立，各有独立跳闸回路和电源</text>
    </g>
    
    <!-- 一次设备 -->
    <g transform="translate(100, 220)">
        <text x="0" y="0" font-size="14" font-weight="bold">一次设备:</text>
        <line x1="0" y1="20" x2="300" y2="20" stroke="black" stroke-width="8"/>
        <text x="150" y="15" text-anchor="middle" font-size="12">220kV出线</text>
        
        <!-- 电流互感器（双绕组） -->
        <g transform="translate(80, 0)">
            <circle cx="0" cy="20" r="15" fill="none" stroke="red" stroke-width="3"/>
            <text x="0" y="50" text-anchor="middle" font-size="10">TA</text>
            <text x="0" y="65" text-anchor="middle" font-size="8">双绕组</text>
            <!-- A组绕组 -->
            <circle cx="-8" cy="85" r="3" fill="red"/>
            <text x="-20" y="80" font-size="7">A组</text>
            <!-- B组绕组 -->
            <circle cx="8" cy="85" r="3" fill="red"/>
            <text x="15" y="80" font-size="7">B组</text>
        </g>
        
        <!-- 断路器 -->
        <g transform="translate(220, 0)">
            <rect x="0" y="10" width="30" height="20" fill="none" stroke="black" stroke-width="3"/>
            <text x="15" y="50" text-anchor="middle" font-size="10">QF1</text>
            <!-- 双跳闸线圈 -->
            <rect x="-20" y="70" width="15" height="20" fill="lightcoral" stroke="black" stroke-width="2"/>
            <text x="-12" y="105" text-anchor="middle" font-size="7">YT-A</text>
            <rect x="35" y="70" width="15" height="20" fill="lightcoral" stroke="black" stroke-width="2"/>
            <text x="42" y="105" text-anchor="middle" font-size="7">YT-B</text>
        </g>
    </g>
    
    <!-- 主保护A -->
    <g transform="translate(100, 400)">
        <rect x="0" y="0" width="200" height="120" fill="lightblue" stroke="black" stroke-width="3"/>
        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">主保护A</text>
        <text x="100" y="45" text-anchor="middle" font-size="10">符合Q/GDW 441-2010</text>
        <text x="100" y="60" text-anchor="middle" font-size="10">独立电源：DC 220V-I</text>
        <text x="100" y="75" text-anchor="middle" font-size="10">独立CT：A组</text>
        
        <!-- CT输入 -->
        <circle cx="-10" cy="30" r="4" fill="red"/>
        <text x="-25" y="25" font-size="8">CT-A</text>
        
        <!-- 硬接线跳闸输出 -->
        <circle cx="210" cy="40" r="5" fill="red"/>
        <text x="220" y="35" font-size="10" font-weight="bold" fill="red">硬接线跳闸</text>
        
        <!-- GOOSE信息输出 -->
        <circle cx="210" cy="80" r="3" fill="purple"/>
        <text x="220" y="75" font-size="8">GOOSE信息</text>
        
        <!-- 电源输入 -->
        <circle cx="-10" cy="100" r="3" fill="blue"/>
        <text x="-35" y="95" font-size="8">DC 220V-I</text>
    </g>
    
    <!-- 主保护B -->
    <g transform="translate(100, 580)">
        <rect x="0" y="0" width="200" height="120" fill="lightgreen" stroke="black" stroke-width="3"/>
        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">主保护B</text>
        <text x="100" y="45" text-anchor="middle" font-size="10">符合Q/GDW 441-2010</text>
        <text x="100" y="60" text-anchor="middle" font-size="10">独立电源：DC 220V-II</text>
        <text x="100" y="75" text-anchor="middle" font-size="10">独立CT：B组</text>
        
        <!-- CT输入 -->
        <circle cx="-10" cy="30" r="4" fill="red"/>
        <text x="-25" y="25" font-size="8">CT-B</text>
        
        <!-- 硬接线跳闸输出 -->
        <circle cx="210" cy="40" r="5" fill="red"/>
        <text x="220" y="35" font-size="10" font-weight="bold" fill="red">硬接线跳闸</text>
        
        <!-- GOOSE信息输出 -->
        <circle cx="210" cy="80" r="3" fill="purple"/>
        <text x="220" y="75" font-size="8">GOOSE信息</text>
        
        <!-- 电源输入 -->
        <circle cx="-10" cy="100" r="3" fill="blue"/>
        <text x="-35" y="95" font-size="8">DC 220V-II</text>
    </g>
    
    <!-- 直流电源系统 -->
    <g transform="translate(50, 750)">
        <text x="0" y="0" font-size="14" font-weight="bold">直流电源系统（双系统独立）:</text>
        
        <!-- 直流电源I -->
        <rect x="0" y="20" width="120" height="60" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="60" y="40" text-anchor="middle" font-size="12" font-weight="bold">直流电源I</text>
        <text x="60" y="55" text-anchor="middle" font-size="10">DC 220V-I</text>
        <text x="60" y="70" text-anchor="middle" font-size="10">供电：主保护A</text>
        
        <!-- 直流电源II -->
        <rect x="150" y="20" width="120" height="60" fill="lightsteelblue" stroke="black" stroke-width="2"/>
        <text x="210" y="40" text-anchor="middle" font-size="12" font-weight="bold">直流电源II</text>
        <text x="210" y="55" text-anchor="middle" font-size="10">DC 220V-II</text>
        <text x="210" y="70" text-anchor="middle" font-size="10">供电：主保护B</text>
    </g>
    
    <!-- 连接线 -->
    <!-- CT到保护装置 -->
    <line x1="172" y1="305" x2="90" y2="430" stroke="red" stroke-width="3"/>
    <line x1="188" y1="305" x2="90" y2="610" stroke="red" stroke-width="3"/>
    
    <!-- 硬接线跳闸回路 -->
    <line x1="310" y1="440" x2="280" y2="290" class="hardwire-line"/>
    <text x="295" y="365" text-anchor="middle" font-size="10" font-weight="bold" fill="red">硬接线A</text>
    
    <line x1="310" y1="620" x2="320" y2="290" class="hardwire-line"/>
    <text x="315" y="455" text-anchor="middle" font-size="10" font-weight="bold" fill="red">硬接线B</text>
    
    <!-- 电源连接 -->
    <line x1="60" y1="770" x2="90" y2="500" class="power-line"/>
    <line x1="210" y1="770" x2="90" y2="680" class="power-line"/>
    
    <!-- GOOSE信息网络 -->
    <g transform="translate(400, 400)">
        <rect x="0" y="0" width="300" height="300" fill="lavender" stroke="purple" stroke-width="2"/>
        <text x="150" y="25" text-anchor="middle" font-size="14" font-weight="bold">GOOSE信息网络</text>
        <text x="150" y="45" text-anchor="middle" font-size="10">（仅用于信息共享）</text>
        
        <!-- 网络交换机 -->
        <rect x="100" y="60" width="100" height="40" fill="lightgray" stroke="black" stroke-width="2"/>
        <text x="150" y="85" text-anchor="middle" font-size="10">过程层交换机</text>
        
        <!-- 信息流向 -->
        <text x="20" y="130" font-size="11" font-weight="bold">信息内容:</text>
        <text x="20" y="150" font-size="10">• 保护动作信息</text>
        <text x="20" y="165" font-size="10">• 断路器位置状态</text>
        <text x="20" y="180" font-size="10">• 设备运行状态</text>
        <text x="20" y="195" font-size="10">• 联跳闭锁信号</text>
        
        <text x="20" y="220" font-size="11" font-weight="bold" fill="red">重要说明:</text>
        <text x="20" y="240" font-size="10" fill="red">GOOSE不参与主跳闸</text>
        <text x="20" y="255" font-size="10" fill="red">仅用于信息交换</text>
        <text x="20" y="270" font-size="10" fill="red">符合Q/GDW 441-2010</text>
    </g>
    
    <!-- GOOSE连接线 -->
    <line x1="310" y1="480" x2="400" y2="480" class="goose-line"/>
    <line x1="310" y1="660" x2="400" y2="660" class="goose-line"/>
    <line x1="400" y1="480" x2="500" y2="480" class="goose-line"/>
    <line x1="400" y1="660" x2="500" y2="660" class="goose-line"/>
    
    <!-- 技术要求说明 -->
    <g transform="translate(800, 400)">
        <text x="0" y="0" font-size="14" font-weight="bold">权威标准技术要求:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">跳闸回路要求:</text>
            <text x="0" y="20" font-size="10">• 保护装置直接硬接线跳闸</text>
            <text x="0" y="35" font-size="10">• 不依赖通信网络</text>
            <text x="0" y="50" font-size="10">• 双重化保护独立跳闸回路</text>
            <text x="0" y="65" font-size="10">• 符合国家能源局要求</text>
        </g>
        
        <g transform="translate(0, 120)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">GOOSE应用要求:</text>
            <text x="0" y="20" font-size="10">• 仅用于信息交换</text>
            <text x="0" y="35" font-size="10">• 不作为主跳闸手段</text>
            <text x="0" y="50" font-size="10">• 传输延时≤4ms</text>
            <text x="0" y="65" font-size="10">• 符合Q/GDW 441-2010</text>
        </g>
        
        <g transform="translate(0, 210)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">可靠性保证:</text>
            <text x="0" y="20" font-size="10">• 双重化保护完全独立</text>
            <text x="0" y="35" font-size="10">• 独立电源供电</text>
            <text x="0" y="50" font-size="10">• 独立CT/PT输入</text>
            <text x="0" y="65" font-size="10">• 网络故障不影响保护</text>
        </g>
    </g>
    
</svg>'''
        
        return svg_content


def main():
    """主函数"""
    
    print("🔧 完全重新设计的智能变电站二次回路图系统")
    print("=" * 80)
    print("严格按照权威标准进行技术修正")
    print("=" * 80)
    
    # 创建修正后的生成器
    generator = CorrectedStandardCircuitGenerator()
    
    # 输出目录
    output_dir = "design_reports/corrected_standards"
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print("\n📋 权威标准依据:")
    for requirement, details in generator.authoritative_requirements.items():
        print(f"   📌 {requirement}:")
        if '基本原则' in details:
            print(f"      • {details['基本原则']}")
        elif '主要功能' in details:
            print(f"      • {details['主要功能']}")
        elif '基本要求' in details:
            print(f"      • {details['基本要求']}")
        print(f"      • 标准依据: {details['标准依据']}")
    
    print("\n🔧 生成修正后的保护回路图...")
    
    # 生成修正后的保护回路图
    corrected_svg = generator.generate_corrected_protection_circuit_svg()
    svg_file = output_path / "corrected_protection_circuit.svg"
    with open(svg_file, 'w', encoding='utf-8') as f:
        f.write(corrected_svg)
    
    print(f"✅ 修正后的保护回路图已保存: {svg_file}")
    
    print("\n✅ 主要技术修正:")
    print("   🔴 保护装置直接硬接线跳闸")
    print("   💜 GOOSE仅用于信息共享")
    print("   🔵 双重化保护完全独立")
    print("   ⚡ 独立电源供电")
    print("   🛡️ 不依赖通信网络")
    
    print("\n📋 符合的权威标准:")
    print("   ✅ 防止电力生产事故的二十五项重点要求（2023版）")
    print("   ✅ Q/GDW 441-2010智能变电站继电保护技术规范")
    print("   ✅ GB/T 50976-2014继电保护及二次回路安装及验收规范")


if __name__ == "__main__":
    main()
