"""
文档生成器
生成各种工程文档和报告
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import json

from ..models import SCLDocument


class DocumentGenerator:
    """
    文档生成器
    
    为智能变电站工程师生成标准化的工程文档：
    1. 设计说明书
    2. 配置清单
    3. 测试报告模板
    4. 验收文档
    """
    
    def __init__(self):
        self.templates = {
            'design_specification': self._generate_design_spec,
            'configuration_list': self._generate_config_list,
            'test_report': self._generate_test_report,
            'acceptance_document': self._generate_acceptance_doc
        }
    
    def generate_document(self, doc_type: str, scd_doc: SCLDocument, 
                         project_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成指定类型的文档
        
        Args:
            doc_type: 文档类型
            scd_doc: SCD文档
            project_info: 项目信息
            
        Returns:
            Dict: 生成的文档数据
        """
        if doc_type not in self.templates:
            raise ValueError(f"不支持的文档类型: {doc_type}")
        
        generator = self.templates[doc_type]
        return generator(scd_doc, project_info)
    
    def _generate_design_spec(self, scd_doc: SCLDocument, 
                            project_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成设计说明书"""
        return {
            'title': f"{project_info.get('name', '智能变电站项目')} 二次设计说明书",
            'version': '1.0',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'author': project_info.get('author', '设计工程师'),
            'sections': [
                {
                    'title': '1. 项目概述',
                    'content': self._generate_project_overview(scd_doc, project_info)
                },
                {
                    'title': '2. 系统架构',
                    'content': self._generate_system_architecture(scd_doc)
                },
                {
                    'title': '3. 设备配置',
                    'content': self._generate_device_configuration(scd_doc)
                },
                {
                    'title': '4. 通信配置',
                    'content': self._generate_communication_config(scd_doc)
                },
                {
                    'title': '5. 技术规范',
                    'content': self._generate_technical_specs(scd_doc)
                }
            ]
        }
    
    def _generate_config_list(self, scd_doc: SCLDocument, 
                            project_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成配置清单"""
        return {
            'title': f"{project_info.get('name', '项目')} 配置清单",
            'version': '1.0',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'summary': {
                'total_ieds': len(scd_doc.ieds),
                'substation': scd_doc.substation.name if scd_doc.substation else 'N/A',
                'scl_version': scd_doc.version
            },
            'ied_list': [
                {
                    'name': ied.name,
                    'manufacturer': ied.manufacturer or 'N/A',
                    'type': ied.type or 'N/A',
                    'description': ied.desc or 'N/A'
                }
                for ied in scd_doc.ieds
            ],
            'network_config': self._extract_network_config(scd_doc),
            'data_types': self._extract_data_types(scd_doc)
        }
    
    def _generate_test_report(self, scd_doc: SCLDocument, 
                           project_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试报告模板"""
        return {
            'title': f"{project_info.get('name', '项目')} 测试报告",
            'version': '1.0',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'test_categories': [
                {
                    'category': '配置文件验证',
                    'tests': [
                        '语法正确性检查',
                        '语义完整性验证',
                        '引用关系检查',
                        '命名规范验证'
                    ]
                },
                {
                    'category': '通信功能测试',
                    'tests': [
                        'GOOSE通信测试',
                        'SMV通信测试',
                        'MMS通信测试',
                        '网络连通性测试'
                    ]
                },
                {
                    'category': '设备功能测试',
                    'tests': [
                        'IED设备功能验证',
                        '逻辑节点功能测试',
                        '数据对象访问测试',
                        '服务功能验证'
                    ]
                }
            ],
            'test_results': {
                'passed': 0,
                'failed': 0,
                'skipped': 0,
                'total': 0
            }
        }
    
    def _generate_acceptance_doc(self, scd_doc: SCLDocument, 
                               project_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成验收文档"""
        return {
            'title': f"{project_info.get('name', '项目')} 验收文档",
            'version': '1.0',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'acceptance_criteria': [
                {
                    'item': '配置文件完整性',
                    'requirement': '所有必需的配置文件完整且格式正确',
                    'status': 'pending'
                },
                {
                    'item': '设备配置正确性',
                    'requirement': '所有IED设备配置符合设计要求',
                    'status': 'pending'
                },
                {
                    'item': '通信配置验证',
                    'requirement': '网络通信配置正确且连通性良好',
                    'status': 'pending'
                },
                {
                    'item': '功能测试通过',
                    'requirement': '所有功能测试项目通过',
                    'status': 'pending'
                }
            ],
            'deliverables': [
                'SCD配置文件',
                '设计说明书',
                '配置清单',
                '测试报告',
                '用户手册'
            ]
        }
    
    def _generate_project_overview(self, scd_doc: SCLDocument, 
                                 project_info: Dict[str, Any]) -> str:
        """生成项目概述"""
        overview = f"""
本项目为{project_info.get('name', '智能变电站')}的二次设计项目，
采用IEC61850标准进行配置。

项目基本信息：
- 变电站名称：{scd_doc.substation.name if scd_doc.substation else 'N/A'}
- IED设备数量：{len(scd_doc.ieds)}
- SCL版本：{scd_doc.version}
- 设计标准：IEC61850

本设计旨在解决智能变电站二次设计中的实际痛点，
特别是虚端子连接的直观性问题，通过标准化的配置
确保系统的可靠性和可维护性。
"""
        return overview.strip()
    
    def _generate_system_architecture(self, scd_doc: SCLDocument) -> str:
        """生成系统架构说明"""
        arch_desc = f"""
系统采用分层架构设计：

1. 站控层：负责全站监控和操作
2. 间隔层：各间隔的保护和控制设备
3. 过程层：传感器和执行器

网络架构：
- 站控层网络：MMS通信
- 过程层网络：GOOSE/SMV通信

设备配置：
"""
        
        # 添加设备列表
        for ied in scd_doc.ieds:
            arch_desc += f"- {ied.name}: {ied.type or '未知类型'} ({ied.manufacturer or '未知厂商'})\n"
        
        return arch_desc
    
    def _generate_device_configuration(self, scd_doc: SCLDocument) -> str:
        """生成设备配置说明"""
        config_desc = "设备配置详情：\n\n"
        
        for i, ied in enumerate(scd_doc.ieds, 1):
            config_desc += f"{i}. {ied.name}\n"
            config_desc += f"   - 制造商：{ied.manufacturer or 'N/A'}\n"
            config_desc += f"   - 类型：{ied.type or 'N/A'}\n"
            config_desc += f"   - 描述：{ied.desc or 'N/A'}\n"
            config_desc += f"   - 访问点数量：{len(ied.access_points)}\n\n"
        
        return config_desc
    
    def _generate_communication_config(self, scd_doc: SCLDocument) -> str:
        """生成通信配置说明"""
        comm_desc = "通信配置说明：\n\n"
        
        if scd_doc.communication:
            comm_desc += "网络配置已定义，包含以下子网：\n"
            if hasattr(scd_doc.communication, 'sub_networks'):
                for subnet in scd_doc.communication.sub_networks:
                    comm_desc += f"- {subnet.name}: {subnet.type}\n"
        else:
            comm_desc += "通信配置待完善。\n"
        
        comm_desc += "\n建议的通信架构：\n"
        comm_desc += "- 站控层：以太网，MMS协议\n"
        comm_desc += "- 过程层：以太网，GOOSE/SMV协议\n"
        
        return comm_desc
    
    def _generate_technical_specs(self, scd_doc: SCLDocument) -> str:
        """生成技术规范"""
        specs = """
技术规范：

1. 标准符合性
   - IEC61850-6: 配置描述语言
   - IEC61850-7-4: 兼容逻辑节点类和数据类
   - IEC61850-8-1: 特定通信服务映射

2. 性能要求
   - GOOSE传输时间：≤4ms
   - SMV传输时间：≤1ms
   - MMS响应时间：≤100ms

3. 可靠性要求
   - 系统可用性：≥99.9%
   - 网络冗余：双网配置
   - 设备冗余：关键设备双重化

4. 安全要求
   - 网络隔离：物理隔离
   - 访问控制：基于角色的权限管理
   - 数据完整性：数字签名验证
"""
        return specs
    
    def _extract_network_config(self, scd_doc: SCLDocument) -> Dict[str, Any]:
        """提取网络配置信息"""
        if not scd_doc.communication:
            return {'status': 'not_configured'}
        
        return {
            'status': 'configured',
            'subnets': len(getattr(scd_doc.communication, 'sub_networks', [])),
            'details': 'Network configuration available'
        }
    
    def _extract_data_types(self, scd_doc: SCLDocument) -> Dict[str, Any]:
        """提取数据类型信息"""
        if not scd_doc.data_type_templates:
            return {'status': 'not_defined'}
        
        return {
            'status': 'defined',
            'templates': 'Data type templates available'
        }
    
    def save_document(self, document: Dict[str, Any], output_path: str, format_type: str = 'json'):
        """
        保存文档到文件
        
        Args:
            document: 文档数据
            output_path: 输出路径
            format_type: 格式类型
        """
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        if format_type.lower() == 'json':
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(document, f, ensure_ascii=False, indent=2)
        
        elif format_type.lower() == 'markdown':
            md_content = self._convert_to_markdown(document)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(md_content)
        
        else:
            raise ValueError(f"不支持的格式类型: {format_type}")
    
    def _convert_to_markdown(self, document: Dict[str, Any]) -> str:
        """转换为Markdown格式"""
        md_content = f"# {document.get('title', '文档')}\n\n"
        
        # 添加基本信息
        if 'version' in document:
            md_content += f"**版本：** {document['version']}\n"
        if 'date' in document:
            md_content += f"**日期：** {document['date']}\n"
        if 'author' in document:
            md_content += f"**作者：** {document['author']}\n"
        
        md_content += "\n---\n\n"
        
        # 添加章节内容
        if 'sections' in document:
            for section in document['sections']:
                md_content += f"## {section['title']}\n\n"
                md_content += f"{section['content']}\n\n"
        
        return md_content
