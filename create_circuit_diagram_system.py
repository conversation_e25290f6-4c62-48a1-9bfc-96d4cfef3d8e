#!/usr/bin/env python3
"""
虚端子回路图生成系统
基于IEC61850虚端子连接关系生成传统二次回路图
"""

import os
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class CircuitDiagramGenerator:
    """虚端子回路图生成器"""
    
    def __init__(self):
        self.circuit_types = {
            'current': '电流回路',
            'voltage': '电压回路', 
            'protection': '保护回路',
            'control': '控制回路',
            'trip': '跳闸回路',
            'measurement': '计量回路',
            'signal': '信号回路'
        }
        
        # 电气符号定义（基于GB/T 4728标准）
        self.symbols = {
            'breaker': {
                'name': '断路器',
                'symbol': '⚡',
                'svg': '<rect x="0" y="0" width="20" height="10" fill="none" stroke="black"/><line x1="5" y1="0" x2="15" y2="10" stroke="black"/>',
                'terminals': ['T1', 'T2']
            },
            'disconnector': {
                'name': '隔离开关',
                'symbol': '⚊',
                'svg': '<line x1="0" y1="5" x2="20" y2="5" stroke="black"/><circle cx="10" cy="5" r="3" fill="none" stroke="black"/>',
                'terminals': ['T1', 'T2']
            },
            'current_transformer': {
                'name': '电流互感器',
                'symbol': 'TA',
                'svg': '<circle cx="10" cy="10" r="8" fill="none" stroke="black"/><text x="10" y="15" text-anchor="middle" font-size="8">TA</text>',
                'terminals': ['P1', 'P2', 'S1', 'S2']
            },
            'voltage_transformer': {
                'name': '电压互感器',
                'symbol': 'TV',
                'svg': '<circle cx="10" cy="10" r="8" fill="none" stroke="black"/><text x="10" y="15" text-anchor="middle" font-size="8">TV</text>',
                'terminals': ['P1', 'P2', 'S1', 'S2']
            },
            'relay': {
                'name': '继电器',
                'symbol': 'K',
                'svg': '<rect x="0" y="0" width="20" height="15" fill="none" stroke="black"/><text x="10" y="10" text-anchor="middle" font-size="8">K</text>',
                'terminals': ['1', '2', '3', '4']
            },
            'protection_device': {
                'name': '保护装置',
                'symbol': 'PROT',
                'svg': '<rect x="0" y="0" width="40" height="20" fill="none" stroke="black"/><text x="20" y="12" text-anchor="middle" font-size="8">PROT</text>',
                'terminals': ['AI1', 'AI2', 'AI3', 'DI1', 'DI2', 'DO1', 'DO2']
            },
            'measurement_device': {
                'name': '测量装置',
                'symbol': 'MEAS',
                'svg': '<rect x="0" y="0" width="40" height="20" fill="none" stroke="black"/><text x="20" y="12" text-anchor="middle" font-size="8">MEAS</text>',
                'terminals': ['AI1', 'AI2', 'AI3', 'DI1', 'DI2']
            }
        }
        
        # 线型定义
        self.line_types = {
            'current': {'color': 'red', 'width': 2, 'style': 'solid'},
            'voltage': {'color': 'blue', 'width': 2, 'style': 'solid'},
            'control': {'color': 'green', 'width': 1, 'style': 'dashed'},
            'signal': {'color': 'orange', 'width': 1, 'style': 'dotted'}
        }
    
    def parse_virtual_terminals(self, scd_file: str) -> Dict:
        """解析SCD文件中的虚端子连接关系"""
        
        virtual_terminals = {
            'devices': {},
            'connections': [],
            'goose_connections': [],
            'sv_connections': []
        }
        
        try:
            tree = ET.parse(scd_file)
            root = tree.getroot()
            
            # 解析IED设备
            ieds = root.findall(".//IED")
            for ied in ieds:
                ied_name = ied.get('name', '未知IED')
                ied_type = ied.get('type', '未知类型')
                
                virtual_terminals['devices'][ied_name] = {
                    'type': ied_type,
                    'logical_devices': {},
                    'access_points': {}
                }
                
                # 解析逻辑设备
                logical_devices = ied.findall(".//LDevice")
                for ld in logical_devices:
                    ld_inst = ld.get('inst', '未知LD')
                    virtual_terminals['devices'][ied_name]['logical_devices'][ld_inst] = {
                        'logical_nodes': {}
                    }
                    
                    # 解析逻辑节点
                    logical_nodes = ld.findall(".//LN")
                    for ln in logical_nodes:
                        ln_class = ln.get('lnClass', '未知LN')
                        ln_inst = ln.get('inst', '')
                        ln_key = f"{ln_class}{ln_inst}"
                        
                        virtual_terminals['devices'][ied_name]['logical_devices'][ld_inst]['logical_nodes'][ln_key] = {
                            'class': ln_class,
                            'instance': ln_inst,
                            'data_objects': []
                        }
            
            # 解析虚端子连接（Inputs部分）
            inputs = root.findall(".//Inputs")
            for input_section in inputs:
                ext_refs = input_section.findall("ExtRef")
                for ext_ref in ext_refs:
                    connection = {
                        'source_ied': ext_ref.get('iedName', ''),
                        'source_ld': ext_ref.get('ldInst', ''),
                        'source_ln': ext_ref.get('lnClass', '') + ext_ref.get('lnInst', ''),
                        'source_do': ext_ref.get('doName', ''),
                        'source_da': ext_ref.get('daName', ''),
                        'target_address': ext_ref.get('intAddr', ''),
                        'service_type': ext_ref.get('serviceType', 'GOOSE')
                    }
                    virtual_terminals['connections'].append(connection)
            
            # 解析GOOSE控制块
            goose_controls = root.findall(".//GSEControl")
            for goose in goose_controls:
                goose_info = {
                    'name': goose.get('name', ''),
                    'app_id': goose.get('appID', ''),
                    'dataset': goose.get('datSet', ''),
                    'conf_rev': goose.get('confRev', '1')
                }
                virtual_terminals['goose_connections'].append(goose_info)
            
            # 解析SV控制块
            sv_controls = root.findall(".//SampledValueControl")
            for sv in sv_controls:
                sv_info = {
                    'name': sv.get('name', ''),
                    'smv_id': sv.get('smvID', ''),
                    'dataset': sv.get('datSet', ''),
                    'sample_rate': sv.get('smpRate', '4000')
                }
                virtual_terminals['sv_connections'].append(sv_info)
        
        except Exception as e:
            print(f"解析SCD文件失败: {e}")
        
        return virtual_terminals
    
    def generate_current_circuit(self, virtual_terminals: Dict) -> Dict:
        """生成电流回路图"""
        
        circuit = {
            'type': 'current',
            'name': '电流回路',
            'devices': [],
            'connections': [],
            'layout': {'width': 800, 'height': 600}
        }
        
        # 查找电流相关的设备和连接
        for device_name, device_info in virtual_terminals['devices'].items():
            device_type = device_info.get('type', '')
            
            # 电流互感器
            if 'CTR' in str(device_info) or '电流' in device_type:
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'current_transformer',
                    'symbol': self.symbols['current_transformer'],
                    'position': {'x': 100, 'y': 100},
                    'terminals': {
                        'P1': {'x': 90, 'y': 100, 'type': 'primary'},
                        'P2': {'x': 110, 'y': 100, 'type': 'primary'},
                        'S1': {'x': 90, 'y': 120, 'type': 'secondary'},
                        'S2': {'x': 110, 'y': 120, 'type': 'secondary'}
                    }
                })
            
            # 保护装置
            elif device_type == 'Protection':
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'protection_device',
                    'symbol': self.symbols['protection_device'],
                    'position': {'x': 300, 'y': 100},
                    'terminals': {
                        'AI1': {'x': 300, 'y': 110, 'type': 'analog_input'},
                        'AI2': {'x': 300, 'y': 120, 'type': 'analog_input'},
                        'AI3': {'x': 300, 'y': 130, 'type': 'analog_input'}
                    }
                })
            
            # 测量装置
            elif device_type == 'Measurement':
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'measurement_device',
                    'symbol': self.symbols['measurement_device'],
                    'position': {'x': 500, 'y': 100},
                    'terminals': {
                        'AI1': {'x': 500, 'y': 110, 'type': 'analog_input'},
                        'AI2': {'x': 500, 'y': 120, 'type': 'analog_input'}
                    }
                })
        
        # 生成连接线
        for connection in virtual_terminals['connections']:
            if connection.get('service_type') == 'SV':  # 采样值连接
                circuit['connections'].append({
                    'from': f"{connection['source_ied']}.{connection['source_do']}",
                    'to': connection['target_address'],
                    'type': 'current',
                    'style': self.line_types['current']
                })
        
        return circuit
    
    def generate_voltage_circuit(self, virtual_terminals: Dict) -> Dict:
        """生成电压回路图"""
        
        circuit = {
            'type': 'voltage',
            'name': '电压回路',
            'devices': [],
            'connections': [],
            'layout': {'width': 800, 'height': 600}
        }
        
        # 查找电压相关的设备
        for device_name, device_info in virtual_terminals['devices'].items():
            device_type = device_info.get('type', '')
            
            # 电压互感器
            if 'VTR' in str(device_info) or '电压' in device_type:
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'voltage_transformer',
                    'symbol': self.symbols['voltage_transformer'],
                    'position': {'x': 100, 'y': 200},
                    'terminals': {
                        'P1': {'x': 90, 'y': 200, 'type': 'primary'},
                        'P2': {'x': 110, 'y': 200, 'type': 'primary'},
                        'S1': {'x': 90, 'y': 220, 'type': 'secondary'},
                        'S2': {'x': 110, 'y': 220, 'type': 'secondary'}
                    }
                })
        
        return circuit
    
    def generate_protection_circuit(self, virtual_terminals: Dict) -> Dict:
        """生成保护回路图"""
        
        circuit = {
            'type': 'protection',
            'name': '保护回路',
            'devices': [],
            'connections': [],
            'layout': {'width': 1000, 'height': 800}
        }
        
        # 查找保护相关的设备和连接
        for device_name, device_info in virtual_terminals['devices'].items():
            device_type = device_info.get('type', '')
            
            if device_type == 'Protection':
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'protection_device',
                    'symbol': self.symbols['protection_device'],
                    'position': {'x': 400, 'y': 300},
                    'functions': ['距离保护', '差动保护', '过流保护']
                })
        
        # 生成GOOSE连接
        for connection in virtual_terminals['connections']:
            if connection.get('service_type') == 'GOOSE':
                circuit['connections'].append({
                    'from': f"{connection['source_ied']}.{connection['source_do']}",
                    'to': connection['target_address'],
                    'type': 'control',
                    'style': self.line_types['control']
                })
        
        return circuit
    
    def generate_trip_circuit(self, virtual_terminals: Dict) -> Dict:
        """生成跳闸回路图"""
        
        circuit = {
            'type': 'trip',
            'name': '跳闸回路',
            'devices': [],
            'connections': [],
            'layout': {'width': 800, 'height': 600}
        }
        
        # 查找跳闸相关的设备
        for device_name, device_info in virtual_terminals['devices'].items():
            device_type = device_info.get('type', '')
            
            # 断路器
            if 'CBR' in str(device_info) or device_type == 'CircuitBreaker':
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'breaker',
                    'symbol': self.symbols['breaker'],
                    'position': {'x': 200, 'y': 100},
                    'terminals': {
                        'trip_coil': {'x': 200, 'y': 120, 'type': 'control'},
                        'close_coil': {'x': 200, 'y': 140, 'type': 'control'}
                    }
                })
            
            # 保护装置（跳闸输出）
            elif device_type == 'Protection':
                circuit['devices'].append({
                    'name': device_name,
                    'type': 'protection_device',
                    'symbol': self.symbols['protection_device'],
                    'position': {'x': 400, 'y': 100},
                    'terminals': {
                        'trip_output': {'x': 440, 'y': 110, 'type': 'digital_output'}
                    }
                })
        
        return circuit
    
    def generate_svg_diagram(self, circuit: Dict) -> str:
        """生成SVG格式的回路图"""
        
        width = circuit['layout']['width']
        height = circuit['layout']['height']
        
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .device-text {{ font-family: Arial, sans-serif; font-size: 12px; }}
            .terminal-text {{ font-family: Arial, sans-serif; font-size: 10px; }}
            .title-text {{ font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; }}
        </style>
    </defs>
    
    <!-- 标题 -->
    <text x="{width//2}" y="30" text-anchor="middle" class="title-text">{circuit['name']}</text>
    
    <!-- 设备符号 -->'''
        
        for device in circuit['devices']:
            x = device['position']['x']
            y = device['position']['y']
            symbol = device['symbol']
            
            # 添加设备符号
            svg_content += f'''
    <!-- {device['name']} -->
    <g transform="translate({x},{y})">
        {symbol['svg']}
        <text x="20" y="-5" class="device-text">{device['name']}</text>
    </g>'''
            
            # 添加端子标注
            if 'terminals' in device:
                for terminal_name, terminal_info in device['terminals'].items():
                    tx = terminal_info['x']
                    ty = terminal_info['y']
                    svg_content += f'''
    <circle cx="{tx}" cy="{ty}" r="2" fill="black"/>
    <text x="{tx+5}" y="{ty+3}" class="terminal-text">{terminal_name}</text>'''
        
        # 连接线
        for connection in circuit['connections']:
            style = connection['style']
            svg_content += f'''
    <line x1="100" y1="120" x2="300" y2="110" 
          stroke="{style['color']}" 
          stroke-width="{style['width']}" 
          stroke-dasharray="{'5,5' if style['style'] == 'dashed' else '2,2' if style['style'] == 'dotted' else 'none'}"/>'''
        
        svg_content += '''
</svg>'''
        
        return svg_content
    
    def generate_all_circuits(self, scd_file: str, output_dir: str) -> Dict:
        """生成所有类型的回路图"""
        
        print(f"🔍 解析SCD文件: {scd_file}")
        virtual_terminals = self.parse_virtual_terminals(scd_file)
        
        print(f"📊 发现设备: {len(virtual_terminals['devices'])} 个")
        print(f"📊 发现连接: {len(virtual_terminals['connections'])} 个")
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        circuits = {}
        
        # 生成各种回路图
        circuit_generators = {
            'current': self.generate_current_circuit,
            'voltage': self.generate_voltage_circuit,
            'protection': self.generate_protection_circuit,
            'trip': self.generate_trip_circuit
        }
        
        for circuit_type, generator in circuit_generators.items():
            print(f"🎨 生成{self.circuit_types[circuit_type]}...")
            
            circuit = generator(virtual_terminals)
            circuits[circuit_type] = circuit
            
            # 生成SVG文件
            svg_content = self.generate_svg_diagram(circuit)
            svg_file = output_path / f"{circuit_type}_circuit.svg"
            
            with open(svg_file, 'w', encoding='utf-8') as f:
                f.write(svg_content)
            
            print(f"✅ {self.circuit_types[circuit_type]}已保存: {svg_file}")
        
        # 生成总结报告
        report = {
            'generation_time': datetime.now().isoformat(),
            'source_file': scd_file,
            'output_directory': str(output_path),
            'virtual_terminals_summary': {
                'total_devices': len(virtual_terminals['devices']),
                'total_connections': len(virtual_terminals['connections']),
                'goose_connections': len(virtual_terminals['goose_connections']),
                'sv_connections': len(virtual_terminals['sv_connections'])
            },
            'generated_circuits': list(circuits.keys()),
            'circuit_files': [f"{ct}_circuit.svg" for ct in circuits.keys()]
        }
        
        report_file = output_path / "circuit_generation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📋 生成报告已保存: {report_file}")
        
        return {
            'circuits': circuits,
            'virtual_terminals': virtual_terminals,
            'report': report,
            'output_directory': str(output_path)
        }


def main():
    """主函数"""
    
    print("🎨 虚端子回路图生成系统")
    print("=" * 60)
    
    # 创建生成器
    generator = CircuitDiagramGenerator()
    
    # 输入文件
    scd_file = "test_project/problematic_substation.scd"
    
    if not os.path.exists(scd_file):
        print(f"❌ SCD文件不存在: {scd_file}")
        return
    
    # 输出目录
    output_dir = "design_reports/circuit_analysis"
    
    # 生成回路图
    try:
        result = generator.generate_all_circuits(scd_file, output_dir)
        
        print("\n🎉 回路图生成完成！")
        print(f"📁 输出目录: {result['output_directory']}")
        print(f"📊 生成的回路图: {len(result['circuits'])} 个")
        
        for circuit_type, circuit in result['circuits'].items():
            print(f"   • {circuit['name']}: {len(circuit['devices'])} 个设备, {len(circuit['connections'])} 个连接")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
