"""
文件对比器
对比两个IEC61850配置文件的差异
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import difflib

from .comparison_models import (
    ComparisonResult, DiffItem, DiffType, DiffCategory, DiffSeverity
)
from ..core.parser.scd_parser import SCDParser


logger = logging.getLogger(__name__)


class FileComparator:
    """文件对比器"""
    
    def __init__(self):
        """初始化对比器"""
        self.parser = SCDParser()
        self.comparison_rules = self._load_comparison_rules()
        
        logger.info("文件对比器初始化完成")
    
    def compare_files(self, 
                     source_file: str,
                     target_file: str,
                     comparison_options: Optional[Dict[str, Any]] = None) -> ComparisonResult:
        """
        对比两个文件
        
        Args:
            source_file: 源文件路径
            target_file: 目标文件路径
            comparison_options: 对比选项
            
        Returns:
            ComparisonResult: 对比结果
        """
        try:
            logger.info(f"开始对比文件: {source_file} vs {target_file}")
            
            # 创建对比结果
            comparison_id = f"comp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            result = ComparisonResult(
                comparison_id=comparison_id,
                source_file=source_file,
                target_file=target_file
            )
            
            # 解析文件
            source_data = self._parse_file(source_file)
            target_data = self._parse_file(target_file)
            
            if not source_data or not target_data:
                logger.error("文件解析失败")
                return result
            
            # 设置对比选项
            options = comparison_options or {}
            
            # 执行对比
            self._compare_headers(source_data, target_data, result, options)
            self._compare_ieds(source_data, target_data, result, options)
            self._compare_communication(source_data, target_data, result, options)
            self._compare_substation(source_data, target_data, result, options)
            self._compare_data_type_templates(source_data, target_data, result, options)
            
            # 分析差异影响
            self._analyze_diff_impacts(result)
            
            # 添加元数据
            result.metadata = {
                'source_file_size': Path(source_file).stat().st_size,
                'target_file_size': Path(target_file).stat().st_size,
                'comparison_options': options,
                'parser_version': self.parser.get_version() if hasattr(self.parser, 'get_version') else '1.0'
            }
            
            logger.info(f"文件对比完成: 发现 {len(result.diff_items)} 个差异")
            return result
            
        except Exception as e:
            logger.error(f"文件对比失败: {e}")
            return ComparisonResult(
                comparison_id="error",
                source_file=source_file,
                target_file=target_file
            )
    
    def compare_parsed_data(self,
                           source_data: Dict[str, Any],
                           target_data: Dict[str, Any],
                           source_name: str = "source",
                           target_name: str = "target") -> ComparisonResult:
        """
        对比已解析的数据
        
        Args:
            source_data: 源数据
            target_data: 目标数据
            source_name: 源名称
            target_name: 目标名称
            
        Returns:
            ComparisonResult: 对比结果
        """
        try:
            comparison_id = f"comp_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            result = ComparisonResult(
                comparison_id=comparison_id,
                source_file=source_name,
                target_file=target_name
            )
            
            # 执行对比
            self._compare_headers(source_data, target_data, result)
            self._compare_ieds(source_data, target_data, result)
            self._compare_communication(source_data, target_data, result)
            self._compare_substation(source_data, target_data, result)
            self._compare_data_type_templates(source_data, target_data, result)
            
            # 分析差异影响
            self._analyze_diff_impacts(result)
            
            return result
            
        except Exception as e:
            logger.error(f"数据对比失败: {e}")
            return ComparisonResult(
                comparison_id="error",
                source_file=source_name,
                target_file=target_name
            )
    
    def _parse_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """解析文件"""
        try:
            return self.parser.parse_file(file_path)
        except Exception as e:
            logger.error(f"文件解析失败 {file_path}: {e}")
            return None
    
    def _compare_headers(self,
                        source_data: Dict[str, Any],
                        target_data: Dict[str, Any],
                        result: ComparisonResult,
                        options: Dict[str, Any] = None) -> None:
        """对比文件头"""
        try:
            source_header = source_data.get('header', {})
            target_header = target_data.get('header', {})
            
            # 对比关键头部信息
            header_fields = ['version', 'revision', 'toolID', 'nameStructure']
            
            for field in header_fields:
                source_value = source_header.get(field)
                target_value = target_header.get(field)
                
                if source_value != target_value:
                    severity = DiffSeverity.MINOR
                    if field in ['version', 'nameStructure']:
                        severity = DiffSeverity.MAJOR
                    
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.MODIFIED,
                        category=DiffCategory.HEADER,
                        severity=severity,
                        path=f"Header/{field}",
                        description=f"文件头{field}不同",
                        old_value=source_value,
                        new_value=target_value
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"头部对比失败: {e}")
    
    def _compare_ieds(self,
                     source_data: Dict[str, Any],
                     target_data: Dict[str, Any],
                     result: ComparisonResult,
                     options: Dict[str, Any] = None) -> None:
        """对比IED设备"""
        try:
            source_ieds = {ied.get('name'): ied for ied in source_data.get('ieds', [])}
            target_ieds = {ied.get('name'): ied for ied in target_data.get('ieds', [])}
            
            # 查找新增的IED
            for ied_name in target_ieds:
                if ied_name not in source_ieds:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.IED,
                        severity=DiffSeverity.MAJOR,
                        path=f"IED/{ied_name}",
                        description=f"新增IED设备: {ied_name}",
                        new_value=target_ieds[ied_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的IED
            for ied_name in source_ieds:
                if ied_name not in target_ieds:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.IED,
                        severity=DiffSeverity.MAJOR,
                        path=f"IED/{ied_name}",
                        description=f"删除IED设备: {ied_name}",
                        old_value=source_ieds[ied_name]
                    )
                    result.add_diff(diff)
            
            # 对比共同的IED
            common_ieds = set(source_ieds.keys()) & set(target_ieds.keys())
            for ied_name in common_ieds:
                self._compare_ied_details(
                    source_ieds[ied_name],
                    target_ieds[ied_name],
                    ied_name,
                    result
                )
            
        except Exception as e:
            logger.error(f"IED对比失败: {e}")
    
    def _compare_ied_details(self,
                            source_ied: Dict[str, Any],
                            target_ied: Dict[str, Any],
                            ied_name: str,
                            result: ComparisonResult) -> None:
        """对比IED详细信息"""
        try:
            # 对比IED基本属性
            ied_fields = ['type', 'manufacturer', 'configVersion', 'desc']
            
            for field in ied_fields:
                source_value = source_ied.get(field)
                target_value = target_ied.get(field)
                
                if source_value != target_value:
                    severity = DiffSeverity.MINOR
                    if field in ['type', 'manufacturer']:
                        severity = DiffSeverity.MAJOR
                    
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.MODIFIED,
                        category=DiffCategory.IED,
                        severity=severity,
                        path=f"IED/{ied_name}/{field}",
                        description=f"IED {ied_name} 的 {field} 不同",
                        old_value=source_value,
                        new_value=target_value
                    )
                    result.add_diff(diff)
            
            # 对比逻辑设备
            self._compare_logical_devices(
                source_ied.get('logical_devices', []),
                target_ied.get('logical_devices', []),
                ied_name,
                result
            )
            
        except Exception as e:
            logger.error(f"IED详细对比失败: {e}")
    
    def _compare_logical_devices(self,
                               source_lds: List[Dict[str, Any]],
                               target_lds: List[Dict[str, Any]],
                               ied_name: str,
                               result: ComparisonResult) -> None:
        """对比逻辑设备"""
        try:
            source_ld_map = {ld.get('name'): ld for ld in source_lds}
            target_ld_map = {ld.get('name'): ld for ld in target_lds}
            
            # 查找新增的逻辑设备
            for ld_name in target_ld_map:
                if ld_name not in source_ld_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.LOGICAL_DEVICE,
                        severity=DiffSeverity.MAJOR,
                        path=f"IED/{ied_name}/LD/{ld_name}",
                        description=f"新增逻辑设备: {ied_name}/{ld_name}",
                        new_value=target_ld_map[ld_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的逻辑设备
            for ld_name in source_ld_map:
                if ld_name not in target_ld_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.LOGICAL_DEVICE,
                        severity=DiffSeverity.MAJOR,
                        path=f"IED/{ied_name}/LD/{ld_name}",
                        description=f"删除逻辑设备: {ied_name}/{ld_name}",
                        old_value=source_ld_map[ld_name]
                    )
                    result.add_diff(diff)
            
            # 对比共同的逻辑设备
            common_lds = set(source_ld_map.keys()) & set(target_ld_map.keys())
            for ld_name in common_lds:
                self._compare_logical_nodes(
                    source_ld_map[ld_name].get('logical_nodes', []),
                    target_ld_map[ld_name].get('logical_nodes', []),
                    f"{ied_name}/{ld_name}",
                    result
                )
            
        except Exception as e:
            logger.error(f"逻辑设备对比失败: {e}")
    
    def _compare_logical_nodes(self,
                             source_lns: List[Dict[str, Any]],
                             target_lns: List[Dict[str, Any]],
                             ld_path: str,
                             result: ComparisonResult) -> None:
        """对比逻辑节点"""
        try:
            source_ln_map = {ln.get('name'): ln for ln in source_lns}
            target_ln_map = {ln.get('name'): ln for ln in target_lns}
            
            # 查找新增的逻辑节点
            for ln_name in target_ln_map:
                if ln_name not in source_ln_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.LOGICAL_NODE,
                        severity=DiffSeverity.MINOR,
                        path=f"LD/{ld_path}/LN/{ln_name}",
                        description=f"新增逻辑节点: {ld_path}/{ln_name}",
                        new_value=target_ln_map[ln_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的逻辑节点
            for ln_name in source_ln_map:
                if ln_name not in target_ln_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.LOGICAL_NODE,
                        severity=DiffSeverity.MINOR,
                        path=f"LD/{ld_path}/LN/{ln_name}",
                        description=f"删除逻辑节点: {ld_path}/{ln_name}",
                        old_value=source_ln_map[ln_name]
                    )
                    result.add_diff(diff)
            
            # 对比共同的逻辑节点
            common_lns = set(source_ln_map.keys()) & set(target_ln_map.keys())
            for ln_name in common_lns:
                self._compare_data_objects(
                    source_ln_map[ln_name].get('data_objects', []),
                    target_ln_map[ln_name].get('data_objects', []),
                    f"{ld_path}/{ln_name}",
                    result
                )
            
        except Exception as e:
            logger.error(f"逻辑节点对比失败: {e}")
    
    def _compare_data_objects(self,
                            source_dos: List[Dict[str, Any]],
                            target_dos: List[Dict[str, Any]],
                            ln_path: str,
                            result: ComparisonResult) -> None:
        """对比数据对象"""
        try:
            source_do_map = {do.get('name'): do for do in source_dos}
            target_do_map = {do.get('name'): do for do in target_dos}
            
            # 查找新增的数据对象
            for do_name in target_do_map:
                if do_name not in source_do_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.DATA_OBJECT,
                        severity=DiffSeverity.MINOR,
                        path=f"LN/{ln_path}/DO/{do_name}",
                        description=f"新增数据对象: {ln_path}/{do_name}",
                        new_value=target_do_map[do_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的数据对象
            for do_name in source_do_map:
                if do_name not in target_do_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.DATA_OBJECT,
                        severity=DiffSeverity.MINOR,
                        path=f"LN/{ln_path}/DO/{do_name}",
                        description=f"删除数据对象: {ln_path}/{do_name}",
                        old_value=source_do_map[do_name]
                    )
                    result.add_diff(diff)
            
            # 对比共同的数据对象
            common_dos = set(source_do_map.keys()) & set(target_do_map.keys())
            for do_name in common_dos:
                self._compare_do_details(
                    source_do_map[do_name],
                    target_do_map[do_name],
                    f"{ln_path}/{do_name}",
                    result
                )
            
        except Exception as e:
            logger.error(f"数据对象对比失败: {e}")
    
    def _compare_do_details(self,
                          source_do: Dict[str, Any],
                          target_do: Dict[str, Any],
                          do_path: str,
                          result: ComparisonResult) -> None:
        """对比数据对象详细信息"""
        try:
            # 对比数据对象属性
            do_fields = ['type', 'fc', 'desc']
            
            for field in do_fields:
                source_value = source_do.get(field)
                target_value = target_do.get(field)
                
                if source_value != target_value:
                    severity = DiffSeverity.INFO
                    if field in ['type', 'fc']:
                        severity = DiffSeverity.MINOR
                    
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.MODIFIED,
                        category=DiffCategory.DATA_OBJECT,
                        severity=severity,
                        path=f"DO/{do_path}/{field}",
                        description=f"数据对象 {do_path} 的 {field} 不同",
                        old_value=source_value,
                        new_value=target_value
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"数据对象详细对比失败: {e}")
    
    def _compare_communication(self,
                             source_data: Dict[str, Any],
                             target_data: Dict[str, Any],
                             result: ComparisonResult,
                             options: Dict[str, Any] = None) -> None:
        """对比通信配置"""
        try:
            source_comm = source_data.get('communication', {})
            target_comm = target_data.get('communication', {})
            
            # 对比子网配置
            self._compare_subnets(
                source_comm.get('subnets', []),
                target_comm.get('subnets', []),
                result
            )
            
            # 对比GOOSE配置
            self._compare_goose_config(
                source_comm.get('goose', []),
                target_comm.get('goose', []),
                result
            )
            
            # 对比SMV配置
            self._compare_smv_config(
                source_comm.get('smv', []),
                target_comm.get('smv', []),
                result
            )
            
        except Exception as e:
            logger.error(f"通信配置对比失败: {e}")
    
    def _compare_subnets(self,
                        source_subnets: List[Dict[str, Any]],
                        target_subnets: List[Dict[str, Any]],
                        result: ComparisonResult) -> None:
        """对比子网配置"""
        try:
            source_subnet_map = {subnet.get('name'): subnet for subnet in source_subnets}
            target_subnet_map = {subnet.get('name'): subnet for subnet in target_subnets}
            
            # 查找新增的子网
            for subnet_name in target_subnet_map:
                if subnet_name not in source_subnet_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.COMMUNICATION,
                        severity=DiffSeverity.MAJOR,
                        path=f"Communication/Subnet/{subnet_name}",
                        description=f"新增子网: {subnet_name}",
                        new_value=target_subnet_map[subnet_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的子网
            for subnet_name in source_subnet_map:
                if subnet_name not in target_subnet_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.COMMUNICATION,
                        severity=DiffSeverity.MAJOR,
                        path=f"Communication/Subnet/{subnet_name}",
                        description=f"删除子网: {subnet_name}",
                        old_value=source_subnet_map[subnet_name]
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"子网对比失败: {e}")
    
    def _compare_goose_config(self,
                            source_goose: List[Dict[str, Any]],
                            target_goose: List[Dict[str, Any]],
                            result: ComparisonResult) -> None:
        """对比GOOSE配置"""
        try:
            source_goose_map = {goose.get('name'): goose for goose in source_goose}
            target_goose_map = {goose.get('name'): goose for goose in target_goose}
            
            # 查找新增的GOOSE
            for goose_name in target_goose_map:
                if goose_name not in source_goose_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.GOOSE,
                        severity=DiffSeverity.MAJOR,
                        path=f"Communication/GOOSE/{goose_name}",
                        description=f"新增GOOSE配置: {goose_name}",
                        new_value=target_goose_map[goose_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的GOOSE
            for goose_name in source_goose_map:
                if goose_name not in target_goose_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.GOOSE,
                        severity=DiffSeverity.MAJOR,
                        path=f"Communication/GOOSE/{goose_name}",
                        description=f"删除GOOSE配置: {goose_name}",
                        old_value=source_goose_map[goose_name]
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"GOOSE配置对比失败: {e}")
    
    def _compare_smv_config(self,
                          source_smv: List[Dict[str, Any]],
                          target_smv: List[Dict[str, Any]],
                          result: ComparisonResult) -> None:
        """对比SMV配置"""
        try:
            source_smv_map = {smv.get('name'): smv for smv in source_smv}
            target_smv_map = {smv.get('name'): smv for smv in target_smv}
            
            # 查找新增的SMV
            for smv_name in target_smv_map:
                if smv_name not in source_smv_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.SMV,
                        severity=DiffSeverity.MAJOR,
                        path=f"Communication/SMV/{smv_name}",
                        description=f"新增SMV配置: {smv_name}",
                        new_value=target_smv_map[smv_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的SMV
            for smv_name in source_smv_map:
                if smv_name not in target_smv_map:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.SMV,
                        severity=DiffSeverity.MAJOR,
                        path=f"Communication/SMV/{smv_name}",
                        description=f"删除SMV配置: {smv_name}",
                        old_value=source_smv_map[smv_name]
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"SMV配置对比失败: {e}")
    
    def _compare_substation(self,
                          source_data: Dict[str, Any],
                          target_data: Dict[str, Any],
                          result: ComparisonResult,
                          options: Dict[str, Any] = None) -> None:
        """对比变电站配置"""
        try:
            source_substation = source_data.get('substation', {})
            target_substation = target_data.get('substation', {})
            
            # 对比变电站基本信息
            substation_fields = ['name', 'desc']
            
            for field in substation_fields:
                source_value = source_substation.get(field)
                target_value = target_substation.get(field)
                
                if source_value != target_value:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.MODIFIED,
                        category=DiffCategory.SUBSTATION,
                        severity=DiffSeverity.MINOR,
                        path=f"Substation/{field}",
                        description=f"变电站{field}不同",
                        old_value=source_value,
                        new_value=target_value
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"变电站配置对比失败: {e}")
    
    def _compare_data_type_templates(self,
                                   source_data: Dict[str, Any],
                                   target_data: Dict[str, Any],
                                   result: ComparisonResult,
                                   options: Dict[str, Any] = None) -> None:
        """对比数据类型模板"""
        try:
            source_templates = source_data.get('data_type_templates', {})
            target_templates = target_data.get('data_type_templates', {})
            
            # 查找新增的模板
            for template_name in target_templates:
                if template_name not in source_templates:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.ADDED,
                        category=DiffCategory.DATA_TYPE_TEMPLATE,
                        severity=DiffSeverity.MINOR,
                        path=f"DataTypeTemplates/{template_name}",
                        description=f"新增数据类型模板: {template_name}",
                        new_value=target_templates[template_name]
                    )
                    result.add_diff(diff)
            
            # 查找删除的模板
            for template_name in source_templates:
                if template_name not in target_templates:
                    diff = DiffItem(
                        diff_id="",
                        diff_type=DiffType.REMOVED,
                        category=DiffCategory.DATA_TYPE_TEMPLATE,
                        severity=DiffSeverity.MINOR,
                        path=f"DataTypeTemplates/{template_name}",
                        description=f"删除数据类型模板: {template_name}",
                        old_value=source_templates[template_name]
                    )
                    result.add_diff(diff)
            
        except Exception as e:
            logger.error(f"数据类型模板对比失败: {e}")
    
    def _analyze_diff_impacts(self, result: ComparisonResult) -> None:
        """分析差异影响"""
        try:
            for diff in result.diff_items:
                # 分析结构性变更的影响
                if diff.is_structural_change():
                    if diff.diff_type == DiffType.REMOVED:
                        diff.impact_analysis = "删除操作可能导致相关功能失效"
                        diff.recommendations.append("检查是否有其他组件依赖此项")
                    elif diff.diff_type == DiffType.ADDED:
                        diff.impact_analysis = "新增项可能需要相应的配置调整"
                        diff.recommendations.append("确认新增项的配置正确性")
                
                # 分析配置变更的影响
                if diff.is_configuration_change():
                    if diff.category == DiffCategory.COMMUNICATION:
                        diff.impact_analysis = "通信配置变更可能影响设备间通信"
                        diff.recommendations.append("验证通信连接的正确性")
                    elif diff.category == DiffCategory.GOOSE:
                        diff.impact_analysis = "GOOSE配置变更可能影响快速信息传输"
                        diff.recommendations.append("测试GOOSE消息的发布和订阅")
                
                # 根据严重程度添加通用建议
                if diff.severity == DiffSeverity.CRITICAL:
                    diff.recommendations.append("立即处理此关键差异")
                elif diff.severity == DiffSeverity.MAJOR:
                    diff.recommendations.append("优先处理此重要差异")
            
        except Exception as e:
            logger.error(f"差异影响分析失败: {e}")
    
    def _load_comparison_rules(self) -> Dict[str, Any]:
        """加载对比规则"""
        return {
            'ignore_fields': ['timestamp', 'lastModified'],
            'critical_fields': ['name', 'type', 'manufacturer'],
            'severity_mapping': {
                'header': DiffSeverity.MINOR,
                'ied': DiffSeverity.MAJOR,
                'communication': DiffSeverity.MAJOR,
                'data_object': DiffSeverity.MINOR
            }
        }
