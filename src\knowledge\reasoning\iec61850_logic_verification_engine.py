"""
IEC61850逻辑节点一致性与复杂回路逻辑关系验证引擎
专注于LD/LN/DO/DA结构验证、通信映射验证、GOOSE链路检查
基于规程规范的各种回路间复杂逻辑关系深度验证
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class LogicalNodeType(Enum):
    """逻辑节点类型"""
    XCBR = "断路器"
    XSWI = "开关"
    CSWI = "控制开关"
    CILO = "联锁"
    PTRC = "保护跳闸条件"
    PTOC = "过电流保护"
    PDIF = "差动保护"
    PDIS = "距离保护"
    MMXU = "测量"
    TCTR = "电流互感器"
    TVTR = "电压互感器"


class DataAttributeType(Enum):
    """数据属性类型"""
    ST = "状态"
    MX = "测量值"
    SP = "设定点"
    CF = "配置"
    DC = "描述"
    EX = "扩展"


@dataclass
class LogicalNode:
    """逻辑节点"""
    ld_name: str  # 逻辑设备名称
    ln_class: str  # 逻辑节点类别
    ln_inst: str  # 逻辑节点实例
    ln_prefix: str  # 逻辑节点前缀
    data_objects: Dict[str, 'DataObject']
    
    @property
    def full_reference(self) -> str:
        """完整引用路径"""
        return f"{self.ld_name}/{self.ln_prefix}{self.ln_class}{self.ln_inst}"


@dataclass
class DataObject:
    """数据对象"""
    name: str
    cdc: str  # 通用数据类别
    data_attributes: Dict[str, 'DataAttribute']
    functional_constraint: str


@dataclass
class DataAttribute:
    """数据属性"""
    name: str
    attribute_type: str
    value: Any
    functional_constraint: str
    trigger_options: List[str]


@dataclass
class GOOSEDataSet:
    """GOOSE数据集"""
    dataset_name: str
    logical_device: str
    data_set_members: List[str]  # 数据集成员引用
    goose_control_block: 'GOOSEControlBlock'


@dataclass
class GOOSEControlBlock:
    """GOOSE控制块"""
    name: str
    dataset_ref: str
    conf_rev: int
    app_id: str
    go_id: str
    multicast_address: str
    vlan_id: int
    vlan_priority: int
    min_time: int  # ms
    max_time: int  # ms


@dataclass
class SMVControlBlock:
    """SMV控制块"""
    name: str
    dataset_ref: str
    smp_rate: int  # 采样率
    no_asdu: int  # ASDU数量
    multicast_address: str
    vlan_id: int
    vlan_priority: int


@dataclass
class VirtualTerminalConnection:
    """虚端子连接"""
    source_reference: str  # 源数据引用
    sink_ied: str  # 目标IED
    sink_reference: str  # 目标数据引用
    connection_type: str  # GOOSE/SMV
    logical_relationship: str  # 逻辑关系描述


class IEC61850LogicVerificationEngine:
    """IEC61850逻辑节点一致性与复杂回路逻辑关系验证引擎"""
    
    def __init__(self):
        """初始化验证引擎"""
        self.standard_logical_nodes = self._build_standard_logical_nodes()
        self.circuit_logic_patterns = self._build_circuit_logic_patterns()
        self.goose_link_rules = self._build_goose_link_rules()
        self.regulation_logic_requirements = self._build_regulation_logic_requirements()
        
        logger.info("IEC61850逻辑验证引擎初始化完成")
    
    def _build_standard_logical_nodes(self) -> Dict[str, Any]:
        """构建标准逻辑节点定义"""
        return {
            'XCBR': {
                'description': '断路器逻辑节点',
                'mandatory_data_objects': {
                    'Pos': {
                        'cdc': 'DPC',
                        'description': '断路器位置',
                        'mandatory_attributes': ['stVal', 'q', 't', 'ctlVal', 'origin', 'ctlNum', 'T', 'Test', 'Check'],
                        'functional_constraints': {
                            'stVal': 'ST',
                            'q': 'ST', 
                            't': 'ST',
                            'ctlVal': 'CO',
                            'origin': 'CO',
                            'ctlNum': 'CO'
                        }
                    },
                    'BlkOpn': {
                        'cdc': 'SPS',
                        'description': '分闸闭锁',
                        'mandatory_attributes': ['stVal', 'q', 't'],
                        'functional_constraints': {
                            'stVal': 'ST',
                            'q': 'ST',
                            't': 'ST'
                        }
                    },
                    'BlkCls': {
                        'cdc': 'SPS', 
                        'description': '合闸闭锁',
                        'mandatory_attributes': ['stVal', 'q', 't'],
                        'functional_constraints': {
                            'stVal': 'ST',
                            'q': 'ST',
                            't': 'ST'
                        }
                    }
                },
                'optional_data_objects': {
                    'CBOpCap': {
                        'cdc': 'INS',
                        'description': '断路器操作能力'
                    },
                    'POWCap': {
                        'cdc': 'INS',
                        'description': '断路器功率能力'
                    }
                }
            },
            'CSWI': {
                'description': '控制开关逻辑节点',
                'mandatory_data_objects': {
                    'Pos': {
                        'cdc': 'DPC',
                        'description': '开关位置控制',
                        'mandatory_attributes': ['stVal', 'q', 't', 'ctlVal', 'origin', 'ctlNum'],
                        'control_model': 'direct-with-normal-security'
                    }
                }
            },
            'CILO': {
                'description': '联锁逻辑节点',
                'mandatory_data_objects': {
                    'EnaOpn': {
                        'cdc': 'SPS',
                        'description': '允许分闸',
                        'mandatory_attributes': ['stVal', 'q', 't']
                    },
                    'EnaCls': {
                        'cdc': 'SPS',
                        'description': '允许合闸', 
                        'mandatory_attributes': ['stVal', 'q', 't']
                    }
                }
            },
            'PTRC': {
                'description': '保护跳闸条件逻辑节点',
                'mandatory_data_objects': {
                    'Tr': {
                        'cdc': 'ACD',
                        'description': '跳闸',
                        'mandatory_attributes': ['general', 'dirGeneral', 'q', 't'],
                        'functional_constraints': {
                            'general': 'ST',
                            'dirGeneral': 'ST',
                            'q': 'ST',
                            't': 'ST'
                        }
                    },
                    'Str': {
                        'cdc': 'ACD',
                        'description': '启动',
                        'mandatory_attributes': ['general', 'dirGeneral', 'q', 't']
                    }
                }
            },
            'PTOC': {
                'description': '过电流保护逻辑节点',
                'mandatory_data_objects': {
                    'Str': {
                        'cdc': 'ACD',
                        'description': '启动',
                        'mandatory_attributes': ['general', 'dirGeneral', 'phsA', 'phsB', 'phsC', 'neut', 'q', 't']
                    },
                    'Op': {
                        'cdc': 'ACT',
                        'description': '动作',
                        'mandatory_attributes': ['general', 'q', 't']
                    }
                }
            },
            'MMXU': {
                'description': '测量逻辑节点',
                'mandatory_data_objects': {
                    'TotW': {
                        'cdc': 'MV',
                        'description': '总有功功率',
                        'mandatory_attributes': ['mag', 'q', 't', 'units']
                    },
                    'TotVAr': {
                        'cdc': 'MV',
                        'description': '总无功功率',
                        'mandatory_attributes': ['mag', 'q', 't', 'units']
                    },
                    'Hz': {
                        'cdc': 'MV',
                        'description': '频率',
                        'mandatory_attributes': ['mag', 'q', 't', 'units']
                    },
                    'PPV': {
                        'cdc': 'WYE',
                        'description': '相间电压',
                        'mandatory_attributes': ['phsAB', 'phsBC', 'phsCA']
                    },
                    'PhV': {
                        'cdc': 'WYE', 
                        'description': '相电压',
                        'mandatory_attributes': ['phsA', 'phsB', 'phsC', 'neut']
                    },
                    'A': {
                        'cdc': 'WYE',
                        'description': '电流',
                        'mandatory_attributes': ['phsA', 'phsB', 'phsC', 'neut']
                    }
                }
            }
        }
    
    def _build_circuit_logic_patterns(self) -> Dict[str, Any]:
        """构建回路逻辑模式"""
        return {
            'protection_trip_logic': {
                'description': '保护跳闸逻辑',
                'pattern_type': 'protection_control_coordination',
                'logical_flow': [
                    {
                        'step': 1,
                        'description': '保护启动检测',
                        'logical_nodes': ['PTOC', 'PDIF', 'PDIS'],
                        'data_objects': ['Str.general'],
                        'logic_condition': 'ANY protection Str.general == TRUE'
                    },
                    {
                        'step': 2,
                        'description': '保护跳闸判断',
                        'logical_nodes': ['PTOC', 'PDIF', 'PDIS'],
                        'data_objects': ['Op.general'],
                        'logic_condition': 'protection_timer_expired AND fault_persists'
                    },
                    {
                        'step': 3,
                        'description': '跳闸条件输出',
                        'logical_nodes': ['PTRC'],
                        'data_objects': ['Tr.general'],
                        'logic_condition': 'PTRC.Tr.general = OR(PTOC.Op.general, PDIF.Op.general, PDIS.Op.general)'
                    },
                    {
                        'step': 4,
                        'description': '联锁检查',
                        'logical_nodes': ['CILO'],
                        'data_objects': ['EnaOpn.stVal'],
                        'logic_condition': 'CILO.EnaOpn.stVal == TRUE'
                    },
                    {
                        'step': 5,
                        'description': '断路器跳闸执行',
                        'logical_nodes': ['CSWI', 'XCBR'],
                        'data_objects': ['Pos.ctlVal'],
                        'logic_condition': 'CSWI.Pos.ctlVal = OPEN AND CILO.EnaOpn.stVal == TRUE'
                    }
                ],
                'goose_mappings': [
                    {
                        'source': 'ProtectionIED/PROTECTION/PTRC1.Tr.general',
                        'destination': 'ControlIED/CONTROL/CSWI1.Pos',
                        'mapping_type': 'trip_signal',
                        'timing_requirement': '≤4ms'
                    }
                ],
                'regulation_requirements': {
                    'gb14285': [
                        '保护动作应可靠，动作时间≤100ms',
                        '跳闸回路应有完整性监视',
                        '应具备防误操作功能'
                    ],
                    'dl5136': [
                        '重要断路器应有双跳闸回路',
                        '跳闸回路应有冗余配置',
                        '控制回路应有明确的操作逻辑'
                    ]
                }
            },
            'interlocking_logic': {
                'description': '五防联锁逻辑',
                'pattern_type': 'safety_interlocking',
                'logical_flow': [
                    {
                        'step': 1,
                        'description': '设备状态检查',
                        'logical_nodes': ['XCBR', 'XSWI'],
                        'data_objects': ['Pos.stVal'],
                        'logic_condition': 'collect_all_equipment_positions'
                    },
                    {
                        'step': 2,
                        'description': '联锁条件计算',
                        'logical_nodes': ['CILO'],
                        'data_objects': ['EnaOpn.stVal', 'EnaCls.stVal'],
                        'logic_condition': 'apply_five_prevention_rules'
                    },
                    {
                        'step': 3,
                        'description': '操作权限输出',
                        'logical_nodes': ['CILO'],
                        'data_objects': ['EnaOpn.stVal', 'EnaCls.stVal'],
                        'logic_condition': 'enable_operation_based_on_interlocking'
                    }
                ],
                'five_prevention_rules': {
                    'rule_1': {
                        'description': '防止误分、误合断路器',
                        'logic': 'CILO.EnaOpn.stVal = NOT(maintenance_mode OR local_control_active)',
                        'affected_ln': ['XCBR', 'CSWI']
                    },
                    'rule_2': {
                        'description': '防止带负荷分、合隔离开关',
                        'logic': 'XSWI.CILO.EnaOpn.stVal = XCBR.Pos.stVal == OPEN',
                        'affected_ln': ['XSWI', 'XCBR']
                    },
                    'rule_3': {
                        'description': '防止带电挂接地线或合接地开关',
                        'logic': 'XSWI_ES.CILO.EnaCls.stVal = XSWI.Pos.stVal == OPEN',
                        'affected_ln': ['XSWI', 'XSWI_ES']
                    },
                    'rule_4': {
                        'description': '防止带接地线送电',
                        'logic': 'XCBR.CILO.EnaCls.stVal = XSWI_ES.Pos.stVal == OPEN',
                        'affected_ln': ['XCBR', 'XSWI_ES']
                    },
                    'rule_5': {
                        'description': '防止误入带电间隔',
                        'logic': 'DOOR_LOCK.EnaOpn.stVal = ALL_EQUIPMENT.Pos.stVal == SAFE_POSITION',
                        'affected_ln': ['XCBR', 'XSWI', 'XSWI_ES']
                    }
                }
            },
            'measurement_protection_coordination': {
                'description': '测量保护协调逻辑',
                'pattern_type': 'measurement_protection_interface',
                'logical_flow': [
                    {
                        'step': 1,
                        'description': 'CT/PT采样值获取',
                        'logical_nodes': ['TCTR', 'TVTR'],
                        'data_objects': ['Amp.instMag', 'Vol.instMag'],
                        'smv_mapping': 'high_speed_sampling_4000Hz'
                    },
                    {
                        'step': 2,
                        'description': '测量值计算',
                        'logical_nodes': ['MMXU'],
                        'data_objects': ['A.phsA.cVal', 'PhV.phsA.cVal'],
                        'logic_condition': 'calculate_rms_values_from_samples'
                    },
                    {
                        'step': 3,
                        'description': '保护判断',
                        'logical_nodes': ['PTOC', 'PDIF'],
                        'data_objects': ['Str.general', 'Op.general'],
                        'logic_condition': 'compare_with_protection_settings'
                    }
                ],
                'smv_requirements': {
                    'sampling_rate': '4000Hz for 50Hz system',
                    'synchronization': 'IEEE 1588 PTP, ±1μs accuracy',
                    'data_integrity': 'CRC check + sequence number'
                }
            }
        }
    
    def _build_goose_link_rules(self) -> Dict[str, Any]:
        """构建GOOSE链路规则"""
        return {
            'trip_signal_goose': {
                'signal_type': 'protection_trip',
                'source_pattern': r'.*/(PTRC\d*|PTOC\d*|PDIF\d*|PDIS\d*)\.Tr\.general',
                'sink_pattern': r'.*/(CSWI\d*|XCBR\d*)\.Pos',
                'timing_requirements': {
                    'type1a_message': '≤4ms',
                    'retransmission': 'immediate on change',
                    'heartbeat': 'configurable, typically 1s'
                },
                'network_requirements': {
                    'vlan_priority': 7,
                    'multicast_filtering': 'enabled',
                    'redundancy': 'dual_network_required'
                },
                'verification_rules': [
                    'source_ln_must_be_protection_type',
                    'sink_ln_must_be_control_type',
                    'signal_path_must_be_continuous',
                    'timing_budget_must_be_satisfied',
                    'network_redundancy_must_be_configured'
                ]
            },
            'interlocking_goose': {
                'signal_type': 'interlocking_status',
                'source_pattern': r'.*/CILO\d*\.(EnaOpn|EnaCls)\.stVal',
                'sink_pattern': r'.*/(CSWI\d*|XCBR\d*)\.BlkOpn|BlkCls',
                'timing_requirements': {
                    'type1a_message': '≤4ms',
                    'consistency_check': 'required'
                },
                'logic_verification': [
                    'interlocking_matrix_consistency',
                    'circular_dependency_check',
                    'safety_logic_completeness'
                ]
            },
            'status_information_goose': {
                'signal_type': 'equipment_status',
                'source_pattern': r'.*/(XCBR\d*|XSWI\d*)\.Pos\.stVal',
                'sink_pattern': r'.*/CILO\d*\..*',
                'timing_requirements': {
                    'type1a_message': '≤100ms',
                    'status_consistency': 'required'
                }
            }
        }
    
    def _build_regulation_logic_requirements(self) -> Dict[str, Any]:
        """构建规程逻辑要求"""
        return {
            'gb14285_logic_requirements': {
                'protection_selectivity': {
                    'requirement': '保护装置应仅切除故障元件',
                    'iec61850_implementation': {
                        'logical_selectivity': 'GOOSE通信实现快速选择性跳闸',
                        'time_coordination': '通过GOOSE消息时序实现时间配合',
                        'zone_coordination': '通过逻辑节点数据对象实现区间配合'
                    },
                    'verification_logic': [
                        'check_protection_zone_coverage',
                        'verify_backup_protection_coordination',
                        'validate_communication_based_selectivity'
                    ]
                },
                'protection_speed': {
                    'requirement': '保护装置应快速切除故障',
                    'iec61850_implementation': {
                        'goose_timing': 'GOOSE消息传输时间≤4ms',
                        'processing_time': '逻辑节点处理时间≤10ms',
                        'total_time': '故障检测到断路器动作≤100ms'
                    }
                }
            },
            'dl5136_logic_requirements': {
                'five_prevention_logic': {
                    'requirement': '实现完整的五防功能',
                    'iec61850_implementation': {
                        'interlocking_ln': 'CILO逻辑节点实现联锁逻辑',
                        'status_collection': 'GOOSE收集所有设备状态',
                        'permission_distribution': 'GOOSE分发操作权限'
                    },
                    'logic_verification': [
                        'verify_all_five_prevention_rules',
                        'check_interlocking_completeness',
                        'validate_safety_logic_correctness'
                    ]
                },
                'redundancy_requirements': {
                    'requirement': '重要回路应有冗余配置',
                    'iec61850_implementation': {
                        'dual_goose_paths': '关键GOOSE消息双路径传输',
                        'backup_communication': '备用通信路径配置',
                        'failover_logic': '通信故障时的切换逻辑'
                    }
                }
            }
        }
    
    def verify_ld_ln_do_da_structure(self, ied_configuration: Dict[str, Any]) -> Dict[str, Any]:
        """验证LD/LN/DO/DA结构一致性"""
        
        verification_results = {
            'structure_compliance': [],
            'data_model_consistency': [],
            'functional_constraint_validation': [],
            'mandatory_elements_check': [],
            'overall_assessment': None
        }
        
        # 验证逻辑设备结构
        for ld_name, ld_config in ied_configuration.get('logical_devices', {}).items():
            ld_verification = self._verify_logical_device_structure(ld_name, ld_config)
            verification_results['structure_compliance'].append(ld_verification)
            
            # 验证逻辑节点
            for ln_config in ld_config.get('logical_nodes', []):
                ln_verification = self._verify_logical_node_structure(ln_config)
                verification_results['data_model_consistency'].append(ln_verification)
                
                # 验证数据对象和数据属性
                do_da_verification = self._verify_do_da_structure(ln_config)
                verification_results['functional_constraint_validation'].extend(do_da_verification)
        
        # 检查强制元素
        mandatory_check = self._check_mandatory_elements(ied_configuration)
        verification_results['mandatory_elements_check'] = mandatory_check
        
        # 综合评估
        verification_results['overall_assessment'] = self._assess_structure_compliance(verification_results)
        
        return verification_results
    
    def _verify_logical_device_structure(self, ld_name: str, ld_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证逻辑设备结构"""
        
        issues = []
        
        # 检查LD命名规范
        if not self._validate_ld_naming(ld_name):
            issues.append({
                'type': 'ld_naming_violation',
                'severity': 'medium',
                'description': f'逻辑设备名称 {ld_name} 不符合IEC61850命名规范',
                'standard_reference': 'IEC 61850-7-1 逻辑设备命名规则'
            })
        
        # 检查必要的逻辑节点
        required_lns = self._get_required_logical_nodes(ld_config.get('device_type'))
        configured_lns = [ln.get('ln_class') for ln in ld_config.get('logical_nodes', [])]
        
        for required_ln in required_lns:
            if required_ln not in configured_lns:
                issues.append({
                    'type': 'missing_mandatory_ln',
                    'severity': 'high',
                    'description': f'缺少必要的逻辑节点 {required_ln}',
                    'standard_reference': f'IEC 61850-7-4 {required_ln} 逻辑节点要求'
                })
        
        return {
            'ld_name': ld_name,
            'verification_status': 'passed' if not issues else 'failed',
            'issues': issues
        }
    
    def _verify_logical_node_structure(self, ln_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证逻辑节点结构"""
        
        ln_class = ln_config.get('ln_class')
        issues = []
        
        if ln_class in self.standard_logical_nodes:
            standard_ln = self.standard_logical_nodes[ln_class]
            
            # 检查强制数据对象
            mandatory_dos = standard_ln.get('mandatory_data_objects', {})
            configured_dos = {do.get('name'): do for do in ln_config.get('data_objects', [])}
            
            for do_name, do_spec in mandatory_dos.items():
                if do_name not in configured_dos:
                    issues.append({
                        'type': 'missing_mandatory_do',
                        'severity': 'high',
                        'description': f'逻辑节点 {ln_class} 缺少强制数据对象 {do_name}',
                        'standard_reference': f'IEC 61850-7-4 {ln_class}.{do_name}'
                    })
                else:
                    # 验证数据对象的CDC
                    configured_do = configured_dos[do_name]
                    if configured_do.get('cdc') != do_spec.get('cdc'):
                        issues.append({
                            'type': 'incorrect_cdc',
                            'severity': 'high',
                            'description': f'{ln_class}.{do_name} CDC应为 {do_spec.get("cdc")}，实际为 {configured_do.get("cdc")}',
                            'standard_reference': f'IEC 61850-7-3 CDC定义'
                        })
        
        return {
            'ln_reference': f"{ln_config.get('ln_prefix', '')}{ln_class}{ln_config.get('ln_inst', '')}",
            'verification_status': 'passed' if not issues else 'failed',
            'issues': issues
        }
    
    def _verify_do_da_structure(self, ln_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """验证数据对象和数据属性结构"""
        
        verification_results = []
        ln_class = ln_config.get('ln_class')
        
        if ln_class in self.standard_logical_nodes:
            standard_ln = self.standard_logical_nodes[ln_class]
            
            for do_config in ln_config.get('data_objects', []):
                do_name = do_config.get('name')
                
                if do_name in standard_ln.get('mandatory_data_objects', {}):
                    do_spec = standard_ln['mandatory_data_objects'][do_name]
                    
                    # 验证强制属性
                    issues = []
                    mandatory_attrs = do_spec.get('mandatory_attributes', [])
                    configured_attrs = [da.get('name') for da in do_config.get('data_attributes', [])]
                    
                    for attr_name in mandatory_attrs:
                        if attr_name not in configured_attrs:
                            issues.append({
                                'type': 'missing_mandatory_da',
                                'severity': 'high',
                                'description': f'{ln_class}.{do_name} 缺少强制属性 {attr_name}',
                                'standard_reference': f'IEC 61850-7-3 CDC {do_spec.get("cdc")}'
                            })
                    
                    # 验证功能约束
                    fc_spec = do_spec.get('functional_constraints', {})
                    for da_config in do_config.get('data_attributes', []):
                        da_name = da_config.get('name')
                        if da_name in fc_spec:
                            expected_fc = fc_spec[da_name]
                            actual_fc = da_config.get('functional_constraint')
                            if actual_fc != expected_fc:
                                issues.append({
                                    'type': 'incorrect_functional_constraint',
                                    'severity': 'medium',
                                    'description': f'{ln_class}.{do_name}.{da_name} 功能约束应为 {expected_fc}，实际为 {actual_fc}',
                                    'standard_reference': 'IEC 61850-7-2 功能约束定义'
                                })
                    
                    verification_results.append({
                        'do_reference': f"{ln_class}.{do_name}",
                        'verification_status': 'passed' if not issues else 'failed',
                        'issues': issues
                    })
        
        return verification_results
    
    def verify_communication_mapping(self, communication_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证GSE/SMV配置"""
        
        verification_results = {
            'goose_configuration': [],
            'smv_configuration': [],
            'network_configuration': [],
            'performance_validation': [],
            'overall_assessment': None
        }
        
        # 验证GOOSE配置
        goose_configs = communication_config.get('goose_configurations', [])
        for goose_config in goose_configs:
            goose_verification = self._verify_goose_configuration(goose_config)
            verification_results['goose_configuration'].append(goose_verification)
        
        # 验证SMV配置
        smv_configs = communication_config.get('smv_configurations', [])
        for smv_config in smv_configs:
            smv_verification = self._verify_smv_configuration(smv_config)
            verification_results['smv_configuration'].append(smv_verification)
        
        # 验证网络配置
        network_verification = self._verify_network_configuration(communication_config)
        verification_results['network_configuration'] = network_verification
        
        # 性能验证
        performance_verification = self._verify_performance_requirements(communication_config)
        verification_results['performance_validation'] = performance_verification
        
        # 综合评估
        verification_results['overall_assessment'] = self._assess_communication_compliance(verification_results)
        
        return verification_results
    
    def _verify_goose_configuration(self, goose_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证GOOSE配置"""
        
        issues = []
        
        # 检查数据集引用
        dataset_ref = goose_config.get('dataset_ref')
        if not dataset_ref:
            issues.append({
                'type': 'missing_dataset_reference',
                'severity': 'high',
                'description': 'GOOSE控制块缺少数据集引用',
                'standard_reference': 'IEC 61850-7-2 GOOSE控制块要求'
            })
        
        # 检查网络参数
        multicast_addr = goose_config.get('multicast_address')
        if not self._validate_multicast_address(multicast_addr):
            issues.append({
                'type': 'invalid_multicast_address',
                'severity': 'high',
                'description': f'无效的组播地址: {multicast_addr}',
                'standard_reference': 'IEC 61850-8-1 网络配置要求'
            })
        
        # 检查VLAN配置
        vlan_priority = goose_config.get('vlan_priority')
        if vlan_priority is None or vlan_priority < 4:
            issues.append({
                'type': 'insufficient_vlan_priority',
                'severity': 'medium',
                'description': f'GOOSE消息VLAN优先级过低: {vlan_priority}，建议≥4',
                'standard_reference': 'IEC 61850-90-4 网络工程指南'
            })
        
        # 检查时间参数
        min_time = goose_config.get('min_time', 0)
        max_time = goose_config.get('max_time', 0)
        
        if min_time > 4:  # ms
            issues.append({
                'type': 'excessive_min_time',
                'severity': 'high',
                'description': f'GOOSE最小发送时间过长: {min_time}ms > 4ms',
                'standard_reference': 'IEC 61850-5 性能要求'
            })
        
        return {
            'goose_id': goose_config.get('go_id'),
            'verification_status': 'passed' if not issues else 'failed',
            'issues': issues
        }
    
    def _verify_smv_configuration(self, smv_config: Dict[str, Any]) -> Dict[str, Any]:
        """验证SMV配置"""
        
        issues = []
        
        # 检查采样率
        smp_rate = smv_config.get('smp_rate')
        if smp_rate not in [4000, 4800, 12800]:  # 标准采样率
            issues.append({
                'type': 'non_standard_sampling_rate',
                'severity': 'medium',
                'description': f'非标准采样率: {smp_rate}Hz',
                'standard_reference': 'IEC 61850-9-2 采样值传输'
            })
        
        # 检查ASDU数量
        no_asdu = smv_config.get('no_asdu', 1)
        if no_asdu > 8:
            issues.append({
                'type': 'excessive_asdu_count',
                'severity': 'medium',
                'description': f'ASDU数量过多: {no_asdu} > 8',
                'standard_reference': 'IEC 61850-9-2LE 实现指南'
            })
        
        return {
            'smv_id': smv_config.get('smv_id'),
            'verification_status': 'passed' if not issues else 'failed',
            'issues': issues
        }
    
    def verify_goose_virtual_terminal_connections(self, virtual_connections: List[VirtualTerminalConnection]) -> Dict[str, Any]:
        """验证GOOSE虚端子连接关系"""
        
        verification_results = {
            'connection_validity': [],
            'logical_consistency': [],
            'timing_analysis': [],
            'redundancy_check': [],
            'regulation_compliance': [],
            'overall_assessment': None
        }
        
        for connection in virtual_connections:
            # 验证连接有效性
            validity_check = self._verify_connection_validity(connection)
            verification_results['connection_validity'].append(validity_check)
            
            # 验证逻辑一致性
            logic_check = self._verify_connection_logic(connection)
            verification_results['logical_consistency'].append(logic_check)
            
            # 时序分析
            timing_check = self._analyze_connection_timing(connection)
            verification_results['timing_analysis'].append(timing_check)
        
        # 冗余检查
        redundancy_analysis = self._analyze_connection_redundancy(virtual_connections)
        verification_results['redundancy_check'] = redundancy_analysis
        
        # 规程合规性检查
        regulation_check = self._check_connection_regulation_compliance(virtual_connections)
        verification_results['regulation_compliance'] = regulation_check
        
        # 综合评估
        verification_results['overall_assessment'] = self._assess_connection_compliance(verification_results)
        
        return verification_results
    
    def _verify_connection_validity(self, connection: VirtualTerminalConnection) -> Dict[str, Any]:
        """验证连接有效性"""
        
        issues = []
        
        # 检查源引用格式
        if not self._validate_object_reference(connection.source_reference):
            issues.append({
                'type': 'invalid_source_reference',
                'severity': 'high',
                'description': f'无效的源数据引用格式: {connection.source_reference}',
                'standard_reference': 'IEC 61850-7-1 对象引用格式'
            })
        
        # 检查目标引用格式
        if not self._validate_object_reference(connection.sink_reference):
            issues.append({
                'type': 'invalid_sink_reference',
                'severity': 'high',
                'description': f'无效的目标数据引用格式: {connection.sink_reference}',
                'standard_reference': 'IEC 61850-7-1 对象引用格式'
            })
        
        # 检查连接类型
        if connection.connection_type not in ['GOOSE', 'SMV']:
            issues.append({
                'type': 'invalid_connection_type',
                'severity': 'high',
                'description': f'无效的连接类型: {connection.connection_type}',
                'standard_reference': 'IEC 61850-8-1 通信服务'
            })
        
        return {
            'connection_id': f"{connection.source_reference} -> {connection.sink_reference}",
            'verification_status': 'passed' if not issues else 'failed',
            'issues': issues
        }
    
    def _verify_connection_logic(self, connection: VirtualTerminalConnection) -> Dict[str, Any]:
        """验证连接逻辑一致性"""
        
        issues = []
        
        # 解析源和目标逻辑节点类型
        source_ln_class = self._extract_ln_class_from_reference(connection.source_reference)
        sink_ln_class = self._extract_ln_class_from_reference(connection.sink_reference)
        
        # 检查逻辑关系合理性
        if not self._validate_logical_relationship(source_ln_class, sink_ln_class, connection.logical_relationship):
            issues.append({
                'type': 'invalid_logical_relationship',
                'severity': 'high',
                'description': f'不合理的逻辑关系: {source_ln_class} -> {sink_ln_class} ({connection.logical_relationship})',
                'standard_reference': '电气二次回路逻辑设计规范'
            })
        
        # 检查信号类型匹配
        source_signal_type = self._get_signal_type_from_reference(connection.source_reference)
        sink_signal_type = self._get_signal_type_from_reference(connection.sink_reference)
        
        if not self._validate_signal_type_compatibility(source_signal_type, sink_signal_type):
            issues.append({
                'type': 'signal_type_mismatch',
                'severity': 'high',
                'description': f'信号类型不匹配: {source_signal_type} -> {sink_signal_type}',
                'standard_reference': 'IEC 61850-7-3 数据类型定义'
            })
        
        return {
            'connection_logic': connection.logical_relationship,
            'verification_status': 'passed' if not issues else 'failed',
            'issues': issues
        }
    
    def verify_complex_circuit_logic_relationships(self, circuit_configuration: Dict[str, Any]) -> Dict[str, Any]:
        """验证基于规程规范的各种回路间复杂逻辑关系"""
        
        verification_results = {
            'protection_control_logic': [],
            'interlocking_logic': [],
            'measurement_protection_logic': [],
            'automation_coordination_logic': [],
            'regulation_compliance_analysis': [],
            'overall_assessment': None
        }
        
        # 验证保护控制逻辑
        protection_control_verification = self._verify_protection_control_logic(circuit_configuration)
        verification_results['protection_control_logic'] = protection_control_verification
        
        # 验证联锁逻辑
        interlocking_verification = self._verify_interlocking_logic(circuit_configuration)
        verification_results['interlocking_logic'] = interlocking_verification
        
        # 验证测量保护逻辑
        measurement_protection_verification = self._verify_measurement_protection_logic(circuit_configuration)
        verification_results['measurement_protection_logic'] = measurement_protection_verification
        
        # 验证自动化协调逻辑
        automation_verification = self._verify_automation_coordination_logic(circuit_configuration)
        verification_results['automation_coordination_logic'] = automation_verification
        
        # 规程合规性分析
        regulation_analysis = self._analyze_regulation_compliance(circuit_configuration)
        verification_results['regulation_compliance_analysis'] = regulation_analysis
        
        # 综合评估
        verification_results['overall_assessment'] = self._assess_complex_logic_compliance(verification_results)
        
        return verification_results
    
    def _verify_protection_control_logic(self, circuit_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """验证保护控制逻辑"""
        
        verification_results = []
        
        # 获取保护控制逻辑模式
        protection_pattern = self.circuit_logic_patterns['protection_trip_logic']
        
        # 验证逻辑流程完整性
        for step in protection_pattern['logical_flow']:
            step_verification = {
                'step_number': step['step'],
                'step_description': step['description'],
                'verification_status': 'passed',
                'issues': []
            }
            
            # 检查相关逻辑节点是否存在
            required_lns = step['logical_nodes']
            configured_lns = self._get_configured_logical_nodes(circuit_config)
            
            for ln_class in required_lns:
                if not any(ln.startswith(ln_class) for ln in configured_lns):
                    step_verification['issues'].append({
                        'type': 'missing_logical_node',
                        'severity': 'high',
                        'description': f'步骤{step["step"]}缺少必要的逻辑节点: {ln_class}',
                        'regulation_reference': 'GB/T 14285-2023 保护配置要求'
                    })
                    step_verification['verification_status'] = 'failed'
            
            # 检查数据对象配置
            required_dos = step['data_objects']
            for do_ref in required_dos:
                if not self._check_data_object_exists(circuit_config, do_ref):
                    step_verification['issues'].append({
                        'type': 'missing_data_object',
                        'severity': 'high',
                        'description': f'步骤{step["step"]}缺少必要的数据对象: {do_ref}',
                        'regulation_reference': 'IEC 61850-7-4 逻辑节点定义'
                    })
                    step_verification['verification_status'] = 'failed'
            
            verification_results.append(step_verification)
        
        # 验证GOOSE映射
        goose_mappings = protection_pattern['goose_mappings']
        for mapping in goose_mappings:
            mapping_verification = self._verify_goose_mapping(circuit_config, mapping)
            verification_results.append(mapping_verification)
        
        return verification_results
    
    def _verify_interlocking_logic(self, circuit_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """验证联锁逻辑"""
        
        verification_results = []
        
        # 获取联锁逻辑模式
        interlocking_pattern = self.circuit_logic_patterns['interlocking_logic']
        
        # 验证五防规则实现
        five_prevention_rules = interlocking_pattern['five_prevention_rules']
        
        for rule_id, rule_spec in five_prevention_rules.items():
            rule_verification = {
                'rule_id': rule_id,
                'rule_description': rule_spec['description'],
                'verification_status': 'passed',
                'issues': []
            }
            
            # 检查相关逻辑节点
            affected_lns = rule_spec['affected_ln']
            configured_lns = self._get_configured_logical_nodes(circuit_config)
            
            for ln_class in affected_lns:
                if not any(ln.startswith(ln_class) for ln in configured_lns):
                    rule_verification['issues'].append({
                        'type': 'missing_interlocking_ln',
                        'severity': 'high',
                        'description': f'五防规则{rule_id}缺少相关逻辑节点: {ln_class}',
                        'regulation_reference': 'DL/T 5136-2012 五防功能要求'
                    })
                    rule_verification['verification_status'] = 'failed'
            
            # 验证逻辑表达式实现
            logic_expression = rule_spec['logic']
            if not self._verify_logic_expression_implementation(circuit_config, logic_expression):
                rule_verification['issues'].append({
                    'type': 'logic_implementation_missing',
                    'severity': 'high',
                    'description': f'五防规则{rule_id}逻辑未正确实现: {logic_expression}',
                    'regulation_reference': 'DL/T 5136-2012 联锁逻辑要求'
                })
                rule_verification['verification_status'] = 'failed'
            
            verification_results.append(rule_verification)
        
        return verification_results
    
    def generate_comprehensive_verification_report(self, verification_results: Dict[str, Any]) -> str:
        """生成综合验证报告"""
        
        report = f"""
# IEC61850逻辑节点一致性与复杂回路逻辑关系验证报告

## 1. 执行摘要

### 1.1 验证范围
- LD/LN/DO/DA结构一致性验证
- GSE/SMV通信配置验证  
- GOOSE虚端子连接关系检查
- 基于规程规范的复杂回路逻辑关系验证

### 1.2 验证结果概览
{self._format_verification_summary(verification_results)}

## 2. LD/LN/DO/DA结构验证

### 2.1 逻辑设备结构验证
{self._format_ld_verification_results(verification_results.get('ld_ln_do_da_verification', {}))}

### 2.2 逻辑节点一致性检查
{self._format_ln_verification_results(verification_results.get('ld_ln_do_da_verification', {}))}

### 2.3 数据对象和属性验证
{self._format_do_da_verification_results(verification_results.get('ld_ln_do_da_verification', {}))}

## 3. 通信配置验证

### 3.1 GOOSE配置验证
{self._format_goose_verification_results(verification_results.get('communication_verification', {}))}

### 3.2 SMV配置验证
{self._format_smv_verification_results(verification_results.get('communication_verification', {}))}

### 3.3 网络性能验证
{self._format_network_verification_results(verification_results.get('communication_verification', {}))}

## 4. GOOSE虚端子连接验证

### 4.1 连接有效性检查
{self._format_connection_validity_results(verification_results.get('goose_connection_verification', {}))}

### 4.2 逻辑一致性验证
{self._format_connection_logic_results(verification_results.get('goose_connection_verification', {}))}

### 4.3 时序性能分析
{self._format_timing_analysis_results(verification_results.get('goose_connection_verification', {}))}

## 5. 复杂回路逻辑关系验证

### 5.1 保护控制逻辑验证
{self._format_protection_control_logic_results(verification_results.get('complex_logic_verification', {}))}

### 5.2 联锁逻辑验证
{self._format_interlocking_logic_results(verification_results.get('complex_logic_verification', {}))}

### 5.3 测量保护协调验证
{self._format_measurement_protection_logic_results(verification_results.get('complex_logic_verification', {}))}

## 6. 规程规范合规性分析

### 6.1 GB/T 14285-2023合规性
{self._format_gb14285_compliance_results(verification_results.get('complex_logic_verification', {}))}

### 6.2 DL/T 5136-2012合规性
{self._format_dl5136_compliance_results(verification_results.get('complex_logic_verification', {}))}

### 6.3 IEC 61850标准符合性
{self._format_iec61850_compliance_results(verification_results)}

## 7. 问题汇总与建议

### 7.1 高严重性问题
{self._format_high_severity_issues(verification_results)}

### 7.2 中等严重性问题
{self._format_medium_severity_issues(verification_results)}

### 7.3 改进建议
{self._format_improvement_recommendations(verification_results)}

## 8. 结论

{self._format_verification_conclusion(verification_results)}

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
验证工具: IEC61850逻辑节点一致性与复杂回路逻辑关系验证引擎 v1.0
        """
        
        return report
    
    # 辅助方法实现
    def _validate_ld_naming(self, ld_name: str) -> bool:
        """验证LD命名规范"""
        # IEC 61850-7-1 命名规则验证
        import re
        pattern = r'^[A-Z][A-Za-z0-9_]*$'
        return bool(re.match(pattern, ld_name)) and len(ld_name) <= 64
    
    def _validate_object_reference(self, reference: str) -> bool:
        """验证对象引用格式"""
        # IEC 61850-7-1 对象引用格式验证
        import re
        pattern = r'^[A-Za-z0-9_]+/[A-Za-z0-9_]+\.[A-Za-z0-9_]+(\.[A-Za-z0-9_]+)*$'
        return bool(re.match(pattern, reference))
    
    def _validate_multicast_address(self, address: str) -> bool:
        """验证组播地址"""
        if not address:
            return False
        # 检查是否为有效的组播MAC地址
        import re
        pattern = r'^01-0C-CD-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}$'
        return bool(re.match(pattern, address))
    
    def _extract_ln_class_from_reference(self, reference: str) -> str:
        """从引用中提取逻辑节点类别"""
        # 解析引用格式: LD/LNClass+Inst.DO.DA
        parts = reference.split('/')
        if len(parts) >= 2:
            ln_part = parts[1].split('.')[0]
            # 提取逻辑节点类别（去除实例号）
            import re
            match = re.match(r'^([A-Z]{4})', ln_part)
            return match.group(1) if match else ''
        return ''
    
    def _get_configured_logical_nodes(self, circuit_config: Dict[str, Any]) -> List[str]:
        """获取已配置的逻辑节点列表"""
        configured_lns = []
        for ied in circuit_config.get('ieds', []):
            for ld in ied.get('logical_devices', []):
                for ln in ld.get('logical_nodes', []):
                    ln_ref = f"{ln.get('ln_prefix', '')}{ln.get('ln_class')}{ln.get('ln_inst', '')}"
                    configured_lns.append(ln_ref)
        return configured_lns
    
    # 格式化方法的简化实现
    def _format_verification_summary(self, results: Dict[str, Any]) -> str:
        return "验证结果概览..."
    
    def _format_ld_verification_results(self, results: Dict[str, Any]) -> str:
        return "逻辑设备结构验证结果..."
    
    def _format_verification_conclusion(self, results: Dict[str, Any]) -> str:
        return "基于IEC61850标准和电气规程的综合验证结论..."  
      """验证LD命名规范"""
        # 简单的命名验证逻辑
        return len(ld_name) > 0 and ld_name.isalnum()
    
    def _validate_ln_naming(self, ln_class: str, ln_inst: str) -> bool:
        """验证LN命名规范"""
        # 简单的命名验证逻辑
        return len(ln_class) > 0 and len(ln_inst) > 0
    
    def _validate_do_naming(self, do_name: str) -> bool:
        """验证DO命名规范"""
        # 简单的命名验证逻辑
        return len(do_name) > 0
    
    def _validate_da_naming(self, da_name: str) -> bool:
        """验证DA命名规范"""
        # 简单的命名验证逻辑
        return len(da_name) > 0


def main():
    """主函数 - 演示IEC61850逻辑验证引擎"""
    print("IEC61850逻辑节点一致性与复杂回路逻辑关系验证引擎")
    print("=" * 60)
    
    # 创建验证引擎实例
    engine = IEC61850LogicVerificationEngine()
    
    # 创建测试数据
    test_scd_data = {
        "SCL": {
            "IED": [
                {
                    "name": "IED1",
                    "AccessPoint": {
                        "Server": {
                            "LDevice": [
                                {
                                    "inst": "LD1",
                                    "LN": [
                                        {
                                            "lnClass": "XCBR",
                                            "inst": "1",
                                            "DOI": [
                                                {
                                                    "name": "Pos",
                                                    "DAI": [
                                                        {"name": "stVal", "Val": "intermediate-state"}
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            ]
        }
    }
    
    # 执行验证
    result = engine.verify_ld_ln_do_da_consistency(test_scd_data)
    
    print(f"\n验证结果:")
    print(f"成功: {result.success}")
    print(f"得分: {result.score:.1f}/100")
    print(f"问题数量: {len(result.issues)}")
    
    for issue in result.issues[:3]:  # 显示前3个问题
        print(f"  - {issue.description}")
    
    print("\n验证完成！")


if __name__ == "__main__":
    main()