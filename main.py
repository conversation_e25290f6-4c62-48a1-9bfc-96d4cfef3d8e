#!/usr/bin/env python3
"""
IEC61850设计检查器主入口文件
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("IEC61850设计检查器")
    print("=" * 50)
    print("专业的智能变电站二次设计验证工具")
    print()

    # 测试核心数据模型
    try:
        from src.core.models import (
            SubStation, VoltageLevel, Bay, ConductingEquipment,
            Voltage, IED, AccessPoint, LogicalNode
        )

        print("✓ 核心数据模型加载成功")

        # 测试XML解析器
        from src.core.parsers import (
            ParserFactory, SCDParser, ICDParser, CIDParser
        )

        print("✓ XML解析器加载成功")
        
        # 创建一个简单的变电站示例
        print("\n创建变电站示例...")
        
        # 创建变电站
        substation = SubStation(
            name="TestSubstation",
            location="Beijing",
            owner="StateGrid"
        )

        # 创建电压等级
        voltage_110kv = Voltage(multiplier="k", unit="V", value=110.0)
        vl_110kv = VoltageLevel(name="VL_110kV", voltage=voltage_110kv)

        voltage_10kv = Voltage(multiplier="k", unit="V", value=10.0)
        vl_10kv = VoltageLevel(name="VL_10kV", voltage=voltage_10kv)

        # 创建间隔
        bay_110kv_1 = Bay(name="Bay_110kV_Line1")
        bay_110kv_2 = Bay(name="Bay_110kV_Line2")
        bay_10kv_1 = Bay(name="Bay_10kV_Feeder1")
        
        # 创建设备
        cbr_110_1 = ConductingEquipment(
            name="CBR_110kV_1",
            type="CBR",
            manufacturer="ABB",
            model="HPL362"
        )

        dis_110_1 = ConductingEquipment(
            name="DIS_110kV_1",
            type="DIS",
            manufacturer="ABB",
            model="DIS1250"
        )
        
        # 组装层次结构
        bay_110kv_1.add_equipment(cbr_110_1)
        bay_110kv_1.add_equipment(dis_110_1)
        
        vl_110kv.add_bay(bay_110kv_1)
        vl_110kv.add_bay(bay_110kv_2)
        vl_10kv.add_bay(bay_10kv_1)
        
        substation.add_voltage_level(vl_110kv)
        substation.add_voltage_level(vl_10kv)
        
        print(f"✓ 变电站 '{substation.name}' 创建成功")
        
        # 显示统计信息
        stats = substation.get_statistics()
        print(f"  - 电压等级数量: {stats['voltage_levels_count']}")
        print(f"  - 间隔数量: {stats['bays_count']}")
        print(f"  - 设备数量: {stats['equipments_count']}")
        print(f"  - 设备类型分布: {stats['equipment_count_by_type']}")
        
        # 测试序列化
        print("\n测试数据序列化...")
        json_data = substation.to_json(indent=2)
        print(f"✓ JSON序列化成功，数据长度: {len(json_data)} 字符")

        # 测试基本属性访问
        print(f"✓ 变电站ID: {substation.id}")
        print(f"✓ 创建时间: {substation.created_at}")

        # 测试路径查找
        equipment = substation.get_equipment_by_path("VL_110kV/Bay_110kV_Line1/CBR_110kV_1")
        if equipment:
            print(f"✓ 路径查找成功，找到设备: {equipment.name}")
        else:
            print("❌ 路径查找失败")
        
        print("\n🎉 核心数据模型测试完成！")

        # 测试XML解析功能
        print("\n测试XML解析功能...")

        # 创建解析器工厂
        factory = ParserFactory()
        print(f"✓ 解析器工厂创建成功，支持类型: {factory.get_supported_types()}")

        # 测试解析示例SCD文件
        sample_scd_path = Path("tests/fixtures/sample.scd")
        if sample_scd_path.exists():
            print(f"\n解析示例SCD文件: {sample_scd_path}")

            # 自动检测文件类型
            detected_type = factory.detect_file_type(sample_scd_path)
            print(f"✓ 检测到文件类型: {detected_type}")

            # 解析文件
            result = factory.parse_file(sample_scd_path, validate_schema=False)

            if result.success:
                print("✓ SCD文件解析成功")

                # 显示解析结果统计
                scl_doc = result.data
                stats = scl_doc.get_statistics()
                print(f"  - SCL版本: {stats['version']}")
                print(f"  - IED数量: {stats['ieds_count']}")
                print(f"  - 电压等级数量: {stats.get('voltage_levels_count', 0)}")
                print(f"  - 间隔数量: {stats.get('bays_count', 0)}")
                print(f"  - 设备数量: {stats.get('equipments_count', 0)}")
                print(f"  - 子网数量: {stats.get('sub_networks_count', 0)}")
                print(f"  - 连接访问点数量: {stats.get('connected_aps_count', 0)}")

                # 显示解析元数据
                metadata = result.metadata
                print(f"  - 解析耗时: {metadata.get('parse_duration', 0):.3f}秒")
                print(f"  - 文件大小: {metadata.get('file_size', 0)} 字节")
                print(f"  - XML元素数量: {metadata.get('element_count', 0)}")

                # 测试获取特定IED
                if scl_doc.ieds:
                    first_ied = scl_doc.ieds[0]
                    print(f"  - 第一个IED: {first_ied.name} ({first_ied.manufacturer})")

                    capabilities = first_ied.get_capabilities()
                    print(f"    - 访问点数量: {capabilities['access_points_count']}")
                    print(f"    - 逻辑设备数量: {capabilities['ldevices_count']}")

            else:
                print("❌ SCD文件解析失败")
                for error in result.errors:
                    print(f"    错误: {error}")

            # 显示警告
            if result.warnings:
                print("⚠️ 解析警告:")
                for warning in result.warnings:
                    print(f"    警告: {warning}")
        else:
            print(f"⚠️ 示例SCD文件不存在: {sample_scd_path}")

        print("\n🎉 XML解析功能测试完成！")

        # 测试规则引擎功能
        print("\n测试规则引擎功能...")

        from src.core.rules import (
            RuleEngine, RuleRegistry, ReportGenerator,
            ExecutionConfig, rule_registry
        )

        print("✓ 规则引擎模块加载成功")

        # 创建规则引擎
        engine = RuleEngine()
        print(f"✓ 规则引擎创建成功")

        # 检查注册的规则
        all_rules = rule_registry.get_all_rules()
        enabled_rules = rule_registry.get_enabled_rules()
        print(f"✓ 发现 {len(all_rules)} 个规则，其中 {len(enabled_rules)} 个已启用")

        # 按类别统计规则
        from src.core.rules.base import RuleCategory
        category_stats = {}
        for rule in all_rules:
            category = rule.category.value
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1

        print("  规则分布:")
        for category, count in category_stats.items():
            print(f"    - {category}: {count} 个")

        # 使用解析的SCD文档测试规则引擎
        if sample_scd_path.exists() and 'scl_doc' in locals():
            print(f"\n使用解析的SCD文档测试规则引擎...")

            # 配置执行参数
            config = ExecutionConfig(
                max_workers=2,
                rule_timeout=10.0,
                stop_on_error=False
            )

            # 执行验证 - 对SCL文档本身
            validation_result = engine.validate(scl_doc, config)

            # 也对每个IED单独执行验证
            for ied in scl_doc.ieds:
                ied_result = engine.validate(ied, config)
                # 合并结果
                validation_result.executed_rules += ied_result.executed_rules
                validation_result.skipped_rules += ied_result.skipped_rules
                validation_result.failed_rules += ied_result.failed_rules
                validation_result.total_time += ied_result.total_time
                validation_result.rule_results.update(ied_result.rule_results)

            print(f"✓ 规则引擎执行完成")
            print(f"  - 执行规则: {validation_result.executed_rules} 个")
            print(f"  - 跳过规则: {validation_result.skipped_rules} 个")
            print(f"  - 失败规则: {validation_result.failed_rules} 个")
            print(f"  - 总耗时: {validation_result.total_time:.3f} 秒")

            # 统计问题
            all_issues = validation_result.get_all_issues()
            errors = validation_result.get_errors()
            warnings = validation_result.get_warnings()
            infos = validation_result.get_infos()

            print(f"  - 发现问题: {len(all_issues)} 个")
            print(f"    - 错误: {len(errors)} 个")
            print(f"    - 警告: {len(warnings)} 个")
            print(f"    - 信息: {len(infos)} 个")

            # 显示前几个问题
            if errors:
                print("  主要错误:")
                for i, error in enumerate(errors[:3]):
                    print(f"    {i+1}. [{error.rule_id}] {error.message}")
                    if error.path:
                        print(f"       位置: {error.path}")

            if warnings:
                print("  主要警告:")
                for i, warning in enumerate(warnings[:3]):
                    print(f"    {i+1}. [{warning.rule_id}] {warning.message}")
                    if warning.path:
                        print(f"       位置: {warning.path}")

            # 生成验证报告
            print("\n生成验证报告...")
            generator = ReportGenerator()
            report = generator.generate_report(validation_result, "SCD文件验证报告")

            print(f"✓ 验证报告生成成功")
            print(f"  - 报告标题: {report.title}")
            print(f"  - 生成时间: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  - 报告章节: {len(report.sections)} 个")

            # 保存报告
            report_path = Path("validation_report.json")
            generator.save_report(report, report_path, 'json')
            print(f"✓ 报告已保存到: {report_path}")

            # 显示报告摘要
            if report.statistics:
                stats = report.statistics
                print("  报告统计:")
                exec_summary = stats.get('execution_summary', {})
                severity_dist = stats.get('severity_distribution', {})

                print(f"    - 执行摘要: {exec_summary}")
                print(f"    - 严重程度分布: {severity_dist}")

        print("\n🎉 规则引擎功能测试完成！")

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
