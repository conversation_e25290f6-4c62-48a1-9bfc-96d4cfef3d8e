#!/usr/bin/env python3
"""
IEC61850设计检查器主入口文件
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("IEC61850设计检查器")
    print("=" * 50)
    print("专业的智能变电站二次设计验证工具")
    print()
    
    # 测试核心数据模型
    try:
        from src.core.models import (
            SubStation, VoltageLevel, Bay, ConductingEquipment,
            Voltage, IED, AccessPoint, LogicalNode
        )
        
        print("✓ 核心数据模型加载成功")
        
        # 创建一个简单的变电站示例
        print("\n创建变电站示例...")
        
        # 创建变电站
        substation = SubStation(
            name="TestSubstation",
            location="Beijing",
            owner="StateGrid"
        )

        # 创建电压等级
        voltage_110kv = Voltage(multiplier="k", unit="V", value=110.0)
        vl_110kv = VoltageLevel(name="VL_110kV", voltage=voltage_110kv)

        voltage_10kv = Voltage(multiplier="k", unit="V", value=10.0)
        vl_10kv = VoltageLevel(name="VL_10kV", voltage=voltage_10kv)

        # 创建间隔
        bay_110kv_1 = Bay(name="Bay_110kV_Line1")
        bay_110kv_2 = Bay(name="Bay_110kV_Line2")
        bay_10kv_1 = Bay(name="Bay_10kV_Feeder1")
        
        # 创建设备
        cbr_110_1 = ConductingEquipment(
            name="CBR_110kV_1",
            type="CBR",
            manufacturer="ABB",
            model="HPL362"
        )

        dis_110_1 = ConductingEquipment(
            name="DIS_110kV_1",
            type="DIS",
            manufacturer="ABB",
            model="DIS1250"
        )
        
        # 组装层次结构
        bay_110kv_1.add_equipment(cbr_110_1)
        bay_110kv_1.add_equipment(dis_110_1)
        
        vl_110kv.add_bay(bay_110kv_1)
        vl_110kv.add_bay(bay_110kv_2)
        vl_10kv.add_bay(bay_10kv_1)
        
        substation.add_voltage_level(vl_110kv)
        substation.add_voltage_level(vl_10kv)
        
        print(f"✓ 变电站 '{substation.name}' 创建成功")
        
        # 显示统计信息
        stats = substation.get_statistics()
        print(f"  - 电压等级数量: {stats['voltage_levels_count']}")
        print(f"  - 间隔数量: {stats['bays_count']}")
        print(f"  - 设备数量: {stats['equipments_count']}")
        print(f"  - 设备类型分布: {stats['equipment_count_by_type']}")
        
        # 测试序列化
        print("\n测试数据序列化...")
        json_data = substation.to_json(indent=2)
        print(f"✓ JSON序列化成功，数据长度: {len(json_data)} 字符")

        # 测试基本属性访问
        print(f"✓ 变电站ID: {substation.id}")
        print(f"✓ 创建时间: {substation.created_at}")

        # 测试路径查找
        equipment = substation.get_equipment_by_path("VL_110kV/Bay_110kV_Line1/CBR_110kV_1")
        if equipment:
            print(f"✓ 路径查找成功，找到设备: {equipment.name}")
        else:
            print("❌ 路径查找失败")
        
        print("\n🎉 核心数据模型测试完成！")
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
