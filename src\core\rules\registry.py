"""
规则注册机制
管理规则的注册、发现和获取
"""

from typing import Dict, List, Optional, Type, Set, Any
from collections import defaultdict
import logging

from .base import BaseRule, RuleCategory, RuleSeverity


class RuleRegistry:
    """规则注册表"""
    
    def __init__(self):
        """初始化注册表"""
        self._rules: Dict[str, BaseRule] = {}
        self._rules_by_category: Dict[RuleCategory, List[BaseRule]] = defaultdict(list)
        self._rules_by_severity: Dict[RuleSeverity, List[BaseRule]] = defaultdict(list)
        self._dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self._reverse_dependency_graph: Dict[str, Set[str]] = defaultdict(set)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def register(self, rule: BaseRule) -> None:
        """
        注册规则
        
        Args:
            rule: 要注册的规则
            
        Raises:
            ValueError: 如果规则ID已存在
        """
        if rule.rule_id in self._rules:
            raise ValueError(f"规则ID '{rule.rule_id}' 已存在")
        
        # 注册规则
        self._rules[rule.rule_id] = rule
        self._rules_by_category[rule.category].append(rule)
        self._rules_by_severity[rule.severity].append(rule)
        
        # 构建依赖图
        dependencies = rule.get_dependencies()
        for dep_id in dependencies:
            self._dependency_graph[rule.rule_id].add(dep_id)
            self._reverse_dependency_graph[dep_id].add(rule.rule_id)
        
        self.logger.info(f"注册规则: {rule.rule_id} ({rule.name})")
    
    def unregister(self, rule_id: str) -> bool:
        """
        注销规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            是否成功注销
        """
        if rule_id not in self._rules:
            return False
        
        rule = self._rules[rule_id]
        
        # 检查是否有其他规则依赖此规则
        dependents = self._reverse_dependency_graph.get(rule_id, set())
        if dependents:
            dependent_names = [self._rules[dep_id].name for dep_id in dependents]
            raise ValueError(f"无法注销规则 '{rule_id}'，以下规则依赖它: {', '.join(dependent_names)}")
        
        # 移除规则
        del self._rules[rule_id]
        self._rules_by_category[rule.category].remove(rule)
        self._rules_by_severity[rule.severity].remove(rule)
        
        # 清理依赖图
        dependencies = self._dependency_graph.get(rule_id, set())
        for dep_id in dependencies:
            self._reverse_dependency_graph[dep_id].discard(rule_id)
        del self._dependency_graph[rule_id]
        
        self.logger.info(f"注销规则: {rule_id}")
        return True
    
    def get_rule(self, rule_id: str) -> Optional[BaseRule]:
        """
        获取规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            规则实例，如果不存在则返回None
        """
        return self._rules.get(rule_id)
    
    def get_all_rules(self) -> List[BaseRule]:
        """
        获取所有规则
        
        Returns:
            所有规则列表
        """
        return list(self._rules.values())
    
    def get_enabled_rules(self) -> List[BaseRule]:
        """
        获取所有启用的规则
        
        Returns:
            启用的规则列表
        """
        return [rule for rule in self._rules.values() if rule.enabled]
    
    def get_rules_by_category(self, category: RuleCategory) -> List[BaseRule]:
        """
        按类别获取规则
        
        Args:
            category: 规则类别
            
        Returns:
            指定类别的规则列表
        """
        return self._rules_by_category[category].copy()
    
    def get_rules_by_severity(self, severity: RuleSeverity) -> List[BaseRule]:
        """
        按严重程度获取规则
        
        Args:
            severity: 严重程度
            
        Returns:
            指定严重程度的规则列表
        """
        return self._rules_by_severity[severity].copy()
    
    def get_rule_dependencies(self, rule_id: str) -> Set[str]:
        """
        获取规则的依赖
        
        Args:
            rule_id: 规则ID
            
        Returns:
            依赖的规则ID集合
        """
        return self._dependency_graph.get(rule_id, set()).copy()
    
    def get_rule_dependents(self, rule_id: str) -> Set[str]:
        """
        获取依赖指定规则的规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            依赖此规则的规则ID集合
        """
        return self._reverse_dependency_graph.get(rule_id, set()).copy()
    
    def resolve_execution_order(self, rule_ids: Optional[List[str]] = None) -> List[str]:
        """
        解析规则执行顺序（拓扑排序）
        
        Args:
            rule_ids: 要排序的规则ID列表，如果为None则使用所有启用的规则
            
        Returns:
            按依赖关系排序的规则ID列表
            
        Raises:
            ValueError: 如果存在循环依赖
        """
        if rule_ids is None:
            rule_ids = [rule.rule_id for rule in self.get_enabled_rules()]
        
        # 构建子图
        subgraph = {}
        in_degree = {}
        
        for rule_id in rule_ids:
            if rule_id not in self._rules:
                raise ValueError(f"规则 '{rule_id}' 不存在")
            
            dependencies = self._dependency_graph.get(rule_id, set())
            # 只考虑在rule_ids中的依赖
            filtered_deps = dependencies.intersection(set(rule_ids))
            subgraph[rule_id] = filtered_deps
            in_degree[rule_id] = len(filtered_deps)
        
        # 拓扑排序
        result = []
        queue = [rule_id for rule_id in rule_ids if in_degree[rule_id] == 0]
        
        while queue:
            current = queue.pop(0)
            result.append(current)
            
            # 更新依赖此规则的规则的入度
            for rule_id in rule_ids:
                if current in subgraph[rule_id]:
                    in_degree[rule_id] -= 1
                    if in_degree[rule_id] == 0:
                        queue.append(rule_id)
        
        # 检查是否存在循环依赖
        if len(result) != len(rule_ids):
            remaining = set(rule_ids) - set(result)
            raise ValueError(f"检测到循环依赖，涉及规则: {', '.join(remaining)}")
        
        return result
    
    def validate_dependencies(self) -> List[str]:
        """
        验证所有规则的依赖关系
        
        Returns:
            依赖问题列表
        """
        issues = []
        
        for rule_id, rule in self._rules.items():
            dependencies = rule.get_dependencies()
            for dep_id in dependencies:
                if dep_id not in self._rules:
                    issues.append(f"规则 '{rule_id}' 依赖不存在的规则 '{dep_id}'")
        
        # 检查循环依赖
        try:
            self.resolve_execution_order()
        except ValueError as e:
            issues.append(str(e))
        
        return issues
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取注册表统计信息
        
        Returns:
            统计信息字典
        """
        total_rules = len(self._rules)
        enabled_rules = len(self.get_enabled_rules())
        
        category_stats = {}
        for category in RuleCategory:
            category_stats[category.value] = len(self._rules_by_category[category])
        
        severity_stats = {}
        for severity in RuleSeverity:
            severity_stats[severity.value] = len(self._rules_by_severity[severity])
        
        return {
            'total_rules': total_rules,
            'enabled_rules': enabled_rules,
            'disabled_rules': total_rules - enabled_rules,
            'category_distribution': category_stats,
            'severity_distribution': severity_stats,
            'dependency_count': sum(len(deps) for deps in self._dependency_graph.values())
        }
    
    def clear(self) -> None:
        """清空注册表"""
        self._rules.clear()
        self._rules_by_category.clear()
        self._rules_by_severity.clear()
        self._dependency_graph.clear()
        self._reverse_dependency_graph.clear()
        self.logger.info("清空规则注册表")


# 全局规则注册表实例
rule_registry = RuleRegistry()


def register_rule(rule: BaseRule) -> None:
    """
    注册规则到全局注册表
    
    Args:
        rule: 要注册的规则
    """
    rule_registry.register(rule)


def get_rule(rule_id: str) -> Optional[BaseRule]:
    """
    从全局注册表获取规则
    
    Args:
        rule_id: 规则ID
        
    Returns:
        规则实例
    """
    return rule_registry.get_rule(rule_id)


def get_all_rules() -> List[BaseRule]:
    """
    从全局注册表获取所有规则
    
    Returns:
        所有规则列表
    """
    return rule_registry.get_all_rules()


def auto_register_rules(module_name: str) -> int:
    """
    自动注册模块中的规则
    
    Args:
        module_name: 模块名称
        
    Returns:
        注册的规则数量
    """
    import importlib
    import inspect
    
    try:
        module = importlib.import_module(module_name)
        registered_count = 0
        
        for name, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, BaseRule) and 
                obj != BaseRule):
                try:
                    rule_instance = obj()
                    rule_registry.register(rule_instance)
                    registered_count += 1
                except Exception as e:
                    logging.getLogger(__name__).warning(
                        f"无法注册规则 {name}: {e}"
                    )
            elif isinstance(obj, BaseRule):
                try:
                    rule_registry.register(obj)
                    registered_count += 1
                except Exception as e:
                    logging.getLogger(__name__).warning(
                        f"无法注册规则 {name}: {e}"
                    )
        
        return registered_count
        
    except ImportError as e:
        logging.getLogger(__name__).error(f"无法导入模块 {module_name}: {e}")
        return 0
