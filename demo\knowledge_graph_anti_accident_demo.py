#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电力系统知识图谱与反事故措施、九统一标准集成演示
展示如何在知识图谱中集成国家电网公司十八项电网重大反事故措施和继电保护九统一标准
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.knowledge.graph.power_system_knowledge_graph import PowerSystemKnowledgeGraph


def demo_knowledge_graph_with_standards():
    print("电力系统知识图谱与标准集成演示")
    print("=" * 50)
    
    # 1. 创建知识图谱实例
    print("\n1. 创建电力系统知识图谱实例...")
    try:
        kg = PowerSystemKnowledgeGraph()
        print("  ✓ 知识图谱实例创建成功")
    except Exception as e:
        print(f"  ✗ 知识图谱实例创建失败: {e}")
        return
    
    # 2. 展示反事故措施知识库
    print("\n2. 反事故措施知识库内容:")
    try:
        # 显示加载的反事故措施
        print("  已加载的反事故措施:")
        for measure_key, measure_data in kg.anti_accident_measures.items():
            print(f"    - {measure_data.get('name', 'N/A')}")
            print(f"      描述: {measure_data.get('description', 'N/A')}")
            print(f"      相关标准: {', '.join(measure_data.get('related_standards', []))}")
            print(f"      关键要点: {', '.join(measure_data.get('key_points', []))}")
            print()
        print(f"  ✓ 成功加载 {len(kg.anti_accident_measures)} 项反事故措施")
    except Exception as e:
        print(f"  ✗ 获取反事故措施失败: {e}")
    
    # 3. 展示九统一标准知识库
    print("\n3. 九统一标准知识库内容:")
    try:
        # 显示加载的九统一标准
        print("  已加载的九统一标准:")
        for standard_key, standard_data in kg.nine_unified_standards.items():
            print(f"    - {standard_data.get('name', 'N/A')}")
            print(f"      描述: {standard_data.get('description', 'N/A')}")
            print(f"      相关文档: {', '.join(standard_data.get('related_documents', []))}")
            print(f"      关键原则: {', '.join(standard_data.get('key_principles', []))}")
            print()
        print(f"  ✓ 成功加载 {len(kg.nine_unified_standards)} 项九统一标准")
    except Exception as e:
        print(f"  ✗ 获取九统一标准失败: {e}")
    
    # 4. 模拟SCD数据构建知识图谱
    print("\n4. 模拟SCD数据构建知识图谱...")
    test_scd_data = {
        'ieds': [
            {
                'name': 'PROT_IED_001',
                'type': 'Protection',
                'manufacturer': 'TestManufacturer',
                'AccessPoint': {
                    'name': 'AP1',
                    'Server': {
                        'LDevice': {
                            'inst': 'LD1',
                            'LN0': {
                                'lnClass': 'LLN0',
                                'inst': ''
                            },
                            'LN': [
                                {
                                    'lnClass': 'PTOC',
                                    'inst': '1',
                                    'DOI': [
                                        {
                                            'name': 'Str',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        },
                                        {
                                            'name': 'Op',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            }
        ],
        'communication': {
            'SubNetwork': {
                'name': 'SubNet1',
                'ConnectedAP': {
                    'iedName': 'PROT_IED_001',
                    'GSE': {
                        'ldInst': 'LD1',
                        'cbName': 'GC1',
                        'Address': {
                            'P': [
                                {'type': 'MAC-Address', 'value': '01-0C-CD-01-00-01'},
                                {'type': 'APPID', 'value': '0001'}
                            ]
                        }
                    }
                }
            }
        }
    }
    
    try:
        # 构建知识图谱
        kg.build_from_scd(test_scd_data)
        print("  ✓ 知识图谱构建完成")
        print(f"    实体数量: {len(kg.entities)}")
        print(f"    关系数量: {len(kg.relations)}")
        print(f"    回路数量: {len(kg.circuits)}")
    except Exception as e:
        print(f"  ✗ 知识图谱构建失败: {e}")
        return
    
    # 5. 分析回路安全性（结合反事故措施）
    print("\n5. 回路安全性分析（结合反事故措施）:")
    try:
        safety_analysis = kg.analyze_circuit_safety()
        print(f"  总回路数: {safety_analysis['total_circuits']}")
        print(f"  符合安全要求的回路: {safety_analysis['safety_compliant_circuits']}")
        print(f"  违反安全要求的回路: {safety_analysis['safety_violation_circuits']}")
        print(f"  反事故措施覆盖率: {safety_analysis['anti_accident_measures_coverage']:.2f}%")
        
        if safety_analysis['issues']:
            print("  发现问题:")
            for issue in safety_analysis['issues'][:3]:  # 只显示前3个问题
                print(f"    - {issue}")
        
        print("  ✓ 回路安全性分析完成")
    except Exception as e:
        print(f"  ✗ 回路安全性分析失败: {e}")
    
    # 6. 获取反事故措施建议
    print("\n6. 反事故措施建议:")
    try:
        if kg.circuits:
            circuit_id = list(kg.circuits.keys())[0]
            recommendations = kg.get_anti_accident_recommendations(circuit_id)
            print(f"  针对回路 {circuit_id} 的反事故措施建议:")
            for rec in recommendations:
                print(f"    - {rec['measure']}: {rec['details']} (优先级: {rec['priority']})")
            print("  ✓ 反事故措施建议生成完成")
        else:
            print("  未找到回路，无法生成建议")
    except Exception as e:
        print(f"  ✗ 反事故措施建议生成失败: {e}")
    
    # 7. 展示实体和关系
    print("\n7. 知识图谱中的实体和关系:")
    try:
        print("  实体类型统计:")
        entity_types = {}
        for entity in kg.entities.values():
            entity_type = entity.type.value
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        
        for entity_type, count in entity_types.items():
            print(f"    {entity_type}: {count}")
        
        print("  关系类型统计:")
        relation_types = {}
        for relation in kg.relations.values():
            relation_type = relation.type.value
            relation_types[relation_type] = relation_types.get(relation_type, 0) + 1
        
        for relation_type, count in relation_types.items():
            print(f"    {relation_type}: {count}")
        
        # 显示一些反事故措施相关的实体
        print("  反事故措施相关实体:")
        anti_accident_entities = [entity for entity in kg.entities.values() 
                                if entity.type.value == 'AntiAccidentMeasure']
        for entity in anti_accident_entities:
            print(f"    - {entity.name}")
        
        # 显示一些标准相关的实体
        print("  标准相关实体:")
        standard_entities = [entity for entity in kg.entities.values() 
                           if entity.type.value == 'Standard']
        for entity in standard_entities:
            print(f"    - {entity.name}")
        
        print("  ✓ 实体和关系展示完成")
    except Exception as e:
        print(f"  ✗ 实体和关系展示失败: {e}")
    
    print("\n演示完成！")


if __name__ == "__main__":
    demo_knowledge_graph_with_standards()