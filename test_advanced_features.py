#!/usr/bin/env python3
"""
测试高级功能模块
验证配置文件对比和虚端子表生成功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_comparison_features():
    """测试配置文件对比功能"""
    print("🔍 测试配置文件对比功能")
    print("=" * 50)
    
    try:
        from src.core.comparison import ConfigComparator, ComparisonReporter
        from src.core.parsers import ParserFactory
        
        # 创建测试数据
        print("1. 创建测试配置文件...")
        
        # 使用简化的测试数据，创建简单的字典对象
        print("   使用简化的测试数据...")

        # 创建简单的测试对象，避免复杂的继承问题
        class SimpleDoc:
            def __init__(self):
                self.version = "2007"
                self.header = None
                self.ieds = []
                self.substation = None

        class SimpleHeader:
            def __init__(self, id_val, version):
                self.id = id_val
                self.version = version

        class SimpleIED:
            def __init__(self, name, manufacturer="", type_val=""):
                self.name = name
                self.manufacturer = manufacturer
                self.type = type_val

        class SimpleSubstation:
            def __init__(self, name):
                self.name = name

        # 创建源文档
        source_doc = SimpleDoc()
        source_doc.header = SimpleHeader("TestProject_v1", "1.0")
        source_doc.ieds = [SimpleIED("IED_PROT_01", "TestVendor", "Protection")]
        source_doc.substation = SimpleSubstation("TestSubstation")

        # 创建目标文档
        target_doc = SimpleDoc()
        target_doc.header = SimpleHeader("TestProject_v2", "2.0")
        target_doc.ieds = [
            SimpleIED("IED_PROT_01", "TestVendor", "Protection"),
            SimpleIED("IED_PROT_02", "TestVendor", "Protection")
        ]
        target_doc.substation = SimpleSubstation("TestSubstation")
        
        print(f"   ✓ 创建源文档: {source_doc.header.id}")
        print(f"   ✓ 创建目标文档: {target_doc.header.id}")

        # 2. 测试文档对比
        print("\n2. 执行配置文件对比...")

        comparator = ConfigComparator()
        comparison_result = comparator.compare_documents(source_doc, target_doc)
        
        print(f"   ✓ 对比完成，发现 {len(comparison_result.changes)} 处变更")
        print(f"   ✓ 对比摘要: {comparison_result.summary}")
        
        # 显示变更详情
        if comparison_result.changes:
            print("\n   变更详情:")
            for i, change in enumerate(comparison_result.changes[:5], 1):  # 只显示前5个
                print(f"     {i}. {change.description}")
                print(f"        路径: {change.path}")
                print(f"        级别: {change.change_level.value}")
        
        # 3. 测试报告生成
        print("\n3. 生成对比报告...")
        
        reporter = ComparisonReporter()
        report = reporter.generate_report(comparison_result, "测试配置文件对比报告")
        
        # 保存报告
        report_dir = Path("reports")
        report_dir.mkdir(exist_ok=True)
        
        json_report_path = report_dir / "comparison_test.json"
        html_report_path = report_dir / "comparison_test.html"
        md_report_path = report_dir / "comparison_test.md"
        
        reporter.save_report(report, str(json_report_path), 'json')
        reporter.save_report(report, str(html_report_path), 'html')
        reporter.save_report(report, str(md_report_path), 'markdown')
        
        print(f"   ✓ JSON报告: {json_report_path}")
        print(f"   ✓ HTML报告: {html_report_path}")
        print(f"   ✓ Markdown报告: {md_report_path}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 对比功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_virtual_terminal_generator():
    """测试虚端子表生成功能"""
    print("\n🔌 测试虚端子表生成功能")
    print("=" * 50)
    
    try:
        from src.core.generators import VirtualTerminalGenerator
        from src.core.parsers import ParserFactory
        
        # 1. 创建测试数据
        print("1. 创建测试SCD数据...")

        # 使用简化的测试数据，避免复杂的解析问题
        print("   使用简化的测试数据...")

        # 创建简单的测试对象
        class SimpleSCDDoc:
            def __init__(self):
                self.version = "2007"
                self.substation = None
                self.ieds = []

        class SimpleSubstation:
            def __init__(self, name):
                self.name = name

        class SimpleIED:
            def __init__(self, name, manufacturer="", type_val=""):
                self.name = name
                self.manufacturer = manufacturer
                self.type = type_val
                self.logical_devices = []
                self.access_points = []  # 添加访问点属性

        class SimpleLogicalDevice:
            def __init__(self, inst, name=""):
                self.inst = inst
                self.name = name or inst
                self.logical_nodes = []

        class SimpleLogicalNode:
            def __init__(self, ln_class, inst="1", desc=""):
                self.ln_class = ln_class
                self.lnclass = ln_class  # 别名
                self.inst = inst
                self.desc = desc
                self.data_objects = []

        class SimpleDataObject:
            def __init__(self, name, fc="", cdc="", desc=""):
                self.name = name
                self.fc = fc
                self.cdc = cdc
                self.desc = desc
                self.type = ""

        # 创建测试文档
        scd_doc = SimpleSCDDoc()

        # 创建变电站
        scd_doc.substation = SimpleSubstation("测试变电站")

        # 创建IED
        ied = SimpleIED("IED_PROT_01", "测试厂商", "保护装置")

        # 创建逻辑设备
        ld = SimpleLogicalDevice("PROT")

        # 创建逻辑节点
        ln = SimpleLogicalNode("PTOC", "1", "过流保护")

        # 创建数据对象
        do1 = SimpleDataObject("Str", "ST", "ACD", "启动状态")
        do2 = SimpleDataObject("Op", "ST", "ACT", "动作状态")

        ln.data_objects = [do1, do2]
        ld.logical_nodes = [ln]
        ied.logical_devices = [ld]
        scd_doc.ieds = [ied]

        print(f"   ✓ 创建测试文档: {scd_doc.substation.name}")
        print(f"   ✓ IED数量: {len(scd_doc.ieds)}")
        print(f"   ✓ 逻辑节点数量: {len(ied.logical_devices[0].logical_nodes)}")
        print(f"   ✓ 数据对象数量: {len(ln.data_objects)}")
        
        # 2. 生成虚端子表
        print("\n2. 生成虚端子表...")
        
        generator = VirtualTerminalGenerator()
        vt_table = generator.generate_from_scd(
            scd_doc, 
            project_name="智能变电站测试项目",
            options={'include_all': True}
        )
        
        print(f"   ✓ 生成虚端子表成功")
        print(f"   ✓ 项目名称: {vt_table.project_name}")
        print(f"   ✓ 变电站: {vt_table.substation_name}")
        print(f"   ✓ 虚端子数量: {len(vt_table.terminals)}")
        
        # 显示统计信息
        if vt_table.statistics:
            print("\n   统计信息:")
            for key, value in vt_table.statistics.items():
                if isinstance(value, dict):
                    print(f"     {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"       {sub_key}: {sub_value}")
                else:
                    print(f"     {key}: {value}")
        
        # 显示部分虚端子
        if vt_table.terminals:
            print("\n   虚端子示例:")
            for i, terminal in enumerate(vt_table.terminals[:3], 1):
                print(f"     {i}. {terminal.ied_name}.{terminal.logical_node}.{terminal.data_object}")
                print(f"        信号类型: {terminal.signal_type}")
                print(f"        方向: {terminal.direction}")
                print(f"        描述: {terminal.description}")
        
        # 3. 导出虚端子表
        print("\n3. 导出虚端子表...")
        
        export_dir = Path("exports")
        export_dir.mkdir(exist_ok=True)
        
        # 导出JSON格式
        json_path = export_dir / "virtual_terminals.json"
        generator.export_to_json(vt_table, str(json_path))
        print(f"   ✓ JSON格式: {json_path}")
        
        # 导出CSV格式
        csv_path = export_dir / "virtual_terminals.csv"
        generator.export_to_csv(vt_table, str(csv_path))
        print(f"   ✓ CSV格式: {csv_path}")
        
        # 尝试导出Excel格式
        try:
            excel_path = export_dir / "virtual_terminals.xlsx"
            generator.export_to_excel(vt_table, str(excel_path))
            print(f"   ✓ Excel格式: {excel_path}")
        except ImportError:
            print("   ⚠️ Excel导出需要pandas库，已跳过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 虚端子表生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 IEC61850设计检查器 - 高级功能测试")
    print("=" * 60)
    print("📋 测试目标：")
    print("   • 配置文件智能对比功能")
    print("   • 虚端子表自动生成功能")
    print("   • 专业报告生成和导出")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试配置文件对比
    if test_comparison_features():
        success_count += 1
    
    # 测试虚端子表生成
    if test_virtual_terminal_generator():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 测试完成: {success_count}/{total_tests} 项功能正常")
    
    if success_count == total_tests:
        print("🎉 所有高级功能测试通过！")
        print("\n💡 这些功能解决了智能变电站工程师的实际痛点：")
        print("   ✓ 配置文件版本对比 - 快速识别关键变更")
        print("   ✓ 虚端子表自动生成 - 告别手工制表的繁琐")
        print("   ✓ 专业报告导出 - 标准化的工程交付文档")
        return 0
    else:
        print("❌ 部分功能测试失败，请检查错误信息")
        return 1


if __name__ == '__main__':
    sys.exit(main())
