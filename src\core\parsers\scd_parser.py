"""
SCD (System Configuration Description) 文件解析器
解析IEC61850系统配置描述文件
"""

from typing import List, Optional, Dict, Any
from lxml import etree
import logging

from .base_parser import BaseParser, ParseError
from .schema_validator import SchemaValidator
from ..models import (
    SubStation, VoltageLevel, Bay, ConductingEquipment, Voltage,
    IED, AccessPoint, Server, LDevice, Services,
    Communication, SubNetwork, ConnectedAP, Address, PhysConn,
    DataTypeTemplates, LNodeType, DOType, DAType, EnumType, EnumVal
)


class SCDParser(BaseParser):
    """SCD文件解析器"""
    
    def __init__(self, validate_schema: bool = True, strict_mode: bool = False):
        super().__init__(validate_schema, strict_mode)
        self.schema_validator = SchemaValidator()
        
        # 确保Schema文件存在
        self.schema_validator.ensure_schema_files()
    
    def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型"""
        return ['.scd', '.xml']
    
    def get_root_element_name(self) -> str:
        """获取根元素名称"""
        return 'SCL'
    
    def parse_root_element(self, root: etree.Element) -> 'SCLDocument':
        """解析SCL根元素"""
        # 创建SCL文档对象
        scl_doc = SCLDocument()
        
        # 解析Header
        header_elem = self.find_child_element(root, 'Header', required=True)
        scl_doc.header = self.parse_header(header_elem)
        
        # 解析Substation
        substation_elem = self.find_child_element(root, 'Substation')
        if substation_elem is not None:
            scl_doc.substation = self.parse_substation(substation_elem)
        
        # 解析Communication
        communication_elem = self.find_child_element(root, 'Communication')
        if communication_elem is not None:
            scl_doc.communication = self.parse_communication(communication_elem)
        
        # 解析IED列表
        ied_elements = self.find_child_elements(root, 'IED')
        for ied_elem in ied_elements:
            ied = self.parse_ied(ied_elem)
            scl_doc.ieds.append(ied)
        
        # 解析DataTypeTemplates
        templates_elem = self.find_child_element(root, 'DataTypeTemplates')
        if templates_elem is not None:
            scl_doc.data_type_templates = self.parse_data_type_templates(templates_elem)
        
        # 设置SCL属性
        scl_doc.version = self.get_required_attribute(root, 'version')
        scl_doc.revision = self.get_element_attribute(root, 'revision', 'A')

        # 现在可以安全地验证
        scl_doc.validate()

        return scl_doc
    
    def parse_header(self, header_elem: etree.Element) -> 'SCLHeader':
        """解析Header元素"""
        # 先获取所有属性
        header_id = self.get_required_attribute(header_elem, 'id')
        version = self.get_element_attribute(header_elem, 'version')
        revision = self.get_element_attribute(header_elem, 'revision')
        tool_id = self.get_element_attribute(header_elem, 'toolID')
        name_structure = self.get_element_attribute(header_elem, 'nameStructure')

        # 创建Header对象并设置属性
        header = SCLHeader(
            id=header_id,
            version=version,
            revision=revision,
            tool_id=tool_id,
            name_structure=name_structure
        )

        return header
    
    def parse_substation(self, substation_elem: etree.Element) -> SubStation:
        """解析Substation元素"""
        name = self.get_required_attribute(substation_elem, 'name')
        desc = self.get_element_attribute(substation_elem, 'desc')
        
        substation = SubStation(name=name, desc=desc)
        
        # 解析VoltageLevel列表
        vl_elements = self.find_child_elements(substation_elem, 'VoltageLevel')
        for vl_elem in vl_elements:
            voltage_level = self.parse_voltage_level(vl_elem)
            substation.add_voltage_level(voltage_level)
        
        return substation
    
    def parse_voltage_level(self, vl_elem: etree.Element) -> VoltageLevel:
        """解析VoltageLevel元素"""
        name = self.get_required_attribute(vl_elem, 'name')
        desc = self.get_element_attribute(vl_elem, 'desc')
        
        voltage_level = VoltageLevel(name=name, desc=desc)
        
        # 解析Voltage
        voltage_elem = self.find_child_element(vl_elem, 'Voltage')
        if voltage_elem is not None:
            voltage_level.voltage = self.parse_voltage(voltage_elem)
        
        # 解析Bay列表
        bay_elements = self.find_child_elements(vl_elem, 'Bay')
        for bay_elem in bay_elements:
            bay = self.parse_bay(bay_elem)
            voltage_level.add_bay(bay)
        
        return voltage_level
    
    def parse_voltage(self, voltage_elem: etree.Element) -> Voltage:
        """解析Voltage元素"""
        unit = self.get_required_attribute(voltage_elem, 'unit')
        multiplier = self.get_element_attribute(voltage_elem, 'multiplier', '')
        value_str = self.get_required_attribute(voltage_elem, 'value')
        
        try:
            value = float(value_str)
        except ValueError:
            raise ParseError(f"无效的电压值: {value_str}", element_name='Voltage')
        
        return Voltage(unit=unit, multiplier=multiplier, value=value)
    
    def parse_bay(self, bay_elem: etree.Element) -> Bay:
        """解析Bay元素"""
        name = self.get_required_attribute(bay_elem, 'name')
        desc = self.get_element_attribute(bay_elem, 'desc')
        
        bay = Bay(name=name, desc=desc)
        
        # 解析ConductingEquipment列表
        eq_elements = self.find_child_elements(bay_elem, 'ConductingEquipment')
        for eq_elem in eq_elements:
            equipment = self.parse_conducting_equipment(eq_elem)
            bay.add_equipment(equipment)
        
        return bay
    
    def parse_conducting_equipment(self, eq_elem: etree.Element) -> ConductingEquipment:
        """解析ConductingEquipment元素"""
        name = self.get_required_attribute(eq_elem, 'name')
        eq_type = self.get_required_attribute(eq_elem, 'type')
        desc = self.get_element_attribute(eq_elem, 'desc')
        virtual = self.parse_boolean_attribute(eq_elem, 'virtual', False)
        
        return ConductingEquipment(
            name=name,
            type=eq_type,
            desc=desc,
            virtual=virtual
        )
    
    def parse_communication(self, comm_elem: etree.Element) -> Communication:
        """解析Communication元素"""
        communication = Communication()
        
        # 解析SubNetwork列表
        subnet_elements = self.find_child_elements(comm_elem, 'SubNetwork')
        for subnet_elem in subnet_elements:
            subnet = self.parse_subnet(subnet_elem)
            communication.add_sub_network(subnet)
        
        return communication
    
    def parse_subnet(self, subnet_elem: etree.Element) -> SubNetwork:
        """解析SubNetwork元素"""
        name = self.get_required_attribute(subnet_elem, 'name')
        desc = self.get_element_attribute(subnet_elem, 'desc')
        net_type = self.get_element_attribute(subnet_elem, 'type', '8-MMS')
        
        subnet = SubNetwork(name=name, desc=desc, type=net_type)
        
        # 解析ConnectedAP列表
        cap_elements = self.find_child_elements(subnet_elem, 'ConnectedAP')
        for cap_elem in cap_elements:
            connected_ap = self.parse_connected_ap(cap_elem)
            subnet.add_connected_ap(connected_ap)
        
        return subnet
    
    def parse_connected_ap(self, cap_elem: etree.Element) -> ConnectedAP:
        """解析ConnectedAP元素"""
        ied_name = self.get_required_attribute(cap_elem, 'iedName')
        ap_name = self.get_required_attribute(cap_elem, 'apName')
        
        connected_ap = ConnectedAP(
            name=f"{ied_name}_{ap_name}",
            ied_name=ied_name,
            ap_name=ap_name
        )
        
        # 解析Address
        address_elem = self.find_child_element(cap_elem, 'Address')
        if address_elem is not None:
            connected_ap.address = self.parse_address(address_elem)
        
        return connected_ap
    
    def parse_address(self, address_elem: etree.Element) -> PhysConn:
        """解析Address元素"""
        phys_conn = PhysConn()
        
        # 解析P元素（地址参数）
        p_elements = self.find_child_elements(address_elem, 'P')
        for p_elem in p_elements:
            addr_type = self.get_required_attribute(p_elem, 'type')
            addr_value = self.get_element_text(p_elem)
            
            if addr_value:
                address = Address(type=addr_type, value=addr_value)
                phys_conn.add_address(address)
        
        return phys_conn
    
    def parse_ied(self, ied_elem: etree.Element) -> IED:
        """解析IED元素"""
        name = self.get_required_attribute(ied_elem, 'name')
        desc = self.get_element_attribute(ied_elem, 'desc')
        ied_type = self.get_element_attribute(ied_elem, 'type', 'TEMPLATE')
        manufacturer = self.get_element_attribute(ied_elem, 'manufacturer', '')
        config_version = self.get_element_attribute(ied_elem, 'configVersion', '1.0')
        
        ied = IED(
            name=name,
            desc=desc,
            type=ied_type,
            manufacturer=manufacturer,
            config_version=config_version
        )
        
        # 解析AccessPoint列表
        ap_elements = self.find_child_elements(ied_elem, 'AccessPoint')
        for ap_elem in ap_elements:
            access_point = self.parse_access_point(ap_elem)
            ied.add_access_point(access_point)
        
        return ied
    
    def parse_access_point(self, ap_elem: etree.Element) -> AccessPoint:
        """解析AccessPoint元素"""
        name = self.get_required_attribute(ap_elem, 'name')
        desc = self.get_element_attribute(ap_elem, 'desc')
        router = self.parse_boolean_attribute(ap_elem, 'router', False)
        clock = self.parse_boolean_attribute(ap_elem, 'clock', False)
        
        access_point = AccessPoint(
            name=name,
            desc=desc,
            router=router,
            clock=clock
        )
        
        # 解析Server
        server_elem = self.find_child_element(ap_elem, 'Server')
        if server_elem is not None:
            access_point.server = self.parse_server(server_elem)
        
        # 解析Services
        services_elem = self.find_child_element(ap_elem, 'Services')
        if services_elem is not None:
            access_point.services = self.parse_services(services_elem)
        
        return access_point
    
    def parse_server(self, server_elem: etree.Element) -> Server:
        """解析Server元素"""
        timeout = self.parse_int_attribute(server_elem, 'timeout', 30)
        
        server = Server(timeout=timeout)
        
        # 解析LDevice列表
        ld_elements = self.find_child_elements(server_elem, 'LDevice')
        for ld_elem in ld_elements:
            ldevice = self.parse_ldevice(ld_elem)
            server.add_ldevice(ldevice)
        
        return server
    
    def parse_ldevice(self, ld_elem: etree.Element) -> LDevice:
        """解析LDevice元素"""
        name = self.get_required_attribute(ld_elem, 'name')
        inst = self.get_required_attribute(ld_elem, 'inst')
        ldname = self.get_element_attribute(ld_elem, 'ldName')
        desc = self.get_element_attribute(ld_elem, 'desc')
        
        return LDevice(
            name=name,
            inst=inst,
            ldname=ldname,
            desc=desc
        )
    
    def parse_services(self, services_elem: etree.Element) -> Services:
        """解析Services元素"""
        services = Services()
        
        # 解析各种服务配置
        services.dyn_association = self._has_child_element(services_elem, 'DynAssociation')
        services.setting_groups = self._has_child_element(services_elem, 'SettingGroups')
        services.get_directory = self._has_child_element(services_elem, 'GetDirectory')
        services.get_data_object_definition = self._has_child_element(services_elem, 'GetDataObjectDefinition')
        services.data_object_directory = self._has_child_element(services_elem, 'DataObjectDirectory')
        services.get_data_set_value = self._has_child_element(services_elem, 'GetDataSetValue')
        services.set_data_set_value = self._has_child_element(services_elem, 'SetDataSetValue')
        services.data_set_directory = self._has_child_element(services_elem, 'DataSetDirectory')
        services.conf_data_set = self._has_child_element(services_elem, 'ConfDataSet')
        services.dyn_data_set = self._has_child_element(services_elem, 'DynDataSet')
        services.read_write = self._has_child_element(services_elem, 'ReadWrite')
        services.timer_activated_control = self._has_child_element(services_elem, 'TimerActivatedControl')
        services.conf_report_control = self._has_child_element(services_elem, 'ConfReportControl')
        services.get_cb_values = self._has_child_element(services_elem, 'GetCBValues')
        services.conf_log_control = self._has_child_element(services_elem, 'ConfLogControl')
        services.report_settings = self._has_child_element(services_elem, 'ReportSettings')
        services.log_settings = self._has_child_element(services_elem, 'LogSettings')
        services.gse_settings = self._has_child_element(services_elem, 'GSESettings')
        services.smv_settings = self._has_child_element(services_elem, 'SMVSettings')
        services.gse_dir = self._has_child_element(services_elem, 'GSEDir')
        services.goose = self._has_child_element(services_elem, 'GOOSE')
        services.gsse = self._has_child_element(services_elem, 'GSSE')
        services.sv = self._has_child_element(services_elem, 'SMV')
        services.sup_subscription = self._has_child_element(services_elem, 'SupSubscription')
        services.conf_sig_ref = self._has_child_element(services_elem, 'ConfSigRef')
        
        return services
    
    def parse_data_type_templates(self, templates_elem: etree.Element) -> DataTypeTemplates:
        """解析DataTypeTemplates元素"""
        templates = DataTypeTemplates()
        
        # 解析LNodeType列表
        lnt_elements = self.find_child_elements(templates_elem, 'LNodeType')
        for lnt_elem in lnt_elements:
            lnode_type = self.parse_lnode_type(lnt_elem)
            templates.add_lnode_type(lnode_type)
        
        # 解析DOType列表
        dot_elements = self.find_child_elements(templates_elem, 'DOType')
        for dot_elem in dot_elements:
            do_type = self.parse_do_type(dot_elem)
            templates.add_do_type(do_type)
        
        # 解析DAType列表
        dat_elements = self.find_child_elements(templates_elem, 'DAType')
        for dat_elem in dat_elements:
            da_type = self.parse_da_type(dat_elem)
            templates.add_da_type(da_type)
        
        # 解析EnumType列表
        et_elements = self.find_child_elements(templates_elem, 'EnumType')
        for et_elem in et_elements:
            enum_type = self.parse_enum_type(et_elem)
            templates.add_enum_type(enum_type)
        
        return templates
    
    def parse_lnode_type(self, lnt_elem: etree.Element) -> LNodeType:
        """解析LNodeType元素"""
        name = self.get_required_attribute(lnt_elem, 'id')
        lnclass = self.get_required_attribute(lnt_elem, 'lnClass')
        iedtype = self.get_element_attribute(lnt_elem, 'iedType')
        desc = self.get_element_attribute(lnt_elem, 'desc')
        
        return LNodeType(
            name=name,
            lnclass=lnclass,
            iedtype=iedtype,
            desc=desc
        )
    
    def parse_do_type(self, dot_elem: etree.Element) -> DOType:
        """解析DOType元素"""
        name = self.get_required_attribute(dot_elem, 'id')
        cdc = self.get_required_attribute(dot_elem, 'cdc')
        iedtype = self.get_element_attribute(dot_elem, 'iedType')
        desc = self.get_element_attribute(dot_elem, 'desc')

        do_type = DOType(
            name=name,
            cdc=cdc,
            iedtype=iedtype,
            desc=desc
        )

        # 解析SDO列表
        sdo_elements = self.find_child_elements(dot_elem, 'SDO')
        for sdo_elem in sdo_elements:
            sdo = self.parse_sdo(sdo_elem)
            do_type.add_sdo(sdo)

        # 解析DA列表
        da_elements = self.find_child_elements(dot_elem, 'DA')
        for da_elem in da_elements:
            da = self.parse_da(da_elem)
            do_type.add_da(da)

        return do_type

    def parse_sdo(self, sdo_elem: etree.Element) -> 'SDO':
        """解析SDO元素"""
        from ..models.data_type import SDO

        name = self.get_required_attribute(sdo_elem, 'name')
        sdo_type = self.get_required_attribute(sdo_elem, 'type')
        count = self.parse_int_attribute(sdo_elem, 'count')
        desc = self.get_element_attribute(sdo_elem, 'desc')

        return SDO(
            name=name,
            type=sdo_type,
            count=count,
            desc=desc
        )

    def parse_da(self, da_elem: etree.Element) -> 'BDA':
        """解析DA元素（在DOType中）"""
        from ..models.data_type import BDA

        name = self.get_required_attribute(da_elem, 'name')
        fc = self.get_required_attribute(da_elem, 'fc')
        btype = self.get_required_attribute(da_elem, 'bType')
        type_ref = self.get_element_attribute(da_elem, 'type')
        saddr = self.get_element_attribute(da_elem, 'saddr')
        val_kind = self.get_element_attribute(da_elem, 'valKind', 'Set')
        val_import = self.parse_boolean_attribute(da_elem, 'valImport', False)
        count = self.parse_int_attribute(da_elem, 'count')
        desc = self.get_element_attribute(da_elem, 'desc')

        # 解析触发选项
        trigger_options = []
        if self.parse_boolean_attribute(da_elem, 'dchg', False):
            trigger_options.append('dchg')
        if self.parse_boolean_attribute(da_elem, 'qchg', False):
            trigger_options.append('qchg')
        if self.parse_boolean_attribute(da_elem, 'dupd', False):
            trigger_options.append('dupd')

        # 注意：这里创建的是BDA对象，但设置了fc属性
        bda = BDA(
            name=name,
            btype=btype,
            type=type_ref,
            saddr=saddr,
            val_kind=val_kind,
            val_import=val_import,
            count=count,
            desc=desc
        )

        # 手动设置fc属性（BDA类可能没有这个属性，但我们需要存储它）
        # 这是一个临时解决方案，理想情况下应该有专门的DA类
        if hasattr(bda, 'fc'):
            bda.fc = fc

        return bda

    def parse_da_type(self, dat_elem: etree.Element) -> DAType:
        """解析DAType元素"""
        name = self.get_required_attribute(dat_elem, 'id')
        desc = self.get_element_attribute(dat_elem, 'desc')

        # 创建DAType但不立即验证
        da_type = DAType.__new__(DAType)
        da_type.name = name
        da_type.desc = desc
        da_type.bdas = []

        # 手动设置基础属性
        from uuid import uuid4
        from datetime import datetime
        da_type.id = str(uuid4())
        da_type.created_at = datetime.now()
        da_type.updated_at = datetime.now()

        # 解析BDA列表
        bda_elements = self.find_child_elements(dat_elem, 'BDA')
        for bda_elem in bda_elements:
            bda = self.parse_bda(bda_elem)
            da_type.bdas.append(bda)

        # 现在验证
        da_type.validate()

        return da_type

    def parse_bda(self, bda_elem: etree.Element) -> 'BDA':
        """解析BDA元素"""
        from ..models.data_type import BDA

        name = self.get_required_attribute(bda_elem, 'name')
        btype = self.get_required_attribute(bda_elem, 'bType')
        type_ref = self.get_element_attribute(bda_elem, 'type')
        saddr = self.get_element_attribute(bda_elem, 'saddr')
        val_kind = self.get_element_attribute(bda_elem, 'valKind', 'Set')
        val_import = self.parse_boolean_attribute(bda_elem, 'valImport', False)
        count = self.parse_int_attribute(bda_elem, 'count')
        desc = self.get_element_attribute(bda_elem, 'desc')

        return BDA(
            name=name,
            btype=btype,
            type=type_ref,
            saddr=saddr,
            val_kind=val_kind,
            val_import=val_import,
            count=count,
            desc=desc
        )

    def parse_enum_type(self, et_elem: etree.Element) -> EnumType:
        """解析EnumType元素"""
        name = self.get_required_attribute(et_elem, 'id')
        desc = self.get_element_attribute(et_elem, 'desc')

        # 创建EnumType但不立即验证
        enum_type = EnumType.__new__(EnumType)
        enum_type.name = name
        enum_type.desc = desc
        enum_type.enum_vals = []

        # 手动设置基础属性
        from uuid import uuid4
        from datetime import datetime
        enum_type.id = str(uuid4())
        enum_type.created_at = datetime.now()
        enum_type.updated_at = datetime.now()

        # 解析EnumVal列表
        ev_elements = self.find_child_elements(et_elem, 'EnumVal')
        for ev_elem in ev_elements:
            ord_val = self.parse_int_attribute(ev_elem, 'ord', 0)
            value = self.get_element_text(ev_elem)
            ev_desc = self.get_element_attribute(ev_elem, 'desc')

            enum_val = EnumVal(ord=ord_val, value=value, desc=ev_desc)
            enum_type.enum_vals.append(enum_val)

        # 现在验证
        enum_type.validate()

        return enum_type
    
    def _has_child_element(self, parent: etree.Element, tag_name: str) -> bool:
        """检查是否有指定的子元素"""
        return self.find_child_element(parent, tag_name) is not None
    
    def _validate_schema(self, tree: etree.ElementTree) -> List[ParseError]:
        """Schema验证"""
        if not self.validate_schema:
            return []
        
        return self.schema_validator.validate_document(tree, 'SCL')


# SCL文档相关的数据类
from dataclasses import dataclass
from ..models.base import BaseModel

@dataclass
class SCLHeader(BaseModel):
    """SCL文件头"""
    id: str = ""
    version: Optional[str] = None
    revision: Optional[str] = None
    tool_id: Optional[str] = None
    name_structure: Optional[str] = None
    
    def validate(self) -> None:
        if not self.id:
            from ..models.base import ValidationError
            raise ValidationError("Header ID不能为空", "id")


@dataclass
class SCLDocument(BaseModel):
    """SCL文档"""
    version: str = ""
    revision: str = "A"
    header: Optional[SCLHeader] = None
    substation: Optional[SubStation] = None
    communication: Optional[Communication] = None
    ieds: List[IED] = None
    data_type_templates: Optional[DataTypeTemplates] = None
    
    def __post_init__(self):
        if self.ieds is None:
            self.ieds = []
        # 不调用super().__post_init__()，因为version可能还没设置
    
    def validate(self) -> None:
        if not self.version:
            from ..models.base import ValidationError
            raise ValidationError("SCL版本不能为空", "version")
        
        if self.header:
            self.header.validate()
    
    def get_ied_by_name(self, name: str) -> Optional[IED]:
        """根据名称获取IED"""
        for ied in self.ieds:
            if ied.name == name:
                return ied
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'version': self.version,
            'revision': self.revision,
            'ieds_count': len(self.ieds),
            'has_substation': self.substation is not None,
            'has_communication': self.communication is not None,
            'has_data_type_templates': self.data_type_templates is not None
        }
        
        if self.substation:
            substation_stats = self.substation.get_statistics()
            stats.update({
                'voltage_levels_count': substation_stats['voltage_levels_count'],
                'bays_count': substation_stats['bays_count'],
                'equipments_count': substation_stats['equipments_count']
            })
        
        if self.communication:
            comm_topology = self.communication.get_network_topology()
            stats.update({
                'sub_networks_count': len(comm_topology['sub_networks']),
                'connected_aps_count': comm_topology['total_connected_aps']
            })
        
        return stats
