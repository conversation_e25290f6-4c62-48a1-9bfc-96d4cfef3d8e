"""
对比报告生成器
生成专业的配置文件对比报告
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from .comparator import ComparisonResult, Change, ChangeType, ChangeLevel


class ComparisonReporter:
    """
    配置文件对比报告生成器
    
    为智能变电站工程师生成专业的对比报告：
    1. 结构化的变更摘要
    2. 详细的变更列表
    3. 风险评估和建议
    4. 多种输出格式支持
    """
    
    def __init__(self):
        self.report_templates = {
            'executive_summary': self._generate_executive_summary,
            'detailed_changes': self._generate_detailed_changes,
            'risk_assessment': self._generate_risk_assessment,
            'recommendations': self._generate_recommendations,
            'statistics': self._generate_statistics
        }
    
    def generate_report(self, comparison_result: ComparisonResult, 
                       title: str = "配置文件对比报告") -> Dict[str, Any]:
        """
        生成完整的对比报告
        
        Args:
            comparison_result: 对比结果
            title: 报告标题
            
        Returns:
            Dict: 报告数据
        """
        report = {
            'title': title,
            'generated_at': datetime.now().isoformat(),
            'version': '1.0.0',
            'comparison_id': comparison_result.id,
            'source_file': comparison_result.source_file,
            'target_file': comparison_result.target_file,
            'source_version': comparison_result.source_version,
            'target_version': comparison_result.target_version,
            'comparison_time': comparison_result.comparison_time.isoformat(),
            'sections': []
        }
        
        # 生成各个报告部分
        for section_name, generator in self.report_templates.items():
            try:
                section_data = generator(comparison_result)
                if section_data:
                    report['sections'].append(section_data)
            except Exception as e:
                print(f"生成报告部分 {section_name} 失败: {e}")
        
        # 添加元数据
        report['metadata'] = {
            'total_changes': len(comparison_result.changes),
            'critical_changes': len(comparison_result.get_critical_changes()),
            'has_critical_issues': comparison_result.has_critical_changes(),
            'statistics': comparison_result.statistics
        }
        
        return report
    
    def _generate_executive_summary(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成执行摘要"""
        stats = result.statistics
        total_changes = stats.get('total_changes', 0)
        
        if total_changes == 0:
            summary_text = "两个配置文件完全相同，未发现任何差异。这表明配置保持了良好的一致性。"
            risk_level = "无风险"
        else:
            critical = stats.get('by_level', {}).get('critical', 0)
            major = stats.get('by_level', {}).get('major', 0)
            minor = stats.get('by_level', {}).get('minor', 0)
            
            summary_parts = [f"共发现 {total_changes} 处变更"]
            
            if critical > 0:
                summary_parts.append(f"其中 {critical} 处关键变更需要立即处理")
                risk_level = "高风险"
            elif major > 0:
                summary_parts.append(f"{major} 处重要变更需要仔细评估")
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            if minor > 0:
                summary_parts.append(f"{minor} 处次要变更")
            
            summary_text = "，".join(summary_parts) + "。"
        
        return {
            'title': '执行摘要',
            'type': 'executive_summary',
            'content': summary_text,
            'risk_level': risk_level,
            'key_metrics': {
                '总变更数': total_changes,
                '关键变更': stats.get('by_level', {}).get('critical', 0),
                '重要变更': stats.get('by_level', {}).get('major', 0),
                '次要变更': stats.get('by_level', {}).get('minor', 0)
            }
        }
    
    def _generate_detailed_changes(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成详细变更列表"""
        if not result.changes:
            return {
                'title': '详细变更',
                'type': 'detailed_changes',
                'content': '未发现任何变更。',
                'changes': []
            }
        
        # 按级别分组变更
        changes_by_level = {}
        for level in ChangeLevel:
            level_changes = result.get_changes_by_level(level)
            if level_changes:
                changes_by_level[level.value] = [
                    self._format_change_for_report(change) for change in level_changes
                ]
        
        return {
            'title': '详细变更',
            'type': 'detailed_changes',
            'content': f'以下是 {len(result.changes)} 处变更的详细信息：',
            'changes_by_level': changes_by_level,
            'changes': [self._format_change_for_report(change) for change in result.changes]
        }
    
    def _generate_risk_assessment(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成风险评估"""
        critical_changes = result.get_critical_changes()
        
        if not critical_changes:
            risk_content = "本次变更未发现关键风险项。所有变更都在可接受的风险范围内。"
            overall_risk = "低风险"
        else:
            risk_items = []
            for change in critical_changes:
                risk_items.append({
                    'item': change.description,
                    'impact': change.impact_analysis,
                    'mitigation': change.recommendation
                })
            
            risk_content = f"发现 {len(critical_changes)} 项关键风险需要重点关注。"
            overall_risk = "高风险"
        
        return {
            'title': '风险评估',
            'type': 'risk_assessment',
            'content': risk_content,
            'overall_risk': overall_risk,
            'risk_items': risk_items if critical_changes else [],
            'mitigation_required': len(critical_changes) > 0
        }
    
    def _generate_recommendations(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成建议"""
        recommendations = []
        
        # 收集所有建议
        for change in result.changes:
            if change.recommendation and change.recommendation not in [r['text'] for r in recommendations]:
                recommendations.append({
                    'priority': self._get_recommendation_priority(change.change_level),
                    'text': change.recommendation,
                    'related_changes': [c.description for c in result.changes 
                                      if c.recommendation == change.recommendation]
                })
        
        # 按优先级排序
        recommendations.sort(key=lambda x: {'critical': 0, 'major': 1, 'minor': 2, 'cosmetic': 3}
                           .get(x['priority'], 4))
        
        if not recommendations:
            content = "当前变更无需特殊处理建议。"
        else:
            content = f"基于 {len(result.changes)} 处变更，提供以下 {len(recommendations)} 项建议："
        
        return {
            'title': '处理建议',
            'type': 'recommendations',
            'content': content,
            'recommendations': recommendations
        }
    
    def _generate_statistics(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成统计信息"""
        stats = result.statistics
        
        # 按类型统计
        type_stats = stats.get('by_type', {})
        type_chart_data = [
            {'label': '新增', 'value': type_stats.get('added', 0), 'color': '#28a745'},
            {'label': '删除', 'value': type_stats.get('removed', 0), 'color': '#dc3545'},
            {'label': '修改', 'value': type_stats.get('modified', 0), 'color': '#ffc107'},
            {'label': '移动', 'value': type_stats.get('moved', 0), 'color': '#17a2b8'},
            {'label': '重命名', 'value': type_stats.get('renamed', 0), 'color': '#6f42c1'}
        ]
        
        # 按级别统计
        level_stats = stats.get('by_level', {})
        level_chart_data = [
            {'label': '关键', 'value': level_stats.get('critical', 0), 'color': '#dc3545'},
            {'label': '重要', 'value': level_stats.get('major', 0), 'color': '#fd7e14'},
            {'label': '次要', 'value': level_stats.get('minor', 0), 'color': '#ffc107'},
            {'label': '外观', 'value': level_stats.get('cosmetic', 0), 'color': '#6c757d'}
        ]
        
        return {
            'title': '统计分析',
            'type': 'statistics',
            'content': f'变更统计分析（总计 {stats.get("total_changes", 0)} 处变更）',
            'charts': {
                'by_type': {
                    'title': '按变更类型分布',
                    'type': 'pie',
                    'data': type_chart_data
                },
                'by_level': {
                    'title': '按严重程度分布',
                    'type': 'pie',
                    'data': level_chart_data
                }
            },
            'summary_table': {
                'headers': ['类型', '数量', '占比'],
                'rows': [
                    ['新增', type_stats.get('added', 0), 
                     f"{type_stats.get('added', 0) / max(stats.get('total_changes', 1), 1) * 100:.1f}%"],
                    ['删除', type_stats.get('removed', 0),
                     f"{type_stats.get('removed', 0) / max(stats.get('total_changes', 1), 1) * 100:.1f}%"],
                    ['修改', type_stats.get('modified', 0),
                     f"{type_stats.get('modified', 0) / max(stats.get('total_changes', 1), 1) * 100:.1f}%"]
                ]
            }
        }
    
    def _format_change_for_report(self, change: Change) -> Dict[str, Any]:
        """格式化变更信息用于报告"""
        return {
            'id': change.id,
            'type': change.change_type.value,
            'level': change.change_level.value,
            'path': change.path,
            'element_type': change.element_type,
            'element_name': change.element_name,
            'description': change.description,
            'old_value': change.old_value,
            'new_value': change.new_value,
            'impact': change.impact_analysis,
            'recommendation': change.recommendation,
            'timestamp': change.timestamp.isoformat()
        }
    
    def _get_recommendation_priority(self, change_level: ChangeLevel) -> str:
        """获取建议优先级"""
        priority_map = {
            ChangeLevel.CRITICAL: 'critical',
            ChangeLevel.MAJOR: 'major',
            ChangeLevel.MINOR: 'minor',
            ChangeLevel.COSMETIC: 'cosmetic'
        }
        return priority_map.get(change_level, 'minor')
    
    def save_report(self, report: Dict[str, Any], output_path: str, format_type: str = 'json'):
        """
        保存报告到文件
        
        Args:
            report: 报告数据
            output_path: 输出路径
            format_type: 格式类型 (json, html, markdown)
        """
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        if format_type.lower() == 'json':
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
        
        elif format_type.lower() == 'html':
            html_content = self._generate_html_report(report)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        elif format_type.lower() == 'markdown':
            md_content = self._generate_markdown_report(report)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(md_content)
        
        else:
            raise ValueError(f"不支持的格式类型: {format_type}")
    
    def _generate_html_report(self, report: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{report['title']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        .header {{ border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }}
        .section {{ margin-bottom: 30px; }}
        .change-item {{ background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }}
        .critical {{ border-left: 5px solid #dc3545; }}
        .major {{ border-left: 5px solid #fd7e14; }}
        .minor {{ border-left: 5px solid #ffc107; }}
        .cosmetic {{ border-left: 5px solid #6c757d; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{report['title']}</h1>
        <p><strong>生成时间:</strong> {report['generated_at']}</p>
        <p><strong>源文件:</strong> {report['source_file']}</p>
        <p><strong>目标文件:</strong> {report['target_file']}</p>
    </div>
"""
        
        # 添加各个部分
        for section in report['sections']:
            html_template += f"""
    <div class="section">
        <h2>{section['title']}</h2>
        <p>{section['content']}</p>
"""
            
            # 如果有变更列表，添加变更详情
            if 'changes' in section:
                for change in section['changes']:
                    level_class = change['level']
                    html_template += f"""
        <div class="change-item {level_class}">
            <h4>{change['description']}</h4>
            <p><strong>路径:</strong> {change['path']}</p>
            <p><strong>影响分析:</strong> {change['impact']}</p>
            <p><strong>建议:</strong> {change['recommendation']}</p>
        </div>
"""
            
            html_template += "    </div>\n"
        
        html_template += """
</body>
</html>
"""
        return html_template
    
    def _generate_markdown_report(self, report: Dict[str, Any]) -> str:
        """生成Markdown格式报告"""
        md_content = f"""# {report['title']}

**生成时间:** {report['generated_at']}  
**源文件:** {report['source_file']}  
**目标文件:** {report['target_file']}  
**对比时间:** {report['comparison_time']}

---

"""
        
        # 添加各个部分
        for section in report['sections']:
            md_content += f"## {section['title']}\n\n"
            md_content += f"{section['content']}\n\n"
            
            # 如果有变更列表，添加变更详情
            if 'changes' in section:
                for change in section['changes']:
                    level_emoji = {
                        'critical': '🔴',
                        'major': '🟠', 
                        'minor': '🟡',
                        'cosmetic': '⚪'
                    }.get(change['level'], '⚪')
                    
                    md_content += f"### {level_emoji} {change['description']}\n\n"
                    md_content += f"**路径:** `{change['path']}`\n\n"
                    md_content += f"**影响分析:** {change['impact']}\n\n"
                    md_content += f"**建议:** {change['recommendation']}\n\n"
                    md_content += "---\n\n"
        
        return md_content
