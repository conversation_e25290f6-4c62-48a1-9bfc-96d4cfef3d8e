<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .control-line { stroke: green; stroke-width: 2; fill: none; }
            .power-line { stroke: red; stroke-width: 3; fill: none; }
            .signal-line { stroke: orange; stroke-width: 1.5; fill: none; stroke-dasharray: 3,3; }
            .voltage-label { font-family: Arial, sans-serif; font-size: 10px; fill: #666; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1000" height="800" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="500" y="30" text-anchor="middle" class="title-text">断路器控制回路图</text>
    <text x="500" y="50" text-anchor="middle" font-size="12" fill="#666">DC 220V操作电源</text>
    
    <!-- 电源母线 -->
    <g transform="translate(50, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">直流操作电源:</text>
        <line x1="0" y1="20" x2="900" y2="20" stroke="red" stroke-width="4"/>
        <text x="10" y="15" class="voltage-label">+KM (DC +220V)</text>
        <line x1="0" y1="700" x2="900" y2="700" stroke="blue" stroke-width="4"/>
        <text x="10" y="695" class="voltage-label">-KM (DC -220V)</text>
    </g>
    
    <!-- 控制开关 -->
    <g transform="translate(100, 150)">
        <rect x="0" y="0" width="80" height="60" fill="lightblue" stroke="black" stroke-width="2"/>
        <text x="40" y="25" text-anchor="middle" font-size="10" font-weight="bold">控制开关</text>
        <text x="40" y="40" text-anchor="middle" font-size="8">SA1</text>
        <text x="40" y="55" text-anchor="middle" font-size="8">220kV出线</text>
        <!-- 合闸按钮 -->
        <circle cx="20" cy="80" r="8" fill="green" stroke="black"/>
        <text x="20" y="85" text-anchor="middle" font-size="6" fill="white">合</text>
        <!-- 分闸按钮 -->
        <circle cx="60" cy="80" r="8" fill="red" stroke="black"/>
        <text x="60" y="85" text-anchor="middle" font-size="6" fill="white">分</text>
    </g>
    
    <!-- 合闸线圈 -->
    <g transform="translate(350, 130)">
        <rect x="0" y="0" width="60" height="30" fill="lightgreen" stroke="black" stroke-width="2"/>
        <text x="30" y="20" text-anchor="middle" font-size="10" font-weight="bold">YC</text>
        <text x="30" y="45" text-anchor="middle" font-size="8">合闸线圈</text>
        <circle cx="-5" cy="15" r="2" fill="green"/>
        <circle cx="65" cy="15" r="2" fill="green"/>
    </g>
    
    <!-- 跳闸线圈 -->
    <g transform="translate(350, 200)">
        <rect x="0" y="0" width="60" height="30" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="30" y="20" text-anchor="middle" font-size="10" font-weight="bold">YT</text>
        <text x="30" y="45" text-anchor="middle" font-size="8">跳闸线圈</text>
        <circle cx="-5" cy="15" r="2" fill="red"/>
        <circle cx="65" cy="15" r="2" fill="red"/>
    </g>
    
    <!-- 断路器本体 -->
    <g transform="translate(550, 150)">
        <rect x="0" y="0" width="80" height="80" fill="none" stroke="black" stroke-width="3"/>
        <line x1="10" y1="10" x2="70" y2="70" stroke="black" stroke-width="4"/>
        <text x="40" y="95" text-anchor="middle" font-size="10" font-weight="bold">QF1</text>
        <text x="40" y="110" text-anchor="middle" font-size="8">220kV出线断路器</text>
        <!-- 辅助触点 -->
        <circle cx="-10" cy="40" r="3" fill="blue"/>
        <text x="-25" y="35" font-size="6">合位</text>
        <circle cx="90" cy="40" r="3" fill="blue"/>
        <text x="95" y="35" font-size="6">分位</text>
    </g>
    
    <!-- 信号灯 -->
    <g transform="translate(750, 130)">
        <circle cx="20" cy="20" r="12" fill="green" stroke="black" stroke-width="2"/>
        <text x="20" y="25" text-anchor="middle" font-size="8" fill="white">合</text>
        <text x="20" y="50" text-anchor="middle" font-size="8">合位灯 HL1</text>
    </g>
    
    <g transform="translate(750, 200)">
        <circle cx="20" cy="20" r="12" fill="red" stroke="black" stroke-width="2"/>
        <text x="20" y="25" text-anchor="middle" font-size="8" fill="white">分</text>
        <text x="20" y="50" text-anchor="middle" font-size="8">分位灯 HL2</text>
    </g>
    
    <!-- 连接线 -->
    <!-- 电源线 -->
    <line x1="50" y1="100" x2="120" y2="150" class="power-line"/>
    <line x1="50" y1="100" x2="345" y2="145" class="power-line"/>
    <line x1="50" y1="100" x2="345" y2="215" class="power-line"/>
    
    <!-- 控制线 -->
    <line x1="120" y1="230" x2="345" y2="145" class="control-line"/>
    <line x1="180" y1="230" x2="345" y2="215" class="control-line"/>
    
    <!-- 线圈到断路器 -->
    <line x1="415" y1="145" x2="550" y2="170" class="control-line"/>
    <line x1="415" y1="215" x2="550" y2="210" class="control-line"/>
    
    <!-- 辅助触点到信号灯 -->
    <line x1="540" y1="190" x2="750" y2="150" class="signal-line"/>
    <line x1="640" y1="190" x2="750" y2="220" class="signal-line"/>
    
    <!-- 回路说明 -->
    <g transform="translate(50, 400)">
        <text x="0" y="0" font-size="14" font-weight="bold">控制回路工作原理:</text>
        <text x="0" y="25" font-size="11">1. 操作人员按下合闸按钮，合闸回路接通</text>
        <text x="0" y="45" font-size="11">2. 合闸线圈YC得电，断路器QF1合闸</text>
        <text x="0" y="65" font-size="11">3. 断路器合位辅助触点闭合，合位灯HL1亮</text>
        <text x="0" y="85" font-size="11">4. 分闸操作类似，通过跳闸线圈YT实现</text>
        
        <text x="400" y="0" font-size="14" font-weight="bold">技术要点:</text>
        <text x="400" y="25" font-size="11">• 操作电源：DC 220V（高可靠性）</text>
        <text x="400" y="45" font-size="11">• 防跳回路：防止断路器跳跃</text>
        <text x="400" y="65" font-size="11">• 位置信号：实时反映断路器状态</text>
        <text x="400" y="85" font-size="11">• 虚端子：可通过GOOSE实现远程控制</text>
    </g>
    
    <!-- 虚端子技术对比 -->
    <g transform="translate(50, 550)">
        <rect x="0" y="0" width="900" height="150" fill="#e8f5e8" stroke="#28a745" stroke-width="2"/>
        <text x="450" y="25" text-anchor="middle" font-size="14" font-weight="bold">虚端子技术在控制回路中的应用</text>
        
        <g transform="translate(50, 50)">
            <text x="0" y="0" font-size="12" font-weight="bold">传统硬接线控制:</text>
            <text x="0" y="20" font-size="10">• 控制室到开关场需要大量控制电缆</text>
            <text x="0" y="35" font-size="10">• 接线复杂，故障点多</text>
            <text x="0" y="50" font-size="10">• 维护工作量大</text>
            <text x="0" y="65" font-size="10">• 扩展困难</text>
        </g>
        
        <g transform="translate(450, 50)">
            <text x="0" y="0" font-size="12" font-weight="bold">虚端子数字化控制:</text>
            <text x="0" y="20" font-size="10">• 通过GOOSE网络传输控制命令</text>
            <text x="0" y="35" font-size="10">• 减少硬接线，提高可靠性</text>
            <text x="0" y="50" font-size="10">• 远程操作，维护方便</text>
            <text x="0" y="65" font-size="10">• 易于扩展和修改</text>
        </g>
    </g>
    
</svg>