#!/usr/bin/env python3
"""
自动化测试运行脚本
运行完整的测试套件并生成报告
"""

import os
import sys
import subprocess
import argparse
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional


class TestRunner:
    """测试运行器"""
    
    def __init__(self, verbose: bool = False):
        """初始化测试运行器"""
        self.verbose = verbose
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 确保在项目根目录
        self.project_root = Path(__file__).parent.parent
        os.chdir(self.project_root)
        
        print("测试运行器初始化完成")
    
    def run_all_tests(self, test_types: List[str] = None) -> Dict[str, Any]:
        """运行所有测试"""
        self.start_time = time.time()
        
        print(f"\n{'='*60}")
        print(f"IEC61850智能设计检查器 - 自动化测试")
        print(f"{'='*60}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 默认测试类型
        if test_types is None:
            test_types = ['unit', 'integration', 'e2e', 'performance']
        
        # 运行各类测试
        for test_type in test_types:
            print(f"\n🧪 运行 {test_type.upper()} 测试...")
            
            if test_type == 'unit':
                self.test_results['unit'] = self._run_unit_tests()
            elif test_type == 'integration':
                self.test_results['integration'] = self._run_integration_tests()
            elif test_type == 'e2e':
                self.test_results['e2e'] = self._run_e2e_tests()
            elif test_type == 'performance':
                self.test_results['performance'] = self._run_performance_tests()
            elif test_type == 'coverage':
                self.test_results['coverage'] = self._run_coverage_tests()
        
        self.end_time = time.time()
        
        # 生成测试报告
        report = self._generate_test_report()
        
        # 显示结果摘要
        self._display_summary(report)
        
        return report
    
    def _run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        result = {
            'name': '单元测试',
            'status': 'unknown',
            'duration': 0,
            'tests_run': 0,
            'failures': 0,
            'errors': 0,
            'output': '',
            'details': {}
        }
        
        try:
            start_time = time.time()
            
            # 运行pytest单元测试
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/unit/',
                '-v',
                '--tb=short',
                '--json-report',
                '--json-report-file=test_results_unit.json'
            ]
            
            if not self.verbose:
                cmd.append('-q')
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            result['duration'] = time.time() - start_time
            result['output'] = process.stdout + process.stderr
            result['return_code'] = process.returncode
            
            # 解析JSON报告
            json_report_file = Path('test_results_unit.json')
            if json_report_file.exists():
                try:
                    with open(json_report_file, 'r', encoding='utf-8') as f:
                        json_report = json.load(f)
                    
                    result['tests_run'] = json_report.get('summary', {}).get('total', 0)
                    result['failures'] = json_report.get('summary', {}).get('failed', 0)
                    result['errors'] = json_report.get('summary', {}).get('error', 0)
                    result['details'] = json_report.get('summary', {})
                    
                    # 清理临时文件
                    json_report_file.unlink()
                    
                except Exception as e:
                    result['details']['json_parse_error'] = str(e)
            
            # 确定状态
            if process.returncode == 0:
                result['status'] = 'pass'
            else:
                result['status'] = 'fail'
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['output'] = '测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['output'] = str(e)
        
        return result
    
    def _run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        result = {
            'name': '集成测试',
            'status': 'unknown',
            'duration': 0,
            'tests_run': 0,
            'failures': 0,
            'errors': 0,
            'output': '',
            'details': {}
        }
        
        try:
            start_time = time.time()
            
            # 运行集成测试
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/integration/',
                '-v',
                '--tb=short'
            ]
            
            if not self.verbose:
                cmd.append('-q')
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            result['duration'] = time.time() - start_time
            result['output'] = process.stdout + process.stderr
            result['return_code'] = process.returncode
            
            # 解析输出获取测试统计
            output_lines = process.stdout.split('\n')
            for line in output_lines:
                if 'passed' in line or 'failed' in line:
                    # 简单解析测试结果
                    if 'passed' in line:
                        result['status'] = 'pass'
                    elif 'failed' in line:
                        result['status'] = 'fail'
            
            if result['status'] == 'unknown':
                result['status'] = 'pass' if process.returncode == 0 else 'fail'
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['output'] = '集成测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['output'] = str(e)
        
        return result
    
    def _run_e2e_tests(self) -> Dict[str, Any]:
        """运行端到端测试"""
        result = {
            'name': '端到端测试',
            'status': 'unknown',
            'duration': 0,
            'tests_run': 0,
            'failures': 0,
            'errors': 0,
            'output': '',
            'details': {}
        }
        
        try:
            start_time = time.time()
            
            # 运行端到端测试
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/e2e/',
                '-v',
                '--tb=short',
                '-s'  # 显示print输出
            ]
            
            if not self.verbose:
                cmd.append('-q')
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=900  # 15分钟超时
            )
            
            result['duration'] = time.time() - start_time
            result['output'] = process.stdout + process.stderr
            result['return_code'] = process.returncode
            result['status'] = 'pass' if process.returncode == 0 else 'fail'
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['output'] = '端到端测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['output'] = str(e)
        
        return result
    
    def _run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        result = {
            'name': '性能测试',
            'status': 'unknown',
            'duration': 0,
            'tests_run': 0,
            'failures': 0,
            'errors': 0,
            'output': '',
            'details': {}
        }
        
        try:
            start_time = time.time()
            
            # 运行性能测试
            cmd = [
                sys.executable, '-m', 'pytest',
                'tests/performance/',
                '-v',
                '--tb=short',
                '-s'
            ]
            
            if not self.verbose:
                cmd.append('-q')
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            result['duration'] = time.time() - start_time
            result['output'] = process.stdout + process.stderr
            result['return_code'] = process.returncode
            result['status'] = 'pass' if process.returncode == 0 else 'fail'
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['output'] = '性能测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['output'] = str(e)
        
        return result
    
    def _run_coverage_tests(self) -> Dict[str, Any]:
        """运行代码覆盖率测试"""
        result = {
            'name': '代码覆盖率测试',
            'status': 'unknown',
            'duration': 0,
            'coverage_percent': 0,
            'output': '',
            'details': {}
        }
        
        try:
            start_time = time.time()
            
            # 运行覆盖率测试
            cmd = [
                sys.executable, '-m', 'pytest',
                '--cov=src',
                '--cov-report=term-missing',
                '--cov-report=html:htmlcov',
                '--cov-report=json:coverage.json',
                'tests/unit/',
                'tests/integration/'
            ]
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10分钟超时
            )
            
            result['duration'] = time.time() - start_time
            result['output'] = process.stdout + process.stderr
            result['return_code'] = process.returncode
            
            # 解析覆盖率报告
            coverage_file = Path('coverage.json')
            if coverage_file.exists():
                try:
                    with open(coverage_file, 'r', encoding='utf-8') as f:
                        coverage_data = json.load(f)
                    
                    result['coverage_percent'] = coverage_data.get('totals', {}).get('percent_covered', 0)
                    result['details'] = coverage_data.get('totals', {})
                    
                except Exception as e:
                    result['details']['coverage_parse_error'] = str(e)
            
            result['status'] = 'pass' if process.returncode == 0 else 'fail'
            
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['output'] = '覆盖率测试超时'
        except Exception as e:
            result['status'] = 'error'
            result['output'] = str(e)
        
        return result
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
        
        # 计算总体统计
        total_tests = sum(result.get('tests_run', 0) for result in self.test_results.values())
        total_failures = sum(result.get('failures', 0) for result in self.test_results.values())
        total_errors = sum(result.get('errors', 0) for result in self.test_results.values())
        
        # 确定总体状态
        overall_status = 'pass'
        for result in self.test_results.values():
            if result.get('status') in ['fail', 'error', 'timeout']:
                overall_status = 'fail'
                break
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status,
            'total_duration': total_duration,
            'summary': {
                'total_test_suites': len(self.test_results),
                'total_tests': total_tests,
                'total_failures': total_failures,
                'total_errors': total_errors,
                'success_rate': ((total_tests - total_failures - total_errors) / total_tests * 100) if total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'environment': {
                'python_version': sys.version,
                'platform': sys.platform,
                'working_directory': str(self.project_root)
            }
        }
        
        return report
    
    def _display_summary(self, report: Dict[str, Any]):
        """显示测试结果摘要"""
        print(f"\n{'='*60}")
        print(f"测试结果摘要")
        print(f"{'='*60}")
        print(f"总体状态: {report['overall_status'].upper()}")
        print(f"总耗时: {report['total_duration']:.2f}秒")
        print(f"测试套件: {report['summary']['total_test_suites']}")
        print(f"总测试数: {report['summary']['total_tests']}")
        print(f"失败数: {report['summary']['total_failures']}")
        print(f"错误数: {report['summary']['total_errors']}")
        print(f"成功率: {report['summary']['success_rate']:.1f}%")
        
        print(f"\n详细结果:")
        for test_type, result in self.test_results.items():
            status_symbol = {
                'pass': '✅',
                'fail': '❌',
                'error': '💥',
                'timeout': '⏰',
                'unknown': '❓'
            }.get(result.get('status'), '❓')
            
            print(f"{status_symbol} {result['name']}: {result.get('status', 'unknown').upper()} ({result.get('duration', 0):.2f}s)")
            
            if self.verbose and result.get('output'):
                print(f"   输出: {result['output'][:200]}...")
    
    def save_report(self, report: Dict[str, Any], filename: str = None):
        """保存测试报告"""
        if filename is None:
            filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"\n📄 测试报告已保存: {filename}")
            
        except Exception as e:
            print(f"\n❌ 保存测试报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='IEC61850智能设计检查器自动化测试')
    parser.add_argument('--types', nargs='+', 
                       choices=['unit', 'integration', 'e2e', 'performance', 'coverage'],
                       help='指定要运行的测试类型')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--output', type=str, help='测试报告输出文件')
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner(verbose=args.verbose)
    
    try:
        # 运行测试
        report = runner.run_all_tests(test_types=args.types)
        
        # 保存报告
        if args.output:
            runner.save_report(report, args.output)
        else:
            runner.save_report(report)
        
        # 返回适当的退出码
        sys.exit(0 if report['overall_status'] == 'pass' else 1)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
