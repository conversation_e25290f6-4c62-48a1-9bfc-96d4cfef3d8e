"""
简化的IEC61850标准规则
用于测试规则引擎功能
"""

import re
from typing import List, Set, Dict, Any
from ..models import (
    SubStation, VoltageLevel, Bay, ConductingEquipment,
    IED, AccessPoint, LDevice, LogicalNode
)
from ..parsers.scd_parser import SCLDocument
from .base import (
    BaseRule, RuleContext, RuleResult, RuleCategory, RuleSeverity,
    rule, applicable_to
)


@rule(
    rule_id="STD001",
    name="SCL版本检查",
    description="检查SCL文档版本是否符合标准",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.WARNING
)
@applicable_to(SCLDocument)
def check_scl_version(context: RuleContext) -> RuleResult:
    """检查SCL版本"""
    result = RuleResult(rule_id="STD001", success=True)
    scl_doc = context.data
    
    # 支持的版本列表
    supported_versions = ["2003", "2007", "2007B", "2007B4"]
    
    if not scl_doc.version:
        result.add_error("SCL文档缺少版本信息", context.get_path_string())
    elif scl_doc.version not in supported_versions:
        result.add_warning(
            f"SCL版本 '{scl_doc.version}' 可能不被完全支持",
            context.get_path_string(),
            suggestion=f"建议使用支持的版本: {', '.join(supported_versions)}"
        )
    else:
        result.add_info(f"SCL版本 '{scl_doc.version}' 符合标准", context.get_path_string())
    
    return result


@rule(
    rule_id="STD002",
    name="Header信息完整性",
    description="检查SCL文档Header信息的完整性",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.ERROR
)
@applicable_to(SCLDocument)
def check_header_completeness(context: RuleContext) -> RuleResult:
    """检查Header完整性"""
    result = RuleResult(rule_id="STD002", success=True)
    scl_doc = context.data
    
    if not scl_doc.header:
        result.add_error("SCL文档缺少Header元素", context.get_path_string())
        return result
    
    header = scl_doc.header
    
    # 检查必需字段
    if not header.id:
        result.add_error("Header缺少id属性", f"{context.get_path_string()}.header")
    
    # 检查推荐字段
    if not header.version:
        result.add_warning("Header缺少version属性", f"{context.get_path_string()}.header")
    
    if not header.tool_id:
        result.add_warning("Header缺少toolID属性", f"{context.get_path_string()}.header")
    
    return result


@rule(
    rule_id="STD003",
    name="IED命名规范",
    description="检查IED名称是否符合IEC61850命名规范",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.ERROR
)
@applicable_to(IED)
def check_ied_naming(context: RuleContext) -> RuleResult:
    """检查IED命名规范"""
    result = RuleResult(rule_id="STD003", success=True)
    ied = context.data
    
    # IED名称规范：1-64个字符，字母数字和下划线
    name_pattern = r'^[A-Za-z][A-Za-z0-9_]{0,63}$'
    
    if not ied.name:
        result.add_error("IED名称不能为空", context.get_path_string())
    elif not re.match(name_pattern, ied.name):
        result.add_error(
            f"IED名称 '{ied.name}' 不符合命名规范",
            context.get_path_string(),
            details="IED名称必须以字母开头，只能包含字母、数字和下划线，长度不超过64个字符",
            suggestion="修改IED名称以符合命名规范"
        )
    
    return result


@rule(
    rule_id="STD004",
    name="IED制造商信息",
    description="检查IED制造商信息的完整性",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.INFO
)
@applicable_to(IED)
def check_ied_manufacturer(context: RuleContext) -> RuleResult:
    """检查IED制造商信息"""
    result = RuleResult(rule_id="STD004", success=True)
    ied = context.data
    
    if not ied.manufacturer:
        result.add_warning(
            f"IED '{ied.name}' 缺少制造商信息",
            context.get_path_string(),
            suggestion="添加制造商信息以便设备识别"
        )
    
    if not ied.type:
        result.add_info(
            f"IED '{ied.name}' 缺少类型信息",
            context.get_path_string()
        )
    
    if not ied.config_version:
        result.add_info(
            f"IED '{ied.name}' 缺少配置版本信息",
            context.get_path_string()
        )
    
    return result


@rule(
    rule_id="STD005",
    name="名称唯一性检查",
    description="检查同级元素名称的唯一性",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.ERROR
)
@applicable_to(SCLDocument)
def check_name_uniqueness(context: RuleContext) -> RuleResult:
    """检查名称唯一性"""
    result = RuleResult(rule_id="STD005", success=True)
    scl_doc = context.data
    
    # 检查IED名称唯一性
    if scl_doc.ieds:
        ied_names = [ied.name for ied in scl_doc.ieds if ied.name]
        duplicates = [name for name in set(ied_names) if ied_names.count(name) > 1]
        
        for duplicate in duplicates:
            result.add_error(
                f"IED名称 '{duplicate}' 重复",
                context.get_path_string(),
                suggestion="确保所有IED名称唯一"
            )
    
    # 检查变电站内部名称唯一性
    if scl_doc.substation:
        substation = scl_doc.substation
        
        # 检查电压等级名称唯一性
        vl_names = [vl.name for vl in substation.voltage_levels if vl.name]
        duplicates = [name for name in set(vl_names) if vl_names.count(name) > 1]
        
        for duplicate in duplicates:
            result.add_error(
                f"电压等级名称 '{duplicate}' 重复",
                f"{context.get_path_string()}.substation"
            )
    
    return result


# 自动注册所有规则
def register_standard_rules():
    """注册所有标准规则"""
    from .registry import rule_registry
    from .base import BaseRule
    import inspect
    
    # 获取当前模块中的所有函数
    current_module = inspect.getmodule(register_standard_rules)
    
    for name, obj in inspect.getmembers(current_module):
        # 检查是否是BaseRule实例（由@rule装饰器创建）
        if isinstance(obj, BaseRule):
            try:
                rule_registry.register(obj)
                print(f"注册规则: {obj.rule_id}")
            except Exception as e:
                print(f"注册规则 {name} 失败: {e}")


# 在模块加载时自动注册规则
register_standard_rules()
