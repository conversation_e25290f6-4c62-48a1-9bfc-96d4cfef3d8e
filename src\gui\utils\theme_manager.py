"""
主题管理器
管理应用程序的主题和样式
"""

import logging
from typing import Dict, Any
from pathlib import Path

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtCore import QObject
from PySide6.QtGui import QPalette, QColor

logger = logging.getLogger(__name__)


class ThemeManager(QObject):
    """主题管理器"""
    
    def __init__(self):
        """初始化主题管理器"""
        super().__init__()
        
        self.current_theme = "default"
        self.themes = self._load_themes()
        
        logger.debug("主题管理器初始化完成")
    
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载主题定义"""
        themes = {
            "default": self._get_default_theme(),
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme()
        }
        
        return themes
    
    def _get_default_theme(self) -> Dict[str, Any]:
        """获取默认主题"""
        return {
            "name": "默认主题",
            "description": "系统默认主题",
            "colors": {
                "primary": "#0078d4",
                "secondary": "#6c757d",
                "success": "#28a745",
                "warning": "#ffc107",
                "danger": "#dc3545",
                "info": "#17a2b8",
                "light": "#f8f9fa",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#f5f5f5",
                "text_primary": "#212529",
                "text_secondary": "#6c757d",
                "border": "#dee2e6"
            },
            "fonts": {
                "family": "Microsoft YaHei",
                "size": 10,
                "weight": "normal"
            },
            "styles": {
                "border_radius": "4px",
                "border_width": "1px",
                "padding": "8px",
                "margin": "4px"
            }
        }
    
    def _get_dark_theme(self) -> Dict[str, Any]:
        """获取深色主题"""
        return {
            "name": "深色主题",
            "description": "深色护眼主题",
            "colors": {
                "primary": "#0d7377",
                "secondary": "#495057",
                "success": "#198754",
                "warning": "#fd7e14",
                "danger": "#dc3545",
                "info": "#0dcaf0",
                "light": "#495057",
                "dark": "#212529",
                "background": "#2b2b2b",
                "surface": "#3c3c3c",
                "text_primary": "#ffffff",
                "text_secondary": "#adb5bd",
                "border": "#495057"
            },
            "fonts": {
                "family": "Microsoft YaHei",
                "size": 10,
                "weight": "normal"
            },
            "styles": {
                "border_radius": "4px",
                "border_width": "1px",
                "padding": "8px",
                "margin": "4px"
            }
        }
    
    def _get_light_theme(self) -> Dict[str, Any]:
        """获取浅色主题"""
        return {
            "name": "浅色主题",
            "description": "明亮清新主题",
            "colors": {
                "primary": "#0066cc",
                "secondary": "#6c757d",
                "success": "#28a745",
                "warning": "#ffc107",
                "danger": "#dc3545",
                "info": "#17a2b8",
                "light": "#ffffff",
                "dark": "#343a40",
                "background": "#ffffff",
                "surface": "#fafafa",
                "text_primary": "#333333",
                "text_secondary": "#666666",
                "border": "#e0e0e0"
            },
            "fonts": {
                "family": "Microsoft YaHei",
                "size": 10,
                "weight": "normal"
            },
            "styles": {
                "border_radius": "6px",
                "border_width": "1px",
                "padding": "10px",
                "margin": "6px"
            }
        }
    
    def apply_theme(self, widget: QWidget, theme_name: str):
        """应用主题到控件"""
        try:
            if theme_name not in self.themes:
                logger.warning(f"主题不存在: {theme_name}")
                theme_name = "default"
            
            theme = self.themes[theme_name]
            self.current_theme = theme_name
            
            # 应用主题样式
            self._apply_theme_styles(widget, theme)
            
            logger.info(f"已应用主题: {theme['name']}")
            
        except Exception as e:
            logger.error(f"应用主题失败: {e}")
    
    def _apply_theme_styles(self, widget: QWidget, theme: Dict[str, Any]):
        """应用主题样式"""
        try:
            colors = theme["colors"]
            fonts = theme["fonts"]
            styles = theme["styles"]
            
            # 构建样式表
            stylesheet = self._build_stylesheet(colors, fonts, styles)
            
            # 应用样式表
            widget.setStyleSheet(stylesheet)
            
            # 如果是应用程序主窗口，还要设置调色板
            app = QApplication.instance()
            if app and widget == app.activeWindow():
                self._apply_palette(app, colors)
            
        except Exception as e:
            logger.error(f"应用主题样式失败: {e}")
    
    def _build_stylesheet(self, colors: Dict[str, str], fonts: Dict[str, Any], styles: Dict[str, str]) -> str:
        """构建样式表"""
        try:
            stylesheet_parts = []
            
            # 全局样式
            global_style = f"""
            QWidget {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                font-family: {fonts['family']};
                font-size: {fonts['size']}pt;
                border: none;
            }}
            """
            stylesheet_parts.append(global_style)
            
            # 主窗口样式
            main_window_style = f"""
            QMainWindow {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
            }}
            """
            stylesheet_parts.append(main_window_style)
            
            # 菜单栏样式
            menubar_style = f"""
            QMenuBar {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border-bottom: {styles['border_width']} solid {colors['border']};
                padding: {styles['padding']};
            }}
            
            QMenuBar::item {{
                background-color: transparent;
                padding: 4px 8px;
                border-radius: {styles['border_radius']};
            }}
            
            QMenuBar::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            
            QMenu {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: {styles['border_width']} solid {colors['border']};
                border-radius: {styles['border_radius']};
                padding: 4px;
            }}
            
            QMenu::item {{
                padding: 6px 20px;
                border-radius: {styles['border_radius']};
            }}
            
            QMenu::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            """
            stylesheet_parts.append(menubar_style)
            
            # 工具栏样式
            toolbar_style = f"""
            QToolBar {{
                background-color: {colors['surface']};
                border: {styles['border_width']} solid {colors['border']};
                padding: {styles['padding']};
                spacing: 4px;
            }}
            
            QToolButton {{
                background-color: transparent;
                border: {styles['border_width']} solid transparent;
                border-radius: {styles['border_radius']};
                padding: 6px;
                margin: 2px;
            }}
            
            QToolButton:hover {{
                background-color: {colors['light']};
                border-color: {colors['border']};
            }}
            
            QToolButton:pressed {{
                background-color: {colors['primary']};
                color: white;
            }}
            """
            stylesheet_parts.append(toolbar_style)
            
            # 状态栏样式
            statusbar_style = f"""
            QStatusBar {{
                background-color: {colors['surface']};
                color: {colors['text_secondary']};
                border-top: {styles['border_width']} solid {colors['border']};
                padding: 4px;
            }}
            """
            stylesheet_parts.append(statusbar_style)
            
            # 按钮样式
            button_style = f"""
            QPushButton {{
                background-color: {colors['primary']};
                color: white;
                border: none;
                border-radius: {styles['border_radius']};
                padding: {styles['padding']};
                font-weight: bold;
            }}
            
            QPushButton:hover {{
                background-color: {self._darken_color(colors['primary'], 0.1)};
            }}
            
            QPushButton:pressed {{
                background-color: {self._darken_color(colors['primary'], 0.2)};
            }}
            
            QPushButton:disabled {{
                background-color: {colors['secondary']};
                color: {colors['text_secondary']};
            }}
            """
            stylesheet_parts.append(button_style)
            
            # 输入框样式
            input_style = f"""
            QLineEdit, QTextEdit, QPlainTextEdit {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                border: {styles['border_width']} solid {colors['border']};
                border-radius: {styles['border_radius']};
                padding: {styles['padding']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
                border-color: {colors['primary']};
            }}
            """
            stylesheet_parts.append(input_style)
            
            # 表格样式
            table_style = f"""
            QTableWidget {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                border: {styles['border_width']} solid {colors['border']};
                border-radius: {styles['border_radius']};
                gridline-color: {colors['border']};
            }}
            
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {colors['border']};
            }}
            
            QTableWidget::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            
            QHeaderView::section {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: none;
                border-bottom: {styles['border_width']} solid {colors['border']};
                padding: 8px;
                font-weight: bold;
            }}
            """
            stylesheet_parts.append(table_style)
            
            # 树形控件样式
            tree_style = f"""
            QTreeWidget {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
                border: {styles['border_width']} solid {colors['border']};
                border-radius: {styles['border_radius']};
            }}
            
            QTreeWidget::item {{
                padding: 4px;
                border-bottom: 1px solid transparent;
            }}
            
            QTreeWidget::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            
            QTreeWidget::item:hover {{
                background-color: {colors['light']};
            }}
            """
            stylesheet_parts.append(tree_style)
            
            # 标签页样式
            tab_style = f"""
            QTabWidget::pane {{
                background-color: {colors['background']};
                border: {styles['border_width']} solid {colors['border']};
                border-radius: {styles['border_radius']};
            }}
            
            QTabBar::tab {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: {styles['border_width']} solid {colors['border']};
                border-bottom: none;
                border-radius: {styles['border_radius']} {styles['border_radius']} 0 0;
                padding: 8px 16px;
                margin-right: 2px;
            }}
            
            QTabBar::tab:selected {{
                background-color: {colors['background']};
                border-bottom: {styles['border_width']} solid {colors['background']};
            }}
            
            QTabBar::tab:hover {{
                background-color: {colors['light']};
            }}
            """
            stylesheet_parts.append(tab_style)
            
            # 分组框样式
            groupbox_style = f"""
            QGroupBox {{
                background-color: {colors['surface']};
                color: {colors['text_primary']};
                border: {styles['border_width']} solid {colors['border']};
                border-radius: {styles['border_radius']};
                margin-top: 10px;
                padding-top: 10px;
                font-weight: bold;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: {colors['surface']};
            }}
            """
            stylesheet_parts.append(groupbox_style)
            
            return "\n".join(stylesheet_parts)
            
        except Exception as e:
            logger.error(f"构建样式表失败: {e}")
            return ""
    
    def _apply_palette(self, app: QApplication, colors: Dict[str, str]):
        """应用调色板"""
        try:
            palette = QPalette()
            
            # 设置基本颜色
            palette.setColor(QPalette.Window, QColor(colors['background']))
            palette.setColor(QPalette.WindowText, QColor(colors['text_primary']))
            palette.setColor(QPalette.Base, QColor(colors['background']))
            palette.setColor(QPalette.AlternateBase, QColor(colors['surface']))
            palette.setColor(QPalette.Text, QColor(colors['text_primary']))
            palette.setColor(QPalette.Button, QColor(colors['surface']))
            palette.setColor(QPalette.ButtonText, QColor(colors['text_primary']))
            palette.setColor(QPalette.Highlight, QColor(colors['primary']))
            palette.setColor(QPalette.HighlightedText, QColor('white'))
            
            app.setPalette(palette)
            
        except Exception as e:
            logger.error(f"应用调色板失败: {e}")
    
    def _darken_color(self, color: str, factor: float) -> str:
        """使颜色变暗"""
        try:
            qcolor = QColor(color)
            h, s, l, a = qcolor.getHsl()
            l = max(0, int(l * (1 - factor)))
            qcolor.setHsl(h, s, l, a)
            return qcolor.name()
        except Exception as e:
            logger.error(f"颜色变暗失败: {e}")
            return color
    
    def get_available_themes(self) -> List[str]:
        """获取可用主题列表"""
        return list(self.themes.keys())
    
    def get_theme_info(self, theme_name: str) -> Dict[str, Any]:
        """获取主题信息"""
        return self.themes.get(theme_name, {})
    
    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self.current_theme
