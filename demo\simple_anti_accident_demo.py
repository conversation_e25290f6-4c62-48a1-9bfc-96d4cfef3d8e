#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
国家电网公司十八项电网重大反事故措施简单演示脚本
直接演示反事故措施知识库的核心功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 直接从文件路径导入，避免复杂的依赖问题
from src.knowledge.standards.state_grid_anti_accident_measures import StateGridAntiAccidentMeasures
from src.knowledge.standards.comprehensive_circuit_knowledge import ComprehensiveCircuitKnowledge


def demo_anti_accident_measures():
    print("国家电网公司十八项电网重大反事故措施演示")
    print("=" * 50)
    
    # 1. 初始化反事故措施知识库
    print("\n1. 初始化国家电网公司十八项电网重大反事故措施知识库...")
    try:
        anti_accident_knowledge = StateGridAntiAccidentMeasures()
        print("  ✓ 知识库初始化成功")
    except Exception as e:
        print(f"  ✗ 知识库初始化失败: {e}")
        return
    
    # 2. 展示反事故措施标准
    print("\n2. 国家电网公司十八项电网重大反事故措施标准:")
    try:
        standards = anti_accident_knowledge.get_all_standards()
        for standard in standards:
            print(f"  - {standard.name}")
            print(f"    标准编号: {standard.standard_number}")
            print(f"    描述: {standard.description}")
            print(f"    内容概要: {standard.content[:100]}...")
            print()
        print(f"  ✓ 成功获取 {len(standards)} 个标准")
    except Exception as e:
        print(f"  ✗ 获取标准失败: {e}")
    
    # 3. 展示反事故措施规则
    print("\n3. 反事故措施规则:")
    try:
        rules = anti_accident_knowledge.get_all_rules()
        for rule in rules:
            print(f"  - {rule.name}")
            print(f"    描述: {rule.description}")
            print(f"    严重程度: {rule.severity}")
            print(f"    类别: {rule.category}")
            print(f"    适用设备: {', '.join(rule.applicable_devices) if rule.applicable_devices else '无'}")
            print(f"    关键词: {', '.join(rule.keywords) if rule.keywords else '无'}")
            print()
        print(f"  ✓ 成功获取 {len(rules)} 条规则")
    except Exception as e:
        print(f"  ✗ 获取规则失败: {e}")
    
    # 4. 展示反事故措施技术要求
    print("\n4. 反事故措施技术要求:")
    try:
        requirements = anti_accident_knowledge.get_all_requirements()
        for req in requirements:
            print(f"  - {req.name}")
            print(f"    描述: {req.description}")
            print(f"    优先级: {req.priority}")
            print(f"    强制性: {'是' if req.mandatory else '否'}")
            print(f"    内容概要: {req.content[:100]}...")
            print()
        print(f"  ✓ 成功获取 {len(requirements)} 项技术要求")
    except Exception as e:
        print(f"  ✗ 获取技术要求失败: {e}")
    
    # 5. 初始化综合回路知识库
    print("\n5. 初始化综合回路知识库...")
    try:
        circuit_knowledge = ComprehensiveCircuitKnowledge()
        print("  ✓ 综合回路知识库初始化成功")
    except Exception as e:
        print(f"  ✗ 综合回路知识库初始化失败: {e}")
        return
    
    # 6. 展示保护回路的反事故措施
    print("\n6. 保护回路反事故措施:")
    try:
        protection_anti_accident = circuit_knowledge.get_anti_accident_measures('protection_circuit')
        if protection_anti_accident:
            print(f"  名称: {protection_anti_accident.get('name', 'N/A')}")
            measures = protection_anti_accident.get('measures', {})
            for measure_category, measure_details in measures.items():
                print(f"  {measure_category}:")
                for key, value in measure_details.items():
                    print(f"    {key}: {value}")
            
            common_accidents = protection_anti_accident.get('common_accidents', [])
            print("  常见事故及预防措施:")
            for accident in common_accidents:
                print(f"    事故类型: {accident.get('type', 'N/A')}")
                print(f"    事故原因: {', '.join(accident.get('causes', []))}")
                print(f"    预防措施: {', '.join(accident.get('prevention', []))}")
                print()
            print("  ✓ 成功获取保护回路反事故措施")
        else:
            print("  未找到保护回路反事故措施")
    except Exception as e:
        print(f"  ✗ 获取保护回路反事故措施失败: {e}")
    
    # 7. 展示控制回路的反事故措施
    print("\n7. 控制回路反事故措施:")
    try:
        control_anti_accident = circuit_knowledge.get_anti_accident_measures('control_circuit')
        if control_anti_accident:
            print(f"  名称: {control_anti_accident.get('name', 'N/A')}")
            measures = control_anti_accident.get('measures', {})
            for measure_category, measure_details in measures.items():
                print(f"  {measure_category}:")
                for key, value in measure_details.items():
                    print(f"    {key}: {value}")
            
            common_accidents = control_anti_accident.get('common_accidents', [])
            print("  常见事故及预防措施:")
            for accident in common_accidents:
                print(f"    事故类型: {accident.get('type', 'N/A')}")
                print(f"    事故原因: {', '.join(accident.get('causes', []))}")
                print(f"    预防措施: {', '.join(accident.get('prevention', []))}")
                print()
            print("  ✓ 成功获取控制回路反事故措施")
        else:
            print("  未找到控制回路反事故措施")
    except Exception as e:
        print(f"  ✗ 获取控制回路反事故措施失败: {e}")
    
    print("\n演示完成！")


if __name__ == "__main__":
    demo_anti_accident_measures()