"""
虚端子分析器
分析虚端子表的连接关系、覆盖率和问题
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import defaultdict, Counter
import networkx as nx

from .vt_models import (
    VirtualTerminal, VirtualConnection, VirtualTerminalTable,
    TerminalType, ConnectionType, SignalType
)


logger = logging.getLogger(__name__)


class VirtualTerminalAnalyzer:
    """虚端子分析器"""
    
    def __init__(self):
        """初始化分析器"""
        logger.info("虚端子分析器初始化完成")
    
    def analyze_table(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """
        分析虚端子表
        
        Args:
            table: 虚端子表
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            logger.info(f"开始分析虚端子表: {table.name}")
            
            analysis_result = {
                'table_info': self._analyze_table_info(table),
                'terminal_analysis': self._analyze_terminals(table),
                'connection_analysis': self._analyze_connections(table),
                'coverage_analysis': self._analyze_coverage(table),
                'topology_analysis': self._analyze_topology(table),
                'quality_analysis': self._analyze_quality(table),
                'issues': self._find_issues(table),
                'recommendations': self._generate_recommendations(table)
            }
            
            logger.info("虚端子表分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"虚端子表分析失败: {e}")
            return {}
    
    def analyze_connectivity(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """
        分析连接性
        
        Args:
            table: 虚端子表
            
        Returns:
            Dict[str, Any]: 连接性分析结果
        """
        try:
            # 构建连接图
            graph = self._build_connection_graph(table)
            
            # 分析连通性
            connectivity = {
                'total_nodes': graph.number_of_nodes(),
                'total_edges': graph.number_of_edges(),
                'connected_components': list(nx.connected_components(graph)),
                'isolated_nodes': list(nx.isolates(graph)),
                'density': nx.density(graph),
                'diameter': self._calculate_diameter(graph),
                'average_path_length': self._calculate_average_path_length(graph)
            }
            
            # 分析中心性
            centrality = {
                'degree_centrality': nx.degree_centrality(graph),
                'betweenness_centrality': nx.betweenness_centrality(graph),
                'closeness_centrality': nx.closeness_centrality(graph)
            }
            
            return {
                'connectivity': connectivity,
                'centrality': centrality,
                'critical_nodes': self._find_critical_nodes(graph),
                'bottlenecks': self._find_bottlenecks(graph)
            }
            
        except Exception as e:
            logger.error(f"连接性分析失败: {e}")
            return {}
    
    def analyze_device_interactions(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """
        分析设备间交互
        
        Args:
            table: 虚端子表
            
        Returns:
            Dict[str, Any]: 设备交互分析结果
        """
        try:
            device_interactions = defaultdict(lambda: defaultdict(list))
            device_stats = defaultdict(lambda: {
                'input_count': 0,
                'output_count': 0,
                'connections_in': 0,
                'connections_out': 0
            })
            
            # 统计设备端子
            for terminal in table.terminals:
                device = terminal.device_name
                if terminal.terminal_type == TerminalType.INPUT:
                    device_stats[device]['input_count'] += 1
                elif terminal.terminal_type == TerminalType.OUTPUT:
                    device_stats[device]['output_count'] += 1
            
            # 统计设备间连接
            for connection in table.connections:
                source_device = connection.source_terminal.device_name
                target_device = connection.target_terminal.device_name
                
                if source_device != target_device:
                    device_interactions[source_device][target_device].append(connection)
                    device_stats[source_device]['connections_out'] += 1
                    device_stats[target_device]['connections_in'] += 1
            
            # 计算交互矩阵
            devices = list(device_stats.keys())
            interaction_matrix = {}
            for source in devices:
                interaction_matrix[source] = {}
                for target in devices:
                    interaction_matrix[source][target] = len(device_interactions[source][target])
            
            return {
                'device_stats': dict(device_stats),
                'device_interactions': {k: dict(v) for k, v in device_interactions.items()},
                'interaction_matrix': interaction_matrix,
                'most_connected_devices': self._find_most_connected_devices(device_stats),
                'isolated_devices': self._find_isolated_devices(device_stats)
            }
            
        except Exception as e:
            logger.error(f"设备交互分析失败: {e}")
            return {}
    
    def find_redundant_connections(self, table: VirtualTerminalTable) -> List[Dict[str, Any]]:
        """
        查找冗余连接
        
        Args:
            table: 虚端子表
            
        Returns:
            List[Dict[str, Any]]: 冗余连接列表
        """
        redundant_connections = []
        
        try:
            # 按目标端子分组连接
            target_groups = defaultdict(list)
            for connection in table.connections:
                target_id = connection.target_terminal.terminal_id
                target_groups[target_id].append(connection)
            
            # 查找多重连接
            for target_id, connections in target_groups.items():
                if len(connections) > 1:
                    # 按连接类型和优先级排序
                    connections.sort(key=lambda c: (c.connection_type.value, -c.priority))
                    
                    # 除了第一个，其他都可能是冗余的
                    for i, connection in enumerate(connections[1:], 1):
                        redundant_connections.append({
                            'connection': connection,
                            'reason': 'multiple_connections_to_same_target',
                            'primary_connection': connections[0],
                            'redundancy_level': i
                        })
            
            # 查找循环连接
            graph = self._build_connection_graph(table)
            cycles = list(nx.simple_cycles(graph.to_directed()))
            
            for cycle in cycles:
                if len(cycle) > 1:
                    # 找到构成循环的连接
                    cycle_connections = []
                    for i in range(len(cycle)):
                        source = cycle[i]
                        target = cycle[(i + 1) % len(cycle)]
                        
                        for connection in table.connections:
                            if (connection.source_terminal.terminal_id == source and
                                connection.target_terminal.terminal_id == target):
                                cycle_connections.append(connection)
                    
                    # 标记为潜在冗余
                    for connection in cycle_connections:
                        redundant_connections.append({
                            'connection': connection,
                            'reason': 'circular_connection',
                            'cycle': cycle,
                            'cycle_length': len(cycle)
                        })
            
            logger.info(f"发现 {len(redundant_connections)} 个潜在冗余连接")
            return redundant_connections
            
        except Exception as e:
            logger.error(f"查找冗余连接失败: {e}")
            return []
    
    def analyze_signal_flow(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """
        分析信号流
        
        Args:
            table: 虚端子表
            
        Returns:
            Dict[str, Any]: 信号流分析结果
        """
        try:
            signal_flows = defaultdict(list)
            flow_paths = []
            
            # 按信号类型分组
            for connection in table.connections:
                signal_type = connection.source_terminal.signal_type
                signal_flows[signal_type.value].append(connection)
            
            # 分析每种信号类型的流向
            signal_analysis = {}
            for signal_type, connections in signal_flows.items():
                # 构建该信号类型的子图
                subgraph = nx.DiGraph()
                
                for connection in connections:
                    source_id = connection.source_terminal.terminal_id
                    target_id = connection.target_terminal.terminal_id
                    subgraph.add_edge(source_id, target_id, connection=connection)
                
                # 分析流向
                sources = [n for n in subgraph.nodes() if subgraph.in_degree(n) == 0]
                sinks = [n for n in subgraph.nodes() if subgraph.out_degree(n) == 0]
                
                # 计算路径
                paths = []
                for source in sources:
                    for sink in sinks:
                        try:
                            path = nx.shortest_path(subgraph, source, sink)
                            paths.append(path)
                        except nx.NetworkXNoPath:
                            continue
                
                signal_analysis[signal_type] = {
                    'connection_count': len(connections),
                    'node_count': subgraph.number_of_nodes(),
                    'sources': sources,
                    'sinks': sinks,
                    'paths': paths,
                    'max_path_length': max(len(p) for p in paths) if paths else 0,
                    'avg_path_length': sum(len(p) for p in paths) / len(paths) if paths else 0
                }
            
            return {
                'signal_flows': dict(signal_flows),
                'signal_analysis': signal_analysis,
                'flow_complexity': self._calculate_flow_complexity(signal_analysis),
                'critical_paths': self._find_critical_paths(signal_analysis)
            }
            
        except Exception as e:
            logger.error(f"信号流分析失败: {e}")
            return {}
    
    def _analyze_table_info(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """分析表基本信息"""
        return {
            'table_id': table.table_id,
            'name': table.name,
            'description': table.description,
            'version': table.version,
            'created_time': table.created_time.isoformat(),
            'modified_time': table.modified_time.isoformat(),
            'metadata': table.metadata
        }
    
    def _analyze_terminals(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """分析端子"""
        terminal_stats = {
            'total_count': len(table.terminals),
            'by_type': Counter(t.terminal_type.value for t in table.terminals),
            'by_signal_type': Counter(t.signal_type.value for t in table.terminals),
            'by_device': Counter(t.device_name for t in table.terminals),
            'by_data_type': Counter(t.data_type for t in table.terminals if t.data_type),
            'by_fc': Counter(t.functional_constraint for t in table.terminals if t.functional_constraint),
            'critical_count': len([t for t in table.terminals if t.is_critical])
        }
        
        return terminal_stats
    
    def _analyze_connections(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """分析连接"""
        connection_stats = {
            'total_count': len(table.connections),
            'by_type': Counter(c.connection_type.value for c in table.connections),
            'active_count': len([c for c in table.connections if c.is_active]),
            'by_priority': Counter(c.priority for c in table.connections),
            'avg_quality': sum(c.quality_factor for c in table.connections) / len(table.connections) if table.connections else 0,
            'with_delay': len([c for c in table.connections if c.delay_ms is not None])
        }
        
        return connection_stats
    
    def _analyze_coverage(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """分析覆盖率"""
        input_terminals = table.get_input_terminals()
        output_terminals = table.get_output_terminals()
        unconnected_terminals = table.get_unconnected_terminals()
        
        connected_inputs = len([t for t in input_terminals if t.terminal_id not in 
                               [ut.terminal_id for ut in unconnected_terminals]])
        connected_outputs = len([t for t in output_terminals if t.terminal_id not in 
                                [ut.terminal_id for ut in unconnected_terminals]])
        
        input_coverage = connected_inputs / len(input_terminals) if input_terminals else 0
        output_coverage = connected_outputs / len(output_terminals) if output_terminals else 0
        
        return {
            'input_coverage': input_coverage,
            'output_coverage': output_coverage,
            'overall_coverage': (connected_inputs + connected_outputs) / len(table.terminals) if table.terminals else 0,
            'unconnected_count': len(unconnected_terminals),
            'unconnected_terminals': [t.terminal_id for t in unconnected_terminals]
        }
    
    def _analyze_topology(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """分析拓扑结构"""
        graph = self._build_connection_graph(table)
        
        return {
            'node_count': graph.number_of_nodes(),
            'edge_count': graph.number_of_edges(),
            'density': nx.density(graph),
            'connected_components': len(list(nx.connected_components(graph))),
            'isolated_nodes': len(list(nx.isolates(graph))),
            'average_degree': sum(dict(graph.degree()).values()) / graph.number_of_nodes() if graph.number_of_nodes() > 0 else 0
        }
    
    def _analyze_quality(self, table: VirtualTerminalTable) -> Dict[str, Any]:
        """分析质量"""
        issues = table.validate_table()
        
        quality_metrics = {
            'validation_issues': len(issues),
            'critical_issues': len([i for i in issues if 'error' in i.lower() or 'critical' in i.lower()]),
            'data_completeness': self._calculate_data_completeness(table),
            'naming_consistency': self._calculate_naming_consistency(table),
            'type_consistency': self._calculate_type_consistency(table)
        }
        
        return quality_metrics
    
    def _find_issues(self, table: VirtualTerminalTable) -> List[Dict[str, Any]]:
        """查找问题"""
        issues = []
        
        # 验证表的基本问题
        validation_issues = table.validate_table()
        for issue in validation_issues:
            issues.append({
                'type': 'validation',
                'severity': 'error',
                'message': issue,
                'category': 'table_validation'
            })
        
        # 查找未连接的关键端子
        unconnected_critical = [t for t in table.get_unconnected_terminals() if t.is_critical]
        for terminal in unconnected_critical:
            issues.append({
                'type': 'connectivity',
                'severity': 'warning',
                'message': f"关键端子未连接: {terminal.terminal_id}",
                'category': 'critical_unconnected',
                'terminal_id': terminal.terminal_id
            })
        
        # 查找类型不匹配的连接
        for connection in table.connections:
            if not connection.source_terminal.is_compatible_with(connection.target_terminal):
                issues.append({
                    'type': 'compatibility',
                    'severity': 'error',
                    'message': f"端子类型不兼容: {connection.connection_id}",
                    'category': 'type_mismatch',
                    'connection_id': connection.connection_id
                })
        
        return issues
    
    def _generate_recommendations(self, table: VirtualTerminalTable) -> List[str]:
        """生成推荐"""
        recommendations = []
        
        # 基于覆盖率的推荐
        coverage = self._analyze_coverage(table)
        if coverage['input_coverage'] < 0.8:
            recommendations.append("输入端子覆盖率较低，建议检查未连接的输入端子")
        
        if coverage['output_coverage'] < 0.8:
            recommendations.append("输出端子覆盖率较低，建议检查未连接的输出端子")
        
        # 基于连接质量的推荐
        low_quality_connections = [c for c in table.connections if c.quality_factor < 0.8]
        if low_quality_connections:
            recommendations.append(f"发现 {len(low_quality_connections)} 个低质量连接，建议检查")
        
        # 基于拓扑的推荐
        isolated_terminals = table.get_unconnected_terminals()
        if len(isolated_terminals) > len(table.terminals) * 0.1:
            recommendations.append("孤立端子过多，建议检查连接完整性")
        
        return recommendations
    
    def _build_connection_graph(self, table: VirtualTerminalTable) -> nx.Graph:
        """构建连接图"""
        graph = nx.Graph()
        
        # 添加节点
        for terminal in table.terminals:
            graph.add_node(terminal.terminal_id, terminal=terminal)
        
        # 添加边
        for connection in table.connections:
            source_id = connection.source_terminal.terminal_id
            target_id = connection.target_terminal.terminal_id
            graph.add_edge(source_id, target_id, connection=connection)
        
        return graph
    
    def _calculate_diameter(self, graph: nx.Graph) -> Optional[int]:
        """计算图的直径"""
        try:
            if nx.is_connected(graph):
                return nx.diameter(graph)
            else:
                # 对于非连通图，计算最大连通分量的直径
                largest_cc = max(nx.connected_components(graph), key=len)
                subgraph = graph.subgraph(largest_cc)
                return nx.diameter(subgraph)
        except:
            return None
    
    def _calculate_average_path_length(self, graph: nx.Graph) -> Optional[float]:
        """计算平均路径长度"""
        try:
            if nx.is_connected(graph):
                return nx.average_shortest_path_length(graph)
            else:
                # 对于非连通图，计算最大连通分量的平均路径长度
                largest_cc = max(nx.connected_components(graph), key=len)
                subgraph = graph.subgraph(largest_cc)
                return nx.average_shortest_path_length(subgraph)
        except:
            return None
    
    def _find_critical_nodes(self, graph: nx.Graph) -> List[str]:
        """查找关键节点"""
        critical_nodes = []
        
        # 基于度中心性
        degree_centrality = nx.degree_centrality(graph)
        high_degree_nodes = [node for node, centrality in degree_centrality.items() 
                           if centrality > 0.1]  # 阈值可调
        
        # 基于介数中心性
        betweenness_centrality = nx.betweenness_centrality(graph)
        high_betweenness_nodes = [node for node, centrality in betweenness_centrality.items() 
                                if centrality > 0.1]  # 阈值可调
        
        critical_nodes = list(set(high_degree_nodes + high_betweenness_nodes))
        return critical_nodes
    
    def _find_bottlenecks(self, graph: nx.Graph) -> List[str]:
        """查找瓶颈节点"""
        # 查找割点（移除后会增加连通分量数的节点）
        articulation_points = list(nx.articulation_points(graph))
        return articulation_points
    
    def _find_most_connected_devices(self, device_stats: Dict[str, Dict[str, int]]) -> List[Tuple[str, int]]:
        """查找连接最多的设备"""
        device_connections = []
        
        for device, stats in device_stats.items():
            total_connections = stats['connections_in'] + stats['connections_out']
            device_connections.append((device, total_connections))
        
        # 按连接数排序
        device_connections.sort(key=lambda x: x[1], reverse=True)
        return device_connections[:5]  # 返回前5个
    
    def _find_isolated_devices(self, device_stats: Dict[str, Dict[str, int]]) -> List[str]:
        """查找孤立设备"""
        isolated_devices = []
        
        for device, stats in device_stats.items():
            if stats['connections_in'] == 0 and stats['connections_out'] == 0:
                isolated_devices.append(device)
        
        return isolated_devices
    
    def _calculate_flow_complexity(self, signal_analysis: Dict[str, Any]) -> float:
        """计算流复杂度"""
        total_complexity = 0
        signal_count = 0
        
        for signal_type, analysis in signal_analysis.items():
            if analysis['paths']:
                # 基于路径数量和平均长度计算复杂度
                path_complexity = len(analysis['paths']) * analysis['avg_path_length']
                total_complexity += path_complexity
                signal_count += 1
        
        return total_complexity / signal_count if signal_count > 0 else 0
    
    def _find_critical_paths(self, signal_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查找关键路径"""
        critical_paths = []
        
        for signal_type, analysis in signal_analysis.items():
            # 查找最长路径
            if analysis['paths']:
                longest_path = max(analysis['paths'], key=len)
                if len(longest_path) > 3:  # 阈值可调
                    critical_paths.append({
                        'signal_type': signal_type,
                        'path': longest_path,
                        'length': len(longest_path),
                        'reason': 'longest_path'
                    })
        
        return critical_paths
    
    def _calculate_data_completeness(self, table: VirtualTerminalTable) -> float:
        """计算数据完整性"""
        total_fields = 0
        filled_fields = 0
        
        for terminal in table.terminals:
            total_fields += 8  # 主要字段数量
            
            if terminal.data_type:
                filled_fields += 1
            if terminal.functional_constraint:
                filled_fields += 1
            if terminal.description:
                filled_fields += 1
            if terminal.unit:
                filled_fields += 1
            if terminal.range_min is not None:
                filled_fields += 1
            if terminal.range_max is not None:
                filled_fields += 1
            if terminal.default_value is not None:
                filled_fields += 1
            if terminal.tags:
                filled_fields += 1
        
        return filled_fields / total_fields if total_fields > 0 else 0
    
    def _calculate_naming_consistency(self, table: VirtualTerminalTable) -> float:
        """计算命名一致性"""
        # 简化的命名一致性检查
        device_patterns = set()
        
        for terminal in table.terminals:
            # 检查命名模式
            parts = terminal.terminal_id.split('/')
            if len(parts) >= 4:
                pattern = f"{len(parts[0])}-{len(parts[1])}-{len(parts[2])}-{len(parts[3])}"
                device_patterns.add(pattern)
        
        # 如果所有设备使用相同的命名模式，一致性为1.0
        consistency = 1.0 / len(device_patterns) if device_patterns else 0
        return min(consistency, 1.0)
    
    def _calculate_type_consistency(self, table: VirtualTerminalTable) -> float:
        """计算类型一致性"""
        # 检查相同功能约束的端子是否使用一致的数据类型
        fc_types = defaultdict(set)
        
        for terminal in table.terminals:
            if terminal.functional_constraint and terminal.data_type:
                fc_types[terminal.functional_constraint].add(terminal.data_type)
        
        consistent_fcs = 0
        total_fcs = 0
        
        for fc, types in fc_types.items():
            total_fcs += 1
            if len(types) == 1:  # 只有一种数据类型
                consistent_fcs += 1
        
        return consistent_fcs / total_fcs if total_fcs > 0 else 1.0
