"""
XML解析器单元测试
"""

import pytest
from pathlib import Path
import tempfile
import os

from src.core.parsers import (
    BaseParser, ParseError, ParseResult,
    SCDParser, ICDParser, CIDParser,
    ParserFactory, parser_factory
)


class TestParseError:
    """测试解析错误类"""
    
    def test_parse_error_creation(self):
        """测试解析错误创建"""
        error = ParseError("测试错误")
        assert error.message == "测试错误"
        assert error.line_number is None
        assert error.column_number is None
        assert error.element_name is None
    
    def test_parse_error_with_details(self):
        """测试带详细信息的解析错误"""
        error = ParseError(
            message="属性错误",
            line_number=10,
            column_number=5,
            element_name="IED"
        )
        
        assert error.message == "属性错误"
        assert error.line_number == 10
        assert error.column_number == 5
        assert error.element_name == "IED"
        
        formatted = error.format_message()
        assert "第10行第5列" in formatted
        assert "元素 'IED'" in formatted
        assert "属性错误" in formatted


class TestParseResult:
    """测试解析结果类"""
    
    def test_parse_result_creation(self):
        """测试解析结果创建"""
        result = ParseResult()
        
        assert result.success is False
        assert result.data is None
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert isinstance(result.metadata, dict)
    
    def test_add_error(self):
        """测试添加错误"""
        result = ParseResult()
        
        # 添加字符串错误
        result.add_error("测试错误")
        assert len(result.errors) == 1
        assert result.success is False
        
        # 添加ParseError对象
        parse_error = ParseError("解析错误", line_number=5)
        result.add_error(parse_error)
        assert len(result.errors) == 2
    
    def test_add_warning(self):
        """测试添加警告"""
        result = ParseResult()
        
        result.add_warning("测试警告")
        assert len(result.warnings) == 1
        assert result.has_warnings() is True
    
    def test_get_summary(self):
        """测试获取摘要"""
        result = ParseResult()
        
        # 成功情况
        result.success = True
        summary = result.get_summary()
        assert "解析成功" in summary
        
        # 失败情况
        result.success = False
        result.add_error("测试错误")
        summary = result.get_summary()
        assert "解析失败" in summary
        assert "1个错误" in summary


class TestParserFactory:
    """测试解析器工厂"""
    
    def test_factory_creation(self):
        """测试工厂创建"""
        factory = ParserFactory()
        
        supported_types = factory.get_supported_types()
        assert 'SCD' in supported_types
        assert 'ICD' in supported_types
        assert 'CID' in supported_types
        
        supported_extensions = factory.get_supported_extensions()
        assert '.scd' in supported_extensions
        assert '.icd' in supported_extensions
        assert '.cid' in supported_extensions
        assert '.xml' in supported_extensions
    
    def test_detect_file_type_by_extension(self):
        """测试根据扩展名检测文件类型"""
        factory = ParserFactory()
        
        assert factory.detect_file_type("test.scd") == "SCD"
        assert factory.detect_file_type("test.icd") == "ICD"
        assert factory.detect_file_type("test.cid") == "CID"
    
    def test_detect_file_type_by_filename(self):
        """测试根据文件名检测文件类型"""
        factory = ParserFactory()
        
        assert factory._detect_by_filename(Path("system_config.xml")) == "SCD"
        assert factory._detect_by_filename(Path("device_capability.xml")) == "ICD"
        assert factory._detect_by_filename(Path("configured_device.xml")) == "CID"
        assert factory._detect_by_filename(Path("unknown.xml")) == "SCL"
    
    def test_create_parser(self):
        """测试创建解析器"""
        factory = ParserFactory()
        
        # 创建SCD解析器
        parser = factory.create_parser("test.scd", "SCD")
        assert isinstance(parser, SCDParser)
        
        # 创建ICD解析器
        parser = factory.create_parser("test.icd", "ICD")
        assert isinstance(parser, ICDParser)
        
        # 创建CID解析器
        parser = factory.create_parser("test.cid", "CID")
        assert isinstance(parser, CIDParser)
    
    def test_create_parser_invalid_type(self):
        """测试创建无效类型的解析器"""
        factory = ParserFactory()
        
        with pytest.raises(ValueError):
            factory.create_parser("test.xml", "INVALID")
    
    def test_get_parser_info(self):
        """测试获取解析器信息"""
        factory = ParserFactory()
        
        info = factory.get_parser_info("SCD")
        assert info is not None
        assert info['type'] == 'SCD'
        assert info['class_name'] == 'SCDParser'
        assert info['root_element_name'] == 'SCL'
        
        # 测试无效类型
        info = factory.get_parser_info("INVALID")
        assert info is None


class TestSCDParser:
    """测试SCD解析器"""
    
    def test_scd_parser_creation(self):
        """测试SCD解析器创建"""
        parser = SCDParser()
        
        assert parser.get_root_element_name() == 'SCL'
        assert '.scd' in parser.get_supported_file_types()
        assert '.xml' in parser.get_supported_file_types()
    
    def test_parse_simple_scl_string(self):
        """测试解析简单的SCL字符串"""
        parser = SCDParser(validate_schema=False)  # 关闭Schema验证以简化测试
        
        xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007" revision="B">
    <Header id="TestSCD" version="1.0" toolID="TestTool"/>
    <Substation name="TestSubstation">
        <VoltageLevel name="110kV">
            <Voltage unit="V" multiplier="k" value="110"/>
            <Bay name="TestBay">
                <ConductingEquipment name="CBR1" type="CBR"/>
            </Bay>
        </VoltageLevel>
    </Substation>
    <IED name="TestIED" type="Protection" manufacturer="TestManufacturer">
        <AccessPoint name="AP1">
            <Server timeout="30">
                <LDevice name="LD1" inst="LD1"/>
            </Server>
        </AccessPoint>
    </IED>
</SCL>'''
        
        result = parser.parse_string(xml_content)
        
        assert result.success is True
        assert result.data is not None
        assert len(result.errors) == 0
        
        # 检查解析的数据
        scl_doc = result.data
        assert scl_doc.version == "2007"
        assert scl_doc.revision == "B"
        assert scl_doc.header.id == "TestSCD"
        assert scl_doc.substation.name == "TestSubstation"
        assert len(scl_doc.ieds) == 1
        assert scl_doc.ieds[0].name == "TestIED"
    
    def test_parse_invalid_xml(self):
        """测试解析无效XML"""
        parser = SCDParser()
        
        invalid_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007">
    <Header id="Test"
    <!-- 缺少闭合标签 -->
</SCL>'''
        
        result = parser.parse_string(invalid_xml)
        
        assert result.success is False
        assert len(result.errors) > 0
        assert any("XML语法错误" in str(error) for error in result.errors)
    
    def test_parse_missing_required_elements(self):
        """测试解析缺少必需元素的XML"""
        parser = SCDParser(validate_schema=False)
        
        xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007">
    <!-- 缺少Header元素 -->
</SCL>'''
        
        result = parser.parse_string(xml_content)
        
        assert result.success is False
        assert len(result.errors) > 0
    
    def test_parse_with_communication(self):
        """测试解析包含通信配置的SCL"""
        parser = SCDParser(validate_schema=False)
        
        xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007">
    <Header id="TestSCD"/>
    <Communication>
        <SubNetwork name="TestNetwork" type="8-MMS">
            <ConnectedAP iedName="TestIED" apName="AP1">
                <Address>
                    <P type="IP">************0</P>
                    <P type="IP-SUBNET">*************</P>
                </Address>
            </ConnectedAP>
        </SubNetwork>
    </Communication>
    <IED name="TestIED" manufacturer="TestManufacturer">
        <AccessPoint name="AP1"/>
    </IED>
</SCL>'''
        
        result = parser.parse_string(xml_content)
        
        assert result.success is True
        scl_doc = result.data
        assert scl_doc.communication is not None
        assert len(scl_doc.communication.sub_networks) == 1
        
        subnet = scl_doc.communication.sub_networks[0]
        assert subnet.name == "TestNetwork"
        assert len(subnet.connected_aps) == 1
        
        connected_ap = subnet.connected_aps[0]
        assert connected_ap.ied_name == "TestIED"
        assert connected_ap.ap_name == "AP1"


class TestICDParser:
    """测试ICD解析器"""
    
    def test_icd_parser_creation(self):
        """测试ICD解析器创建"""
        parser = ICDParser()
        
        assert parser.get_root_element_name() == 'SCL'
        assert '.icd' in parser.get_supported_file_types()
    
    def test_parse_simple_icd(self):
        """测试解析简单的ICD文件"""
        parser = ICDParser(validate_schema=False)
        
        xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007">
    <Header id="TestICD"/>
    <IED name="ProtectionIED" type="Protection" manufacturer="ABB">
        <AccessPoint name="AP1">
            <Server>
                <LDevice name="PROT" inst="PROT"/>
            </Server>
        </AccessPoint>
    </IED>
    <DataTypeTemplates>
        <LNodeType id="PTOC1" lnClass="PTOC"/>
        <DOType id="SPS" cdc="SPS"/>
    </DataTypeTemplates>
</SCL>'''
        
        result = parser.parse_string(xml_content)
        
        assert result.success is True
        icd_doc = result.data
        assert len(icd_doc.ieds) == 1
        assert icd_doc.data_type_templates is not None
        
        primary_ied = icd_doc.get_primary_ied()
        assert primary_ied.name == "ProtectionIED"
        assert primary_ied.type == "Protection"


class TestCIDParser:
    """测试CID解析器"""
    
    def test_cid_parser_creation(self):
        """测试CID解析器创建"""
        parser = CIDParser()
        
        assert parser.get_root_element_name() == 'SCL'
        assert '.cid' in parser.get_supported_file_types()
    
    def test_parse_simple_cid(self):
        """测试解析简单的CID文件"""
        parser = CIDParser(validate_schema=False)
        
        xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL version="2007">
    <Header id="TestCID"/>
    <Communication>
        <SubNetwork name="StationBus" type="8-MMS">
            <ConnectedAP iedName="IED1" apName="AP1">
                <Address>
                    <P type="IP">************</P>
                </Address>
            </ConnectedAP>
        </SubNetwork>
    </Communication>
    <IED name="IED1" manufacturer="TestManufacturer">
        <AccessPoint name="AP1">
            <Server>
                <LDevice name="LD1" inst="LD1"/>
            </Server>
        </AccessPoint>
    </IED>
</SCL>'''
        
        result = parser.parse_string(xml_content)
        
        assert result.success is True
        cid_doc = result.data
        assert cid_doc.communication is not None
        assert len(cid_doc.ieds) == 1
        
        # 测试网络配置
        network_config = cid_doc.get_network_configuration()
        assert network_config is not None
        
        # 测试IP地址分配
        ip_allocation = cid_doc.get_ip_address_allocation()
        assert "IED1_AP1" in ip_allocation
        assert ip_allocation["IED1_AP1"] == "************"


if __name__ == "__main__":
    pytest.main([__file__])
