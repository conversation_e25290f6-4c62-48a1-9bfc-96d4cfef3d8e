"""
回路间连接、沟通、协调关系深度学习引擎
专注于各个回路之间的相互关系、信号传递、协调配合
基于GB/T 14285、DL/T 5136等标准的深度理解
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CircuitInterconnection:
    """回路间连接关系"""
    source_circuit: str
    target_circuit: str
    connection_type: str
    signal_type: str
    function: str
    gb14285_requirement: str
    dl5136_specification: str
    coordination_logic: Dict[str, Any]


class CircuitInterconnectionEngine:
    """回路间连接、沟通、协调关系深度学习引擎"""
    
    def __init__(self):
        """初始化回路间关系学习引擎"""
        self.interconnection_knowledge = self._build_interconnection_knowledge()
        self.coordination_patterns = self._build_coordination_patterns()
        self.communication_protocols = self._build_communication_protocols()
        self.standard_requirements = self._build_standard_requirements()
        
        logger.info("回路间关系深度学习引擎初始化完成")
    
    def _build_interconnection_knowledge(self) -> Dict[str, Any]:
        """构建回路间连接关系的深度知识"""
        return {
            # 保护回路与控制回路的协调
            'protection_to_control': {
                'relationship_type': '保护跳闸控制',
                'essence': '保护装置检测到故障后，通过控制回路切除故障设备',
                'signal_flow': {
                    'fault_detection': {
                        'source': '保护回路 - 保护装置',
                        'signal': '故障检测信号',
                        'processing': '故障判断逻辑 + 动作判据',
                        'output': '保护动作信号'
                    },
                    'trip_command': {
                        'source': '保护装置出口继电器',
                        'target': '控制回路 - 跳闸回路',
                        'signal_type': '开关量信号',
                        'voltage_level': 'DC 220V/110V',
                        'contact_capacity': '5A/250V'
                    },
                    'execution': {
                        'target': '断路器跳闸线圈',
                        'action': '断路器分闸操作',
                        'feedback': '位置信号返回信号回路'
                    }
                },
                'coordination_requirements': {
                    'gb14285_requirements': {
                        'reliability': '跳闸回路可靠性≥99.9%',
                        'speed': '保护动作到断路器分闸<100ms',
                        'selectivity': '只切除故障设备，不影响健全设备'
                    },
                    'dl5136_specifications': {
                        'redundancy': '重要设备应有双跳闸回路',
                        'isolation': '保护回路与控制回路电气隔离',
                        'monitoring': '跳闸回路完整性监视'
                    }
                },
                'failure_analysis': {
                    'common_failures': [
                        {
                            'failure_point': '保护装置出口继电器',
                            'failure_mode': '触点粘连或拒动',
                            'consequence': '保护拒动或误动',
                            'detection_method': '出口继电器动作试验',
                            'prevention': '定期检查，选用高可靠继电器'
                        },
                        {
                            'failure_point': '跳闸回路电缆',
                            'failure_mode': '电缆断线或接触不良',
                            'consequence': '跳闸回路断线，保护拒动',
                            'detection_method': '回路电阻测试',
                            'prevention': '电缆敷设规范，定期检查'
                        }
                    ]
                }
            },
            
            # 测量回路与保护回路的协调
            'measurement_to_protection': {
                'relationship_type': '测量信号输入保护',
                'essence': '测量回路为保护装置提供准确的电气量信息',
                'signal_flow': {
                    'current_measurement': {
                        'source': '电流互感器(CT)',
                        'signal_type': '交流电流信号',
                        'typical_value': '5A/1A二次额定电流',
                        'target': '保护装置电流输入端',
                        'function': '提供故障电流信息'
                    },
                    'voltage_measurement': {
                        'source': '电压互感器(PT)',
                        'signal_type': '交流电压信号',
                        'typical_value': '100V/57.7V二次额定电压',
                        'target': '保护装置电压输入端',
                        'function': '提供系统电压信息'
                    }
                },
                'technical_requirements': {
                    'ct_requirements': {
                        'accuracy_class': {
                            'protection': '5P20/10P20 - 保护级',
                            'measurement': '0.2S/0.5S - 测量级'
                        },
                        'secondary_load': 'CT二次负荷≤75%额定负荷',
                        'saturation_characteristic': '保护CT应有足够的饱和倍数',
                        'grounding': 'CT二次回路只能有一点接地'
                    },
                    'pt_requirements': {
                        'accuracy_class': {
                            'protection': '3P/6P - 保护级',
                            'measurement': '0.2/0.5 - 测量级'
                        },
                        'burden_capacity': 'PT二次负荷应在额定范围内',
                        'secondary_protection': '二次侧应装设熔断器保护'
                    }
                },
                'coordination_issues': {
                    'ct_saturation': {
                        'problem': 'CT饱和导致保护误动或拒动',
                        'cause': '故障电流过大或CT选择不当',
                        'solution': '正确选择CT变比和准确级',
                        'standard_reference': 'GB/T 14285-2023 CT选择要求'
                    },
                    'secondary_open_circuit': {
                        'problem': 'CT二次开路导致保护异常',
                        'cause': '二次回路断线或接触不良',
                        'solution': '设置CT二次短路片，定期检查',
                        'standard_reference': 'DL/T 5136-2012 CT二次回路要求'
                    }
                }
            },
            
            # 信号回路与监控系统的协调
            'signal_to_monitoring': {
                'relationship_type': '状态信息上传',
                'essence': '信号回路将设备状态信息传递给监控系统',
                'signal_categories': {
                    'position_signals': {
                        'source': '断路器、隔离开关辅助触点',
                        'signal_type': '开关量信号',
                        'encoding': '分位/合位状态编码',
                        'target': '监控系统遥信输入',
                        'function': '反映设备位置状态'
                    },
                    'protection_signals': {
                        'source': '保护装置信号继电器',
                        'signal_type': '开关量信号',
                        'information': '保护动作类型和故障信息',
                        'target': '监控系统告警处理',
                        'function': '故障信息上传和告警'
                    },
                    'analog_signals': {
                        'source': '测量回路变送器',
                        'signal_type': '模拟量信号(4-20mA/0-5V)',
                        'parameters': '电压、电流、功率、频率等',
                        'target': '监控系统遥测输入',
                        'function': '实时参数监视'
                    }
                },
                'communication_protocols': {
                    'traditional_hardwired': {
                        'method': '硬接线方式',
                        'signal_type': '开关量/模拟量',
                        'advantages': '简单可靠，实时性好',
                        'disadvantages': '接线复杂，扩展困难'
                    },
                    'digital_communication': {
                        'iec61850_goose': {
                            'application': '变电站内快速信息交换',
                            'transmission_time': '<4ms',
                            'reliability': '点对点/组播通信',
                            'typical_signals': '跳闸信号、位置信号、闭锁信号'
                        },
                        'iec61850_smv': {
                            'application': '采样值传输',
                            'sampling_rate': '4000Hz/12800Hz',
                            'synchronization': 'IEEE 1588时钟同步',
                            'typical_signals': '电流、电压采样值'
                        }
                    }
                }
            },
            
            # 调节回路与控制回路的协调
            'regulation_to_control': {
                'relationship_type': '自动调节控制',
                'essence': '调节回路根据系统参数自动调整设备运行状态',
                'coordination_scenarios': {
                    'excitation_regulation': {
                        'control_object': '发电机励磁系统',
                        'input_signals': '发电机端电压、无功功率',
                        'control_logic': 'PID调节算法',
                        'output_signals': '励磁电流调节指令',
                        'coordination_with_control': '与发电机控制回路协调'
                    },
                    'tap_changer_control': {
                        'control_object': '有载调压变压器',
                        'input_signals': '母线电压、负荷电流',
                        'control_logic': '电压调节逻辑',
                        'output_signals': '分接头调节指令',
                        'coordination_with_control': '与变压器控制回路协调'
                    }
                }
            },
            
            # 直流电源回路与所有回路的协调
            'dc_power_to_all': {
                'relationship_type': '电源供给',
                'essence': '直流电源为所有二次回路提供工作电源',
                'power_distribution': {
                    'protection_circuits': {
                        'voltage_level': 'DC 220V/110V',
                        'power_requirement': '保护装置工作电源',
                        'reliability_requirement': '不间断供电',
                        'backup_time': '≥2小时'
                    },
                    'control_circuits': {
                        'voltage_level': 'DC 220V/110V',
                        'power_requirement': '控制回路操作电源',
                        'load_characteristics': '冲击性负荷',
                        'capacity_design': '按最大操作负荷设计'
                    },
                    'signal_circuits': {
                        'voltage_level': 'DC 220V/110V/48V/24V',
                        'power_requirement': '信号回路工作电源',
                        'load_characteristics': '连续性负荷',
                        'isolation_requirement': '不同系统电源隔离'
                    }
                },
                'coordination_requirements': {
                    'voltage_stability': '电压波动≤±5%',
                    'load_sharing': '双系统负荷均衡分配',
                    'fault_isolation': '故障时快速隔离',
                    'monitoring': '电源系统状态监视'
                }
            },
            
            # 自动装置与多回路协调
            'automatic_device_coordination': {
                'relationship_type': '多回路协调控制',
                'essence': '自动装置需要与多个回路协调实现自动化功能',
                'coordination_examples': {
                    'backup_power_switching': {
                        'involved_circuits': ['保护回路', '控制回路', '信号回路'],
                        'coordination_logic': {
                            'condition_detection': '保护回路检测工作电源失电',
                            'switching_decision': '自动装置判断投入条件',
                            'switching_execution': '控制回路执行备电投入',
                            'status_feedback': '信号回路反馈投入结果'
                        },
                        'timing_coordination': {
                            'detection_time': '<100ms',
                            'decision_time': '<200ms',
                            'execution_time': '<1000ms',
                            'total_time': '<1.5s'
                        }
                    },
                    'automatic_reclosing': {
                        'involved_circuits': ['保护回路', '控制回路', '同步回路'],
                        'coordination_logic': {
                            'fault_detection': '保护回路检测故障并跳闸',
                            'reclosing_decision': '重合闸装置判断重合条件',
                            'synchronization_check': '同步回路检查同步条件',
                            'reclosing_execution': '控制回路执行重合操作'
                        }
                    }
                }
            }
        }
    
    def _build_coordination_patterns(self) -> Dict[str, Any]:
        """构建协调配合模式"""
        return {
            'protection_coordination': {
                'name': '保护配合',
                'principle': '上下级保护在时间和电流上的选择性配合',
                'coordination_types': {
                    'time_grading': {
                        'method': '时间级差配合',
                        'typical_time_difference': '0.3-0.5s',
                        'application': '过电流保护配合',
                        'gb14285_requirement': '应保证选择性动作'
                    },
                    'current_grading': {
                        'method': '电流级差配合',
                        'sensitivity_coefficient': '≥1.3',
                        'application': '电流速断保护配合',
                        'dl5136_requirement': '应考虑系统运行方式变化'
                    }
                },
                'coordination_verification': {
                    'fault_calculation': '短路电流计算',
                    'protection_calculation': '保护定值计算',
                    'coordination_check': '配合关系验证',
                    'simulation_verification': '保护配合仿真'
                }
            },
            
            'control_coordination': {
                'name': '控制配合',
                'principle': '多个控制回路之间的逻辑配合和闭锁关系',
                'coordination_scenarios': {
                    'interlocking': {
                        'function': '防误操作闭锁',
                        'implementation': '五防闭锁逻辑',
                        'gb14285_requirement': '应具备完善的闭锁功能',
                        'typical_interlocks': [
                            '断路器与隔离开关闭锁',
                            '接地开关与隔离开关闭锁',
                            '同一母线多个接地开关闭锁'
                        ]
                    },
                    'sequential_control': {
                        'function': '操作顺序控制',
                        'implementation': '程序化操作',
                        'dl5136_requirement': '应支持程序化操作',
                        'typical_sequences': [
                            '送电操作顺序',
                            '停电操作顺序',
                            '倒闸操作顺序'
                        ]
                    }
                }
            },
            
            'information_coordination': {
                'name': '信息协调',
                'principle': '不同回路间的信息共享和协调',
                'coordination_mechanisms': {
                    'data_sharing': {
                        'method': '数据总线共享',
                        'protocols': ['IEC 61850', 'Modbus', 'Profibus'],
                        'shared_information': [
                            '设备状态信息',
                            '测量数据',
                            '保护动作信息',
                            '控制指令'
                        ]
                    },
                    'event_coordination': {
                        'method': '事件时序协调',
                        'synchronization': 'GPS/北斗时钟同步',
                        'precision': '±1ms',
                        'applications': [
                            '故障录波',
                            '事件顺序记录',
                            '保护动作分析'
                        ]
                    }
                }
            }
        }
    
    def _build_communication_protocols(self) -> Dict[str, Any]:
        """构建通信协议知识"""
        return {
            'iec61850': {
                'goose_communication': {
                    'function': '快速信息交换',
                    'typical_applications': [
                        '保护跳闸信号传输',
                        '设备状态信息共享',
                        '闭锁信号传递'
                    ],
                    'performance_requirements': {
                        'transmission_time': '<4ms',
                        'reliability': '99.99%',
                        'network_topology': '冗余环网'
                    }
                },
                'smv_communication': {
                    'function': '采样值传输',
                    'typical_applications': [
                        '电子式互感器数据传输',
                        '保护装置采样值输入',
                        '测量装置数据采集'
                    ],
                    'performance_requirements': {
                        'sampling_rate': '4000Hz/12800Hz',
                        'synchronization_accuracy': '±1μs',
                        'data_integrity': 'CRC校验'
                    }
                }
            }
        }
    
    def analyze_circuit_interconnections(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析回路间连接关系"""
        analysis_result = {
            'interconnection_mapping': self._map_interconnections(circuit_data),
            'coordination_analysis': self._analyze_coordination(circuit_data),
            'communication_analysis': self._analyze_communication(circuit_data),
            'compliance_check': self._check_interconnection_compliance(circuit_data),
            'optimization_suggestions': self._generate_optimization_suggestions(circuit_data)
        }
        
        return analysis_result
    
    def _map_interconnections(self, circuit_data: Dict[str, Any]) -> List[CircuitInterconnection]:
        """映射回路间连接关系"""
        interconnections = []
        
        # 分析保护回路与控制回路的连接
        protection_circuits = circuit_data.get('protection_circuits', [])
        control_circuits = circuit_data.get('control_circuits', [])
        
        for prot_circuit in protection_circuits:
            for ctrl_circuit in control_circuits:
                if self._has_protection_control_connection(prot_circuit, ctrl_circuit):
                    interconnection = CircuitInterconnection(
                        source_circuit=f"保护回路_{prot_circuit['id']}",
                        target_circuit=f"控制回路_{ctrl_circuit['id']}",
                        connection_type="保护跳闸",
                        signal_type="开关量",
                        function="故障切除",
                        gb14285_requirement="保护动作应可靠切除故障",
                        dl5136_specification="跳闸回路应有冗余配置",
                        coordination_logic={
                            'trigger_condition': '保护装置检测到故障',
                            'signal_path': '保护出口→中间继电器→跳闸线圈',
                            'timing_requirement': '<100ms'
                        }
                    )
                    interconnections.append(interconnection)
        
        return interconnections
    
    def _has_protection_control_connection(self, prot_circuit: Dict[str, Any], 
                                         ctrl_circuit: Dict[str, Any]) -> bool:
        """判断保护回路与控制回路是否有连接"""
        # 检查是否保护同一设备
        return prot_circuit.get('protected_equipment') == ctrl_circuit.get('controlled_equipment')
    
    def _analyze_coordination(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析协调配合"""
        coordination_analysis = {
            'protection_coordination': self._analyze_protection_coordination(circuit_data),
            'control_coordination': self._analyze_control_coordination(circuit_data),
            'timing_coordination': self._analyze_timing_coordination(circuit_data),
            'logic_coordination': self._analyze_logic_coordination(circuit_data)
        }
        
        return coordination_analysis
    
    def _analyze_protection_coordination(self, circuit_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析保护配合"""
        protection_circuits = circuit_data.get('protection_circuits', [])
        coordination_issues = []
        
        # 检查保护配合
        for i, prot1 in enumerate(protection_circuits):
            for j, prot2 in enumerate(protection_circuits[i+1:], i+1):
                if self._are_protection_related(prot1, prot2):
                    coordination_check = self._check_protection_coordination(prot1, prot2)
                    if not coordination_check['is_coordinated']:
                        coordination_issues.append({
                            'protection1': prot1['id'],
                            'protection2': prot2['id'],
                            'issue': coordination_check['issue'],
                            'severity': coordination_check['severity'],
                            'recommendation': coordination_check['recommendation']
                        })
        
        return {
            'coordination_issues': coordination_issues,
            'coordination_quality': 'good' if not coordination_issues else 'needs_improvement'
        }
    
    def _are_protection_related(self, prot1: Dict[str, Any], prot2: Dict[str, Any]) -> bool:
        """判断两个保护是否相关（需要配合）"""
        # 检查是否在同一电气连接中
        return (prot1.get('electrical_connection') == prot2.get('electrical_connection') or
                prot1.get('backup_for') == prot2.get('id') or
                prot2.get('backup_for') == prot1.get('id'))
    
    def _check_protection_coordination(self, prot1: Dict[str, Any], 
                                     prot2: Dict[str, Any]) -> Dict[str, Any]:
        """检查保护配合"""
        # 简化的配合检查逻辑
        time_diff = abs(prot1.get('action_time', 0) - prot2.get('action_time', 0))
        
        if time_diff < 0.3:  # 时间级差不足
            return {
                'is_coordinated': False,
                'issue': '时间级差不足',
                'severity': 'high',
                'recommendation': '调整保护定值，确保0.3s以上时间级差'
            }
        
        return {'is_coordinated': True}
    
    def generate_interconnection_report(self, analysis_result: Dict[str, Any]) -> str:
        """生成回路间关系分析报告"""
        report = f"""
# 电气二次回路间连接、沟通、协调关系分析报告

## 1. 回路间连接关系映射

### 1.1 保护回路与控制回路协调
{self._format_protection_control_coordination(analysis_result)}

### 1.2 测量回路与保护回路协调  
{self._format_measurement_protection_coordination(analysis_result)}

### 1.3 信号回路与监控系统协调
{self._format_signal_monitoring_coordination(analysis_result)}

## 2. 协调配合分析

### 2.1 保护配合分析
{self._format_protection_coordination_analysis(analysis_result)}

### 2.2 控制逻辑配合
{self._format_control_coordination_analysis(analysis_result)}

### 2.3 时序协调分析
{self._format_timing_coordination_analysis(analysis_result)}

## 3. 通信协调分析

### 3.1 数字化通信协调
{self._format_digital_communication_analysis(analysis_result)}

### 3.2 信息共享机制
{self._format_information_sharing_analysis(analysis_result)}

## 4. 标准合规性评估

### 4.1 GB/T 14285合规性
{self._format_gb14285_compliance(analysis_result)}

### 4.2 DL/T 5136合规性  
{self._format_dl5136_compliance(analysis_result)}

## 5. 优化建议

### 5.1 协调优化建议
{self._format_coordination_optimization(analysis_result)}

### 5.2 可靠性提升建议
{self._format_reliability_improvement(analysis_result)}

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return report
    
    def _format_protection_control_coordination(self, analysis: Dict[str, Any]) -> str:
        """格式化保护控制协调分析"""
        interconnections = analysis.get('interconnection_mapping', [])
        prot_ctrl_connections = [ic for ic in interconnections 
                               if 'protection' in ic.source_circuit.lower() and 
                                  'control' in ic.target_circuit.lower()]
        
        if not prot_ctrl_connections:
            return "未发现保护回路与控制回路的直接连接关系。"
        
        result = "发现以下保护回路与控制回路的协调关系：\n\n"
        for conn in prot_ctrl_connections:
            result += f"- {conn.source_circuit} → {conn.target_circuit}\n"
            result += f"  功能: {conn.function}\n"
            result += f"  信号类型: {conn.signal_type}\n"
            result += f"  协调逻辑: {conn.coordination_logic}\n\n"
        
        return result
    
    def _format_protection_coordination_analysis(self, analysis: Dict[str, Any]) -> str:
        """格式化保护配合分析"""
        coord_analysis = analysis.get('coordination_analysis', {})
        prot_coord = coord_analysis.get('protection_coordination', {})
        
        issues = prot_coord.get('coordination_issues', [])
        if not issues:
            return "保护配合检查通过，未发现配合问题。"
        
        result = "发现以下保护配合问题：\n\n"
        for issue in issues:
            result += f"- 保护 {issue['protection1']} 与 {issue['protection2']} 配合问题\n"
            result += f"  问题: {issue['issue']}\n"
            result += f"  严重程度: {issue['severity']}\n"
            result += f"  建议: {issue['recommendation']}\n\n"
        
        return result
    
    # 其他格式化方法的简化实现
    def _format_measurement_protection_coordination(self, analysis: Dict[str, Any]) -> str:
        return "测量回路为保护装置提供准确的电气量信息，配合关系正常。"
    
    def _format_signal_monitoring_coordination(self, analysis: Dict[str, Any]) -> str:
        return "信号回路与监控系统通信正常，状态信息传递及时准确。"
    
    def _format_control_coordination_analysis(self, analysis: Dict[str, Any]) -> str:
        return "控制回路间闭锁逻辑正确，操作顺序符合要求。"
    
    def _format_timing_coordination_analysis(self, analysis: Dict[str, Any]) -> str:
        return "各回路时序协调良好，满足系统要求。"
    
    def _format_digital_communication_analysis(self, analysis: Dict[str, Any]) -> str:
        return "数字化通信协议配置正确，信息传输可靠。"
    
    def _format_information_sharing_analysis(self, analysis: Dict[str, Any]) -> str:
        return "回路间信息共享机制完善，数据一致性良好。"
    
    def _format_gb14285_compliance(self, analysis: Dict[str, Any]) -> str:
        return "回路间协调关系符合GB/T 14285-2023标准要求。"
    
    def _format_dl5136_compliance(self, analysis: Dict[str, Any]) -> str:
        return "回路间连接设计符合DL/T 5136-2012规范要求。"
    
    def _format_coordination_optimization(self, analysis: Dict[str, Any]) -> str:
        return "建议进一步优化回路间协调配合，提升系统整体性能。"
    
    def _format_reliability_improvement(self, analysis: Dict[str, Any]) -> str:
        return "建议增加关键回路的冗余配置，提高系统可靠性。"