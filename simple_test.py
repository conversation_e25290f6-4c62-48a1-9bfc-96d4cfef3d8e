#!/usr/bin/env python3
"""
简单的Web应用测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("=== 简单Web应用测试 ===")

try:
    print("1. 测试基础导入...")
    from flask import Flask
    print("✅ Flask导入成功")
    
    print("2. 测试视图模块...")
    from src.web.views import main_bp
    print("✅ 视图模块导入成功")
    
    print("3. 创建简单应用...")
    app = Flask(__name__)
    app.register_blueprint(main_bp)
    print("✅ 应用创建成功")
    
    print("4. 测试路由...")
    with app.app_context():
        routes = [str(rule) for rule in app.url_map.iter_rules()]
        print(f"✅ 注册了 {len(routes)} 个路由")
        
        # 检查统一审查路由
        unified_route_exists = any('/unified-review' in route for route in routes)
        print(f"{'✅' if unified_route_exists else '❌'} 统一审查路由: {unified_route_exists}")
    
    print("5. 启动简单应用...")
    print("访问: http://localhost:5000")
    print("统一审查: http://localhost:5000/unified-review")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
