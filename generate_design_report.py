#!/usr/bin/env python3
"""
生成设计问题检测报告
"""

import os
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path


def check_design_problems(config_file):
    """检查设计问题"""
    issues = []
    
    try:
        # 解析XML文件
        tree = ET.parse(config_file)
        root = tree.getroot()
        
        # 检查断路器Terminal连接
        breakers = root.findall(".//ConductingEquipment[@type='CBR']")
        for breaker in breakers:
            breaker_name = breaker.get('name', '未知断路器')
            terminals = breaker.findall("Terminal")
            
            if len(terminals) == 0:
                issues.append({
                    'id': f'CBR_{breaker_name}_NO_TERMINAL',
                    'title': f"断路器{breaker_name}缺少Terminal连接",
                    'description': f"断路器{breaker_name}没有定义任何Terminal连接，导致回路不通",
                    'severity': "critical",
                    'category': "回路连接",
                    'location': f"断路器: {breaker_name}",
                    'suggestion': f"为断路器{breaker_name}添加至少两个Terminal连接，分别连接到母线和线路侧",
                    'standard_reference': "IEC61850-6: 配置描述语言",
                    'affected_elements': [breaker_name],
                    'auto_fixable': False
                })
        
        # 检查电流互感器
        bays = root.findall(".//Bay")
        for bay in bays:
            bay_name = bay.get('name', '未知间隔')
            breakers = bay.findall(".//ConductingEquipment[@type='CBR']")
            current_transformers = bay.findall(".//ConductingEquipment[@type='CTR']")
            
            if breakers and not current_transformers:
                issues.append({
                    'id': f'BAY_{bay_name}_NO_CTR',
                    'title': f"间隔{bay_name}缺少电流互感器",
                    'description': f"间隔{bay_name}有断路器但缺少电流互感器，无法进行电流测量和保护",
                    'severity': "error",
                    'category': "设备配置",
                    'location': f"间隔: {bay_name}",
                    'suggestion': f"在间隔{bay_name}中添加电流互感器(CTR)用于电流测量",
                    'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                    'affected_elements': [bay_name],
                    'auto_fixable': False
                })
        
        # 检查电流互感器二次侧连接
        current_transformers = root.findall(".//ConductingEquipment[@type='CTR']")
        for ct in current_transformers:
            ct_name = ct.get('name', '未知电流互感器')
            terminals = ct.findall("Terminal")
            
            if len(terminals) < 2:
                issues.append({
                    'id': f'CTR_{ct_name}_INCOMPLETE_TERMINAL',
                    'title': f"电流互感器{ct_name}缺少二次侧连接",
                    'description': f"电流互感器{ct_name}缺少二次侧Terminal连接，无法向保护和测量装置提供电流信号",
                    'severity': "error",
                    'category': "虚端子连接",
                    'location': f"电流互感器: {ct_name}",
                    'suggestion': f"为电流互感器{ct_name}添加二次侧Terminal连接，连接到保护和测量装置",
                    'standard_reference': "IEC61850-9-2: 采样值传输的特殊通信服务映射",
                    'affected_elements': [ct_name],
                    'auto_fixable': False
                })
        
        # 检查电压等级配置完整性
        voltage_levels = root.findall(".//VoltageLevel")
        for vl in voltage_levels:
            vl_name = vl.get('name', '未知电压等级')
            bays = vl.findall("Bay")
            if not bays:
                issues.append({
                    'id': f'VL_{vl_name}_NO_BAY',
                    'title': f"电压等级{vl_name}没有配置任何间隔",
                    'description': f"电压等级{vl_name}下没有定义任何间隔，这是不完整的配置",
                    'severity': "error",
                    'category': "配置完整性",
                    'location': f"电压等级: {vl_name}",
                    'suggestion': f"在电压等级{vl_name}下添加相应的间隔配置，如出线间隔、母联间隔等",
                    'standard_reference': "IEC61850-6: 配置描述语言",
                    'affected_elements': [vl_name],
                    'auto_fixable': False
                })
        
        # 检查IP地址冲突
        ip_addresses = {}
        connected_aps = root.findall(".//ConnectedAP")
        for ap in connected_aps:
            ied_name = ap.get('iedName', '未知IED')
            address = ap.find("Address")
            if address is not None:
                ip_elem = address.find("P[@type='IP']")
                if ip_elem is not None:
                    ip_addr = ip_elem.text
                    if ip_addr in ip_addresses:
                        issues.append({
                            'id': f'IP_CONFLICT_{ip_addr}',
                            'title': f"IP地址冲突: {ip_addr}",
                            'description': f"IP地址{ip_addr}被多个IED使用: {ip_addresses[ip_addr]} 和 {ied_name}",
                            'severity': "critical",
                            'category': "通信配置",
                            'location': f"IED: {ied_name}",
                            'suggestion': f"为IED {ied_name}分配唯一的IP地址",
                            'standard_reference': "IEC61850-8-1: 特定通信服务映射",
                            'affected_elements': [ip_addresses[ip_addr], ied_name],
                            'auto_fixable': True
                        })
                    else:
                        ip_addresses[ip_addr] = ied_name
                
                # 检查网关配置
                gateway = address.find("P[@type='IP-GATEWAY']")
                if gateway is None:
                    issues.append({
                        'id': f'IED_{ied_name}_NO_GATEWAY',
                        'title': f"IED {ied_name}缺少网关配置",
                        'description': f"IED {ied_name}的网络配置中缺少IP-GATEWAY设置",
                        'severity': "warning",
                        'category': "通信配置",
                        'location': f"IED: {ied_name}",
                        'suggestion': f"为IED {ied_name}添加IP-GATEWAY配置",
                        'standard_reference': "IEC61850-8-1: 特定通信服务映射",
                        'affected_elements': [ied_name],
                        'auto_fixable': True
                    })
        
        # 检查数据集引用
        datasets = root.findall(".//DataSet")
        for dataset in datasets:
            dataset_name = dataset.get('name', '未知数据集')
            fcdas = dataset.findall("FCDA")
            for fcda in fcdas:
                ln_class = fcda.get('lnClass')
                ln_inst = fcda.get('lnInst')
                
                # 查找对应的逻辑节点
                ln_xpath = f".//LN[@lnClass='{ln_class}'][@inst='{ln_inst}']"
                logical_nodes = root.findall(ln_xpath)
                
                if not logical_nodes:
                    issues.append({
                        'id': f'DATASET_{dataset_name}_INVALID_REF_{ln_class}{ln_inst}',
                        'title': f"数据集{dataset_name}引用不存在的逻辑节点",
                        'description': f"数据集{dataset_name}中引用了不存在的逻辑节点: {ln_class}{ln_inst}",
                        'severity': "error",
                        'category': "数据集配置",
                        'location': f"数据集: {dataset_name}",
                        'suggestion': f"检查逻辑节点{ln_class}{ln_inst}是否正确定义，或修正数据集中的引用",
                        'standard_reference': "IEC61850-7-2: 抽象通信服务接口",
                        'affected_elements': [dataset_name, f"{ln_class}{ln_inst}"],
                        'auto_fixable': False
                    })
        
        # 检查IED配置完整性
        ieds = root.findall(".//IED")
        for ied in ieds:
            ied_name = ied.get('name', '未知IED')
            ied_type = ied.get('type', '未知类型')
            
            if ied_type == 'Protection':
                protection_lns = ied.findall(".//LN[@lnClass='PTRC']")
                if not protection_lns:
                    issues.append({
                        'id': f'IED_{ied_name}_NO_PROTECTION_LN',
                        'title': f"保护IED {ied_name}缺少保护逻辑节点",
                        'description': f"保护类型的IED {ied_name}没有定义任何保护逻辑节点(PTRC)",
                        'severity': "error",
                        'category': "IED配置",
                        'location': f"IED: {ied_name}",
                        'suggestion': f"为保护IED {ied_name}添加相应的保护逻辑节点(PTRC)",
                        'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        'affected_elements': [ied_name],
                        'auto_fixable': False
                    })
        
        # 检查数据类型定义
        templates = root.find("DataTypeTemplates")
        if templates is not None:
            lln0_types = templates.findall("LNodeType[@lnClass='LLN0']")
            for lln0_type in lln0_types:
                type_id = lln0_type.get('id', '未知类型')
                namPlt_do = lln0_type.find("DO[@name='NamPlt']")
                if namPlt_do is None:
                    issues.append({
                        'id': f'LNTYPE_{type_id}_NO_NAMEPLT',
                        'title': f"LLN0类型{type_id}缺少NamPlt数据对象",
                        'description': f"LLN0类型{type_id}缺少必要的NamPlt(名牌)数据对象",
                        'severity': "error",
                        'category': "数据类型定义",
                        'location': f"LNodeType: {type_id}",
                        'suggestion': f"为LLN0类型{type_id}添加NamPlt数据对象",
                        'standard_reference': "IEC61850-7-4: 兼容的逻辑节点类和数据类",
                        'affected_elements': [type_id],
                        'auto_fixable': True
                    })
    
    except Exception as e:
        issues.append({
            'id': 'PARSE_ERROR',
            'title': "配置文件解析错误",
            'description': f"解析配置文件时发生错误: {str(e)}",
            'severity': "critical",
            'category': "解析错误",
            'location': "文件根节点",
            'suggestion': "检查XML文件格式是否正确",
            'standard_reference': "IEC61850-6: 配置描述语言",
            'affected_elements': ["整个配置文件"],
            'auto_fixable': False
        })
    
    return issues


def generate_json_report(issues, config_file, output_file):
    """生成JSON格式报告"""
    
    # 统计信息
    severity_stats = {}
    category_stats = {}
    
    for issue in issues:
        severity = issue.get('severity', 'info')
        category = issue.get('category', '未知类别')
        
        severity_stats[severity] = severity_stats.get(severity, 0) + 1
        category_stats[category] = category_stats.get(category, 0) + 1
    
    # 计算合规性评分
    total_issues = len(issues)
    critical_count = severity_stats.get('critical', 0)
    error_count = severity_stats.get('error', 0)
    warning_count = severity_stats.get('warning', 0)
    
    # 评分算法：严重问题-20分，错误-10分，警告-5分
    deduction = critical_count * 20 + error_count * 10 + warning_count * 5
    compliance_score = max(0, 100 - deduction)
    
    report = {
        'report_info': {
            'report_id': f"design_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'file_path': config_file,
            'file_type': "IEC61850配置文件",
            'check_date': datetime.now().isoformat(),
            'compliance_score': compliance_score,
            'checker_version': "1.0.0"
        },
        'summary': {
            'total_issues': total_issues,
            'critical_issues': critical_count,
            'error_issues': error_count,
            'warning_issues': warning_count,
            'info_issues': severity_stats.get('info', 0),
            'auto_fixable_issues': len([i for i in issues if i.get('auto_fixable', False)]),
            'compliance_score': compliance_score,
            'severity_distribution': severity_stats,
            'category_distribution': category_stats
        },
        'issues': issues,
        'recommendations': [
            "优先解决严重问题和错误级别的问题",
            "检查回路连接的完整性，确保电气回路通畅",
            "完善设备配置，添加缺少的互感器和虚端子连接",
            "规范通信网络配置，避免IP地址冲突",
            "完善数据类型定义，确保引用的完整性"
        ]
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    return report


def generate_html_report(report_data, output_file):
    """生成HTML格式报告"""
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计问题检测报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; padding: 30px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }}
        .header h1 {{ margin: 0; font-size: 2.5em; }}
        .header .subtitle {{ margin-top: 10px; font-size: 1.2em; opacity: 0.9; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .summary-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }}
        .summary-card h3 {{ margin: 0 0 10px 0; color: #007bff; }}
        .summary-card .value {{ font-size: 2em; font-weight: bold; color: #333; }}
        .issues {{ margin-top: 30px; }}
        .issue {{ background: white; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 20px; padding: 20px; }}
        .issue.critical {{ border-left: 5px solid #dc3545; }}
        .issue.error {{ border-left: 5px solid #fd7e14; }}
        .issue.warning {{ border-left: 5px solid #ffc107; }}
        .issue.info {{ border-left: 5px solid #17a2b8; }}
        .issue-header {{ display: flex; align-items: center; margin-bottom: 15px; }}
        .severity-badge {{ padding: 4px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; margin-right: 15px; }}
        .severity-badge.critical {{ background-color: #dc3545; }}
        .severity-badge.error {{ background-color: #fd7e14; }}
        .severity-badge.warning {{ background-color: #ffc107; color: #333; }}
        .severity-badge.info {{ background-color: #17a2b8; }}
        .issue-title {{ font-size: 1.3em; font-weight: bold; color: #333; }}
        .issue-details {{ margin-top: 15px; }}
        .issue-details div {{ margin-bottom: 8px; }}
        .label {{ font-weight: bold; color: #666; }}
        .stats {{ display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0; }}
        .chart {{ background: #f8f9fa; padding: 20px; border-radius: 8px; }}
        .chart h3 {{ margin-top: 0; color: #333; }}
        .bar {{ display: flex; align-items: center; margin-bottom: 10px; }}
        .bar-label {{ width: 100px; font-size: 0.9em; }}
        .bar-fill {{ height: 20px; background: #007bff; margin-right: 10px; border-radius: 3px; }}
        .bar-value {{ font-weight: bold; }}
        .recommendations {{ background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin-top: 30px; }}
        .recommendations h3 {{ margin-top: 0; color: #28a745; }}
        .recommendations ul {{ margin: 0; padding-left: 20px; }}
        .recommendations li {{ margin-bottom: 8px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 设计问题检测报告</h1>
            <div class="subtitle">IEC61850配置文件设计质量分析</div>
            <div class="subtitle">检测时间: {report_data['report_info']['check_date'][:19].replace('T', ' ')}</div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>📊 总问题数</h3>
                <div class="value">{report_data['summary']['total_issues']}</div>
            </div>
            <div class="summary-card">
                <h3>📈 合规性评分</h3>
                <div class="value">{report_data['summary']['compliance_score']}/100</div>
            </div>
            <div class="summary-card">
                <h3>🔴 严重问题</h3>
                <div class="value">{report_data['summary']['critical_issues']}</div>
            </div>
            <div class="summary-card">
                <h3>🟠 错误问题</h3>
                <div class="value">{report_data['summary']['error_issues']}</div>
            </div>
            <div class="summary-card">
                <h3>🟡 警告问题</h3>
                <div class="value">{report_data['summary']['warning_issues']}</div>
            </div>
            <div class="summary-card">
                <h3>🔧 可自动修复</h3>
                <div class="value">{report_data['summary']['auto_fixable_issues']}</div>
            </div>
        </div>
        
        <div class="stats">
            <div class="chart">
                <h3>📋 按严重程度统计</h3>"""
    
    # 添加严重程度统计图表
    severity_colors = {'critical': '#dc3545', 'error': '#fd7e14', 'warning': '#ffc107', 'info': '#17a2b8'}
    max_count = max(report_data['summary']['severity_distribution'].values()) if report_data['summary']['severity_distribution'] else 1
    
    for severity, count in report_data['summary']['severity_distribution'].items():
        width = (count / max_count) * 200
        color = severity_colors.get(severity, '#6c757d')
        html_content += f"""
                <div class="bar">
                    <div class="bar-label">{severity}</div>
                    <div class="bar-fill" style="width: {width}px; background-color: {color};"></div>
                    <div class="bar-value">{count}</div>
                </div>"""
    
    html_content += """
            </div>
            <div class="chart">
                <h3>📂 按类别统计</h3>"""
    
    # 添加类别统计图表
    max_count = max(report_data['summary']['category_distribution'].values()) if report_data['summary']['category_distribution'] else 1
    
    for category, count in report_data['summary']['category_distribution'].items():
        width = (count / max_count) * 200
        html_content += f"""
                <div class="bar">
                    <div class="bar-label">{category}</div>
                    <div class="bar-fill" style="width: {width}px;"></div>
                    <div class="bar-value">{count}</div>
                </div>"""
    
    html_content += """
            </div>
        </div>
        
        <div class="issues">
            <h2>🔍 详细问题列表</h2>"""
    
    # 添加问题列表
    for i, issue in enumerate(report_data['issues'], 1):
        severity = issue.get('severity', 'info')
        html_content += f"""
            <div class="issue {severity}">
                <div class="issue-header">
                    <span class="severity-badge {severity}">{severity.upper()}</span>
                    <span class="issue-title">{i}. {issue.get('title', '未知问题')}</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> {issue.get('description', '无描述')}</div>
                    <div><span class="label">📍 位置:</span> {issue.get('location', '未知位置')}</div>
                    <div><span class="label">💡 建议:</span> {issue.get('suggestion', '无建议')}</div>
                    <div><span class="label">📚 标准:</span> {issue.get('standard_reference', '无标准引用')}</div>
                    <div><span class="label">🎯 影响元素:</span> {', '.join(issue.get('affected_elements', []))}</div>
                    <div><span class="label">🔧 可自动修复:</span> {'是' if issue.get('auto_fixable', False) else '否'}</div>
                </div>
            </div>"""
    
    html_content += """
        </div>
        
        <div class="recommendations">
            <h3>💡 修复建议</h3>
            <ul>"""
    
    for recommendation in report_data['recommendations']:
        html_content += f"<li>{recommendation}</li>"
    
    html_content += """
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 检测器版本: {report_data['report_info']['checker_version']}</p>
        </div>
    </div>
</body>
</html>"""
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)


def main():
    """主函数"""
    
    print("🔍 设计问题检测报告生成器")
    print("=" * 50)
    
    # 配置文件路径
    config_file = "test_project/problematic_substation.scd"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    print(f"✅ 检测文件: {config_file}")
    
    # 检查设计问题
    print("🔍 正在检查设计问题...")
    issues = check_design_problems(config_file)
    print(f"📊 发现 {len(issues)} 个设计问题")
    
    # 创建报告目录
    reports_dir = Path("reports")
    reports_dir.mkdir(exist_ok=True)
    
    # 生成报告文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    json_file = reports_dir / f"design_check_report_{timestamp}.json"
    html_file = reports_dir / f"design_check_report_{timestamp}.html"
    
    # 生成JSON报告
    print("📄 生成JSON报告...")
    report_data = generate_json_report(issues, config_file, json_file)
    print(f"✅ JSON报告已保存: {json_file}")
    
    # 生成HTML报告
    print("🌐 生成HTML报告...")
    generate_html_report(report_data, html_file)
    print(f"✅ HTML报告已保存: {html_file}")
    
    # 显示报告摘要
    print("\n📊 检测结果摘要:")
    print("=" * 50)
    print(f"📁 检测文件: {config_file}")
    print(f"📊 总问题数: {report_data['summary']['total_issues']}")
    print(f"📈 合规性评分: {report_data['summary']['compliance_score']}/100")
    print(f"🔴 严重问题: {report_data['summary']['critical_issues']} 个")
    print(f"🟠 错误问题: {report_data['summary']['error_issues']} 个")
    print(f"🟡 警告问题: {report_data['summary']['warning_issues']} 个")
    print(f"🔧 可自动修复: {report_data['summary']['auto_fixable_issues']} 个")
    
    print(f"\n📁 生成的报告文件:")
    print(f"   • JSON报告: {json_file}")
    print(f"   • HTML报告: {html_file}")
    
    print(f"\n💡 使用建议:")
    print(f"   • 在浏览器中打开HTML报告查看详细内容")
    print(f"   • JSON报告可用于程序化处理和集成")
    print(f"   • 优先修复严重问题和错误级别的问题")


if __name__ == "__main__":
    main()
