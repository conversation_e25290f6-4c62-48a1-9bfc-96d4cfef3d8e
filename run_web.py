#!/usr/bin/env python3
"""
IEC61850设计检查器 Web界面启动脚本

项目使命：
解决智能变电站二次设计的实际痛点，特别是虚端子连接检查难题。
通过智能分析和可视化技术，帮助工程师快速诊断配置错误，改正设计问题。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """启动Web应用"""
    try:
        # 导入Web应用
        from src.web.app import run_app
        
        print("🔧 IEC61850设计检查器 - 启动中...")
        print("=" * 60)
        print("📋 项目使命：")
        print("   解决智能变电站二次设计的实际痛点")
        print("   • 虚端子连接缺乏直观性 → 可视化网络拓扑")
        print("   • 传统回路检查方法失效 → 智能回路追踪")
        print("   • 配置错误难以发现 → 自动错误诊断")
        print("   • 专业知识门槛高 → 直观用户界面")
        print("=" * 60)
        
        # 启动应用
        run_app(
            host='127.0.0.1',
            port=5000,
            debug=True
        )
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装所需依赖：pip install flask flask-cors")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
