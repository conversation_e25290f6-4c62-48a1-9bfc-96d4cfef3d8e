<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计报告仪表板</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; padding: 30px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-value { font-size: 2.5em; font-weight: bold; color: #667eea; }
        .stat-label { font-size: 1.1em; color: #666; margin-top: 10px; }
        .reports-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .report-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .report-header { background: #667eea; color: white; padding: 15px; font-weight: bold; }
        .report-content { padding: 20px; }
        .file-item { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #eee; }
        .file-name { font-weight: 500; color: #333; }
        .file-info { font-size: 0.9em; color: #666; }
        .btn { padding: 8px 16px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; font-size: 0.9em; }
        .btn:hover { background: #5a6fd8; }
        .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 设计报告仪表板</h1>
            <p>智能变电站设计质量检测报告管理中心</p>
            <p>更新时间: 2025-08-18 14:45:00</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">15</div>
                <div class="stat-label">📄 总报告数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">5</div>
                <div class="stat-label">📂 活跃类型</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">156.8</div>
                <div class="stat-label">💾 总大小(KB)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">10</div>
                <div class="stat-label">🗂️ 报告类型</div>
            </div>
        </div>
        
        <div class="highlight">
            <h3>🌟 重点推荐</h3>
            <p><strong>🔧 全面修正系统</strong>: 基于16个技术错误的全面审查，修正了所有回路逻辑问题，包括控制、保护、测量、信号、直流电源等。⭐ <strong>最新完整修正</strong></p>
            <p><strong>🔍 全面审查报告</strong>: 发现16个技术错误（4个严重、11个重要、1个一般），涵盖5大回路类型的深度分析。🚨 <strong>重要发现</strong></p>
            <p><strong>🏢 国网标准系统</strong>: 严格按照国网公司技术规范(Q/GDW系列标准)重新设计的智能变电站系统，符合企业标准要求。</p>
            <p><strong>🏭 完整二次回路图系统</strong>: 基于您提供的专业知识生成的完整变电站二次回路图，涵盖九大功能分类，符合GB/T 4728标准。</p>
        </div>
        
        <div class="reports-grid">
            <div class="report-card">
                <div class="report-header">
                    🔧 全面修正系统 (3 个文件) - ⭐ 最新完整修正
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">📋 corrected_control_circuit.svg</div>
                            <div class="file-info">08-29 19:00 - 修正后的控制回路图（五防逻辑+独立回路）</div>
                        </div>
                        <a href="fully_corrected/corrected_control_circuit.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🔍 comprehensive_circuit_audit.json</div>
                            <div class="file-info">08-29 18:53 - 全面回路逻辑审查报告（16个错误）</div>
                        </div>
                        <a href="circuit_audit/comprehensive_circuit_audit.json" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📊 correction_report.json</div>
                            <div class="file-info">08-29 19:00 - 修正报告</div>
                        </div>
                        <a href="fully_corrected/correction_report.json" class="btn" target="_blank">查看</a>
                    </div>
                </div>
            </div>

            <div class="report-card">
                <div class="report-header">
                    🔧 权威标准修正系统 (3 个文件) - 🚨 重要修正
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🛡️ corrected_protection_circuit.svg</div>
                            <div class="file-info">08-29 18:30 - 修正后的保护回路图（符合权威标准）</div>
                        </div>
                        <a href="corrected_standards/corrected_protection_circuit.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📋 technical_error_analysis.json</div>
                            <div class="file-info">08-29 18:28 - 技术错误分析报告</div>
                        </div>
                        <a href="standard_analysis/technical_error_analysis.json" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🔧 correction_plan.json</div>
                            <div class="file-info">08-29 18:28 - 全面修正计划</div>
                        </div>
                        <a href="standard_analysis/correction_plan.json" class="btn" target="_blank">查看</a>
                    </div>
                </div>
            </div>

            <div class="report-card">
                <div class="report-header">
                    🏢 国网标准系统 (3 个文件)
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🌐 sgcc_standard_viewer.html</div>
                            <div class="file-info">08-29 18:10 - 国网公司智能变电站技术规范系统</div>
                        </div>
                        <a href="sgcc_standards/sgcc_standard_viewer.html" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📋 sgcc_standard_info.svg</div>
                            <div class="file-info">08-29 18:10 - 国网技术规范体系图</div>
                        </div>
                        <a href="sgcc_standards/sgcc_standard_info.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🛡️ sgcc_protection_circuit.svg</div>
                            <div class="file-info">08-29 18:10 - 符合Q/GDW 441-2010的保护回路图</div>
                        </div>
                        <a href="sgcc_standards/sgcc_protection_circuit.svg" class="btn" target="_blank">查看</a>
                    </div>
                </div>
            </div>

            <div class="report-card">
                <div class="report-header">
                    🏭 完整二次回路图系统 (6 个文件)
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🌐 comprehensive_circuit_viewer.html</div>
                            <div class="file-info">08-18 15:09 - 完整的变电站二次回路图系统</div>
                        </div>
                        <a href="comprehensive_circuits/comprehensive_circuit_viewer.html" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 control_circuit_comprehensive.svg</div>
                            <div class="file-info">08-18 15:09 - 控制回路图(DC 220V)</div>
                        </div>
                        <a href="comprehensive_circuits/control_circuit_comprehensive.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 protection_circuit_comprehensive.svg</div>
                            <div class="file-info">08-18 15:09 - 保护回路图(CT/PT二次回路)</div>
                        </div>
                        <a href="comprehensive_circuits/protection_circuit_comprehensive.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 measurement_circuit_comprehensive.svg</div>
                            <div class="file-info">08-18 15:09 - 测量回路图(电流/电压测量)</div>
                        </div>
                        <a href="comprehensive_circuits/measurement_circuit_comprehensive.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 signal_circuit_comprehensive.svg</div>
                            <div class="file-info">08-18 15:25 - 信号回路图(状态/告警/故障信号)</div>
                        </div>
                        <a href="comprehensive_circuits/signal_circuit_comprehensive.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 dc_power_circuit_comprehensive.svg</div>
                            <div class="file-info">08-18 15:25 - 直流电源回路图(系统心脏)</div>
                        </div>
                        <a href="comprehensive_circuits/dc_power_circuit_comprehensive.svg" class="btn" target="_blank">查看</a>
                    </div>
                </div>
            </div>

            <div class="report-card">
                <div class="report-header">
                    🎨 回路分析报告 (7 个文件)
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🌐 circuit_viewer.html</div>
                            <div class="file-info">08-18 14:36 - 交互式回路图查看器</div>
                        </div>
                        <a href="circuit_analysis/circuit_viewer.html" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 current_circuit_enhanced.svg</div>
                            <div class="file-info">08-18 14:36 - 增强版电流回路图</div>
                        </div>
                        <a href="circuit_analysis/current_circuit_enhanced.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 protection_circuit_enhanced.svg</div>
                            <div class="file-info">08-18 14:36 - 增强版保护回路图</div>
                        </div>
                        <a href="circuit_analysis/protection_circuit_enhanced.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">🎨 trip_circuit_enhanced.svg</div>
                            <div class="file-info">08-18 14:36 - 增强版跳闸回路图</div>
                        </div>
                        <a href="circuit_analysis/trip_circuit_enhanced.svg" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📄 enhanced_circuit_report.json</div>
                            <div class="file-info">08-18 14:36 - 回路生成报告</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="report-card">
                <div class="report-header">
                    🔍 设计检查报告 (2 个文件)
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🌐 design_check_report_20250818_114504.html</div>
                            <div class="file-info">08-18 11:45 - 可视化设计检查报告</div>
                        </div>
                        <a href="design_check/design_check_report_20250818_114504.html" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📄 design_check_report_20250818_114504.json</div>
                            <div class="file-info">08-18 11:45 - 结构化检查数据</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="report-card">
                <div class="report-header">
                    📋 合规性检查报告 (2 个文件)
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🌐 unified_review_test_report.html</div>
                            <div class="file-info">08-18 10:30 - 统一审查测试报告</div>
                        </div>
                        <a href="compliance_check/unified_review_test_report.html" class="btn" target="_blank">查看</a>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📋 unified_review_test_report_alt.pdf</div>
                            <div class="file-info">08-18 10:30 - PDF格式报告</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="report-card">
                <div class="report-header">
                    📦 历史报告存档 (4 个文件)
                </div>
                <div class="report-content">
                    <div class="file-item">
                        <div>
                            <div class="file-name">🌐 demo_review_1755445754.html</div>
                            <div class="file-info">历史演示报告</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📄 comparison_test.json</div>
                            <div class="file-info">对比测试数据</div>
                        </div>
                    </div>
                    <div class="file-item">
                        <div>
                            <div class="file-name">📝 comparison_test.md</div>
                            <div class="file-info">对比测试文档</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-top: 30px;">
            <h3>📖 使用说明</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div>
                    <h4>🎨 回路分析报告</h4>
                    <ul>
                        <li>查看 <code>circuit_viewer.html</code> 获得最佳体验</li>
                        <li>包含电流回路、保护回路、跳闸回路图</li>
                        <li>展示虚端子技术的应用优势</li>
                        <li>SVG格式支持缩放和编辑</li>
                    </ul>
                    
                    <h4>🔍 设计检查报告</h4>
                    <ul>
                        <li>HTML格式提供最佳可视化体验</li>
                        <li>发现了9个具体的设计问题</li>
                        <li>包括2个严重问题和6个错误问题</li>
                        <li>按优先级排序的修复建议</li>
                    </ul>
                </div>
                <div>
                    <h4>🌟 系统亮点</h4>
                    <ul>
                        <li><strong>虚端子回路图生成</strong>: 基于IEC61850配置自动生成传统回路图</li>
                        <li><strong>符合国标</strong>: 遵循GB/T 4728电气图形符号标准</li>
                        <li><strong>智能检测</strong>: 自动发现回路不通、IP冲突等关键问题</li>
                        <li><strong>可视化报告</strong>: 提供HTML、SVG等多种格式</li>
                        <li><strong>实用价值</strong>: 提前发现问题，降低现场调试成本</li>
                    </ul>
                    
                    <h4>🚀 快速操作</h4>
                    <ul>
                        <li>点击"查看"按钮直接在浏览器中打开报告</li>
                        <li>SVG文件可在支持的软件中编辑和打印</li>
                        <li>JSON文件可用于程序化处理和系统集成</li>
                        <li>所有报告支持离线查看和分享</li>
                    </ul>
                </div>
            </div>
            
            <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h4>💡 主要成果展示</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                    <div>
                        <strong>🔧 全面修正系统:</strong>
                        <ul style="margin: 5px 0;">
                            <li>🔍 发现16个技术错误（5大回路类型）</li>
                            <li>🚨 4个严重错误：跳闸逻辑、CT配置等</li>
                            <li>⚠️ 11个重要错误：防误操作、信号分类等</li>
                            <li>✅ 控制回路：五防逻辑+独立回路</li>
                            <li>✅ 保护回路：硬接线跳闸+双重化</li>
                            <li>✅ 符合权威标准和工程实际</li>
                        </ul>
                    </div>
                    <div>
                        <strong>🔍 设计问题检测:</strong>
                        <ul style="margin: 5px 0;">
                            <li>发现断路器QF1缺少Terminal连接</li>
                            <li>检测出IP地址冲突问题</li>
                            <li>识别电流互感器二次侧连接不完整</li>
                            <li>发现数据集引用完整性问题</li>
                        </ul>
                    </div>
                    <div>
                        <strong>🎨 虚端子技术应用:</strong>
                        <ul style="margin: 5px 0;">
                            <li>GOOSE虚端子 - 快速跳闸信号</li>
                            <li>SV采样值 - 数字化CT/PT信号</li>
                            <li>IEC 61850标准 - 智能变电站</li>
                            <li>对比传统硬接线优势</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
