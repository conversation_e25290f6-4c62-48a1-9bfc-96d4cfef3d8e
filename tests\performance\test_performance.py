"""
性能测试套件
测试系统在各种负载下的性能表现
"""

import pytest
import time
import psutil
import threading
import concurrent.futures
import os
import tempfile
import gc
from pathlib import Path
from memory_profiler import profile
import cProfile
import pstats
from io import StringIO

from src.core.parser.scd_parser import SCDParser
from src.core.validator.validation_engine import ValidationEngine
from src.comparison.file_comparator import FileComparator
from src.virtual_terminal.vt_generator import VirtualTerminalGenerator


class PerformanceTestSuite:
    """性能测试套件"""
    
    @pytest.fixture(scope="class")
    def setup_performance_environment(self):
        """设置性能测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建不同大小的测试文件
        self.small_file = self._create_test_file("small", 10)  # 10个IED
        self.medium_file = self._create_test_file("medium", 50)  # 50个IED
        self.large_file = self._create_test_file("large", 100)  # 100个IED
        self.xlarge_file = self._create_test_file("xlarge", 200)  # 200个IED
        
        yield
        
        # 清理
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_parsing_performance(self, setup_performance_environment):
        """测试解析性能"""
        parser = SCDParser()
        
        test_cases = [
            ("小文件", self.small_file, 5.0),
            ("中等文件", self.medium_file, 15.0),
            ("大文件", self.large_file, 30.0),
            ("超大文件", self.xlarge_file, 60.0)
        ]
        
        results = {}
        
        for name, file_path, max_time in test_cases:
            start_time = time.time()
            
            parsed_data = parser.parse_file(file_path)
            
            elapsed_time = time.time() - start_time
            results[name] = elapsed_time
            
            assert parsed_data is not None
            assert elapsed_time < max_time, f"{name}解析时间超出预期: {elapsed_time:.2f}s > {max_time}s"
            
            print(f"✅ {name}解析性能: {elapsed_time:.2f}s")
        
        return results
    
    def test_validation_performance(self, setup_performance_environment):
        """测试验证性能"""
        parser = SCDParser()
        validation_engine = ValidationEngine()
        
        test_cases = [
            ("小文件", self.small_file, 10.0),
            ("中等文件", self.medium_file, 30.0),
            ("大文件", self.large_file, 60.0),
            ("超大文件", self.xlarge_file, 120.0)
        ]
        
        results = {}
        
        for name, file_path, max_time in test_cases:
            # 解析文件
            parsed_data = parser.parse_file(file_path)
            
            # 测试验证性能
            start_time = time.time()
            
            validation_result = validation_engine.validate(parsed_data)
            
            elapsed_time = time.time() - start_time
            results[name] = elapsed_time
            
            assert validation_result is not None
            assert elapsed_time < max_time, f"{name}验证时间超出预期: {elapsed_time:.2f}s > {max_time}s"
            
            print(f"✅ {name}验证性能: {elapsed_time:.2f}s")
        
        return results
    
    def test_comparison_performance(self, setup_performance_environment):
        """测试对比性能"""
        comparator = FileComparator()
        
        test_cases = [
            ("小文件", self.small_file, self.small_file, 10.0),
            ("中等文件", self.medium_file, self.medium_file, 30.0),
            ("大文件", self.large_file, self.large_file, 60.0)
        ]
        
        results = {}
        
        for name, file1, file2, max_time in test_cases:
            start_time = time.time()
            
            comparison_result = comparator.compare_files(file1, file2)
            
            elapsed_time = time.time() - start_time
            results[name] = elapsed_time
            
            assert comparison_result is not None
            assert elapsed_time < max_time, f"{name}对比时间超出预期: {elapsed_time:.2f}s > {max_time}s"
            
            print(f"✅ {name}对比性能: {elapsed_time:.2f}s")
        
        return results
    
    def test_memory_usage(self, setup_performance_environment):
        """测试内存使用情况"""
        process = psutil.Process()
        
        def get_memory_mb():
            return process.memory_info().rss / 1024 / 1024
        
        initial_memory = get_memory_mb()
        
        parser = SCDParser()
        validation_engine = ValidationEngine()
        
        # 处理大文件并监控内存
        parsed_data = parser.parse_file(self.large_file)
        parse_memory = get_memory_mb()
        
        validation_result = validation_engine.validate(parsed_data)
        validation_memory = get_memory_mb()
        
        # 清理并强制垃圾回收
        del parsed_data
        del validation_result
        gc.collect()
        
        final_memory = get_memory_mb()
        
        parse_increase = parse_memory - initial_memory
        validation_increase = validation_memory - parse_memory
        memory_leak = final_memory - initial_memory
        
        print(f"内存使用情况:")
        print(f"  初始内存: {initial_memory:.2f}MB")
        print(f"  解析后增长: {parse_increase:.2f}MB")
        print(f"  验证后增长: {validation_increase:.2f}MB")
        print(f"  清理后泄漏: {memory_leak:.2f}MB")
        
        # 验证内存使用合理
        assert parse_increase < 500, f"解析内存增长过大: {parse_increase:.2f}MB"
        assert validation_increase < 300, f"验证内存增长过大: {validation_increase:.2f}MB"
        assert memory_leak < 50, f"内存泄漏: {memory_leak:.2f}MB"
        
        return {
            'initial': initial_memory,
            'parse_increase': parse_increase,
            'validation_increase': validation_increase,
            'memory_leak': memory_leak
        }
    
    def test_concurrent_performance(self, setup_performance_environment):
        """测试并发性能"""
        def process_file(file_path):
            parser = SCDParser()
            validation_engine = ValidationEngine()
            
            start_time = time.time()
            
            parsed_data = parser.parse_file(file_path)
            validation_result = validation_engine.validate(parsed_data)
            
            elapsed_time = time.time() - start_time
            return elapsed_time
        
        # 测试不同并发级别
        files = [self.small_file, self.medium_file] * 5  # 10个文件
        
        # 串行处理
        start_time = time.time()
        serial_times = [process_file(f) for f in files]
        serial_total = time.time() - start_time
        
        # 并行处理
        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            parallel_times = list(executor.map(process_file, files))
        parallel_total = time.time() - start_time
        
        speedup = serial_total / parallel_total
        
        print(f"并发性能测试:")
        print(f"  串行总时间: {serial_total:.2f}s")
        print(f"  并行总时间: {parallel_total:.2f}s")
        print(f"  加速比: {speedup:.2f}x")
        
        assert speedup > 1.5, f"并发加速效果不明显: {speedup:.2f}x"
        
        return {
            'serial_total': serial_total,
            'parallel_total': parallel_total,
            'speedup': speedup
        }
    
    def test_stress_testing(self, setup_performance_environment):
        """压力测试"""
        parser = SCDParser()
        validation_engine = ValidationEngine()
        
        # 连续处理多个文件
        files = [self.medium_file] * 20
        
        start_time = time.time()
        success_count = 0
        error_count = 0
        
        for i, file_path in enumerate(files):
            try:
                parsed_data = parser.parse_file(file_path)
                validation_result = validation_engine.validate(parsed_data)
                
                if validation_result is not None:
                    success_count += 1
                else:
                    error_count += 1
                
                # 清理内存
                del parsed_data
                del validation_result
                
                if i % 5 == 0:
                    gc.collect()
                
            except Exception as e:
                error_count += 1
                print(f"处理文件 {i} 时出错: {e}")
        
        total_time = time.time() - start_time
        avg_time = total_time / len(files)
        
        print(f"压力测试结果:")
        print(f"  处理文件数: {len(files)}")
        print(f"  成功数: {success_count}")
        print(f"  失败数: {error_count}")
        print(f"  总时间: {total_time:.2f}s")
        print(f"  平均时间: {avg_time:.2f}s/文件")
        
        success_rate = success_count / len(files)
        assert success_rate > 0.95, f"成功率过低: {success_rate:.2%}"
        
        return {
            'total_files': len(files),
            'success_count': success_count,
            'error_count': error_count,
            'total_time': total_time,
            'avg_time': avg_time,
            'success_rate': success_rate
        }
    
    @profile
    def test_memory_profiling(self, setup_performance_environment):
        """内存分析测试"""
        parser = SCDParser()
        validation_engine = ValidationEngine()
        
        # 处理大文件
        parsed_data = parser.parse_file(self.large_file)
        validation_result = validation_engine.validate(parsed_data)
        
        # 生成虚端子表
        vt_generator = VirtualTerminalGenerator()
        vt_table = vt_generator.generate_from_scd(parsed_data, "性能测试")
        
        return True
    
    def test_cpu_profiling(self, setup_performance_environment):
        """CPU性能分析"""
        profiler = cProfile.Profile()
        
        def profile_target():
            parser = SCDParser()
            validation_engine = ValidationEngine()
            
            parsed_data = parser.parse_file(self.medium_file)
            validation_result = validation_engine.validate(parsed_data)
            
            return validation_result
        
        # 执行性能分析
        profiler.enable()
        result = profile_target()
        profiler.disable()
        
        # 生成报告
        s = StringIO()
        ps = pstats.Stats(profiler, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)  # 显示前20个最耗时的函数
        
        profile_output = s.getvalue()
        print("CPU性能分析报告:")
        print(profile_output)
        
        assert result is not None
        return profile_output
    
    def _create_test_file(self, size_name, ied_count):
        """创建指定大小的测试文件"""
        file_path = os.path.join(self.temp_dir, f"test_{size_name}.scd")
        
        # 基础SCD模板
        header = '''<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL">
    <Header id="PerformanceTestSCD" version="1.0" revision="A" toolID="IEC61850Validator">
        <Text>Performance Test SCD File</Text>
    </Header>
    <Substation name="TestSubstation">
        <VoltageLevel name="110kV">
            <Bay name="Bay1">
                <ConductingEquipment name="CB1" type="CBR"/>
            </Bay>
        </VoltageLevel>
    </Substation>'''
        
        # 生成多个IED
        ieds = ""
        for i in range(1, ied_count + 1):
            ieds += f'''
    <IED name="TestIED{i}" type="Protection" manufacturer="TestManufacturer">
        <Services>
            <DynAssociation/>
            <GetDirectory/>
            <GetDataObjectDefinition/>
            <DataObjectDirectory/>
            <GetDataSetValue/>
            <SetDataSetValue/>
            <DataSetDirectory/>
            <ConfDataSet modify="true"/>
            <DynDataSet maxAttributes="100"/>
            <ReadWrite/>
        </Services>
        <AccessPoint name="AP{i}">
            <Server>
                <Authentication/>
                <LDevice inst="LD{i}">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type">
                        <DataSet name="DS{i}">
                            <FCDA ldInst="LD{i}" lnClass="PTOC" lnInst="1" doName="Str" fc="ST"/>
                        </DataSet>
                    </LN0>
                    <LN lnClass="PTOC" inst="1" lnType="PTOC_Type"/>
                    <LN lnClass="PTOC" inst="2" lnType="PTOC_Type"/>
                    <LN lnClass="PDIF" inst="1" lnType="PDIF_Type"/>
                </LDevice>
            </Server>
        </AccessPoint>
    </IED>'''
        
        # 数据类型模板
        templates = '''
    <DataTypeTemplates>
        <LNodeType id="LLN0_Type" lnClass="LLN0">
            <DO name="Mod" type="INC_1"/>
            <DO name="Beh" type="INS_1"/>
            <DO name="Health" type="INS_1"/>
        </LNodeType>
        <LNodeType id="PTOC_Type" lnClass="PTOC">
            <DO name="Str" type="ACD_1"/>
            <DO name="Op" type="ACT_1"/>
        </LNodeType>
        <LNodeType id="PDIF_Type" lnClass="PDIF">
            <DO name="Str" type="ACD_1"/>
            <DO name="Op" type="ACT_1"/>
        </LNodeType>
        <DOType id="INC_1" cdc="INC">
            <DA name="stVal" fc="ST" dchg="true" bType="INT32"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <DOType id="INS_1" cdc="INS">
            <DA name="stVal" fc="ST" dchg="true" bType="INT32"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <DOType id="ACD_1" cdc="ACD">
            <DA name="general" fc="ST" dchg="true" bType="BOOLEAN"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <DOType id="ACT_1" cdc="ACT">
            <DA name="general" fc="ST" dchg="true" bType="BOOLEAN"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
    </DataTypeTemplates>'''
        
        # 通信配置
        communication = '''
    <Communication>
        <SubNetwork name="SubNetwork1" type="8-MMS">'''
        
        for i in range(1, ied_count + 1):
            communication += f'''
            <ConnectedAP iedName="TestIED{i}" apName="AP{i}">
                <Address>
                    <P type="IP">192.168.1.{100 + i}</P>
                    <P type="IP-SUBNET">*************</P>
                    <P type="IP-GATEWAY">***********</P>
                </Address>
            </ConnectedAP>'''
        
        communication += '''
        </SubNetwork>
    </Communication>'''
        
        footer = '''
</SCL>'''
        
        # 写入文件
        content = header + ieds + templates + communication + footer
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return file_path


if __name__ == "__main__":
    # 运行性能测试
    pytest.main([__file__, "-v", "--tb=short", "-s"])
