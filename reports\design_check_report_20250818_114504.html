<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计问题检测报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 30px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header .subtitle { margin-top: 10px; font-size: 1.2em; opacity: 0.9; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .summary-card h3 { margin: 0 0 10px 0; color: #007bff; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #333; }
        .issues { margin-top: 30px; }
        .issue { background: white; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 20px; padding: 20px; }
        .issue.critical { border-left: 5px solid #dc3545; }
        .issue.error { border-left: 5px solid #fd7e14; }
        .issue.warning { border-left: 5px solid #ffc107; }
        .issue.info { border-left: 5px solid #17a2b8; }
        .issue-header { display: flex; align-items: center; margin-bottom: 15px; }
        .severity-badge { padding: 4px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; margin-right: 15px; }
        .severity-badge.critical { background-color: #dc3545; }
        .severity-badge.error { background-color: #fd7e14; }
        .severity-badge.warning { background-color: #ffc107; color: #333; }
        .severity-badge.info { background-color: #17a2b8; }
        .issue-title { font-size: 1.3em; font-weight: bold; color: #333; }
        .issue-details { margin-top: 15px; }
        .issue-details div { margin-bottom: 8px; }
        .label { font-weight: bold; color: #666; }
        .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0; }
        .chart { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .chart h3 { margin-top: 0; color: #333; }
        .bar { display: flex; align-items: center; margin-bottom: 10px; }
        .bar-label { width: 100px; font-size: 0.9em; }
        .bar-fill { height: 20px; background: #007bff; margin-right: 10px; border-radius: 3px; }
        .bar-value { font-weight: bold; }
        .recommendations { background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin-top: 30px; }
        .recommendations h3 { margin-top: 0; color: #28a745; }
        .recommendations ul { margin: 0; padding-left: 20px; }
        .recommendations li { margin-bottom: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 设计问题检测报告</h1>
            <div class="subtitle">IEC61850配置文件设计质量分析</div>
            <div class="subtitle">检测时间: 2025-08-18 11:45:04</div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>📊 总问题数</h3>
                <div class="value">0</div>
            </div>
            <div class="summary-card">
                <h3>📈 合规性评分</h3>
                <div class="value">100/100</div>
            </div>
            <div class="summary-card">
                <h3>🔴 严重问题</h3>
                <div class="value">0</div>
            </div>
            <div class="summary-card">
                <h3>🟠 错误问题</h3>
                <div class="value">0</div>
            </div>
            <div class="summary-card">
                <h3>🟡 警告问题</h3>
                <div class="value">0</div>
            </div>
            <div class="summary-card">
                <h3>🔧 可自动修复</h3>
                <div class="value">0</div>
            </div>
        </div>
        
        <div class="stats">
            <div class="chart">
                <h3>📋 按严重程度统计</h3>
            </div>
            <div class="chart">
                <h3>📂 按类别统计</h3>
            </div>
        </div>
        
        <div class="issues">
            <h2>🔍 详细问题列表</h2>
        </div>
        
        <div class="recommendations">
            <h3>💡 修复建议</h3>
            <ul><li>优先解决严重问题和错误级别的问题</li><li>检查回路连接的完整性，确保电气回路通畅</li><li>完善设备配置，添加缺少的互感器和虚端子连接</li><li>规范通信网络配置，避免IP地址冲突</li><li>完善数据类型定义，确保引用的完整性</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p>报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 检测器版本: {report_data['report_info']['checker_version']}</p>
        </div>
    </div>
</body>
</html>