"""
网络拓扑可视化组件
显示IED网络连接关系和虚端子连接
"""

import logging
import math
from typing import Dict, List, Any, Optional, Tuple

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGraphicsView, QGraphicsScene,
    QGraphicsItem, QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsTextItem,
    QGraphicsRectItem, QToolBar, QPushButton, QSlider, QLabel, QComboBox,
    QGroupBox, QSplitter, QTreeWidget, QTreeWidgetItem, QMessageBox
)
from PySide6.QtCore import Qt, QRectF, QPointF, Signal, QTimer
from PySide6.QtGui import QPen, QBrush, QColor, QFont, QPainter, QPolygonF

logger = logging.getLogger(__name__)


class NetworkNode(QGraphicsEllipseItem):
    """网络节点图形项"""
    
    def __init__(self, node_id: str, node_data: Dict[str, Any], radius: float = 30):
        super().__init__(-radius, -radius, radius * 2, radius * 2)
        
        self.node_id = node_id
        self.node_data = node_data
        self.radius = radius
        
        # 设置外观
        self.setPen(QPen(QColor(70, 130, 180), 2))
        self.setBrush(QBrush(QColor(173, 216, 230)))
        
        # 设置可移动
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        
        # 添加文本标签
        self.text_item = QGraphicsTextItem(node_id, self)
        self.text_item.setPos(-len(node_id) * 3, radius + 5)
        self.text_item.setFont(QFont("Arial", 8))
        
        # 连接线列表
        self.connections = []
    
    def add_connection(self, connection):
        """添加连接线"""
        self.connections.append(connection)
    
    def remove_connection(self, connection):
        """移除连接线"""
        if connection in self.connections:
            self.connections.remove(connection)
    
    def itemChange(self, change, value):
        """项目变化事件"""
        if change == QGraphicsItem.ItemPositionChange:
            # 更新连接线位置
            for connection in self.connections:
                connection.update_position()
        
        return super().itemChange(change, value)
    
    def get_connection_point(self, target_pos: QPointF) -> QPointF:
        """获取连接点位置"""
        # 计算从节点中心到目标的方向
        center = self.pos()
        direction = target_pos - center
        length = math.sqrt(direction.x() ** 2 + direction.y() ** 2)
        
        if length == 0:
            return center
        
        # 归一化方向向量
        direction = direction / length
        
        # 返回圆边缘的点
        return center + direction * self.radius


class NetworkConnection(QGraphicsLineItem):
    """网络连接图形项"""
    
    def __init__(self, source_node: NetworkNode, target_node: NetworkNode, 
                 connection_data: Dict[str, Any]):
        super().__init__()
        
        self.source_node = source_node
        self.target_node = target_node
        self.connection_data = connection_data
        
        # 设置外观
        self.setPen(QPen(QColor(105, 105, 105), 2))
        
        # 添加到节点的连接列表
        source_node.add_connection(self)
        target_node.add_connection(self)
        
        # 更新位置
        self.update_position()
    
    def update_position(self):
        """更新连接线位置"""
        source_pos = self.source_node.get_connection_point(self.target_node.pos())
        target_pos = self.target_node.get_connection_point(self.source_node.pos())
        
        self.setLine(source_pos.x(), source_pos.y(), target_pos.x(), target_pos.y())


class NetworkTopologyWidget(QWidget):
    """网络拓扑可视化组件"""
    
    # 信号
    node_selected = Signal(dict)        # 节点选择信号
    connection_selected = Signal(dict)  # 连接选择信号
    
    def __init__(self, parent=None):
        """初始化网络拓扑组件"""
        super().__init__(parent)
        
        self.network_data = None
        self.nodes = {}  # node_id -> NetworkNode
        self.connections = []  # NetworkConnection列表
        
        self._init_ui()
        self._setup_layout_algorithms()
        
        logger.debug("网络拓扑组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # 主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 图形视图
        self.graphics_view = QGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        self.graphics_view.setRenderHint(QPainter.Antialiasing)
        self.graphics_view.setDragMode(QGraphicsView.RubberBandDrag)
        
        main_splitter.addWidget(self.graphics_view)
        
        # 侧边面板
        side_panel = self._create_side_panel()
        main_splitter.addWidget(side_panel)
        
        # 设置分割器比例
        main_splitter.setSizes([600, 200])
    
    def _create_toolbar(self) -> QToolBar:
        """创建工具栏"""
        toolbar = QToolBar()
        
        # 布局算法选择
        layout_label = QLabel("布局:")
        toolbar.addWidget(layout_label)
        
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(["圆形布局", "网格布局", "力导向布局", "层次布局"])
        self.layout_combo.currentTextChanged.connect(self._on_layout_changed)
        toolbar.addWidget(self.layout_combo)
        
        toolbar.addSeparator()
        
        # 缩放控制
        zoom_label = QLabel("缩放:")
        toolbar.addWidget(zoom_label)
        
        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setRange(10, 200)
        self.zoom_slider.setValue(100)
        self.zoom_slider.valueChanged.connect(self._on_zoom_changed)
        toolbar.addWidget(self.zoom_slider)
        
        # 重置视图
        reset_button = QPushButton("重置视图")
        reset_button.clicked.connect(self._reset_view)
        toolbar.addWidget(reset_button)
        
        toolbar.addSeparator()
        
        # 显示选项
        self.show_labels_button = QPushButton("显示标签")
        self.show_labels_button.setCheckable(True)
        self.show_labels_button.setChecked(True)
        self.show_labels_button.toggled.connect(self._toggle_labels)
        toolbar.addWidget(self.show_labels_button)
        
        return toolbar
    
    def _create_side_panel(self) -> QWidget:
        """创建侧边面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 网络信息
        info_group = QGroupBox("网络信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_tree = QTreeWidget()
        self.info_tree.setHeaderLabel("属性")
        info_layout.addWidget(self.info_tree)
        
        layout.addWidget(info_group)
        
        # 连接详情
        connection_group = QGroupBox("连接详情")
        connection_layout = QVBoxLayout(connection_group)
        
        self.connection_tree = QTreeWidget()
        self.connection_tree.setHeaderLabel("连接")
        connection_layout.addWidget(self.connection_tree)
        
        layout.addWidget(connection_group)
        
        return panel
    
    def _setup_layout_algorithms(self):
        """设置布局算法"""
        self.layout_algorithms = {
            "圆形布局": self._circular_layout,
            "网格布局": self._grid_layout,
            "力导向布局": self._force_directed_layout,
            "层次布局": self._hierarchical_layout
        }
    
    def show_network(self, file_info: Dict[str, Any]):
        """显示网络拓扑"""
        try:
            self.network_data = file_info
            
            # 清除现有内容
            self.graphics_scene.clear()
            self.nodes.clear()
            self.connections.clear()
            
            # 解析网络数据
            self._parse_network_data(file_info)
            
            # 创建节点和连接
            self._create_nodes()
            self._create_connections()
            
            # 应用布局
            self._apply_layout()
            
            # 更新信息面板
            self._update_info_panel()
            
            logger.info("网络拓扑显示完成")
            
        except Exception as e:
            logger.error(f"显示网络拓扑失败: {e}")
            QMessageBox.warning(self, "警告", f"显示网络拓扑失败:\n{e}")
    
    def _parse_network_data(self, file_info: Dict[str, Any]):
        """解析网络数据"""
        # 从文件信息中提取网络数据
        parsed_data = file_info.get('parsed_data')
        if not parsed_data:
            return
        
        # 提取IED信息
        self.ied_data = {}
        if hasattr(parsed_data, 'ieds'):
            for ied in parsed_data.ieds:
                ied_name = getattr(ied, 'name', f'IED_{len(self.ied_data)}')
                self.ied_data[ied_name] = {
                    'name': ied_name,
                    'type': getattr(ied, 'type', 'Unknown'),
                    'manufacturer': getattr(ied, 'manufacturer', ''),
                    'desc': getattr(ied, 'desc', ''),
                    'data': ied
                }
        
        # 提取通信信息
        self.communication_data = {}
        if hasattr(parsed_data, 'communication'):
            comm = parsed_data.communication
            if hasattr(comm, 'subnets'):
                for subnet in comm.subnets:
                    subnet_name = getattr(subnet, 'name', f'Subnet_{len(self.communication_data)}')
                    self.communication_data[subnet_name] = {
                        'name': subnet_name,
                        'type': getattr(subnet, 'type', ''),
                        'data': subnet
                    }
        
        # 提取连接关系（简化处理）
        self.connection_data = []
        # 这里可以根据实际的SCD文件结构来解析连接关系
        # 暂时创建一些示例连接
        ied_names = list(self.ied_data.keys())
        for i in range(len(ied_names) - 1):
            self.connection_data.append({
                'source': ied_names[i],
                'target': ied_names[i + 1],
                'type': 'communication'
            })
    
    def _create_nodes(self):
        """创建网络节点"""
        # 创建IED节点
        for ied_name, ied_info in self.ied_data.items():
            node = NetworkNode(ied_name, ied_info)
            
            # 根据IED类型设置不同颜色
            ied_type = ied_info.get('type', '').lower()
            if 'protection' in ied_type:
                node.setBrush(QBrush(QColor(255, 182, 193)))  # 浅红色
            elif 'control' in ied_type:
                node.setBrush(QBrush(QColor(173, 216, 230)))  # 浅蓝色
            elif 'measurement' in ied_type:
                node.setBrush(QBrush(QColor(144, 238, 144)))  # 浅绿色
            else:
                node.setBrush(QBrush(QColor(221, 221, 221)))  # 浅灰色
            
            self.nodes[ied_name] = node
            self.graphics_scene.addItem(node)
        
        # 创建通信子网节点
        for subnet_name, subnet_info in self.communication_data.items():
            node = NetworkNode(subnet_name, subnet_info, radius=20)
            node.setBrush(QBrush(QColor(255, 255, 224)))  # 浅黄色
            node.setPen(QPen(QColor(255, 140, 0), 2))
            
            self.nodes[subnet_name] = node
            self.graphics_scene.addItem(node)
    
    def _create_connections(self):
        """创建网络连接"""
        for conn_data in self.connection_data:
            source_name = conn_data['source']
            target_name = conn_data['target']
            
            if source_name in self.nodes and target_name in self.nodes:
                source_node = self.nodes[source_name]
                target_node = self.nodes[target_name]
                
                connection = NetworkConnection(source_node, target_node, conn_data)
                self.connections.append(connection)
                self.graphics_scene.addItem(connection)
    
    def _apply_layout(self):
        """应用当前选择的布局算法"""
        layout_name = self.layout_combo.currentText()
        layout_func = self.layout_algorithms.get(layout_name)
        
        if layout_func:
            layout_func()
        
        # 调整视图以适应所有项目
        self.graphics_view.fitInView(self.graphics_scene.itemsBoundingRect(), Qt.KeepAspectRatio)
    
    def _circular_layout(self):
        """圆形布局"""
        if not self.nodes:
            return
        
        center = QPointF(0, 0)
        radius = max(100, len(self.nodes) * 20)
        angle_step = 2 * math.pi / len(self.nodes)
        
        for i, node in enumerate(self.nodes.values()):
            angle = i * angle_step
            x = center.x() + radius * math.cos(angle)
            y = center.y() + radius * math.sin(angle)
            node.setPos(x, y)
    
    def _grid_layout(self):
        """网格布局"""
        if not self.nodes:
            return
        
        cols = math.ceil(math.sqrt(len(self.nodes)))
        rows = math.ceil(len(self.nodes) / cols)
        
        spacing_x = 120
        spacing_y = 100
        
        for i, node in enumerate(self.nodes.values()):
            row = i // cols
            col = i % cols
            
            x = col * spacing_x - (cols - 1) * spacing_x / 2
            y = row * spacing_y - (rows - 1) * spacing_y / 2
            
            node.setPos(x, y)
    
    def _force_directed_layout(self):
        """力导向布局（简化版）"""
        if not self.nodes:
            return
        
        # 简化的力导向算法
        iterations = 50
        k = 100  # 理想距离
        
        for _ in range(iterations):
            forces = {}
            
            # 初始化力
            for node_id in self.nodes:
                forces[node_id] = QPointF(0, 0)
            
            # 计算排斥力
            node_list = list(self.nodes.items())
            for i, (id1, node1) in enumerate(node_list):
                for id2, node2 in node_list[i+1:]:
                    pos1 = node1.pos()
                    pos2 = node2.pos()
                    
                    diff = pos1 - pos2
                    distance = math.sqrt(diff.x() ** 2 + diff.y() ** 2)
                    
                    if distance > 0:
                        force_magnitude = k * k / distance
                        force_direction = diff / distance
                        
                        forces[id1] += force_direction * force_magnitude
                        forces[id2] -= force_direction * force_magnitude
            
            # 计算吸引力（基于连接）
            for connection in self.connections:
                source_id = connection.source_node.node_id
                target_id = connection.target_node.node_id
                
                pos1 = connection.source_node.pos()
                pos2 = connection.target_node.pos()
                
                diff = pos2 - pos1
                distance = math.sqrt(diff.x() ** 2 + diff.y() ** 2)
                
                if distance > 0:
                    force_magnitude = distance * distance / k
                    force_direction = diff / distance
                    
                    forces[source_id] += force_direction * force_magnitude
                    forces[target_id] -= force_direction * force_magnitude
            
            # 应用力
            for node_id, force in forces.items():
                node = self.nodes[node_id]
                new_pos = node.pos() + force * 0.01  # 阻尼因子
                node.setPos(new_pos)
    
    def _hierarchical_layout(self):
        """层次布局"""
        # 简化的层次布局，按IED类型分层
        if not self.nodes:
            return
        
        layers = {}
        
        # 按类型分层
        for node_id, node in self.nodes.items():
            node_type = node.node_data.get('type', 'unknown').lower()
            
            if 'protection' in node_type:
                layer = 0
            elif 'control' in node_type:
                layer = 1
            elif 'measurement' in node_type:
                layer = 2
            else:
                layer = 3
            
            if layer not in layers:
                layers[layer] = []
            layers[layer].append(node)
        
        # 布局每一层
        y_spacing = 150
        x_spacing = 120
        
        for layer_index, nodes_in_layer in layers.items():
            y = layer_index * y_spacing - len(layers) * y_spacing / 2
            
            for i, node in enumerate(nodes_in_layer):
                x = i * x_spacing - (len(nodes_in_layer) - 1) * x_spacing / 2
                node.setPos(x, y)
    
    def _update_info_panel(self):
        """更新信息面板"""
        # 更新网络信息树
        self.info_tree.clear()
        
        # 添加统计信息
        stats_item = QTreeWidgetItem(self.info_tree)
        stats_item.setText(0, "网络统计")
        
        ied_count_item = QTreeWidgetItem(stats_item)
        ied_count_item.setText(0, f"IED数量: {len(self.ied_data)}")
        
        subnet_count_item = QTreeWidgetItem(stats_item)
        subnet_count_item.setText(0, f"子网数量: {len(self.communication_data)}")
        
        connection_count_item = QTreeWidgetItem(stats_item)
        connection_count_item.setText(0, f"连接数量: {len(self.connections)}")
        
        stats_item.setExpanded(True)
        
        # 更新连接详情树
        self.connection_tree.clear()
        
        for i, connection in enumerate(self.connections):
            conn_item = QTreeWidgetItem(self.connection_tree)
            source_name = connection.source_node.node_id
            target_name = connection.target_node.node_id
            conn_item.setText(0, f"{source_name} → {target_name}")
    
    def _on_layout_changed(self, layout_name: str):
        """布局改变事件"""
        self._apply_layout()
    
    def _on_zoom_changed(self, value: int):
        """缩放改变事件"""
        scale = value / 100.0
        transform = self.graphics_view.transform()
        transform.reset()
        transform.scale(scale, scale)
        self.graphics_view.setTransform(transform)
    
    def _reset_view(self):
        """重置视图"""
        self.graphics_view.fitInView(self.graphics_scene.itemsBoundingRect(), Qt.KeepAspectRatio)
        self.zoom_slider.setValue(100)
    
    def _toggle_labels(self, show: bool):
        """切换标签显示"""
        for node in self.nodes.values():
            node.text_item.setVisible(show)
