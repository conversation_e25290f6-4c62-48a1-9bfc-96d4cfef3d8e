#!/usr/bin/env python3
"""
Web界面演示测试脚本
通过API模拟用户在Web界面上的操作
"""

import requests
import json
import time
import os
from pathlib import Path

def print_banner():
    """打印演示横幅"""
    print("=" * 80)
    print("🌐 统一审查Web界面演示测试")
    print("=" * 80)
    print("📋 测试内容:")
    print("   • API接口功能验证")
    print("   • 文件上传和审查流程")
    print("   • 统一审查引擎集成")
    print("   • 用户体验模拟")
    print("=" * 80)

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:5000"
    
    print("\n🔍 测试API端点...")
    
    # 测试支持格式API
    try:
        response = requests.get(f"{base_url}/api/unified-review/formats")
        if response.status_code == 200:
            data = response.json()
            print("✅ 支持格式API正常")
            formats = data['data']['formats']
            print(f"   • 配置文件格式: {', '.join(formats['config_formats'])}")
            print(f"   • 图纸文件格式: {', '.join(formats['drawing_formats'])}")
        else:
            print(f"❌ 支持格式API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 支持格式API错误: {e}")
    
    # 测试检查分类API
    try:
        response = requests.get(f"{base_url}/api/unified-review/categories")
        if response.status_code == 200:
            data = response.json()
            print("✅ 检查分类API正常")
            categories = data['data']['categories']
            print(f"   • 配置检查分类: {', '.join(categories['config_categories'])}")
            print(f"   • 图纸检查分类: {', '.join(categories['drawing_categories'])}")
        else:
            print(f"❌ 检查分类API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 检查分类API错误: {e}")

def test_file_upload():
    """测试文件上传"""
    base_url = "http://localhost:5000"
    
    print("\n📁 测试文件上传...")
    
    # 测试文件列表
    test_files = [
        "test_project/demo_substation.scd",
        "test_project/demo_drawing.dxf"
    ]
    
    uploaded_files = []
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (os.path.basename(file_path), f)}
                response = requests.post(f"{base_url}/api/unified-review/upload", files=files)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 文件上传成功: {os.path.basename(file_path)}")
                    print(f"   • 文件类型: {data['data']['file_type']}")
                    print(f"   • 文件大小: {data['data']['file_size']} bytes")
                    uploaded_files.append(data['data']['file_path'])
                else:
                    print(f"❌ 文件上传失败: {file_path} - {response.status_code}")
                    
        except Exception as e:
            print(f"❌ 文件上传错误: {file_path} - {e}")
    
    return uploaded_files

def test_unified_review(uploaded_files):
    """测试统一审查"""
    base_url = "http://localhost:5000"
    
    print("\n🔍 测试统一审查...")
    
    if not uploaded_files:
        print("❌ 没有上传的文件可供审查")
        return
    
    for file_path in uploaded_files:
        try:
            # 构造审查请求
            review_data = {
                'file_path': file_path,
                'check_categories': ['标准符合性', '线型', '文字标注']
            }
            
            print(f"\n--- 审查文件: {os.path.basename(file_path)} ---")
            
            response = requests.post(
                f"{base_url}/api/unified-review/review",
                json=review_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                result = data['data']
                
                print("✅ 审查完成")
                print(f"   • 文件类型: {result['file_type']}")
                print(f"   • 合规性评分: {result['compliance_score']}/100")
                print(f"   • 发现问题: {len(result['issues'])} 个")
                
                # 显示问题统计
                if result['issues']:
                    severity_counts = {}
                    for issue in result['issues']:
                        severity = issue['severity']
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                    
                    print("   • 问题分布:")
                    for severity, count in severity_counts.items():
                        print(f"     - {severity}: {count} 个")
                    
                    # 显示前3个问题
                    print("   • 主要问题:")
                    for i, issue in enumerate(result['issues'][:3], 1):
                        print(f"     {i}. [{issue['severity'].upper()}] {issue['title']}")
                        if issue['suggestion']:
                            print(f"        💡 {issue['suggestion']}")
                else:
                    print("   • ✅ 未发现问题，完全符合规范")
                
            else:
                print(f"❌ 审查失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   错误信息: {error_data.get('message', '未知错误')}")
                except:
                    print(f"   响应内容: {response.text}")
                    
        except Exception as e:
            print(f"❌ 审查错误: {file_path} - {e}")

def test_batch_review():
    """测试批量审查"""
    base_url = "http://localhost:5000"
    
    print("\n📦 测试批量审查...")
    
    # 准备批量审查数据
    batch_data = {
        'files': [
            'test_project/demo_substation.scd',
            'test_project/demo_drawing.dxf'
        ],
        'check_categories': ['标准符合性', '线型', '文字标注', '设备配置']
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/unified-review/batch",
            json=batch_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            results = data['data']['results']
            
            print("✅ 批量审查完成")
            print(f"   • 审查文件数: {len(results)}")
            
            total_issues = sum(len(result['issues']) for result in results)
            avg_score = sum(result['compliance_score'] for result in results) / len(results) if results else 0
            
            print(f"   • 总问题数: {total_issues}")
            print(f"   • 平均合规性评分: {avg_score:.1f}/100")
            
            # 按来源分类统计
            config_issues = 0
            drawing_issues = 0
            
            for result in results:
                for issue in result['issues']:
                    if issue['source_type'] == 'config':
                        config_issues += 1
                    elif issue['source_type'] == 'drawing':
                        drawing_issues += 1
            
            print(f"   • 配置文件问题: {config_issues} 个")
            print(f"   • 图纸文件问题: {drawing_issues} 个")
            
        else:
            print(f"❌ 批量审查失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   错误信息: {error_data.get('message', '未知错误')}")
            except:
                print(f"   响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 批量审查错误: {e}")

def test_report_generation():
    """测试报告生成"""
    base_url = "http://localhost:5000"
    
    print("\n📄 测试报告生成...")
    
    # 先进行一次审查以获取结果
    review_data = {
        'file_path': 'test_project/demo_substation.scd',
        'check_categories': ['标准符合性']
    }
    
    try:
        # 执行审查
        response = requests.post(
            f"{base_url}/api/unified-review/review",
            json=review_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code != 200:
            print("❌ 无法获取审查结果用于报告生成")
            return
        
        review_result = response.json()['data']
        
        # 测试JSON报告生成
        report_data = {
            'review_result': review_result,
            'format': 'json'
        }
        
        response = requests.post(
            f"{base_url}/api/unified-review/report",
            json=report_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            print("✅ JSON报告生成成功")
            report_data = response.json()
            print(f"   • 报告大小: {len(str(report_data))} 字符")
        else:
            print(f"❌ JSON报告生成失败: {response.status_code}")
        
        # 测试HTML报告生成
        report_data['format'] = 'html'
        
        response = requests.post(
            f"{base_url}/api/unified-review/report",
            json=report_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            print("✅ HTML报告生成成功")
            print(f"   • 报告大小: {len(response.text)} 字符")
        else:
            print(f"❌ HTML报告生成失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 报告生成错误: {e}")

def main():
    """主函数"""
    print_banner()
    
    # 检查Web服务是否运行
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务正在运行")
        else:
            print(f"⚠️ Web服务响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到Web服务: {e}")
        print("请确保Web应用正在运行 (python run_app.py)")
        return
    
    # 执行各项测试
    test_api_endpoints()
    
    uploaded_files = test_file_upload()
    
    test_unified_review(uploaded_files)
    
    test_batch_review()
    
    test_report_generation()
    
    print("\n" + "=" * 80)
    print("🎯 Web界面演示测试总结")
    print("=" * 80)
    print("✅ 测试完成！统一审查Web界面功能正常")
    print("\n🌟 验证的功能:")
    print("   ✅ API端点响应正常")
    print("   ✅ 文件上传功能正常")
    print("   ✅ 智能文件类型识别")
    print("   ✅ 统一审查引擎集成")
    print("   ✅ 批量审查功能")
    print("   ✅ 多格式报告生成")
    print("\n💡 用户体验:")
    print("   • 一站式审查解决方案")
    print("   • 智能化的文件处理")
    print("   • 统一的问题分类和显示")
    print("   • 完整的审查流程支持")
    print("\n🚀 访问地址:")
    print("   • 统一审查页面: http://localhost:5000/unified-review")
    print("   • 使用指南: http://localhost:5000/help")
    print("   • 主页: http://localhost:5000")

if __name__ == "__main__":
    main()
