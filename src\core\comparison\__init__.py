"""
配置文件对比模块
解决智能变电站设计中版本管理和变更追踪的实际痛点

传统问题：
- 配置文件版本间的差异难以直观发现
- 手工对比XML文件容易遗漏关键变更
- 缺乏结构化的变更影响分析
- 版本升级风险评估困难

解决方案：
- 智能的配置文件结构对比
- 可视化的差异展示
- 变更影响分析和风险评估
- 专业的对比报告生成
"""

from .comparator import ConfigComparator, ComparisonResult
from .differ import StructuralDiffer, SemanticDiffer
from .analyzer import ChangeAnalyzer, ImpactAnalyzer
from .reporter import ComparisonReporter
from .visualizer import DifferenceVisualizer

__all__ = [
    'ConfigComparator',
    'ComparisonResult', 
    'StructuralDiffer',
    'SemanticDiffer',
    'ChangeAnalyzer',
    'ImpactAnalyzer',
    'ComparisonReporter',
    'DifferenceVisualizer'
]
