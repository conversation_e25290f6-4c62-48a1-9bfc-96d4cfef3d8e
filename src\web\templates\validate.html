{% extends "base.html" %}

{% block title %}验证结果 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle fa-2x text-success me-3"></i>
                    <div>
                        <h1 class="mb-1">验证结果分析</h1>
                        <p class="text-muted mb-0">智能变电站配置文件验证报告</p>
                    </div>
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>导出报告
                    </button>
                    <a href="{{ url_for('main.upload_page') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>验证新文件
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div id="loadingState" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-3 text-muted">正在加载验证结果...</p>
    </div>

    <!-- 验证结果内容 -->
    <div id="validationContent" class="d-none">
        <!-- 验证摘要 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            验证摘要
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h2 text-primary mb-1" id="executedRules">-</div>
                                    <small class="text-muted">执行规则</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h2 text-danger mb-1" id="errorCount">-</div>
                                    <small class="text-muted">错误</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="border-end">
                                    <div class="h2 text-warning mb-1" id="warningCount">-</div>
                                    <small class="text-muted">警告</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="h2 text-info mb-1" id="infoCount">-</div>
                                <small class="text-muted">信息</small>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">验证状态</h6>
                                <div id="validationStatus">
                                    <!-- 动态填充 -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted mb-3">执行信息</h6>
                                <div id="executionInfo">
                                    <!-- 动态填充 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 问题分类展示 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white">
                        <div class="d-flex align-items-center justify-content-between">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                发现的问题
                            </h5>
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="issueFilter" id="allIssues" checked>
                                <label class="btn btn-outline-primary btn-sm" for="allIssues">全部</label>
                                
                                <input type="radio" class="btn-check" name="issueFilter" id="errorsOnly">
                                <label class="btn btn-outline-danger btn-sm" for="errorsOnly">错误</label>
                                
                                <input type="radio" class="btn-check" name="issueFilter" id="warningsOnly">
                                <label class="btn btn-outline-warning btn-sm" for="warningsOnly">警告</label>
                                
                                <input type="radio" class="btn-check" name="issueFilter" id="infosOnly">
                                <label class="btn btn-outline-info btn-sm" for="infosOnly">信息</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="issuesList">
                            <!-- 动态填充问题列表 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 修复建议 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            修复建议
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="suggestions">
                            <!-- 动态填充修复建议 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细统计 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            规则执行统计
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="ruleStats">
                            <!-- 动态填充规则统计 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            文件信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="fileInfo">
                            <!-- 动态填充文件信息 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 错误状态 -->
    <div id="errorState" class="d-none">
        <div class="alert alert-danger border-0 text-center">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h5>加载失败</h5>
            <p class="mb-3">无法加载验证结果，请检查报告ID是否正确。</p>
            <a href="{{ url_for('main.upload_page') }}" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>重新验证
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentReportId = null;
let currentReportData = null;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从URL参数获取报告ID
    const urlParams = new URLSearchParams(window.location.search);
    currentReportId = urlParams.get('report_id');
    
    if (currentReportId) {
        loadValidationResult(currentReportId);
    } else {
        showError('缺少报告ID参数');
    }
    
    // 绑定过滤器事件
    bindFilterEvents();
});

// 加载验证结果
async function loadValidationResult(reportId) {
    try {
        showLoading();
        
        const response = await fetch(`/api/report/${reportId}`);
        const result = await response.json();
        
        if (result.success) {
            currentReportData = result.data;
            displayValidationResult(result.data);
            hideLoading();
        } else {
            throw new Error(result.error || '加载报告失败');
        }
        
    } catch (error) {
        console.error('加载验证结果失败:', error);
        showError(error.message);
    }
}

// 显示验证结果
function displayValidationResult(reportData) {
    // 显示摘要统计
    displaySummary(reportData);
    
    // 显示问题列表
    displayIssues(reportData);
    
    // 显示修复建议
    displaySuggestions(reportData);
    
    // 显示详细统计
    displayDetailedStats(reportData);
}

// 显示摘要统计
function displaySummary(reportData) {
    const stats = reportData.statistics;
    const execSummary = stats.execution_summary;
    const severityDist = stats.severity_distribution;
    
    // 更新数字
    document.getElementById('executedRules').textContent = execSummary.executed_rules;
    document.getElementById('errorCount').textContent = severityDist.error || 0;
    document.getElementById('warningCount').textContent = severityDist.warning || 0;
    document.getElementById('infoCount').textContent = severityDist.info || 0;
    
    // 验证状态
    const hasErrors = (severityDist.error || 0) > 0;
    const statusHtml = hasErrors ? 
        '<span class="badge bg-danger">验证未通过</span>' :
        '<span class="badge bg-success">验证通过</span>';
    document.getElementById('validationStatus').innerHTML = statusHtml;
    
    // 执行信息
    const execInfoHtml = `
        <div class="small text-muted">
            <div>执行时间: ${execSummary.total_time.toFixed(3)}s</div>
            <div>跳过规则: ${execSummary.skipped_rules}</div>
            <div>失败规则: ${execSummary.failed_rules}</div>
        </div>
    `;
    document.getElementById('executionInfo').innerHTML = execInfoHtml;
}

// 显示问题列表
function displayIssues(reportData) {
    const sections = reportData.sections || [];
    const issuesSection = sections.find(s => s.title === '发现的问题');
    
    if (!issuesSection || !issuesSection.subsections) {
        document.getElementById('issuesList').innerHTML = 
            '<div class="text-center text-muted py-4">未发现任何问题</div>';
        return;
    }
    
    let issuesHtml = '';
    
    issuesSection.subsections.forEach(subsection => {
        const severityClass = getSeverityClass(subsection.title);
        const severityIcon = getSeverityIcon(subsection.title);
        
        issuesHtml += `
            <div class="issue-category mb-4" data-severity="${subsection.title.toLowerCase()}">
                <h6 class="d-flex align-items-center mb-3">
                    <i class="fas fa-${severityIcon} ${severityClass} me-2"></i>
                    ${subsection.title}
                </h6>
                <div class="issue-content">
                    ${formatIssueContent(subsection.content)}
                </div>
            </div>
        `;
    });
    
    document.getElementById('issuesList').innerHTML = issuesHtml;
}

// 显示修复建议
function displaySuggestions(reportData) {
    const sections = reportData.sections || [];
    const suggestionsSection = sections.find(s => s.title === '修复建议');
    
    if (!suggestionsSection) {
        document.getElementById('suggestions').innerHTML = 
            '<div class="text-center text-muted py-4">暂无修复建议</div>';
        return;
    }
    
    const suggestionsHtml = formatSuggestionContent(suggestionsSection.content);
    document.getElementById('suggestions').innerHTML = suggestionsHtml;
}

// 显示详细统计
function displayDetailedStats(reportData) {
    const stats = reportData.statistics;
    
    // 规则统计
    const ruleStats = stats.rule_statistics || {};
    let ruleStatsHtml = '<div class="small">';
    
    Object.entries(ruleStats).forEach(([ruleId, stat]) => {
        const statusIcon = stat.success ? 'check-circle text-success' : 'times-circle text-danger';
        ruleStatsHtml += `
            <div class="d-flex justify-content-between align-items-center py-1">
                <span><i class="fas fa-${statusIcon} me-2"></i>${ruleId}</span>
                <span class="text-muted">${(stat.execution_time * 1000).toFixed(1)}ms</span>
            </div>
        `;
    });
    
    ruleStatsHtml += '</div>';
    document.getElementById('ruleStats').innerHTML = ruleStatsHtml;
    
    // 文件信息
    const fileInfoHtml = `
        <div class="small">
            <div class="d-flex justify-content-between py-1">
                <span>报告生成时间:</span>
                <span class="text-muted">${new Date(reportData.generated_at).toLocaleString('zh-CN')}</span>
            </div>
            <div class="d-flex justify-content-between py-1">
                <span>报告版本:</span>
                <span class="text-muted">${reportData.version}</span>
            </div>
            <div class="d-flex justify-content-between py-1">
                <span>总问题数:</span>
                <span class="text-muted">${reportData.metadata?.total_issues || 0}</span>
            </div>
        </div>
    `;
    document.getElementById('fileInfo').innerHTML = fileInfoHtml;
}

// 绑定过滤器事件
function bindFilterEvents() {
    const filterInputs = document.querySelectorAll('input[name="issueFilter"]');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            filterIssues(this.id);
        });
    });
}

// 过滤问题显示
function filterIssues(filterId) {
    const categories = document.querySelectorAll('.issue-category');
    
    categories.forEach(category => {
        const severity = category.dataset.severity;
        
        switch (filterId) {
            case 'allIssues':
                category.style.display = 'block';
                break;
            case 'errorsOnly':
                category.style.display = severity === '错误' ? 'block' : 'none';
                break;
            case 'warningsOnly':
                category.style.display = severity === '警告' ? 'block' : 'none';
                break;
            case 'infosOnly':
                category.style.display = severity === '信息' ? 'block' : 'none';
                break;
        }
    });
}

// 导出报告
function exportReport() {
    if (currentReportId) {
        window.open(`/api/report/${currentReportId}/download?format=json`, '_blank');
    }
}

// 工具函数
function getSeverityClass(severity) {
    const map = {
        '错误': 'text-danger',
        '警告': 'text-warning',
        '信息': 'text-info'
    };
    return map[severity] || 'text-muted';
}

function getSeverityIcon(severity) {
    const map = {
        '错误': 'exclamation-circle',
        '警告': 'exclamation-triangle',
        '信息': 'info-circle'
    };
    return map[severity] || 'circle';
}

function formatIssueContent(content) {
    // 简单的内容格式化
    return content.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
}

function formatSuggestionContent(content) {
    // 简单的建议格式化
    return content.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
}

function showLoading() {
    document.getElementById('loadingState').classList.remove('d-none');
    document.getElementById('validationContent').classList.add('d-none');
    document.getElementById('errorState').classList.add('d-none');
}

function hideLoading() {
    document.getElementById('loadingState').classList.add('d-none');
    document.getElementById('validationContent').classList.remove('d-none');
}

function showError(message) {
    document.getElementById('loadingState').classList.add('d-none');
    document.getElementById('validationContent').classList.add('d-none');
    document.getElementById('errorState').classList.remove('d-none');
}
</script>
{% endblock %}
