"""
变更分析器
提供变更影响分析和风险评估功能
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .comparator import Change, ChangeType, ChangeLevel, ComparisonResult


class ImpactCategory(Enum):
    """影响类别"""
    FUNCTIONAL = "functional"      # 功能影响
    PERFORMANCE = "performance"    # 性能影响
    SECURITY = "security"         # 安全影响
    COMPATIBILITY = "compatibility"  # 兼容性影响
    OPERATIONAL = "operational"    # 运维影响


@dataclass
class ImpactAssessment:
    """影响评估"""
    category: ImpactCategory
    severity: str  # high, medium, low
    description: str
    affected_components: List[str]
    mitigation_steps: List[str]


class ChangeAnalyzer:
    """
    变更分析器
    分析配置变更的具体影响
    """
    
    def __init__(self):
        self.analysis_rules = {
            'ied_changes': self._analyze_ied_changes,
            'network_changes': self._analyze_network_changes,
            'communication_changes': self._analyze_communication_changes
        }
    
    def analyze_changes(self, changes: List[Change]) -> Dict[str, Any]:
        """分析变更列表"""
        analysis = {
            'summary': self._generate_summary(changes),
            'by_category': self._categorize_changes(changes),
            'risk_factors': self._identify_risk_factors(changes),
            'recommendations': self._generate_recommendations(changes)
        }
        
        return analysis
    
    def _generate_summary(self, changes: List[Change]) -> Dict[str, Any]:
        """生成变更摘要"""
        return {
            'total_changes': len(changes),
            'critical_changes': len([c for c in changes if c.change_level == ChangeLevel.CRITICAL]),
            'major_changes': len([c for c in changes if c.change_level == ChangeLevel.MAJOR]),
            'minor_changes': len([c for c in changes if c.change_level == ChangeLevel.MINOR]),
            'by_type': {
                'added': len([c for c in changes if c.change_type == ChangeType.ADDED]),
                'removed': len([c for c in changes if c.change_type == ChangeType.REMOVED]),
                'modified': len([c for c in changes if c.change_type == ChangeType.MODIFIED])
            }
        }
    
    def _categorize_changes(self, changes: List[Change]) -> Dict[str, List[Change]]:
        """按类别分组变更"""
        categories = {
            'ied_changes': [],
            'network_changes': [],
            'communication_changes': [],
            'data_changes': [],
            'other_changes': []
        }
        
        for change in changes:
            if 'ied' in change.path.lower():
                categories['ied_changes'].append(change)
            elif any(keyword in change.path.lower() for keyword in ['subnet', 'network', 'ip']):
                categories['network_changes'].append(change)
            elif any(keyword in change.path.lower() for keyword in ['communication', 'goose', 'smv']):
                categories['communication_changes'].append(change)
            elif any(keyword in change.path.lower() for keyword in ['data', 'type', 'template']):
                categories['data_changes'].append(change)
            else:
                categories['other_changes'].append(change)
        
        return categories
    
    def _identify_risk_factors(self, changes: List[Change]) -> List[Dict[str, Any]]:
        """识别风险因素"""
        risk_factors = []
        
        # 检查关键变更
        critical_changes = [c for c in changes if c.change_level == ChangeLevel.CRITICAL]
        if critical_changes:
            risk_factors.append({
                'type': 'critical_changes',
                'severity': 'high',
                'description': f'发现{len(critical_changes)}个关键变更，可能影响系统运行',
                'count': len(critical_changes)
            })
        
        # 检查IED删除
        ied_removals = [c for c in changes if c.change_type == ChangeType.REMOVED and 'ied' in c.path.lower()]
        if ied_removals:
            risk_factors.append({
                'type': 'ied_removal',
                'severity': 'high',
                'description': f'删除了{len(ied_removals)}个IED设备，可能影响保护功能',
                'count': len(ied_removals)
            })
        
        # 检查网络配置变更
        network_changes = [c for c in changes if 'network' in c.path.lower() or 'ip' in c.path.lower()]
        if network_changes:
            risk_factors.append({
                'type': 'network_changes',
                'severity': 'medium',
                'description': f'网络配置发生{len(network_changes)}处变更，需要验证连通性',
                'count': len(network_changes)
            })
        
        return risk_factors
    
    def _generate_recommendations(self, changes: List[Change]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 收集所有建议
        all_recommendations = [c.recommendation for c in changes if c.recommendation]
        
        # 去重并排序
        unique_recommendations = list(set(all_recommendations))
        
        # 添加通用建议
        if any(c.change_level == ChangeLevel.CRITICAL for c in changes):
            recommendations.append("建议在测试环境中充分验证关键变更")
        
        if any('ied' in c.path.lower() for c in changes):
            recommendations.append("建议检查IED设备的配置完整性和网络连接")
        
        if any('network' in c.path.lower() for c in changes):
            recommendations.append("建议验证网络配置的正确性和设备连通性")
        
        return recommendations + unique_recommendations
    
    def _analyze_ied_changes(self, changes: List[Change]) -> Dict[str, Any]:
        """分析IED变更"""
        ied_changes = [c for c in changes if 'ied' in c.path.lower()]
        
        return {
            'total': len(ied_changes),
            'added': len([c for c in ied_changes if c.change_type == ChangeType.ADDED]),
            'removed': len([c for c in ied_changes if c.change_type == ChangeType.REMOVED]),
            'modified': len([c for c in ied_changes if c.change_type == ChangeType.MODIFIED])
        }
    
    def _analyze_network_changes(self, changes: List[Change]) -> Dict[str, Any]:
        """分析网络变更"""
        network_changes = [c for c in changes if any(keyword in c.path.lower() 
                                                   for keyword in ['network', 'subnet', 'ip'])]
        
        return {
            'total': len(network_changes),
            'ip_changes': len([c for c in network_changes if 'ip' in c.path.lower()]),
            'subnet_changes': len([c for c in network_changes if 'subnet' in c.path.lower()])
        }
    
    def _analyze_communication_changes(self, changes: List[Change]) -> Dict[str, Any]:
        """分析通信变更"""
        comm_changes = [c for c in changes if any(keyword in c.path.lower() 
                                                for keyword in ['communication', 'goose', 'smv'])]
        
        return {
            'total': len(comm_changes),
            'goose_changes': len([c for c in comm_changes if 'goose' in c.path.lower()]),
            'smv_changes': len([c for c in comm_changes if 'smv' in c.path.lower()])
        }


class ImpactAnalyzer:
    """
    影响分析器
    评估变更对系统的具体影响
    """
    
    def __init__(self):
        self.impact_rules = {
            'functional_impact': self._assess_functional_impact,
            'performance_impact': self._assess_performance_impact,
            'security_impact': self._assess_security_impact,
            'compatibility_impact': self._assess_compatibility_impact
        }
    
    def analyze_impact(self, changes: List[Change]) -> List[ImpactAssessment]:
        """分析变更影响"""
        assessments = []
        
        for rule_name, rule_func in self.impact_rules.items():
            try:
                assessment = rule_func(changes)
                if assessment:
                    assessments.append(assessment)
            except Exception as e:
                print(f"影响分析规则 {rule_name} 执行失败: {e}")
        
        return assessments
    
    def _assess_functional_impact(self, changes: List[Change]) -> Optional[ImpactAssessment]:
        """评估功能影响"""
        critical_changes = [c for c in changes if c.change_level == ChangeLevel.CRITICAL]
        
        if not critical_changes:
            return None
        
        return ImpactAssessment(
            category=ImpactCategory.FUNCTIONAL,
            severity='high' if len(critical_changes) > 3 else 'medium',
            description=f'发现{len(critical_changes)}个关键功能变更',
            affected_components=[c.element_name for c in critical_changes],
            mitigation_steps=[
                '在测试环境中验证功能',
                '检查相关设备的运行状态',
                '准备回滚方案'
            ]
        )
    
    def _assess_performance_impact(self, changes: List[Change]) -> Optional[ImpactAssessment]:
        """评估性能影响"""
        network_changes = [c for c in changes if 'network' in c.path.lower()]
        
        if not network_changes:
            return None
        
        return ImpactAssessment(
            category=ImpactCategory.PERFORMANCE,
            severity='medium',
            description=f'网络配置变更可能影响通信性能',
            affected_components=['网络通信'],
            mitigation_steps=[
                '监控网络延迟',
                '检查带宽使用情况',
                '验证通信质量'
            ]
        )
    
    def _assess_security_impact(self, changes: List[Change]) -> Optional[ImpactAssessment]:
        """评估安全影响"""
        # 简化实现
        return None
    
    def _assess_compatibility_impact(self, changes: List[Change]) -> Optional[ImpactAssessment]:
        """评估兼容性影响"""
        version_changes = [c for c in changes if 'version' in c.path.lower()]
        
        if not version_changes:
            return None
        
        return ImpactAssessment(
            category=ImpactCategory.COMPATIBILITY,
            severity='medium',
            description='版本变更可能影响兼容性',
            affected_components=['系统版本'],
            mitigation_steps=[
                '检查版本兼容性要求',
                '验证新版本功能',
                '测试向后兼容性'
            ]
        )
