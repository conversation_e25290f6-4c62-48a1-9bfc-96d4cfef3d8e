#!/usr/bin/env python3
"""
全面的二次回路逻辑审查系统
对所有回路类型进行深度技术分析和错误检查

审查范围：
1. 控制回路逻辑
2. 保护回路逻辑  
3. 测量回路逻辑
4. 信号回路逻辑
5. 直流电源回路逻辑
6. 通信网络逻辑
7. 设备配置逻辑
8. 安全要求符合性
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class ComprehensiveCircuitAudit:
    """全面的回路逻辑审查器"""
    
    def __init__(self):
        # 各类回路的权威技术要求
        self.circuit_standards = {
            '控制回路': {
                '基本原则': [
                    '控制回路应有明确的操作逻辑',
                    '合闸和分闸应有独立的控制回路',
                    '应有防误操作措施',
                    '远方和就地控制应有明确的切换逻辑'
                ],
                '安全要求': [
                    '控制电源应独立可靠',
                    '控制回路应有断线监视',
                    '应有操作闭锁功能',
                    '紧急情况下应能就地操作'
                ],
                '常见错误': [
                    '合闸分闸回路混用',
                    '缺少防误操作逻辑',
                    '远方就地切换不明确',
                    '控制电源配置错误'
                ]
            },
            '保护回路': {
                '基本原则': [
                    '主保护和后备保护应独立配置',
                    '保护装置应有独立的跳闸出口',
                    '保护用CT应专用或有专用绕组',
                    '保护电源应可靠独立'
                ],
                '双重化要求': [
                    '两套保护装置应完全独立',
                    '各有独立的CT/PT输入',
                    '各有独立的跳闸回路',
                    '各有独立的直流电源'
                ],
                '常见错误': [
                    '保护装置通过GOOSE跳闸',
                    '双重化保护共用CT绕组',
                    '保护装置共用跳闸回路',
                    '保护电源配置不独立'
                ]
            },
            '测量回路': {
                '基本原则': [
                    '测量用CT/PT应有专用绕组',
                    '测量精度应满足要求',
                    '测量回路应完整可靠',
                    '重要测量应有备用'
                ],
                '精度要求': [
                    '计量用CT/PT: 0.2级或0.2S级',
                    '测量用CT/PT: 0.5级',
                    '保护用CT: 5P或10P级',
                    '合并单元采样: 0.2级'
                ],
                '常见错误': [
                    '测量和保护共用CT绕组',
                    '测量精度等级配置错误',
                    'CT二次负荷计算错误',
                    '测量回路接线错误'
                ]
            },
            '信号回路': {
                '基本原则': [
                    '信号应分类明确',
                    '重要信号应有音响',
                    '信号电源应可靠',
                    '信号传输应快速准确'
                ],
                '分类要求': [
                    '位置信号：反映设备状态',
                    '保护信号：反映保护动作',
                    '预告信号：设备异常预警',
                    '故障信号：设备故障告警'
                ],
                '常见错误': [
                    '信号分类不明确',
                    '重要信号无音响',
                    '信号电源配置错误',
                    '信号逻辑设计错误'
                ]
            },
            '直流电源回路': {
                '基本原则': [
                    '应有双套直流电源系统',
                    '重要负荷应双电源供电',
                    '应有绝缘监测装置',
                    '蓄电池容量应满足要求'
                ],
                '配置要求': [
                    'DC 220V: 控制和保护',
                    'DC 110V: 信号和照明',
                    'DC 48V: 通信设备',
                    'DC 24V: 智能设备'
                ],
                '常见错误': [
                    '直流系统配置不合理',
                    '负荷分配不均衡',
                    '绝缘监测配置缺失',
                    '蓄电池容量计算错误'
                ]
            }
        }
        
        # 设备配置审查标准
        self.device_standards = {
            '合并单元': {
                '功能定位': 'A/D转换，输出SV数据',
                '不应承担的功能': ['保护逻辑', '控制逻辑', '信号处理'],
                '技术要求': ['4000Hz采样', 'IEEE 1588同步', '0.2级精度'],
                '常见错误': ['功能定位错误', '精度等级错误', '同步要求不明确']
            },
            '智能终端': {
                '功能定位': '接收GOOSE，控制一次设备，采集状态',
                '应有功能': ['GOOSE接收', '硬接点输出', '状态采集', '就地控制'],
                '技术要求': ['≤4ms响应', '光电隔离', '双电源'],
                '常见错误': ['功能理解错误', '接口配置错误', '响应时间不满足']
            },
            '保护装置': {
                '功能定位': '故障检测，保护逻辑，跳闸输出',
                '必须功能': ['硬接线跳闸', 'SV输入', 'GOOSE通信', '自诊断'],
                '技术要求': ['双重化配置', '独立电源', '独立CT/PT'],
                '常见错误': ['跳闸逻辑错误', '双重化不独立', '电源配置错误']
            }
        }
    
    def audit_control_circuit_logic(self) -> Dict:
        """审查控制回路逻辑"""
        
        audit_result = {
            'circuit_type': '控制回路',
            'audit_time': datetime.now().isoformat(),
            'identified_errors': [],
            'compliance_issues': [],
            'recommendations': []
        }
        
        # 检查控制回路常见错误
        control_errors = [
            {
                'error_type': '合闸分闸回路设计错误',
                'description': '合闸和分闸可能共用了控制回路或电源',
                'severity': '严重',
                'standard_violation': '违反控制回路独立性要求',
                'correct_design': '合闸回路和分闸回路应完全独立，各有独立的控制电源和操作回路'
            },
            {
                'error_type': '防误操作逻辑缺失',
                'description': '可能缺少必要的防误操作闭锁逻辑',
                'severity': '重要',
                'standard_violation': '不符合防误操作安全要求',
                'correct_design': '应有完整的五防逻辑：防误分、防误合、防误入、防误碰、防误操作'
            },
            {
                'error_type': '远方就地切换逻辑不明确',
                'description': '远方控制和就地控制的切换逻辑可能不清晰',
                'severity': '重要',
                'standard_violation': '不符合操作安全要求',
                'correct_design': '应有明确的远方/就地切换开关，且切换逻辑应清晰可靠'
            }
        ]
        
        audit_result['identified_errors'].extend(control_errors)
        return audit_result
    
    def audit_measurement_circuit_logic(self) -> Dict:
        """审查测量回路逻辑"""
        
        audit_result = {
            'circuit_type': '测量回路',
            'audit_time': datetime.now().isoformat(),
            'identified_errors': [],
            'compliance_issues': [],
            'recommendations': []
        }
        
        # 检查测量回路常见错误
        measurement_errors = [
            {
                'error_type': 'CT/PT绕组配置错误',
                'description': '测量、保护、计量可能共用CT/PT绕组',
                'severity': '严重',
                'standard_violation': '违反CT/PT绕组独立性要求',
                'correct_design': '测量、保护、计量应使用不同的CT/PT绕组，或至少保护用CT应独立'
            },
            {
                'error_type': '精度等级配置错误',
                'description': 'CT/PT精度等级可能不满足应用要求',
                'severity': '重要',
                'standard_violation': '不符合测量精度要求',
                'correct_design': '计量0.2级，测量0.5级，保护5P/10P级，应根据用途正确选择'
            },
            {
                'error_type': 'CT二次负荷计算错误',
                'description': 'CT二次回路负荷可能超出额定容量',
                'severity': '重要',
                'standard_violation': '影响测量精度和保护可靠性',
                'correct_design': 'CT二次负荷应在额定容量范围内，并留有适当裕度'
            },
            {
                'error_type': '合并单元配置错误',
                'description': '合并单元的功能定位和技术参数可能错误',
                'severity': '重要',
                'standard_violation': '不符合数字化变电站要求',
                'correct_design': '合并单元仅做A/D转换，4000Hz采样，0.2级精度，IEEE 1588同步'
            }
        ]
        
        audit_result['identified_errors'].extend(measurement_errors)
        return audit_result
    
    def audit_signal_circuit_logic(self) -> Dict:
        """审查信号回路逻辑"""
        
        audit_result = {
            'circuit_type': '信号回路',
            'audit_time': datetime.now().isoformat(),
            'identified_errors': [],
            'compliance_issues': [],
            'recommendations': []
        }
        
        # 检查信号回路常见错误
        signal_errors = [
            {
                'error_type': '信号分类逻辑错误',
                'description': '位置信号、保护信号、预告信号、故障信号分类可能不明确',
                'severity': '重要',
                'standard_violation': '不符合信号系统设计要求',
                'correct_design': '应明确区分四类信号：位置、保护、预告、故障，各有不同的处理逻辑'
            },
            {
                'error_type': '音响信号配置错误',
                'description': '重要信号可能缺少音响告警',
                'severity': '重要',
                'standard_violation': '不符合运行安全要求',
                'correct_design': '保护动作、设备故障等重要信号应配置音响告警'
            },
            {
                'error_type': '信号电源配置错误',
                'description': '信号回路电源可能与控制电源混用',
                'severity': '一般',
                'standard_violation': '影响信号可靠性',
                'correct_design': '信号电源应相对独立，重要信号应有备用电源'
            }
        ]
        
        audit_result['identified_errors'].extend(signal_errors)
        return audit_result
    
    def audit_dc_power_circuit_logic(self) -> Dict:
        """审查直流电源回路逻辑"""
        
        audit_result = {
            'circuit_type': '直流电源回路',
            'audit_time': datetime.now().isoformat(),
            'identified_errors': [],
            'compliance_issues': [],
            'recommendations': []
        }
        
        # 检查直流电源回路常见错误
        dc_power_errors = [
            {
                'error_type': '直流系统配置错误',
                'description': '可能缺少双套直流系统或系统间独立性不足',
                'severity': '严重',
                'standard_violation': '不符合直流系统可靠性要求',
                'correct_design': '应配置双套独立的直流系统，重要负荷双电源供电'
            },
            {
                'error_type': '电压等级配置错误',
                'description': '不同用途的负荷可能使用了错误的电压等级',
                'severity': '重要',
                'standard_violation': '不符合电压等级标准化要求',
                'correct_design': 'DC 220V控制保护，DC 110V信号照明，DC 48V通信，DC 24V智能设备'
            },
            {
                'error_type': '绝缘监测配置缺失',
                'description': '可能缺少直流系统绝缘监测装置',
                'severity': '重要',
                'standard_violation': '不符合直流系统安全要求',
                'correct_design': '应配置绝缘监测装置，实时监测正负极对地绝缘'
            }
        ]
        
        audit_result['identified_errors'].extend(dc_power_errors)
        return audit_result
    
    def audit_communication_logic(self) -> Dict:
        """审查通信网络逻辑"""
        
        audit_result = {
            'circuit_type': '通信网络',
            'audit_time': datetime.now().isoformat(),
            'identified_errors': [],
            'compliance_issues': [],
            'recommendations': []
        }
        
        # 检查通信网络常见错误
        comm_errors = [
            {
                'error_type': 'GOOSE应用边界错误',
                'description': 'GOOSE可能被错误地用作主要控制手段',
                'severity': '严重',
                'standard_violation': '违反GOOSE应用原则',
                'correct_design': 'GOOSE仅用于信息交换、联跳闭锁，不应作为唯一的控制手段'
            },
            {
                'error_type': 'SV网络配置错误',
                'description': 'SV网络的性能参数或冗余配置可能不正确',
                'severity': '重要',
                'standard_violation': '不符合SV传输要求',
                'correct_design': 'SV网络应满足4000Hz采样，≤3ms传输延时，双网冗余'
            },
            {
                'error_type': '网络安全考虑不足',
                'description': '可能缺少必要的网络安全防护措施',
                'severity': '重要',
                'standard_violation': '不符合网络安全要求',
                'correct_design': '应有网络隔离、访问控制、入侵检测等安全措施'
            }
        ]
        
        audit_result['identified_errors'].extend(comm_errors)
        return audit_result


def main():
    """主函数"""
    
    print("🔍 全面的二次回路逻辑审查系统")
    print("=" * 80)
    print("对所有回路类型进行深度技术分析")
    print("=" * 80)
    
    # 创建审查器
    auditor = ComprehensiveCircuitAudit()
    
    # 输出目录
    output_dir = "design_reports/circuit_audit"
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print("\n🔍 开始全面回路逻辑审查...")
    
    # 执行各类回路审查
    audit_results = {}
    
    print("   📋 审查控制回路逻辑...")
    audit_results['control'] = auditor.audit_control_circuit_logic()
    
    print("   📊 审查测量回路逻辑...")
    audit_results['measurement'] = auditor.audit_measurement_circuit_logic()
    
    print("   💡 审查信号回路逻辑...")
    audit_results['signal'] = auditor.audit_signal_circuit_logic()
    
    print("   🔋 审查直流电源回路逻辑...")
    audit_results['dc_power'] = auditor.audit_dc_power_circuit_logic()
    
    print("   📡 审查通信网络逻辑...")
    audit_results['communication'] = auditor.audit_communication_logic()
    
    # 汇总审查结果
    summary_report = {
        'audit_time': datetime.now().isoformat(),
        'audit_scope': '全面二次回路逻辑审查',
        'circuits_audited': list(audit_results.keys()),
        'total_errors_found': sum(len(result['identified_errors']) for result in audit_results.values()),
        'severity_distribution': {},
        'detailed_results': audit_results
    }
    
    # 统计错误严重程度
    severity_count = {'严重': 0, '重要': 0, '一般': 0}
    for result in audit_results.values():
        for error in result['identified_errors']:
            severity = error.get('severity', '一般')
            severity_count[severity] += 1
    
    summary_report['severity_distribution'] = severity_count
    
    # 保存审查结果
    summary_file = output_path / "comprehensive_circuit_audit.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 全面审查报告已保存: {summary_file}")
    
    print(f"\n📊 审查结果统计:")
    print(f"   🔍 审查回路类型: {len(audit_results)} 类")
    print(f"   🚨 发现错误总数: {summary_report['total_errors_found']} 个")
    print(f"   📈 错误严重程度分布:")
    for severity, count in severity_count.items():
        print(f"      • {severity}: {count} 个")
    
    print(f"\n⚠️ 主要发现的错误类型:")
    for circuit_type, result in audit_results.items():
        print(f"   📋 {result['circuit_type']}:")
        for error in result['identified_errors']:
            print(f"      🚨 {error['error_type']} ({error['severity']})")
    
    print(f"\n💡 建议:")
    print("   1. 立即修正所有严重错误")
    print("   2. 逐步完善重要问题")
    print("   3. 建立技术审查机制")
    print("   4. 加强标准学习培训")


if __name__ == "__main__":
    main()
