"""
标准文档提取器
从标准文档中提取知识和规则
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from ..base.knowledge_entity import (
    StandardEntity, RuleEntity, RequirementEntity, EntityType,
    ConfidenceLevel, RelationshipType, EntityRelationship
)
from .text_analyzer import TextAnalyzer


logger = logging.getLogger(__name__)


class StandardExtractor:
    """标准文档提取器"""
    
    def __init__(self):
        """初始化提取器"""
        self.text_analyzer = TextAnalyzer()
        
        # 标准编号模式
        self.standard_patterns = {
            'gb_national': r'GB/T?\s*(\d+(?:\.\d+)*)-(\d{4})',
            'dl_industry': r'DL/T?\s*(\d+(?:\.\d+)*)-(\d{4})',
            'iec_international': r'IEC\s*(\d+(?:-\d+)*):?(\d{4})?',
            'ieee': r'IEEE\s*(\d+(?:\.\d+)*)-(\d{4})',
            'qgdw_enterprise': r'Q/GDW\s*(\d+)-(\d{4})'
        }
        
        # 规则关键词
        self.rule_keywords = [
            '应当', '必须', '应该', '不得', '禁止', '要求', '规定',
            'shall', 'must', 'should', 'may', 'requirement', 'mandatory'
        ]
        
        # 技术要求关键词
        self.requirement_keywords = [
            '技术要求', '技术规范', '性能指标', '验收标准', '测试方法',
            'technical requirement', 'specification', 'performance', 'test'
        ]
        
        logger.info("标准文档提取器初始化完成")
    
    def extract_from_text(self, text: str, source: str = "") -> Dict[str, List[Any]]:
        """
        从文本中提取知识实体
        
        Args:
            text: 文档文本
            source: 文档来源
            
        Returns:
            Dict: 提取的实体字典
        """
        try:
            entities = {
                'standards': [],
                'rules': [],
                'requirements': [],
                'relationships': []
            }
            
            # 提取标准信息
            standards = self._extract_standards(text, source)
            entities['standards'].extend(standards)
            
            # 提取规则
            rules = self._extract_rules(text, source)
            entities['rules'].extend(rules)
            
            # 提取技术要求
            requirements = self._extract_requirements(text, source)
            entities['requirements'].extend(requirements)
            
            # 建立关系
            relationships = self._extract_relationships(entities)
            entities['relationships'].extend(relationships)
            
            logger.info(f"从文档提取实体: 标准{len(standards)}, 规则{len(rules)}, 要求{len(requirements)}")
            return entities
            
        except Exception as e:
            logger.error(f"文本提取失败: {e}")
            return {'standards': [], 'rules': [], 'requirements': [], 'relationships': []}
    
    def _extract_standards(self, text: str, source: str) -> List[StandardEntity]:
        """提取标准信息"""
        standards = []
        
        for pattern_name, pattern in self.standard_patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)
            
            for match in matches:
                try:
                    if pattern_name == 'iec_international':
                        number = match.group(1)
                        year = match.group(2) if match.group(2) else ""
                        standard_number = f"IEC {number}" + (f":{year}" if year else "")
                    else:
                        number = match.group(1)
                        year = match.group(2)
                        prefix = pattern_name.split('_')[0].upper()
                        standard_number = f"{prefix}/T {number}-{year}"
                    
                    # 提取标准标题（在标准编号附近的文本）
                    start = max(0, match.start() - 100)
                    end = min(len(text), match.end() + 200)
                    context = text[start:end]
                    
                    title = self._extract_standard_title(context, standard_number)
                    
                    standard = StandardEntity(
                        standard_number=standard_number,
                        standard_title=title,
                        name=title or standard_number,
                        description=f"标准文档: {standard_number}",
                        content=context,
                        source=source,
                        issuing_organization=self._get_issuing_org(pattern_name),
                        category="electrical",
                        domain="substation",
                        scope="智能变电站设计和运行",
                        confidence=0.9
                    )
                    
                    standards.append(standard)
                    
                except Exception as e:
                    logger.warning(f"提取标准信息失败: {e}")
                    continue
        
        return standards
    
    def _extract_rules(self, text: str, source: str) -> List[RuleEntity]:
        """提取验证规则"""
        rules = []
        
        # 按段落分割文本
        paragraphs = text.split('\n\n')
        
        for i, paragraph in enumerate(paragraphs):
            if not paragraph.strip():
                continue
            
            # 检查是否包含规则关键词
            has_rule_keyword = any(keyword in paragraph for keyword in self.rule_keywords)
            
            if has_rule_keyword:
                try:
                    # 提取规则内容
                    rule_content = paragraph.strip()
                    
                    # 确定规则类型和严重程度
                    rule_type, severity = self._classify_rule(rule_content)
                    
                    # 生成规则名称
                    rule_name = self._generate_rule_name(rule_content)
                    
                    # 提取条件和动作
                    condition, action = self._extract_condition_action(rule_content)
                    
                    rule = RuleEntity(
                        name=rule_name,
                        description=rule_content[:200] + "..." if len(rule_content) > 200 else rule_content,
                        content=rule_content,
                        source=source,
                        rule_type=rule_type,
                        severity=severity,
                        category="standard_compliance",
                        condition=condition,
                        action=action,
                        message_template=f"违反规则 {rule_name}: {{details}}",
                        confidence=0.8
                    )
                    
                    rules.append(rule)
                    
                except Exception as e:
                    logger.warning(f"提取规则失败: {e}")
                    continue
        
        return rules
    
    def _extract_requirements(self, text: str, source: str) -> List[RequirementEntity]:
        """提取技术要求"""
        requirements = []
        
        # 查找技术要求章节
        requirement_sections = []
        for keyword in self.requirement_keywords:
            pattern = rf'({keyword}[^。]*。[^。]*。?)'
            matches = re.finditer(pattern, text, re.IGNORECASE)
            requirement_sections.extend([match.group(1) for match in matches])
        
        for i, req_text in enumerate(requirement_sections):
            try:
                # 生成要求名称
                req_name = self._generate_requirement_name(req_text)
                
                # 确定要求类型和优先级
                req_type, priority, mandatory = self._classify_requirement(req_text)
                
                requirement = RequirementEntity(
                    name=req_name,
                    description=req_text[:200] + "..." if len(req_text) > 200 else req_text,
                    content=req_text,
                    source=source,
                    requirement_type=req_type,
                    priority=priority,
                    mandatory=mandatory,
                    verification_method="文档审查",
                    acceptance_criteria="符合标准要求",
                    source_standard=source,
                    clause_reference=f"第{i+1}项要求",
                    confidence=0.7
                )
                
                requirements.append(requirement)
                
            except Exception as e:
                logger.warning(f"提取技术要求失败: {e}")
                continue
        
        return requirements
    
    def _extract_relationships(self, entities: Dict[str, List[Any]]) -> List[EntityRelationship]:
        """提取实体关系"""
        relationships = []
        
        try:
            # 标准与规则的关系
            for standard in entities['standards']:
                for rule in entities['rules']:
                    if self._is_related(standard.content, rule.content):
                        rel = EntityRelationship(
                            source_entity_id=standard.id,
                            target_entity_id=rule.id,
                            relationship_type=RelationshipType.DERIVED_FROM,
                            strength=0.8,
                            confidence=0.7
                        )
                        relationships.append(rel)
            
            # 标准与要求的关系
            for standard in entities['standards']:
                for requirement in entities['requirements']:
                    if self._is_related(standard.content, requirement.content):
                        rel = EntityRelationship(
                            source_entity_id=standard.id,
                            target_entity_id=requirement.id,
                            relationship_type=RelationshipType.REFERENCES,
                            strength=0.7,
                            confidence=0.6
                        )
                        relationships.append(rel)
            
        except Exception as e:
            logger.warning(f"提取关系失败: {e}")
        
        return relationships
    
    def _extract_standard_title(self, context: str, standard_number: str) -> str:
        """提取标准标题"""
        # 查找标准编号后的标题
        lines = context.split('\n')
        for line in lines:
            if standard_number in line:
                # 移除标准编号，剩余部分可能是标题
                title = line.replace(standard_number, '').strip()
                title = re.sub(r'^[：:：\-\s]+', '', title)
                if title and len(title) > 5:
                    return title[:100]  # 限制标题长度
        
        return ""
    
    def _get_issuing_org(self, pattern_name: str) -> str:
        """获取发布机构"""
        org_map = {
            'gb_national': '国家标准化管理委员会',
            'dl_industry': '国家能源局',
            'iec_international': 'International Electrotechnical Commission',
            'ieee': 'Institute of Electrical and Electronics Engineers',
            'qgdw_enterprise': '国家电网公司'
        }
        return org_map.get(pattern_name, '未知机构')
    
    def _classify_rule(self, rule_content: str) -> Tuple[str, str]:
        """分类规则类型和严重程度"""
        rule_type = "business"
        severity = "warning"
        
        # 根据关键词判断严重程度
        if any(word in rule_content for word in ['必须', '禁止', 'must', 'shall not']):
            severity = "error"
        elif any(word in rule_content for word in ['应当', 'should']):
            severity = "warning"
        else:
            severity = "info"
        
        # 根据内容判断规则类型
        if any(word in rule_content for word in ['语法', '格式', 'syntax', 'format']):
            rule_type = "syntax"
        elif any(word in rule_content for word in ['语义', '逻辑', 'semantic', 'logic']):
            rule_type = "semantic"
        
        return rule_type, severity
    
    def _generate_rule_name(self, rule_content: str) -> str:
        """生成规则名称"""
        # 提取前20个字符作为规则名称
        name = rule_content[:20].strip()
        name = re.sub(r'[^\w\s\u4e00-\u9fff]', '', name)  # 移除特殊字符
        return name if name else "未命名规则"
    
    def _extract_condition_action(self, rule_content: str) -> Tuple[str, str]:
        """提取规则条件和动作"""
        # 简单的条件动作提取
        if '如果' in rule_content or 'if' in rule_content.lower():
            parts = re.split(r'[，,]?则|[，,]?那么|[，,]?应当|[，,]?必须', rule_content, 1)
            if len(parts) == 2:
                return parts[0].strip(), parts[1].strip()
        
        return rule_content, "检查合规性"
    
    def _classify_requirement(self, req_text: str) -> Tuple[str, str, bool]:
        """分类技术要求"""
        req_type = "functional"
        priority = "medium"
        mandatory = True
        
        # 判断要求类型
        if any(word in req_text for word in ['性能', 'performance']):
            req_type = "performance"
        elif any(word in req_text for word in ['安全', 'safety']):
            req_type = "safety"
        elif any(word in req_text for word in ['接口', 'interface']):
            req_type = "interface"
        
        # 判断优先级
        if any(word in req_text for word in ['关键', '重要', 'critical', 'important']):
            priority = "high"
        elif any(word in req_text for word in ['可选', 'optional']):
            priority = "low"
            mandatory = False
        
        return req_type, priority, mandatory
    
    def _generate_requirement_name(self, req_text: str) -> str:
        """生成技术要求名称"""
        # 提取前30个字符作为要求名称
        name = req_text[:30].strip()
        name = re.sub(r'[^\w\s\u4e00-\u9fff]', '', name)
        return name if name else "未命名要求"
    
    def _is_related(self, text1: str, text2: str) -> bool:
        """判断两个文本是否相关"""
        # 简单的相关性判断：共同关键词数量
        words1 = set(re.findall(r'\w+', text1.lower()))
        words2 = set(re.findall(r'\w+', text2.lower()))
        
        common_words = words1.intersection(words2)
        return len(common_words) >= 3  # 至少3个共同词汇
