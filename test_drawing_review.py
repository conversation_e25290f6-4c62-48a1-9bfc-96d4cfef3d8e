#!/usr/bin/env python3
"""
图纸审查功能测试脚本
测试图纸解析、分析和规范检查功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.drawing_review.review_engine import DrawingReviewEngine
from src.drawing_review.drawing_parser import DrawingParserFactory
from src.drawing_review.standards_knowledge import StandardsKnowledgeBase
from src.drawing_review.visualization import DrawingVisualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_parser_factory():
    """测试解析器工厂"""
    print("\n=== 测试解析器工厂 ===")
    
    factory = DrawingParserFactory()
    
    # 获取支持的格式
    formats = factory.get_supported_formats()
    print(f"支持的格式: {formats}")
    
    # 测试解析器获取
    test_files = [
        "test.dxf",
        "test.dwg", 
        "test.pdf",
        "test.txt"
    ]
    
    for file_path in test_files:
        parser = factory.get_parser(file_path)
        if parser:
            print(f"✓ {file_path}: {parser.__class__.__name__}")
        else:
            print(f"✗ {file_path}: 不支持的格式")


def test_standards_knowledge():
    """测试标准知识库"""
    print("\n=== 测试标准知识库 ===")
    
    kb = StandardsKnowledgeBase()
    
    # 获取规则摘要
    summary = kb.get_rules_summary()
    print(f"规则摘要: {summary}")
    
    # 测试文字高度验证
    test_heights = [1.0, 2.5, 3.5, 5.0, 15.0]
    for height in test_heights:
        valid, message = kb.validate_text_height(height)
        status = "✓" if valid else "✗"
        print(f"{status} 文字高度 {height}mm: {message}")
    
    # 测试字体验证
    test_fonts = ["宋体", "Arial", "Comic Sans", "黑体"]
    for font in test_fonts:
        valid, message = kb.validate_font(font)
        status = "✓" if valid else "✗"
        print(f"{status} 字体 {font}: {message}")


def test_review_engine():
    """测试审查引擎"""
    print("\n=== 测试审查引擎 ===")
    
    engine = DrawingReviewEngine()
    
    # 获取引擎统计信息
    stats = engine.get_engine_statistics()
    print(f"引擎统计: {stats}")
    
    # 获取检查分类
    categories = engine.get_available_check_categories()
    print(f"检查分类: {categories}")
    
    # 获取标准摘要
    standards = engine.get_standards_summary()
    print(f"标准摘要: {standards}")


def test_visualization():
    """测试可视化功能"""
    print("\n=== 测试可视化功能 ===")
    
    visualizer = DrawingVisualizer()
    
    # 测试空的可视化生成
    try:
        # 创建一个模拟的审查结果
        from src.drawing_review.drawing_models import DrawingDocument, DrawingReviewResult
        from datetime import datetime
        
        # 创建空的图纸文档
        drawing_doc = DrawingDocument(
            file_path="test.dxf",
            file_type="dxf"
        )
        
        # 创建审查结果
        review_result = DrawingReviewResult(
            drawing_document=drawing_doc,
            review_date=datetime.now()
        )
        
        # 生成可视化
        svg_content = visualizer.generate_review_visualization(review_result)
        if svg_content:
            print("✓ SVG可视化生成成功")
        else:
            print("✗ SVG可视化生成失败")
        
        # 生成交互式查看器
        html_content = visualizer.generate_interactive_viewer(review_result)
        if html_content:
            print("✓ 交互式查看器生成成功")
        else:
            print("✗ 交互式查看器生成失败")
            
    except Exception as e:
        print(f"✗ 可视化测试失败: {e}")


def create_test_drawing():
    """创建测试图纸数据"""
    print("\n=== 创建测试图纸数据 ===")
    
    try:
        from src.drawing_review.drawing_models import (
            DrawingDocument, Line, Circle, Text, Point, Color, LineType
        )
        
        # 创建图纸文档
        drawing = DrawingDocument(
            file_path="test_drawing.dxf",
            file_type="dxf",
            title="测试图纸",
            units="mm"
        )
        
        # 添加一些测试图元
        # 直线
        line1 = Line(
            start_point=Point(0, 0),
            end_point=Point(100, 0),
            layer="OUTLINE",
            color=Color(r=255, g=255, b=255),
            line_type=LineType.CONTINUOUS
        )
        drawing.elements.append(line1)
        
        # 圆
        circle1 = Circle(
            center=Point(50, 50),
            radius=25,
            layer="OUTLINE",
            color=Color(r=255, g=255, b=255),
            line_type=LineType.CONTINUOUS
        )
        drawing.elements.append(circle1)
        
        # 文字
        text1 = Text(
            content="测试文字",
            position=Point(10, 10),
            height=3.5,
            layer="TEXT",
            color=Color(r=255, g=255, b=0)
        )
        drawing.elements.append(text1)
        
        print(f"✓ 创建测试图纸，包含 {len(drawing.elements)} 个图元")
        
        # 测试审查
        engine = DrawingReviewEngine()
        review_result = engine.review_drawing("test_drawing.dxf")
        
        if review_result:
            print(f"✓ 图纸审查完成，发现 {len(review_result.issues)} 个问题")
            print(f"  合规性评分: {review_result.compliance_score:.1f}")
            
            # 显示问题详情
            for issue in review_result.issues[:5]:  # 只显示前5个问题
                print(f"  - {issue.severity.value}: {issue.title}")
        else:
            print("✗ 图纸审查失败")
            
    except Exception as e:
        print(f"✗ 测试图纸创建失败: {e}")


def test_api_integration():
    """测试API集成"""
    print("\n=== 测试API集成 ===")
    
    try:
        # 测试Flask应用创建
        from src.web.app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试图纸审查相关的API端点
            
            # 获取支持的格式
            response = client.get('/api/drawing-review/formats')
            if response.status_code == 200:
                print("✓ 获取支持格式API正常")
            else:
                print(f"✗ 获取支持格式API失败: {response.status_code}")
            
            # 获取检查分类
            response = client.get('/api/drawing-review/categories')
            if response.status_code == 200:
                print("✓ 获取检查分类API正常")
            else:
                print(f"✗ 获取检查分类API失败: {response.status_code}")
            
            # 获取引擎统计
            response = client.get('/api/drawing-review/engine-stats')
            if response.status_code == 200:
                print("✓ 获取引擎统计API正常")
            else:
                print(f"✗ 获取引擎统计API失败: {response.status_code}")
                
    except Exception as e:
        print(f"✗ API集成测试失败: {e}")


def main():
    """主测试函数"""
    print("开始图纸审查功能测试...")
    
    try:
        # 运行各项测试
        test_parser_factory()
        test_standards_knowledge()
        test_review_engine()
        test_visualization()
        create_test_drawing()
        test_api_integration()
        
        print("\n=== 测试完成 ===")
        print("图纸审查功能基本测试通过！")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
