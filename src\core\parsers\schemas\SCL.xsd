<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.iec.ch/61850/2003/SCL"
           xmlns:scl="http://www.iec.ch/61850/2003/SCL"
           elementFormDefault="qualified">
    
    <!-- 根元素定义 -->
    <xs:element name="SCL" type="scl:tSCL"/>
    
    <!-- SCL根类型 -->
    <xs:complexType name="tSCL">
        <xs:sequence>
            <xs:element name="Header" type="scl:tHeader"/>
            <xs:element name="Substation" type="scl:tSubstation" minOccurs="0"/>
            <xs:element name="Communication" type="scl:tCommunication" minOccurs="0"/>
            <xs:element name="IED" type="scl:tIED" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="DataTypeTemplates" type="scl:tDataTypeTemplates" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="version" type="xs:string" use="required"/>
        <xs:attribute name="revision" type="xs:string"/>
    </xs:complexType>
    
    <!-- Header类型 -->
    <xs:complexType name="tHeader">
        <xs:attribute name="id" type="xs:string" use="required"/>
        <xs:attribute name="version" type="xs:string"/>
        <xs:attribute name="revision" type="xs:string"/>
        <xs:attribute name="toolID" type="xs:string"/>
        <xs:attribute name="nameStructure" type="xs:string"/>
    </xs:complexType>
    
    <!-- 基础命名类型 -->
    <xs:complexType name="tNaming">
        <xs:attribute name="name" type="xs:string" use="required"/>
        <xs:attribute name="desc" type="xs:string"/>
    </xs:complexType>
    
    <!-- Substation类型 -->
    <xs:complexType name="tSubstation">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="VoltageLevel" type="scl:tVoltageLevel" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- VoltageLevel类型 -->
    <xs:complexType name="tVoltageLevel">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="Voltage" type="scl:tVoltage" minOccurs="0"/>
                    <xs:element name="Bay" type="scl:tBay" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- Voltage类型 -->
    <xs:complexType name="tVoltage">
        <xs:attribute name="unit" type="xs:string" use="required"/>
        <xs:attribute name="multiplier" type="xs:string"/>
        <xs:attribute name="value" type="xs:decimal" use="required"/>
    </xs:complexType>
    
    <!-- Bay类型 -->
    <xs:complexType name="tBay">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="ConductingEquipment" type="scl:tConductingEquipment" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- ConductingEquipment类型 -->
    <xs:complexType name="tConductingEquipment">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="type" type="xs:string" use="required"/>
                <xs:attribute name="virtual" type="xs:boolean" default="false"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- Communication类型 -->
    <xs:complexType name="tCommunication">
        <xs:sequence>
            <xs:element name="SubNetwork" type="scl:tSubNetwork" 
                       minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- SubNetwork类型 -->
    <xs:complexType name="tSubNetwork">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="ConnectedAP" type="scl:tConnectedAP" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
                <xs:attribute name="type" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- ConnectedAP类型 -->
    <xs:complexType name="tConnectedAP">
        <xs:attribute name="iedName" type="xs:string" use="required"/>
        <xs:attribute name="apName" type="xs:string" use="required"/>
    </xs:complexType>
    
    <!-- IED类型 -->
    <xs:complexType name="tIED">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="AccessPoint" type="scl:tAccessPoint" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
                <xs:attribute name="type" type="xs:string"/>
                <xs:attribute name="manufacturer" type="xs:string"/>
                <xs:attribute name="configVersion" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- AccessPoint类型 -->
    <xs:complexType name="tAccessPoint">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="Server" type="scl:tServer" minOccurs="0"/>
                </xs:sequence>
                <xs:attribute name="router" type="xs:boolean" default="false"/>
                <xs:attribute name="clock" type="xs:boolean" default="false"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- Server类型 -->
    <xs:complexType name="tServer">
        <xs:sequence>
            <xs:element name="LDevice" type="scl:tLDevice" 
                       minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="timeout" type="xs:int" default="30"/>
    </xs:complexType>
    
    <!-- LDevice类型 -->
    <xs:complexType name="tLDevice">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="inst" type="xs:string" use="required"/>
                <xs:attribute name="ldName" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- DataTypeTemplates类型 -->
    <xs:complexType name="tDataTypeTemplates">
        <xs:sequence>
            <xs:element name="LNodeType" type="scl:tLNodeType" 
                       minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="DOType" type="scl:tDOType" 
                       minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="DAType" type="scl:tDAType" 
                       minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="EnumType" type="scl:tEnumType" 
                       minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- LNodeType类型 -->
    <xs:complexType name="tLNodeType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="lnClass" type="xs:string" use="required"/>
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- DOType类型 -->
    <xs:complexType name="tDOType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="cdc" type="xs:string" use="required"/>
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- DAType类型 -->
    <xs:complexType name="tDAType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- EnumType类型 -->
    <xs:complexType name="tEnumType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
</xs:schema>