"""
IEC61850数据类型模板数据模型
定义逻辑节点类型、数据对象类型、数据属性类型、枚举类型等
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum

from .base import (
    BaseModel, NamedModel, ValidationError, IECDataType,
    validate_required, validate_range, validate_enum,
    validate_list_not_empty, validate_unique_names
)


class BasicType(Enum):
    """基本数据类型枚举"""
    BOOLEAN = "BOOLEAN"
    INT8 = "INT8"
    INT16 = "INT16"
    INT32 = "INT32"
    INT64 = "INT64"
    INT128 = "INT128"
    INT8U = "INT8U"
    INT16U = "INT16U"
    INT24U = "INT24U"
    INT32U = "INT32U"
    FLOAT32 = "FLOAT32"
    FLOAT64 = "FLOAT64"
    ENUMERATED = "Enum"
    CODED_ENUM = "CodedEnum"
    OCTET_STRING = "Octet64"
    VISIBLE_STRING = "VisString255"
    UNICODE_STRING = "Unicode255"
    TIMESTAMP = "Timestamp"
    ENTRY_TIME = "EntryTime"
    CHECK = "Check"
    QUALITY = "Quality"
    DBPOS = "Dbpos"
    OBJECT_NAME = "ObjectName"
    OBJECT_REFERENCE = "ObjectReference"
    CURRENCY = "Currency"
    STRUCT = "Struct"


@dataclass
class EnumVal(BaseModel):
    """枚举值"""
    
    ord: int = 0                # 序号
    value: str = ""             # 值
    desc: Optional[str] = None  # 描述
    
    def validate(self) -> None:
        """验证枚举值"""
        validate_required(self.value, "value")
        validate_range(self.ord, 0, 65535, "ord")


@dataclass
class EnumType(NamedModel):
    """枚举类型"""
    
    # 枚举值列表
    enum_vals: List[EnumVal] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证枚举类型"""
        super().validate()
        
        if not self.enum_vals:
            raise ValidationError("枚举类型必须包含至少一个枚举值", "enum_vals")
        
        # 验证序号唯一性
        ords = [ev.ord for ev in self.enum_vals]
        if len(ords) != len(set(ords)):
            raise ValidationError("枚举值序号必须唯一", "enum_vals")
        
        # 验证值唯一性
        values = [ev.value for ev in self.enum_vals]
        if len(values) != len(set(values)):
            raise ValidationError("枚举值必须唯一", "enum_vals")
    
    def add_enum_val(self, enum_val: EnumVal) -> None:
        """添加枚举值"""
        # 检查序号唯一性
        existing_ords = [ev.ord for ev in self.enum_vals]
        if enum_val.ord in existing_ords:
            raise ValidationError(
                f"枚举值序号 {enum_val.ord} 已存在",
                "enum_vals"
            )
        
        # 检查值唯一性
        existing_values = [ev.value for ev in self.enum_vals]
        if enum_val.value in existing_values:
            raise ValidationError(
                f"枚举值 '{enum_val.value}' 已存在",
                "enum_vals"
            )
        
        self.enum_vals.append(enum_val)
        self.update_timestamp()
    
    def get_enum_val_by_ord(self, ord: int) -> Optional[EnumVal]:
        """根据序号获取枚举值"""
        for ev in self.enum_vals:
            if ev.ord == ord:
                return ev
        return None
    
    def get_enum_val_by_value(self, value: str) -> Optional[EnumVal]:
        """根据值获取枚举值"""
        for ev in self.enum_vals:
            if ev.value == value:
                return ev
        return None


@dataclass
class BDA(NamedModel):
    """基本数据属性"""
    
    # IEC61850标准属性
    btype: str = BasicType.BOOLEAN.value    # 基本类型
    type: Optional[str] = None              # 引用类型
    saddr: Optional[str] = None             # 短地址
    val_kind: str = "Set"                   # 值类型
    val_import: bool = False                # 值导入
    count: Optional[int] = None             # 数组大小
    
    def validate(self) -> None:
        """验证基本数据属性"""
        super().validate()
        
        # 验证基本类型
        try:
            BasicType(self.btype)
        except ValueError:
            raise ValidationError(
                f"无效的基本类型: {self.btype}",
                "btype",
                self.btype
            )
        
        # 验证值类型
        valid_val_kinds = ["Set", "Conf", "RO", "Fix"]
        if self.val_kind not in valid_val_kinds:
            raise ValidationError(
                f"值类型必须是以下之一: {valid_val_kinds}",
                "val_kind",
                self.val_kind
            )
        
        # 验证数组大小
        if self.count is not None:
            validate_range(self.count, 1, 1000, "count")


@dataclass
class DAType(NamedModel):
    """数据属性类型"""
    
    # 基本数据属性列表
    bdas: List[BDA] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证数据属性类型"""
        super().validate()
        
        if not self.bdas:
            raise ValidationError("数据属性类型必须包含至少一个基本数据属性", "bdas")
        
        # 验证BDA名称唯一性
        validate_unique_names(self.bdas, "bdas")
    
    def add_bda(self, bda: BDA) -> None:
        """添加基本数据属性"""
        # 检查名称唯一性
        existing_names = [b.name for b in self.bdas]
        if bda.name in existing_names:
            raise ValidationError(
                f"基本数据属性名称 '{bda.name}' 已存在",
                "bdas"
            )
        
        self.bdas.append(bda)
        self.update_timestamp()
    
    def get_bda(self, bda_name: str) -> Optional[BDA]:
        """获取指定基本数据属性"""
        for bda in self.bdas:
            if bda.name == bda_name:
                return bda
        return None


@dataclass
class SDO(NamedModel):
    """子数据对象"""
    
    # IEC61850标准属性
    type: str = ""                      # 数据对象类型
    count: Optional[int] = None         # 数组大小
    
    def validate(self) -> None:
        """验证子数据对象"""
        super().validate()
        validate_required(self.type, "type")
        
        # 验证数组大小
        if self.count is not None:
            validate_range(self.count, 1, 1000, "count")


@dataclass
class DO(NamedModel):
    """数据对象"""
    
    # IEC61850标准属性
    type: str = ""                      # 数据对象类型
    accesscontrol: Optional[str] = None # 访问控制
    transient: bool = False             # 是否为瞬态
    
    def validate(self) -> None:
        """验证数据对象"""
        super().validate()
        validate_required(self.type, "type")
        
        # 验证访问控制
        if self.accesscontrol:
            valid_access_controls = ["ReadOnly", "ReadWrite", "WriteOnly", "NoAccess"]
            if self.accesscontrol not in valid_access_controls:
                raise ValidationError(
                    f"访问控制必须是以下之一: {valid_access_controls}",
                    "accesscontrol",
                    self.accesscontrol
                )


@dataclass
class DOType(NamedModel):
    """数据对象类型"""
    
    # IEC61850标准属性
    iedtype: Optional[str] = None       # IED类型
    cdc: str = ""                       # 通用数据类
    
    # 子数据对象和数据属性
    sdos: List[SDO] = field(default_factory=list)
    das: List[BDA] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证数据对象类型"""
        super().validate()
        validate_required(self.cdc, "cdc")
        
        # 验证SDO名称唯一性
        if self.sdos:
            validate_unique_names(self.sdos, "sdos")
        
        # 验证DA名称唯一性
        if self.das:
            validate_unique_names(self.das, "das")
        
        # 验证SDO和DA名称不冲突
        sdo_names = [sdo.name for sdo in self.sdos]
        da_names = [da.name for da in self.das]
        common_names = set(sdo_names) & set(da_names)
        if common_names:
            raise ValidationError(
                f"SDO和DA名称冲突: {list(common_names)}",
                "sdos/das"
            )
    
    def add_sdo(self, sdo: SDO) -> None:
        """添加子数据对象"""
        # 检查名称唯一性
        existing_names = [s.name for s in self.sdos] + [d.name for d in self.das]
        if sdo.name in existing_names:
            raise ValidationError(
                f"子数据对象名称 '{sdo.name}' 已存在",
                "sdos"
            )
        
        self.sdos.append(sdo)
        self.update_timestamp()
    
    def add_da(self, da: BDA) -> None:
        """添加数据属性"""
        # 检查名称唯一性
        existing_names = [s.name for s in self.sdos] + [d.name for d in self.das]
        if da.name in existing_names:
            raise ValidationError(
                f"数据属性名称 '{da.name}' 已存在",
                "das"
            )
        
        self.das.append(da)
        self.update_timestamp()


@dataclass
class LNodeType(NamedModel):
    """逻辑节点类型"""
    
    # IEC61850标准属性
    lnclass: str = ""                   # 逻辑节点类
    iedtype: Optional[str] = None       # IED类型
    
    # 数据对象列表
    dos: List[DO] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证逻辑节点类型"""
        super().validate()
        validate_required(self.lnclass, "lnclass")
        
        # 验证逻辑节点类格式 (4个字符)
        if len(self.lnclass) != 4:
            raise ValidationError(
                "逻辑节点类必须是4个字符",
                "lnclass",
                self.lnclass
            )
        
        # 验证逻辑节点类命名规范
        if not self.lnclass.isupper():
            raise ValidationError(
                "逻辑节点类必须是大写字母",
                "lnclass",
                self.lnclass
            )
        
        # 验证DO名称唯一性
        if self.dos:
            validate_unique_names(self.dos, "dos")
    
    def add_do(self, do: DO) -> None:
        """添加数据对象"""
        # 检查名称唯一性
        existing_names = [d.name for d in self.dos]
        if do.name in existing_names:
            raise ValidationError(
                f"数据对象名称 '{do.name}' 已存在",
                "dos"
            )
        
        self.dos.append(do)
        self.update_timestamp()
    
    def get_do(self, do_name: str) -> Optional[DO]:
        """获取指定数据对象"""
        for do in self.dos:
            if do.name == do_name:
                return do
        return None


@dataclass
class DataTypeTemplates(BaseModel):
    """数据类型模板"""
    
    # 各种类型定义
    lnode_types: List[LNodeType] = field(default_factory=list)
    do_types: List[DOType] = field(default_factory=list)
    da_types: List[DAType] = field(default_factory=list)
    enum_types: List[EnumType] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证数据类型模板"""
        # 验证各类型名称唯一性
        if self.lnode_types:
            validate_unique_names(self.lnode_types, "lnode_types")
        
        if self.do_types:
            validate_unique_names(self.do_types, "do_types")
        
        if self.da_types:
            validate_unique_names(self.da_types, "da_types")
        
        if self.enum_types:
            validate_unique_names(self.enum_types, "enum_types")
    
    def add_lnode_type(self, lnode_type: LNodeType) -> None:
        """添加逻辑节点类型"""
        existing_names = [ln.name for ln in self.lnode_types]
        if lnode_type.name in existing_names:
            raise ValidationError(
                f"逻辑节点类型名称 '{lnode_type.name}' 已存在",
                "lnode_types"
            )
        
        self.lnode_types.append(lnode_type)
        self.update_timestamp()
    
    def add_do_type(self, do_type: DOType) -> None:
        """添加数据对象类型"""
        existing_names = [dt.name for dt in self.do_types]
        if do_type.name in existing_names:
            raise ValidationError(
                f"数据对象类型名称 '{do_type.name}' 已存在",
                "do_types"
            )
        
        self.do_types.append(do_type)
        self.update_timestamp()
    
    def add_da_type(self, da_type: DAType) -> None:
        """添加数据属性类型"""
        existing_names = [dt.name for dt in self.da_types]
        if da_type.name in existing_names:
            raise ValidationError(
                f"数据属性类型名称 '{da_type.name}' 已存在",
                "da_types"
            )
        
        self.da_types.append(da_type)
        self.update_timestamp()
    
    def add_enum_type(self, enum_type: EnumType) -> None:
        """添加枚举类型"""
        existing_names = [et.name for et in self.enum_types]
        if enum_type.name in existing_names:
            raise ValidationError(
                f"枚举类型名称 '{enum_type.name}' 已存在",
                "enum_types"
            )
        
        self.enum_types.append(enum_type)
        self.update_timestamp()
    
    def get_lnode_type(self, type_name: str) -> Optional[LNodeType]:
        """获取逻辑节点类型"""
        for ln in self.lnode_types:
            if ln.name == type_name:
                return ln
        return None
    
    def get_do_type(self, type_name: str) -> Optional[DOType]:
        """获取数据对象类型"""
        for dt in self.do_types:
            if dt.name == type_name:
                return dt
        return None
    
    def get_da_type(self, type_name: str) -> Optional[DAType]:
        """获取数据属性类型"""
        for dt in self.da_types:
            if dt.name == type_name:
                return dt
        return None
    
    def get_enum_type(self, type_name: str) -> Optional[EnumType]:
        """获取枚举类型"""
        for et in self.enum_types:
            if et.name == type_name:
                return et
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "lnode_types_count": len(self.lnode_types),
            "do_types_count": len(self.do_types),
            "da_types_count": len(self.da_types),
            "enum_types_count": len(self.enum_types),
            "total_types_count": (
                len(self.lnode_types) + len(self.do_types) + 
                len(self.da_types) + len(self.enum_types)
            )
        }
