"""
安全的IEC61850验证器
重构版本 - 精简、安全、高效
"""

import logging
import re
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum


class Severity(Enum):
    """问题严重程度"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class ValidationIssue:
    """验证问题"""
    type: str
    severity: Severity
    description: str
    location: str
    recommendation: str = ""


@dataclass
class ValidationResult:
    """验证结果"""
    success: bool
    score: float
    issues: List[ValidationIssue] = field(default_factory=list)
    summary: Dict[str, Any] = field(default_factory=dict)


class SecureValidator:
    """安全的IEC61850验证器"""
    
    # 安全配置
    MAX_VALIDATION_TIME = 30  # 最大验证时间（秒）
    MAX_ISSUES = 1000  # 最大问题数量
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化验证器"""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # 加载验证规则
        self.rules = self._load_validation_rules()
        
        # 安全配置
        self.max_validation_time = self.config.get('max_validation_time', self.MAX_VALIDATION_TIME)
        self.max_issues = self.config.get('max_issues', self.MAX_ISSUES)
    
    def _setup_logging(self) -> None:
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def validate(self, scd_data: Dict[str, Any]) -> ValidationResult:
        """
        验证SCD数据
        
        Args:
            scd_data: SCD数据
            
        Returns:
            ValidationResult: 验证结果
        """
        try:
            self.logger.info("开始IEC61850验证")
            
            # 输入验证
            if not isinstance(scd_data, dict):
                return ValidationResult(
                    success=False,
                    score=0.0,
                    issues=[ValidationIssue(
                        type="input_error",
                        severity=Severity.CRITICAL,
                        description="输入数据格式错误",
                        location="input"
                    )]
                )
            
            issues = []
            
            # 基础结构验证
            issues.extend(self._validate_basic_structure(scd_data))
            
            # Header验证
            issues.extend(self._validate_header(scd_data.get('header', {})))
            
            # IED验证
            issues.extend(self._validate_ieds(scd_data.get('ieds', [])))
            
            # Communication验证
            issues.extend(self._validate_communication(scd_data.get('communication', {})))
            
            # 限制问题数量（防止DoS）
            if len(issues) > self.max_issues:
                issues = issues[:self.max_issues]
                issues.append(ValidationIssue(
                    type="validation_limit",
                    severity=Severity.HIGH,
                    description=f"问题数量超过限制({self.max_issues})，已截断",
                    location="system"
                ))
            
            # 计算得分
            score = self._calculate_score(issues)
            
            # 生成摘要
            summary = self._generate_summary(issues)
            
            result = ValidationResult(
                success=len([i for i in issues if i.severity == Severity.CRITICAL]) == 0,
                score=score,
                issues=issues,
                summary=summary
            )
            
            self.logger.info(f"验证完成，得分: {score:.1f}")
            return result
            
        except Exception as e:
            self.logger.error(f"验证过程发生错误: {e}")
            return ValidationResult(
                success=False,
                score=0.0,
                issues=[ValidationIssue(
                    type="validation_error",
                    severity=Severity.CRITICAL,
                    description=f"验证过程发生错误: {str(e)}",
                    location="system"
                )]
            )
    
    def _validate_basic_structure(self, scd_data: Dict[str, Any]) -> List[ValidationIssue]:
        """验证基础结构"""
        issues = []
        
        required_sections = ['header', 'ieds']
        for section in required_sections:
            if section not in scd_data:
                issues.append(ValidationIssue(
                    type="missing_section",
                    severity=Severity.HIGH,
                    description=f"缺少必需的{section}部分",
                    location=f"root.{section}",
                    recommendation=f"添加{section}部分"
                ))
        
        return issues
    
    def _validate_header(self, header: Dict[str, Any]) -> List[ValidationIssue]:
        """验证Header"""
        issues = []
        
        if not header:
            issues.append(ValidationIssue(
                type="missing_header",
                severity=Severity.MEDIUM,
                description="缺少Header信息",
                location="SCL.Header",
                recommendation="添加Header元素"
            ))
            return issues
        
        # 验证必需属性
        required_attrs = ['id', 'version']
        for attr in required_attrs:
            if attr not in header or not header[attr]:
                issues.append(ValidationIssue(
                    type="missing_header_attr",
                    severity=Severity.MEDIUM,
                    description=f"Header缺少{attr}属性",
                    location=f"SCL.Header.{attr}",
                    recommendation=f"添加{attr}属性"
                ))
        
        # 验证ID格式
        if 'id' in header:
            if not self._validate_id_format(header['id']):
                issues.append(ValidationIssue(
                    type="invalid_id_format",
                    severity=Severity.LOW,
                    description="Header ID格式不规范",
                    location="SCL.Header.id",
                    recommendation="使用标准的ID命名格式"
                ))
        
        return issues
    
    def _validate_ieds(self, ieds: List[Dict[str, Any]]) -> List[ValidationIssue]:
        """验证IED"""
        issues = []
        
        if not ieds:
            issues.append(ValidationIssue(
                type="no_ieds",
                severity=Severity.HIGH,
                description="未找到任何IED定义",
                location="SCL.IED",
                recommendation="添加至少一个IED定义"
            ))
            return issues
        
        ied_names = set()
        
        for i, ied in enumerate(ieds):
            ied_location = f"SCL.IED[{i}]"
            
            # 验证IED名称
            if 'name' not in ied or not ied['name']:
                issues.append(ValidationIssue(
                    type="missing_ied_name",
                    severity=Severity.HIGH,
                    description=f"IED[{i}]缺少name属性",
                    location=f"{ied_location}.name",
                    recommendation="为IED添加唯一的name属性"
                ))
                continue
            
            ied_name = ied['name']
            
            # 检查名称唯一性
            if ied_name in ied_names:
                issues.append(ValidationIssue(
                    type="duplicate_ied_name",
                    severity=Severity.CRITICAL,
                    description=f"IED名称重复: {ied_name}",
                    location=f"{ied_location}.name",
                    recommendation="确保每个IED名称唯一"
                ))
            else:
                ied_names.add(ied_name)
            
            # 验证IED名称格式
            if not self._validate_ied_name_format(ied_name):
                issues.append(ValidationIssue(
                    type="invalid_ied_name",
                    severity=Severity.MEDIUM,
                    description=f"IED名称格式不规范: {ied_name}",
                    location=f"{ied_location}.name",
                    recommendation="使用符合IEC61850标准的IED命名格式"
                ))
            
            # 验证IED类型
            if 'type' in ied and ied['type']:
                if not self._validate_ied_type(ied['type']):
                    issues.append(ValidationIssue(
                        type="invalid_ied_type",
                        severity=Severity.LOW,
                        description=f"IED类型不规范: {ied['type']}",
                        location=f"{ied_location}.type",
                        recommendation="使用标准的IED类型"
                    ))
        
        return issues
    
    def _validate_communication(self, communication: Dict[str, Any]) -> List[ValidationIssue]:
        """验证Communication"""
        issues = []
        
        if not communication:
            issues.append(ValidationIssue(
                type="missing_communication",
                severity=Severity.MEDIUM,
                description="缺少Communication配置",
                location="SCL.Communication",
                recommendation="添加Communication部分"
            ))
            return issues
        
        # 验证SubNetwork
        subnets = communication.get('subnets', [])
        if not subnets:
            issues.append(ValidationIssue(
                type="no_subnets",
                severity=Severity.MEDIUM,
                description="未找到SubNetwork定义",
                location="SCL.Communication.SubNetwork",
                recommendation="添加至少一个SubNetwork"
            ))
        
        return issues
    
    def _validate_id_format(self, id_value: str) -> bool:
        """验证ID格式"""
        if not isinstance(id_value, str):
            return False
        
        # 基本格式检查：字母数字和下划线，不能以数字开头
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, id_value)) and len(id_value) <= 64
    
    def _validate_ied_name_format(self, name: str) -> bool:
        """验证IED名称格式"""
        if not isinstance(name, str):
            return False
        
        # IED名称格式：字母数字和下划线，长度限制
        pattern = r'^[a-zA-Z][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, name)) and 1 <= len(name) <= 32
    
    def _validate_ied_type(self, ied_type: str) -> bool:
        """验证IED类型"""
        if not isinstance(ied_type, str):
            return False
        
        # 常见的IED类型
        valid_types = {
            'Protection', 'Control', 'Measurement', 'Recording',
            'Gateway', 'HMI', 'Server', 'Client', 'Merging Unit'
        }
        
        return ied_type in valid_types
    
    def _calculate_score(self, issues: List[ValidationIssue]) -> float:
        """计算验证得分"""
        if not issues:
            return 100.0
        
        # 根据问题严重程度计算扣分
        penalty_map = {
            Severity.CRITICAL: 20,
            Severity.HIGH: 10,
            Severity.MEDIUM: 5,
            Severity.LOW: 2,
            Severity.INFO: 1
        }
        
        total_penalty = sum(penalty_map.get(issue.severity, 0) for issue in issues)
        score = max(0.0, 100.0 - total_penalty)
        
        return score
    
    def _generate_summary(self, issues: List[ValidationIssue]) -> Dict[str, Any]:
        """生成验证摘要"""
        summary = {
            'total_issues': len(issues),
            'by_severity': {},
            'by_type': {}
        }
        
        # 按严重程度统计
        for severity in Severity:
            count = len([i for i in issues if i.severity == severity])
            if count > 0:
                summary['by_severity'][severity.value] = count
        
        # 按类型统计
        type_counts = {}
        for issue in issues:
            type_counts[issue.type] = type_counts.get(issue.type, 0) + 1
        
        summary['by_type'] = type_counts
        
        return summary
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载验证规则"""
        return {
            'ied_name_max_length': 32,
            'id_max_length': 64,
            'max_ieds': 1000,
            'required_sections': ['header', 'ieds']
        }


def main():
    """主函数 - 演示安全验证器"""
    print("安全IEC61850验证器演示")
    print("=" * 50)
    
    validator = SecureValidator()
    
    # 测试数据
    test_data = {
        'header': {
            'id': 'test_scd',
            'version': '1.0'
        },
        'ieds': [
            {
                'name': 'IED1',
                'type': 'Protection',
                'manufacturer': 'TestMfg'
            }
        ],
        'communication': {
            'subnets': [
                {'name': 'SubNet1', 'type': '8-MMS'}
            ]
        }
    }
    
    # 执行验证
    result = validator.validate(test_data)
    
    print(f"验证结果: {'成功' if result.success else '失败'}")
    print(f"得分: {result.score:.1f}/100")
    print(f"问题数量: {len(result.issues)}")
    
    if result.issues:
        print("\n发现的问题:")
        for issue in result.issues[:5]:  # 显示前5个问题
            print(f"  [{issue.severity.value}] {issue.description}")
            print(f"    位置: {issue.location}")
            if issue.recommendation:
                print(f"    建议: {issue.recommendation}")
    
    print("\n演示完成")


if __name__ == "__main__":
    main()