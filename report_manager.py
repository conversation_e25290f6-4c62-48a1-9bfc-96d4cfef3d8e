#!/usr/bin/env python3
"""
设计报告管理器
统一管理所有生成的设计质量检测报告
"""

import os
import json
import webbrowser
from pathlib import Path
from datetime import datetime
from typing import Dict, List


class ReportManager:
    """报告管理器"""
    
    def __init__(self):
        self.base_dir = Path("design_reports")
        self.report_types = {
            'fault_reports': '故障检测报告',
            'design_check': '设计检查报告',
            'circuit_analysis': '回路分析报告',
            'compliance_check': '合规性检查报告',
            'virtual_terminal': '虚端子分析报告',
            'communication': '通信配置报告',
            'protection': '保护配置报告',
            'measurement': '测量回路报告',
            'control': '控制回路报告',
            'archives': '历史报告存档'
        }
    
    def scan_reports(self) -> Dict:
        """扫描所有报告文件"""
        
        reports = {}
        
        for report_type, description in self.report_types.items():
            type_dir = self.base_dir / report_type
            if not type_dir.exists():
                continue
            
            reports[report_type] = {
                'description': description,
                'files': [],
                'count': 0,
                'total_size': 0
            }
            
            # 扫描文件
            for file_path in type_dir.glob("*"):
                if file_path.is_file() and not file_path.name.startswith('README'):
                    file_info = {
                        'name': file_path.name,
                        'path': str(file_path),
                        'size': file_path.stat().st_size,
                        'modified': datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                        'type': file_path.suffix.lower()
                    }
                    reports[report_type]['files'].append(file_info)
                    reports[report_type]['total_size'] += file_info['size']
            
            reports[report_type]['count'] = len(reports[report_type]['files'])
        
        return reports
    
    def generate_dashboard(self) -> str:
        """生成报告仪表板HTML"""
        
        reports = self.scan_reports()
        
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计报告仪表板</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { text-align: center; padding: 30px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .stat-value { font-size: 2.5em; font-weight: bold; color: #667eea; }
        .stat-label { font-size: 1.1em; color: #666; margin-top: 10px; }
        .reports-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .report-card { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .report-header { background: #667eea; color: white; padding: 15px; font-weight: bold; }
        .report-content { padding: 20px; }
        .file-list { max-height: 300px; overflow-y: auto; }
        .file-item { display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #eee; }
        .file-name { font-weight: 500; color: #333; }
        .file-info { font-size: 0.9em; color: #666; }
        .file-size { font-size: 0.8em; color: #999; }
        .btn { padding: 8px 16px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; font-size: 0.9em; }
        .btn:hover { background: #5a6fd8; }
        .btn-small { padding: 4px 8px; font-size: 0.8em; }
        .empty-state { text-align: center; color: #999; padding: 40px; }
        .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 设计报告仪表板</h1>
            <p>智能变电站设计质量检测报告管理中心</p>
            <p>更新时间: {update_time}</p>
        </div>'''.format(update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # 统计信息
        total_files = sum(r['count'] for r in reports.values())
        total_size = sum(r['total_size'] for r in reports.values())
        active_types = len([r for r in reports.values() if r['count'] > 0])
        
        html_content += f'''
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{total_files}</div>
                <div class="stat-label">📄 总报告数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{active_types}</div>
                <div class="stat-label">📂 活跃类型</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{total_size/1024:.1f}</div>
                <div class="stat-label">💾 总大小(KB)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{len(self.report_types)}</div>
                <div class="stat-label">🗂️ 报告类型</div>
            </div>
        </div>'''
        
        # 重点推荐
        html_content += '''
        <div class="highlight">
            <h3>🌟 重点推荐</h3>
            <p><strong>回路分析报告</strong>: 查看基于虚端子连接关系生成的传统二次回路图，包含电流回路、保护回路、跳闸回路等。</p>
            <p><strong>设计检查报告</strong>: 查看详细的设计问题检测结果，包含9个具体问题和修复建议。</p>
        </div>'''
        
        # 报告卡片
        html_content += '<div class="reports-grid">'
        
        for report_type, report_info in reports.items():
            if report_info['count'] == 0:
                continue
            
            html_content += f'''
            <div class="report-card">
                <div class="report-header">
                    {report_info['description']} ({report_info['count']} 个文件)
                </div>
                <div class="report-content">'''
            
            if report_info['files']:
                html_content += '<div class="file-list">'
                
                # 按修改时间排序
                sorted_files = sorted(report_info['files'], key=lambda x: x['modified'], reverse=True)
                
                for file_info in sorted_files[:10]:  # 只显示最新的10个文件
                    size_kb = file_info['size'] / 1024
                    modified_date = datetime.fromisoformat(file_info['modified']).strftime('%m-%d %H:%M')
                    
                    # 根据文件类型设置图标
                    icon = '🌐' if file_info['type'] == '.html' else '📄' if file_info['type'] == '.json' else '📝' if file_info['type'] == '.txt' else '🎨' if file_info['type'] == '.svg' else '📋'
                    
                    html_content += f'''
                    <div class="file-item">
                        <div>
                            <div class="file-name">{icon} {file_info['name']}</div>
                            <div class="file-info">{modified_date}</div>
                        </div>
                        <div>
                            <span class="file-size">{size_kb:.1f}KB</span>
                            {f'<a href="{file_info["name"]}" class="btn btn-small" target="_blank">查看</a>' if file_info['type'] in ['.html', '.svg'] else ''}
                        </div>
                    </div>'''
                
                html_content += '</div>'
                
                # 如果有更多文件，显示查看全部按钮
                if len(report_info['files']) > 10:
                    html_content += f'<p style="text-align: center; margin-top: 15px;"><em>还有 {len(report_info["files"]) - 10} 个文件...</em></p>'
            
            else:
                html_content += '<div class="empty-state">暂无报告文件</div>'
            
            html_content += '''
                </div>
            </div>'''
        
        html_content += '</div>'
        
        # 使用说明
        html_content += '''
        
        <div style="background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-top: 30px;">
            <h3>📖 使用说明</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                <div>
                    <h4>🎨 回路分析报告</h4>
                    <ul>
                        <li>查看 <code>circuit_viewer.html</code> 获得最佳体验</li>
                        <li>包含电流回路、保护回路、跳闸回路图</li>
                        <li>展示虚端子技术的应用优势</li>
                        <li>SVG格式支持缩放和编辑</li>
                    </ul>
                    
                    <h4>🔍 设计检查报告</h4>
                    <ul>
                        <li>HTML格式提供最佳可视化体验</li>
                        <li>JSON格式便于程序化处理</li>
                        <li>TXT格式适合快速查看</li>
                        <li>按优先级排序的修复建议</li>
                    </ul>
                </div>
                <div>
                    <h4>📊 报告类型说明</h4>
                    <ul>
                        <li><strong>故障检测</strong>: 系统故障和异常检测</li>
                        <li><strong>设计检查</strong>: 配置文件设计质量检查</li>
                        <li><strong>回路分析</strong>: 二次回路连接关系分析</li>
                        <li><strong>合规性检查</strong>: 标准符合性验证</li>
                        <li><strong>虚端子分析</strong>: GOOSE/SV连接分析</li>
                    </ul>
                    
                    <h4>🚀 快速操作</h4>
                    <ul>
                        <li>点击HTML文件直接在浏览器中查看</li>
                        <li>SVG文件可在支持的软件中编辑</li>
                        <li>JSON文件可用于程序化处理</li>
                        <li>定期清理历史文件到archives目录</li>
                    </ul>
                </div>
            </div>
        </div>
        
    </div>
</body>
</html>'''
        
        return html_content
    
    def create_dashboard(self) -> str:
        """创建并保存仪表板"""
        
        print("📊 生成设计报告仪表板")
        print("=" * 50)
        
        # 生成HTML内容
        html_content = self.generate_dashboard()
        
        # 保存仪表板文件
        dashboard_file = self.base_dir / "dashboard.html"
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 仪表板已保存: {dashboard_file}")
        
        # 扫描报告统计
        reports = self.scan_reports()
        total_files = sum(r['count'] for r in reports.values())
        total_size = sum(r['total_size'] for r in reports.values())
        
        print(f"📊 报告统计:")
        print(f"   📄 总文件数: {total_files}")
        print(f"   💾 总大小: {total_size/1024:.1f} KB")
        print(f"   📂 活跃类型: {len([r for r in reports.values() if r['count'] > 0])}")
        
        print(f"\n📋 各类型报告:")
        for report_type, report_info in reports.items():
            if report_info['count'] > 0:
                size_kb = report_info['total_size'] / 1024
                print(f"   • {report_info['description']}: {report_info['count']} 个文件 ({size_kb:.1f} KB)")
        
        return str(dashboard_file)
    
    def open_dashboard(self, dashboard_file: str):
        """在浏览器中打开仪表板"""
        
        try:
            file_url = f"file:///{Path(dashboard_file).absolute().as_posix()}"
            webbrowser.open(file_url)
            print(f"🌐 已在浏览器中打开仪表板: {file_url}")
        except Exception as e:
            print(f"❌ 打开仪表板失败: {e}")
    
    def show_summary(self):
        """显示报告摘要"""
        
        print("📊 设计报告管理器")
        print("=" * 60)
        
        reports = self.scan_reports()
        
        print("🎯 重点推荐:")
        print("   🎨 回路分析报告 - 查看虚端子回路图")
        print("   🔍 设计检查报告 - 查看设计问题检测结果")
        print("   📋 合规性检查报告 - 查看标准符合性验证")
        
        print(f"\n📁 报告目录结构:")
        for report_type, report_info in reports.items():
            status = f"({report_info['count']} 个文件)" if report_info['count'] > 0 else "(空)"
            print(f"   📂 {report_type}/ - {report_info['description']} {status}")
        
        print(f"\n🌟 亮点功能:")
        print("   ✅ 自动生成传统二次回路图")
        print("   ✅ 基于虚端子连接关系分析")
        print("   ✅ 符合GB/T 4728电气图形符号标准")
        print("   ✅ 支持电流、电压、保护、跳闸回路")
        print("   ✅ 提供HTML可视化查看器")
        print("   ✅ 详细的设计问题检测和修复建议")


def main():
    """主函数"""
    
    # 创建报告管理器
    manager = ReportManager()
    
    # 显示摘要
    manager.show_summary()
    
    # 创建仪表板
    dashboard_file = manager.create_dashboard()
    
    # 询问是否打开仪表板
    print(f"\n🌐 是否在浏览器中打开仪表板? (y/n): ", end="")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes', '是']:
        manager.open_dashboard(dashboard_file)
    
    print(f"\n💡 提示:")
    print(f"   • 仪表板文件: {dashboard_file}")
    print(f"   • 可随时手动打开查看所有报告")
    print(f"   • 支持直接点击HTML和SVG文件查看")


if __name__ == "__main__":
    main()
