<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计问题检测报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 30px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header .subtitle { margin-top: 10px; font-size: 1.2em; opacity: 0.9; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .summary-card h3 { margin: 0 0 10px 0; color: #007bff; }
        .summary-card .value { font-size: 2em; font-weight: bold; color: #333; }
        .issues { margin-top: 30px; }
        .issue { background: white; border: 1px solid #ddd; border-radius: 8px; margin-bottom: 20px; padding: 20px; }
        .issue.critical { border-left: 5px solid #dc3545; }
        .issue.error { border-left: 5px solid #fd7e14; }
        .issue.warning { border-left: 5px solid #ffc107; }
        .issue.info { border-left: 5px solid #17a2b8; }
        .issue-header { display: flex; align-items: center; margin-bottom: 15px; }
        .severity-badge { padding: 4px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; margin-right: 15px; }
        .severity-badge.critical { background-color: #dc3545; }
        .severity-badge.error { background-color: #fd7e14; }
        .severity-badge.warning { background-color: #ffc107; color: #333; }
        .severity-badge.info { background-color: #17a2b8; }
        .issue-title { font-size: 1.3em; font-weight: bold; color: #333; }
        .issue-details { margin-top: 15px; }
        .issue-details div { margin-bottom: 8px; }
        .label { font-weight: bold; color: #666; }
        .stats { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0; }
        .chart { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .chart h3 { margin-top: 0; color: #333; }
        .bar { display: flex; align-items: center; margin-bottom: 10px; }
        .bar-label { width: 100px; font-size: 0.9em; }
        .bar-fill { height: 20px; background: #007bff; margin-right: 10px; border-radius: 3px; }
        .bar-value { font-weight: bold; }
        .recommendations { background: #e8f5e8; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; margin-top: 30px; }
        .recommendations h3 { margin-top: 0; color: #28a745; }
        .recommendations ul { margin: 0; padding-left: 20px; }
        .recommendations li { margin-bottom: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 设计问题检测报告</h1>
            <div class="subtitle">IEC61850配置文件设计质量分析</div>
            <div class="subtitle">检测时间: 2025-08-18 00:15:00</div>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>📊 总问题数</h3>
                <div class="value">9</div>
            </div>
            <div class="summary-card">
                <h3>📈 合规性评分</h3>
                <div class="value">30/100</div>
            </div>
            <div class="summary-card">
                <h3>🔴 严重问题</h3>
                <div class="value">2</div>
            </div>
            <div class="summary-card">
                <h3>🟠 错误问题</h3>
                <div class="value">6</div>
            </div>
            <div class="summary-card">
                <h3>🟡 警告问题</h3>
                <div class="value">1</div>
            </div>
            <div class="summary-card">
                <h3>🔧 可自动修复</h3>
                <div class="value">3</div>
            </div>
        </div>
        
        <div class="stats">
            <div class="chart">
                <h3>📋 按严重程度统计</h3>
                <div class="bar">
                    <div class="bar-label">critical</div>
                    <div class="bar-fill" style="width: 200px; background-color: #dc3545;"></div>
                    <div class="bar-value">2</div>
                </div>
                <div class="bar">
                    <div class="bar-label">error</div>
                    <div class="bar-fill" style="width: 300px; background-color: #fd7e14;"></div>
                    <div class="bar-value">6</div>
                </div>
                <div class="bar">
                    <div class="bar-label">warning</div>
                    <div class="bar-fill" style="width: 100px; background-color: #ffc107;"></div>
                    <div class="bar-value">1</div>
                </div>
            </div>
            <div class="chart">
                <h3>📂 按类别统计</h3>
                <div class="bar">
                    <div class="bar-label">通信配置</div>
                    <div class="bar-fill" style="width: 200px;"></div>
                    <div class="bar-value">2</div>
                </div>
                <div class="bar">
                    <div class="bar-label">回路连接</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
                <div class="bar">
                    <div class="bar-label">设备配置</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
                <div class="bar">
                    <div class="bar-label">虚端子连接</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
                <div class="bar">
                    <div class="bar-label">配置完整性</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
                <div class="bar">
                    <div class="bar-label">数据集配置</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
                <div class="bar">
                    <div class="bar-label">IED配置</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
                <div class="bar">
                    <div class="bar-label">数据类型定义</div>
                    <div class="bar-fill" style="width: 100px;"></div>
                    <div class="bar-value">1</div>
                </div>
            </div>
        </div>
        
        <div class="issues">
            <h2>🔍 详细问题列表</h2>
            
            <div class="issue critical">
                <div class="issue-header">
                    <span class="severity-badge critical">CRITICAL</span>
                    <span class="issue-title">1. 断路器QF1缺少Terminal连接</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> 断路器QF1没有定义任何Terminal连接，导致回路不通</div>
                    <div><span class="label">📍 位置:</span> 断路器: QF1</div>
                    <div><span class="label">💡 建议:</span> 为断路器QF1添加至少两个Terminal连接，分别连接到母线和线路侧</div>
                    <div><span class="label">📚 标准:</span> IEC61850-6: 配置描述语言</div>
                    <div><span class="label">🎯 影响元素:</span> QF1</div>
                    <div><span class="label">🔧 可自动修复:</span> 否</div>
                </div>
            </div>
            
            <div class="issue critical">
                <div class="issue-header">
                    <span class="severity-badge critical">CRITICAL</span>
                    <span class="issue-title">2. IP地址冲突: ************</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> IP地址************被多个IED使用: ProtectionIED1 和 ProtectionIED2</div>
                    <div><span class="label">📍 位置:</span> IED: ProtectionIED2</div>
                    <div><span class="label">💡 建议:</span> 为IED ProtectionIED2分配唯一的IP地址</div>
                    <div><span class="label">📚 标准:</span> IEC61850-8-1: 特定通信服务映射</div>
                    <div><span class="label">🎯 影响元素:</span> ProtectionIED1, ProtectionIED2</div>
                    <div><span class="label">🔧 可自动修复:</span> 是</div>
                </div>
            </div>
            
            <div class="issue error">
                <div class="issue-header">
                    <span class="severity-badge error">ERROR</span>
                    <span class="issue-title">3. 间隔220kV_Line1缺少电流互感器</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> 间隔220kV_Line1有断路器但缺少电流互感器，无法进行电流测量和保护</div>
                    <div><span class="label">📍 位置:</span> 间隔: 220kV_Line1</div>
                    <div><span class="label">💡 建议:</span> 在间隔220kV_Line1中添加电流互感器(CTR)用于电流测量</div>
                    <div><span class="label">📚 标准:</span> IEC61850-7-4: 兼容的逻辑节点类和数据类</div>
                    <div><span class="label">🎯 影响元素:</span> 220kV_Line1</div>
                    <div><span class="label">🔧 可自动修复:</span> 否</div>
                </div>
            </div>
            
            <div class="issue error">
                <div class="issue-header">
                    <span class="severity-badge error">ERROR</span>
                    <span class="issue-title">4. 电流互感器TA2缺少二次侧连接</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> 电流互感器TA2缺少二次侧Terminal连接，无法向保护和测量装置提供电流信号</div>
                    <div><span class="label">📍 位置:</span> 电流互感器: TA2</div>
                    <div><span class="label">💡 建议:</span> 为电流互感器TA2添加二次侧Terminal连接，连接到保护和测量装置</div>
                    <div><span class="label">📚 标准:</span> IEC61850-9-2: 采样值传输的特殊通信服务映射</div>
                    <div><span class="label">🎯 影响元素:</span> TA2</div>
                    <div><span class="label">🔧 可自动修复:</span> 否</div>
                </div>
            </div>
            
            <div class="issue error">
                <div class="issue-header">
                    <span class="severity-badge error">ERROR</span>
                    <span class="issue-title">5. 电压等级110kV没有配置任何间隔</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> 电压等级110kV下没有定义任何间隔，这是不完整的配置</div>
                    <div><span class="label">📍 位置:</span> 电压等级: 110kV</div>
                    <div><span class="label">💡 建议:</span> 在电压等级110kV下添加相应的间隔配置，如出线间隔、母联间隔等</div>
                    <div><span class="label">📚 标准:</span> IEC61850-6: 配置描述语言</div>
                    <div><span class="label">🎯 影响元素:</span> 110kV</div>
                    <div><span class="label">🔧 可自动修复:</span> 否</div>
                </div>
            </div>
            
            <div class="issue error">
                <div class="issue-header">
                    <span class="severity-badge error">ERROR</span>
                    <span class="issue-title">6. 数据集Events引用不存在的逻辑节点</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> 数据集Events中引用了不存在的逻辑节点: PTRC2</div>
                    <div><span class="label">📍 位置:</span> 数据集: Events</div>
                    <div><span class="label">💡 建议:</span> 检查逻辑节点PTRC2是否正确定义，或修正数据集中的引用</div>
                    <div><span class="label">📚 标准:</span> IEC61850-7-2: 抽象通信服务接口</div>
                    <div><span class="label">🎯 影响元素:</span> Events, PTRC2</div>
                    <div><span class="label">🔧 可自动修复:</span> 否</div>
                </div>
            </div>
            
            <div class="issue error">
                <div class="issue-header">
                    <span class="severity-badge error">ERROR</span>
                    <span class="issue-title">7. 保护IED ProtectionIED2缺少保护逻辑节点</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> 保护类型的IED ProtectionIED2没有定义任何保护逻辑节点(PTRC)</div>
                    <div><span class="label">📍 位置:</span> IED: ProtectionIED2</div>
                    <div><span class="label">💡 建议:</span> 为保护IED ProtectionIED2添加相应的保护逻辑节点(PTRC)</div>
                    <div><span class="label">📚 标准:</span> IEC61850-7-4: 兼容的逻辑节点类和数据类</div>
                    <div><span class="label">🎯 影响元素:</span> ProtectionIED2</div>
                    <div><span class="label">🔧 可自动修复:</span> 否</div>
                </div>
            </div>
            
            <div class="issue error">
                <div class="issue-header">
                    <span class="severity-badge error">ERROR</span>
                    <span class="issue-title">8. LLN0类型LLN0_Type缺少NamPlt数据对象</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> LLN0类型LLN0_Type缺少必要的NamPlt(名牌)数据对象</div>
                    <div><span class="label">📍 位置:</span> LNodeType: LLN0_Type</div>
                    <div><span class="label">💡 建议:</span> 为LLN0类型LLN0_Type添加NamPlt数据对象</div>
                    <div><span class="label">📚 标准:</span> IEC61850-7-4: 兼容的逻辑节点类和数据类</div>
                    <div><span class="label">🎯 影响元素:</span> LLN0_Type</div>
                    <div><span class="label">🔧 可自动修复:</span> 是</div>
                </div>
            </div>
            
            <div class="issue warning">
                <div class="issue-header">
                    <span class="severity-badge warning">WARNING</span>
                    <span class="issue-title">9. IED ProtectionIED1缺少网关配置</span>
                </div>
                <div class="issue-details">
                    <div><span class="label">📝 描述:</span> IED ProtectionIED1的网络配置中缺少IP-GATEWAY设置</div>
                    <div><span class="label">📍 位置:</span> IED: ProtectionIED1</div>
                    <div><span class="label">💡 建议:</span> 为IED ProtectionIED1添加IP-GATEWAY配置</div>
                    <div><span class="label">📚 标准:</span> IEC61850-8-1: 特定通信服务映射</div>
                    <div><span class="label">🎯 影响元素:</span> ProtectionIED1</div>
                    <div><span class="label">🔧 可自动修复:</span> 是</div>
                </div>
            </div>
        </div>
        
        <div class="recommendations">
            <h3>💡 修复建议</h3>
            <ul>
                <li>优先解决严重问题和错误级别的问题</li>
                <li>检查回路连接的完整性，确保电气回路通畅</li>
                <li>完善设备配置，添加缺少的互感器和虚端子连接</li>
                <li>规范通信网络配置，避免IP地址冲突</li>
                <li>完善数据类型定义，确保引用的完整性</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p>报告生成时间: 2025-08-18 00:15:00 | 检测器版本: 1.0.0</p>
        </div>
    </div>
</body>
</html>
