"""
SCL文档模型
定义IEC61850 SCL文档的顶层结构
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from datetime import datetime

from .base import BaseModel
from .substation import SubStation
from .ied import IED
from .communication import Communication
from .data_type import DataTypeTemplates


@dataclass
class Header(BaseModel):
    """SCL文档头部信息"""
    id: str = ""
    version: str = ""
    revision: str = ""
    tool_id: str = ""
    name_structure: str = "IEDName"

    # 历史信息
    history: List[Dict[str, Any]] = field(default_factory=list)

    def validate(self) -> None:
        """验证Header有效性"""
        if not self.id:
            raise ValueError("Header ID不能为空")
        if not self.version:
            raise ValueError("Header版本不能为空")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'version': self.version,
            'revision': self.revision,
            'tool_id': self.tool_id,
            'name_structure': self.name_structure,
            'history': self.history
        }


@dataclass
class SCLDocument(BaseModel):
    """
    SCL文档模型
    
    表示完整的IEC61850 SCL配置文件，包含：
    - Header: 文档头部信息
    - Substation: 变电站配置
    - IED: 设备列表
    - Communication: 通信配置
    - DataTypeTemplates: 数据类型模板
    """
    
    # 基本信息
    version: str = "2007"
    xmlns: str = "http://www.iec.ch/61850/2003/SCL"
    
    # 文档组成部分
    header: Optional[Header] = None
    substation: Optional[SubStation] = None
    ieds: List[IED] = field(default_factory=list)
    communication: Optional[Communication] = None
    data_type_templates: Optional[DataTypeTemplates] = None
    
    # 元数据
    file_path: str = ""
    parsed_at: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.parsed_at is None:
            self.parsed_at = datetime.now()
        # 不在初始化时验证，因为可能还在构建中

    def validate(self) -> None:
        """验证SCL文档有效性"""
        # 基本验证，允许空文档用于测试
        pass
    
    def add_ied(self, ied: IED):
        """添加IED设备"""
        if ied not in self.ieds:
            self.ieds.append(ied)
    
    def get_ied_by_name(self, name: str) -> Optional[IED]:
        """根据名称获取IED"""
        for ied in self.ieds:
            if ied.name == name:
                return ied
        return None
    
    def get_all_ied_names(self) -> List[str]:
        """获取所有IED名称"""
        return [ied.name for ied in self.ieds if ied.name]
    
    def validate(self) -> List[str]:
        """验证文档完整性"""
        errors = []
        
        # 检查基本结构
        if not self.header:
            errors.append("缺少Header信息")
        
        if not self.substation:
            errors.append("缺少Substation配置")
        
        if not self.ieds:
            errors.append("缺少IED设备配置")
        
        # 检查IED名称唯一性
        ied_names = [ied.name for ied in self.ieds if ied.name]
        if len(ied_names) != len(set(ied_names)):
            errors.append("存在重复的IED名称")
        
        # 检查通信配置
        if self.communication:
            # 验证通信配置的完整性
            pass
        
        return errors
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        stats = {
            'version': self.version,
            'ied_count': len(self.ieds),
            'ied_names': self.get_all_ied_names(),
            'has_substation': self.substation is not None,
            'has_communication': self.communication is not None,
            'has_data_types': self.data_type_templates is not None,
            'file_path': self.file_path,
            'parsed_at': self.parsed_at.isoformat() if self.parsed_at else None
        }
        
        # 变电站统计
        if self.substation:
            stats['substation'] = {
                'name': self.substation.name,
                'voltage_levels': len(self.substation.voltage_levels or []),
                'bays': sum(len(vl.bays or []) for vl in (self.substation.voltage_levels or []))
            }
        
        # IED统计
        if self.ieds:
            manufacturers = [ied.manufacturer for ied in self.ieds if ied.manufacturer]
            stats['ied_manufacturers'] = list(set(manufacturers))
            
            types = [ied.type for ied in self.ieds if ied.type]
            stats['ied_types'] = list(set(types))
        
        # 通信统计
        if self.communication and self.communication.sub_networks:
            stats['communication'] = {
                'subnet_count': len(self.communication.sub_networks),
                'connected_ap_count': sum(len(sn.connected_aps or []) 
                                        for sn in self.communication.sub_networks)
            }
        
        return stats
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'version': self.version,
            'xmlns': self.xmlns,
            'header': self.header.to_dict() if self.header else None,
            'substation': self.substation.to_dict() if self.substation else None,
            'ieds': [ied.to_dict() for ied in self.ieds],
            'communication': self.communication.to_dict() if self.communication else None,
            'data_type_templates': self.data_type_templates.to_dict() if self.data_type_templates else None,
            'file_path': self.file_path,
            'parsed_at': self.parsed_at.isoformat() if self.parsed_at else None,
            'statistics': self.get_statistics()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SCLDocument':
        """从字典创建SCL文档"""
        doc = cls()
        
        # 基本信息
        doc.version = data.get('version', '2007')
        doc.xmlns = data.get('xmlns', 'http://www.iec.ch/61850/2003/SCL')
        doc.file_path = data.get('file_path', '')
        
        # 解析时间
        if data.get('parsed_at'):
            try:
                doc.parsed_at = datetime.fromisoformat(data['parsed_at'])
            except:
                doc.parsed_at = datetime.now()
        
        # Header
        if data.get('header'):
            doc.header = Header()
            header_data = data['header']
            doc.header.id = header_data.get('id', '')
            doc.header.version = header_data.get('version', '')
            doc.header.revision = header_data.get('revision', '')
            doc.header.tool_id = header_data.get('tool_id', '')
            doc.header.name_structure = header_data.get('name_structure', 'IEDName')
            doc.header.history = header_data.get('history', [])
        
        # Substation
        if data.get('substation'):
            doc.substation = SubStation.from_dict(data['substation'])
        
        # IEDs
        if data.get('ieds'):
            doc.ieds = [IED.from_dict(ied_data) for ied_data in data['ieds']]
        
        # Communication
        if data.get('communication'):
            doc.communication = Communication.from_dict(data['communication'])
        
        # DataTypeTemplates
        if data.get('data_type_templates'):
            doc.data_type_templates = DataTypeTemplates.from_dict(data['data_type_templates'])
        
        return doc
    
    def clone(self) -> 'SCLDocument':
        """克隆文档"""
        return self.from_dict(self.to_dict())
    
    def merge_with(self, other: 'SCLDocument') -> 'SCLDocument':
        """与另一个文档合并"""
        merged = self.clone()
        
        # 合并IED
        for ied in other.ieds:
            if not merged.get_ied_by_name(ied.name):
                merged.add_ied(ied)
        
        # 合并其他部分的逻辑可以根据需要添加
        
        return merged
