#!/usr/bin/env python3
"""
调试Web应用启动问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("=== Web应用启动调试 ===")
print(f"项目根目录: {project_root}")
print(f"Python路径: {sys.path[0]}")

try:
    print("\n1. 检查核心模块导入...")
    
    # 检查核心模块
    from src.core.parsers import ParserFactory
    print("✅ 核心解析器模块正常")
    
    from src.core.rules import RuleEngine
    print("✅ 核心规则引擎模块正常")
    
    # 检查统一审查引擎
    print("\n2. 检查统一审查引擎...")
    from src.core.unified_review_engine import UnifiedReviewEngine
    print("✅ 统一审查引擎模块正常")
    
    # 检查Web模块
    print("\n3. 检查Web模块...")
    from src.web.views import main_bp
    print("✅ Web视图模块正常")
    
    from src.web.api import api_bp
    print("✅ Web API模块正常")
    
    # 检查Flask应用创建
    print("\n4. 检查Flask应用创建...")
    from src.web.app import create_app
    app = create_app()
    print("✅ Flask应用创建成功")
    
    # 检查路由
    print("\n5. 检查路由配置...")
    with app.app_context():
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(f"{rule.methods} {rule.rule}")
        
        print(f"✅ 注册了 {len(routes)} 个路由")
        
        # 检查关键路由
        key_routes = ['/unified-review', '/api/unified-review/upload', '/api/unified-review/review']
        for route in key_routes:
            found = any(route in r for r in routes)
            status = "✅" if found else "❌"
            print(f"{status} 路由 {route}: {'存在' if found else '缺失'}")
    
    print("\n6. 尝试启动应用...")
    print("应用将在 http://localhost:5000 启动")
    print("统一审查页面: http://localhost:5000/unified-review")
    print("按 Ctrl+C 停止应用")
    
    # 启动应用
    app.run(host='0.0.0.0', port=5000, debug=True)
    
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请检查依赖是否正确安装")
    
except Exception as e:
    print(f"❌ 应用启动失败: {e}")
    import traceback
    traceback.print_exc()
    
    print("\n=== 错误诊断建议 ===")
    print("1. 检查是否安装了所有依赖:")
    print("   pip install flask flask-cors")
    print("2. 检查项目结构是否完整")
    print("3. 检查是否有语法错误")
