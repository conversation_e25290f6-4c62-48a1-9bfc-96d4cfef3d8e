"""
图纸审查结果可视化
提供图纸审查结果的可视化展示功能
"""

import logging
import os
from typing import Dict, List, Any, Optional, Tuple
import json
from pathlib import Path

from .drawing_models import DrawingDocument, DrawingReviewResult, ReviewIssue, Point, ElementType

logger = logging.getLogger(__name__)


class DrawingVisualizer:
    """图纸可视化器"""
    
    def __init__(self):
        """初始化可视化器"""
        self.canvas_width = 800
        self.canvas_height = 600
        self.margin = 50
        
        logger.info("图纸可视化器初始化完成")
    
    def generate_review_visualization(self, review_result: DrawingReviewResult) -> str:
        """生成审查结果可视化"""
        try:
            # 生成SVG格式的可视化
            svg_content = self._generate_svg_visualization(review_result)
            return svg_content
        except Exception as e:
            logger.error(f"生成可视化失败: {e}")
            return ""
    
    def generate_interactive_viewer(self, review_result: DrawingReviewResult) -> str:
        """生成交互式查看器"""
        try:
            # 生成HTML + JavaScript交互式查看器
            html_content = self._generate_interactive_html(review_result)
            return html_content
        except Exception as e:
            logger.error(f"生成交互式查看器失败: {e}")
            return ""
    
    def _generate_svg_visualization(self, review_result: DrawingReviewResult) -> str:
        """生成SVG可视化"""
        drawing = review_result.drawing_document
        
        # 计算图纸边界
        bounds = self._calculate_drawing_bounds(drawing)
        if not bounds:
            return self._generate_empty_svg()
        
        min_point, max_point = bounds
        drawing_width = max_point.x - min_point.x
        drawing_height = max_point.y - min_point.y
        
        # 计算缩放比例
        scale_x = (self.canvas_width - 2 * self.margin) / drawing_width if drawing_width > 0 else 1
        scale_y = (self.canvas_height - 2 * self.margin) / drawing_height if drawing_height > 0 else 1
        scale = min(scale_x, scale_y)
        
        # 开始生成SVG
        svg_parts = []
        svg_parts.append(f'<svg width="{self.canvas_width}" height="{self.canvas_height}" xmlns="http://www.w3.org/2000/svg">')
        
        # 添加样式
        svg_parts.append(self._generate_svg_styles())
        
        # 添加背景
        svg_parts.append(f'<rect width="{self.canvas_width}" height="{self.canvas_height}" fill="#f8f9fa" stroke="#dee2e6"/>')
        
        # 添加图纸元素
        for element in drawing.elements:
            svg_element = self._element_to_svg(element, min_point, scale)
            if svg_element:
                svg_parts.append(svg_element)
        
        # 添加问题标记
        for issue in review_result.issues:
            if issue.location:
                marker = self._issue_to_svg_marker(issue, min_point, scale)
                svg_parts.append(marker)
        
        # 添加图例
        legend = self._generate_svg_legend(review_result)
        svg_parts.append(legend)
        
        svg_parts.append('</svg>')
        
        return '\n'.join(svg_parts)
    
    def _generate_interactive_html(self, review_result: DrawingReviewResult) -> str:
        """生成交互式HTML查看器"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>图纸审查结果查看器</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .container { display: flex; height: 90vh; }
                .drawing-panel { flex: 2; border: 1px solid #ccc; position: relative; overflow: auto; }
                .issues-panel { flex: 1; margin-left: 20px; overflow-y: auto; }
                .issue-item { padding: 10px; margin: 5px 0; border-radius: 5px; cursor: pointer; }
                .issue-critical { background-color: #ffebee; border-left: 4px solid #d32f2f; }
                .issue-error { background-color: #fff3e0; border-left: 4px solid #f57c00; }
                .issue-warning { background-color: #fffde7; border-left: 4px solid #fbc02d; }
                .issue-info { background-color: #e3f2fd; border-left: 4px solid #1976d2; }
                .issue-marker { position: absolute; width: 20px; height: 20px; border-radius: 50%; cursor: pointer; }
                .marker-critical { background-color: #d32f2f; }
                .marker-error { background-color: #f57c00; }
                .marker-warning { background-color: #fbc02d; }
                .marker-info { background-color: #1976d2; }
                .tooltip { position: absolute; background: #333; color: white; padding: 5px; border-radius: 3px; font-size: 12px; z-index: 1000; }
                .controls { margin-bottom: 10px; }
                .filter-btn { margin: 2px; padding: 5px 10px; border: 1px solid #ccc; background: white; cursor: pointer; }
                .filter-btn.active { background: #007bff; color: white; }
            </style>
        </head>
        <body>
            <h1>图纸审查结果查看器</h1>
            <div class="controls">
                <button class="filter-btn active" onclick="filterIssues('all')">全部</button>
                <button class="filter-btn" onclick="filterIssues('critical')">关键</button>
                <button class="filter-btn" onclick="filterIssues('error')">错误</button>
                <button class="filter-btn" onclick="filterIssues('warning')">警告</button>
                <button class="filter-btn" onclick="filterIssues('info')">信息</button>
            </div>
            
            <div class="container">
                <div class="drawing-panel" id="drawingPanel">
                    {svg_content}
                </div>
                
                <div class="issues-panel">
                    <h3>问题列表 ({total_issues})</h3>
                    <div id="issuesList">
                        {issues_html}
                    </div>
                </div>
            </div>
            
            <script>
                {javascript_code}
            </script>
        </body>
        </html>
        """
        
        # 生成SVG内容
        svg_content = self._generate_svg_visualization(review_result)
        
        # 生成问题列表HTML
        issues_html = ""
        for i, issue in enumerate(review_result.issues):
            severity_class = f"issue-{issue.severity.value}"
            issues_html += f"""
            <div class="issue-item {severity_class}" data-severity="{issue.severity.value}" 
                 onclick="highlightIssue('{issue.issue_id}')" id="issue-{issue.issue_id}">
                <strong>{issue.title}</strong>
                <p>{issue.description}</p>
                <small>建议: {issue.suggestion}</small>
            </div>
            """
        
        # 生成JavaScript代码
        javascript_code = self._generate_javascript_code(review_result)
        
        return html_template.format(
            svg_content=svg_content,
            issues_html=issues_html,
            total_issues=len(review_result.issues),
            javascript_code=javascript_code
        )
    
    def _generate_svg_styles(self) -> str:
        """生成SVG样式"""
        return """
        <defs>
            <style>
                .line { stroke: #333; stroke-width: 1; fill: none; }
                .circle { stroke: #333; stroke-width: 1; fill: none; }
                .text { font-family: Arial; font-size: 12px; fill: #333; }
                .issue-marker { cursor: pointer; }
                .critical-marker { fill: #d32f2f; stroke: #b71c1c; }
                .error-marker { fill: #f57c00; stroke: #e65100; }
                .warning-marker { fill: #fbc02d; stroke: #f57f17; }
                .info-marker { fill: #1976d2; stroke: #0d47a1; }
            </style>
        </defs>
        """
    
    def _generate_svg_legend(self, review_result: DrawingReviewResult) -> str:
        """生成SVG图例"""
        legend_x = self.canvas_width - 150
        legend_y = 20
        
        legend_parts = []
        legend_parts.append(f'<g id="legend">')
        legend_parts.append(f'<rect x="{legend_x-10}" y="{legend_y-10}" width="140" height="120" fill="white" stroke="#ccc" opacity="0.9"/>')
        legend_parts.append(f'<text x="{legend_x}" y="{legend_y+10}" class="text" font-weight="bold">问题图例</text>')
        
        # 统计各类问题数量
        critical_count = len(review_result.get_critical_issues())
        error_count = len(review_result.get_error_issues())
        warning_count = len(review_result.get_warning_issues())
        info_count = len([i for i in review_result.issues if i.severity.value == 'info'])
        
        y_offset = 25
        if critical_count > 0:
            legend_parts.append(f'<circle cx="{legend_x+10}" cy="{legend_y+y_offset}" r="5" class="critical-marker"/>')
            legend_parts.append(f'<text x="{legend_x+20}" y="{legend_y+y_offset+4}" class="text">关键 ({critical_count})</text>')
            y_offset += 20
        
        if error_count > 0:
            legend_parts.append(f'<circle cx="{legend_x+10}" cy="{legend_y+y_offset}" r="5" class="error-marker"/>')
            legend_parts.append(f'<text x="{legend_x+20}" y="{legend_y+y_offset+4}" class="text">错误 ({error_count})</text>')
            y_offset += 20
        
        if warning_count > 0:
            legend_parts.append(f'<circle cx="{legend_x+10}" cy="{legend_y+y_offset}" r="5" class="warning-marker"/>')
            legend_parts.append(f'<text x="{legend_x+20}" y="{legend_y+y_offset+4}" class="text">警告 ({warning_count})</text>')
            y_offset += 20
        
        if info_count > 0:
            legend_parts.append(f'<circle cx="{legend_x+10}" cy="{legend_y+y_offset}" r="5" class="info-marker"/>')
            legend_parts.append(f'<text x="{legend_x+20}" y="{legend_y+y_offset+4}" class="text">信息 ({info_count})</text>')
        
        legend_parts.append('</g>')
        
        return '\n'.join(legend_parts)
    
    def _generate_javascript_code(self, review_result: DrawingReviewResult) -> str:
        """生成JavaScript代码"""
        # 生成问题位置数据
        issues_data = []
        for issue in review_result.issues:
            if issue.location:
                issues_data.append({
                    'id': issue.issue_id,
                    'severity': issue.severity.value,
                    'title': issue.title,
                    'description': issue.description,
                    'x': issue.location.x,
                    'y': issue.location.y
                })
        
        javascript = f"""
        const issuesData = {json.dumps(issues_data)};
        
        function filterIssues(severity) {{
            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 过滤问题列表
            const issues = document.querySelectorAll('.issue-item');
            issues.forEach(issue => {{
                if (severity === 'all' || issue.dataset.severity === severity) {{
                    issue.style.display = 'block';
                }} else {{
                    issue.style.display = 'none';
                }}
            }});
            
            // 过滤SVG标记
            const markers = document.querySelectorAll('.issue-marker');
            markers.forEach(marker => {{
                if (severity === 'all' || marker.classList.contains(severity + '-marker')) {{
                    marker.style.display = 'block';
                }} else {{
                    marker.style.display = 'none';
                }}
            }});
        }}
        
        function highlightIssue(issueId) {{
            // 高亮选中的问题
            document.querySelectorAll('.issue-item').forEach(item => {{
                item.style.backgroundColor = '';
            }});
            document.getElementById('issue-' + issueId).style.backgroundColor = '#e3f2fd';
            
            // 高亮对应的标记
            const marker = document.getElementById('marker-' + issueId);
            if (marker) {{
                marker.style.transform = 'scale(1.5)';
                setTimeout(() => {{
                    marker.style.transform = 'scale(1)';
                }}, 1000);
            }}
        }}
        
        // 添加工具提示
        function showTooltip(event, text) {{
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = text;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 30 + 'px';
            document.body.appendChild(tooltip);
            
            setTimeout(() => {{
                document.body.removeChild(tooltip);
            }}, 3000);
        }}
        
        // 为SVG标记添加事件监听
        document.addEventListener('DOMContentLoaded', function() {{
            const markers = document.querySelectorAll('.issue-marker');
            markers.forEach(marker => {{
                marker.addEventListener('mouseover', function(e) {{
                    const issueId = this.id.replace('marker-', '');
                    const issue = issuesData.find(i => i.id === issueId);
                    if (issue) {{
                        showTooltip(e, issue.title + ': ' + issue.description);
                    }}
                }});
                
                marker.addEventListener('click', function() {{
                    const issueId = this.id.replace('marker-', '');
                    highlightIssue(issueId);
                    document.getElementById('issue-' + issueId).scrollIntoView({{behavior: 'smooth'}});
                }});
            }});
        }});
        """
        
        return javascript
    
    def _calculate_drawing_bounds(self, drawing: DrawingDocument) -> Optional[Tuple[Point, Point]]:
        """计算图纸边界"""
        if drawing.viewport_bounds:
            return drawing.viewport_bounds
        
        if not drawing.elements:
            return None
        
        # 计算所有元素的边界
        all_bounds = []
        for element in drawing.elements:
            try:
                bounds = element.get_bounds()
                all_bounds.append(bounds)
            except:
                continue
        
        if not all_bounds:
            return None
        
        min_x = min(bounds[0].x for bounds in all_bounds)
        min_y = min(bounds[0].y for bounds in all_bounds)
        max_x = max(bounds[1].x for bounds in all_bounds)
        max_y = max(bounds[1].y for bounds in all_bounds)
        
        return Point(min_x, min_y), Point(max_x, max_y)
    
    def _element_to_svg(self, element, min_point: Point, scale: float) -> Optional[str]:
        """将图元转换为SVG"""
        try:
            if element.element_type == ElementType.LINE:
                return self._line_to_svg(element, min_point, scale)
            elif element.element_type == ElementType.CIRCLE:
                return self._circle_to_svg(element, min_point, scale)
            elif element.element_type == ElementType.TEXT:
                return self._text_to_svg(element, min_point, scale)
            else:
                return None
        except:
            return None
    
    def _line_to_svg(self, line, min_point: Point, scale: float) -> str:
        """将直线转换为SVG"""
        x1 = (line.start_point.x - min_point.x) * scale + self.margin
        y1 = (line.start_point.y - min_point.y) * scale + self.margin
        x2 = (line.end_point.x - min_point.x) * scale + self.margin
        y2 = (line.end_point.y - min_point.y) * scale + self.margin
        
        return f'<line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" class="line"/>'
    
    def _circle_to_svg(self, circle, min_point: Point, scale: float) -> str:
        """将圆转换为SVG"""
        cx = (circle.center.x - min_point.x) * scale + self.margin
        cy = (circle.center.y - min_point.y) * scale + self.margin
        r = circle.radius * scale
        
        return f'<circle cx="{cx}" cy="{cy}" r="{r}" class="circle"/>'
    
    def _text_to_svg(self, text, min_point: Point, scale: float) -> str:
        """将文本转换为SVG"""
        x = (text.position.x - min_point.x) * scale + self.margin
        y = (text.position.y - min_point.y) * scale + self.margin

        # 转义HTML特殊字符
        content = text.content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        return f'<text x="{x}" y="{y}" class="text" font-family="Arial">{content}</text>'
    
    def _issue_to_svg_marker(self, issue: ReviewIssue, min_point: Point, scale: float) -> str:
        """将问题转换为SVG标记"""
        x = (issue.location.x - min_point.x) * scale + self.margin
        y = (issue.location.y - min_point.y) * scale + self.margin
        
        marker_class = f"{issue.severity.value}-marker"
        
        return f'<circle cx="{x}" cy="{y}" r="8" class="issue-marker {marker_class}" id="marker-{issue.issue_id}"/>'
    
    def _generate_empty_svg(self) -> str:
        """生成空的SVG"""
        return f"""
        <svg width="{self.canvas_width}" height="{self.canvas_height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="{self.canvas_width}" height="{self.canvas_height}" fill="#f8f9fa" stroke="#dee2e6"/>
            <text x="{self.canvas_width//2}" y="{self.canvas_height//2}" text-anchor="middle" 
                  font-family="Arial" font-size="16" fill="#666">无法显示图纸内容</text>
        </svg>
        """
    
    def save_visualization(self, content: str, output_path: str, format_type: str = 'html'):
        """保存可视化内容"""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"可视化文件已保存: {output_path}")
            
        except Exception as e:
            logger.error(f"保存可视化文件失败: {e}")


def create_review_dashboard(review_results: List[DrawingReviewResult]) -> str:
    """创建审查结果仪表板"""
    """为多个审查结果创建统计仪表板"""
    
    # 统计数据
    total_drawings = len(review_results)
    total_issues = sum(len(result.issues) for result in review_results)
    avg_score = sum(result.compliance_score for result in review_results) / total_drawings if total_drawings > 0 else 0
    
    # 按严重程度统计
    severity_stats = {'critical': 0, 'error': 0, 'warning': 0, 'info': 0}
    for result in review_results:
        for issue in result.issues:
            severity_stats[issue.severity.value] += 1
    
    dashboard_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>图纸审查仪表板</title>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .dashboard {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
            .card {{ border: 1px solid #ddd; border-radius: 8px; padding: 20px; }}
            .metric {{ text-align: center; margin: 10px 0; }}
            .metric-value {{ font-size: 2em; font-weight: bold; color: #007bff; }}
            .chart-container {{ width: 400px; height: 300px; margin: 0 auto; }}
        </style>
    </head>
    <body>
        <h1>图纸审查仪表板</h1>
        
        <div class="dashboard">
            <div class="card">
                <h3>总体统计</h3>
                <div class="metric">
                    <div class="metric-value">{total_drawings}</div>
                    <div>审查图纸数</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{total_issues}</div>
                    <div>发现问题数</div>
                </div>
                <div class="metric">
                    <div class="metric-value">{avg_score:.1f}</div>
                    <div>平均合规性评分</div>
                </div>
            </div>
            
            <div class="card">
                <h3>问题分布</h3>
                <div class="chart-container">
                    <canvas id="severityChart"></canvas>
                </div>
            </div>
        </div>
        
        <script>
            // 问题严重程度分布图
            const ctx = document.getElementById('severityChart').getContext('2d');
            new Chart(ctx, {{
                type: 'doughnut',
                data: {{
                    labels: ['关键', '错误', '警告', '信息'],
                    datasets: [{{
                        data: [{severity_stats['critical']}, {severity_stats['error']}, 
                               {severity_stats['warning']}, {severity_stats['info']}],
                        backgroundColor: ['#d32f2f', '#f57c00', '#fbc02d', '#1976d2']
                    }}]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false
                }}
            }});
        </script>
    </body>
    </html>
    """
    
    return dashboard_html
