#!/usr/bin/env python3
"""
调试XML解析器
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.core.parsers import ParserFactory

def main():
    """调试主函数"""
    factory = ParserFactory()
    
    # 解析示例SCD文件
    sample_scd_path = Path("tests/fixtures/sample.scd")
    
    if not sample_scd_path.exists():
        print(f"文件不存在: {sample_scd_path}")
        return 1
    
    print(f"解析文件: {sample_scd_path}")
    
    try:
        # 先尝试解析，但捕获验证错误
        parser = factory.create_parser(sample_scd_path, validate_schema=False)

        # 手动解析以便调试
        from lxml import etree
        tree = etree.parse(str(sample_scd_path))
        root = tree.getroot()

        # 检查DataTypeTemplates
        templates_elem = parser.find_child_element(root, 'DataTypeTemplates')
        if templates_elem is not None:
            print("找到DataTypeTemplates元素")

            # 检查DAType元素
            dat_elements = parser.find_child_elements(templates_elem, 'DAType')
            print(f"找到 {len(dat_elements)} 个DAType元素")

            for i, dat_elem in enumerate(dat_elements):
                dat_id = parser.get_required_attribute(dat_elem, 'id')
                bda_elements = parser.find_child_elements(dat_elem, 'BDA')
                print(f"  DAType[{i}]: {dat_id} - BDA数量: {len(bda_elements)}")

                if len(bda_elements) == 0:
                    print(f"    ❌ DAType '{dat_id}' 没有BDA元素")
                    # 打印DAType的所有子元素
                    for child in dat_elem:
                        print(f"      子元素: {child.tag}")

        result = factory.parse_file(sample_scd_path, validate_schema=False)
        
        if result.success:
            print("✓ 解析成功")
            scl_doc = result.data
            
            # 检查DataTypeTemplates
            if scl_doc.data_type_templates:
                templates = scl_doc.data_type_templates
                print(f"DataTypeTemplates:")
                print(f"  - LNodeType数量: {len(templates.lnode_types)}")
                print(f"  - DOType数量: {len(templates.do_types)}")
                print(f"  - DAType数量: {len(templates.da_types)}")
                print(f"  - EnumType数量: {len(templates.enum_types)}")
                
                # 检查每个DAType
                for i, da_type in enumerate(templates.da_types):
                    print(f"  DAType[{i}]: {da_type.name}")
                    print(f"    - BDA数量: {len(da_type.bdas)}")
                    for j, bda in enumerate(da_type.bdas):
                        print(f"      BDA[{j}]: {bda.name} ({bda.btype})")
        else:
            print("❌ 解析失败")
            for error in result.errors:
                print(f"  错误: {error}")
        
        if result.warnings:
            print("⚠️ 警告:")
            for warning in result.warnings:
                print(f"  警告: {warning}")
                
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
