#!/usr/bin/env python3
"""
高级功能演示脚本
展示配置文件对比和虚端子表生成功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def demo_comparison_features():
    """演示配置文件对比功能"""
    print("🔍 配置文件智能对比演示")
    print("=" * 50)
    
    try:
        from src.core.comparison import ConfigComparator, ComparisonReporter
        
        # 创建简单的测试对象
        class SimpleDoc:
            def __init__(self, version="2007"):
                self.version = version
                self.header = None
                self.ieds = []
                self.substation = None
        
        class SimpleHeader:
            def __init__(self, id_val, version):
                self.id = id_val
                self.version = version
        
        class SimpleIED:
            def __init__(self, name, manufacturer="", type_val=""):
                self.name = name
                self.manufacturer = manufacturer
                self.type = type_val
        
        class SimpleSubstation:
            def __init__(self, name):
                self.name = name
        
        # 创建源文档（旧版本）
        print("📄 创建源配置文档（旧版本）...")
        source_doc = SimpleDoc()
        source_doc.header = SimpleHeader("智能变电站项目_v1", "1.0")
        source_doc.ieds = [
            SimpleIED("保护装置_01", "厂商A", "线路保护"),
            SimpleIED("测控装置_01", "厂商B", "测量控制")
        ]
        source_doc.substation = SimpleSubstation("220kV智能变电站")
        
        # 创建目标文档（新版本）
        print("📄 创建目标配置文档（新版本）...")
        target_doc = SimpleDoc()
        target_doc.header = SimpleHeader("智能变电站项目_v2", "2.0")
        target_doc.ieds = [
            SimpleIED("保护装置_01", "厂商A", "线路保护"),  # 保持不变
            SimpleIED("测控装置_01", "厂商C", "测量控制"),  # 制造商变更
            SimpleIED("保护装置_02", "厂商A", "母线保护")   # 新增设备
        ]
        target_doc.substation = SimpleSubstation("220kV智能变电站")
        
        # 执行对比
        print("\n🔍 执行智能对比分析...")
        comparator = ConfigComparator()
        comparison_result = comparator.compare_documents(source_doc, target_doc)
        
        # 显示对比结果
        print(f"\n📊 对比结果摘要:")
        print(f"   • 总变更数: {len(comparison_result.changes)}")
        print(f"   • 摘要: {comparison_result.summary}")
        
        if comparison_result.changes:
            print(f"\n📋 变更详情:")
            for i, change in enumerate(comparison_result.changes[:5], 1):
                print(f"   {i}. {change.description}")
                print(f"      路径: {change.path}")
                print(f"      级别: {change.change_level.value}")
                if change.recommendation:
                    print(f"      建议: {change.recommendation}")
                print()
        
        # 生成报告
        print("📄 生成专业对比报告...")
        reporter = ComparisonReporter()
        report = reporter.generate_report(comparison_result, "智能变电站配置对比报告")
        
        # 保存报告
        report_dir = Path("demo_reports")
        report_dir.mkdir(exist_ok=True)
        
        json_path = report_dir / "comparison_demo.json"
        html_path = report_dir / "comparison_demo.html"
        
        reporter.save_report(report, str(json_path), 'json')
        reporter.save_report(report, str(html_path), 'html')
        
        print(f"   ✓ JSON报告: {json_path}")
        print(f"   ✓ HTML报告: {html_path}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 演示失败: {e}")
        return False


def demo_virtual_terminal_generator():
    """演示虚端子表生成功能"""
    print("\n🔌 虚端子表自动生成演示")
    print("=" * 50)
    
    try:
        from src.core.generators import VirtualTerminalGenerator
        
        # 创建简单的SCD测试数据
        class SimpleSCDDoc:
            def __init__(self):
                self.version = "2007"
                self.substation = None
                self.ieds = []
        
        class SimpleSubstation:
            def __init__(self, name):
                self.name = name
        
        class SimpleIED:
            def __init__(self, name, manufacturer="", type_val=""):
                self.name = name
                self.manufacturer = manufacturer
                self.type = type_val
                self.logical_devices = []
                self.access_points = []
        
        class SimpleLogicalDevice:
            def __init__(self, inst, name=""):
                self.inst = inst
                self.name = name or inst
                self.logical_nodes = []
        
        class SimpleLogicalNode:
            def __init__(self, ln_class, inst="1", desc=""):
                self.ln_class = ln_class
                self.lnclass = ln_class
                self.inst = inst
                self.desc = desc
                self.data_objects = []
        
        class SimpleDataObject:
            def __init__(self, name, fc="", cdc="", desc=""):
                self.name = name
                self.fc = fc
                self.cdc = cdc
                self.desc = desc
                self.type = ""
        
        # 创建测试SCD文档
        print("📄 创建测试SCD配置...")
        scd_doc = SimpleSCDDoc()
        scd_doc.substation = SimpleSubstation("220kV智能变电站")
        
        # 创建保护装置
        prot_ied = SimpleIED("保护装置_01", "厂商A", "线路保护")
        prot_ld = SimpleLogicalDevice("PROT")
        
        # 创建过流保护逻辑节点
        ptoc_ln = SimpleLogicalNode("PTOC", "1", "过流保护")
        ptoc_ln.data_objects = [
            SimpleDataObject("Str", "ST", "ACD", "启动状态"),
            SimpleDataObject("Op", "ST", "ACT", "动作状态"),
            SimpleDataObject("Tr", "ST", "ACT", "跳闸状态")
        ]
        
        # 创建距离保护逻辑节点
        pdis_ln = SimpleLogicalNode("PDIS", "1", "距离保护")
        pdis_ln.data_objects = [
            SimpleDataObject("Str", "ST", "ACD", "启动状态"),
            SimpleDataObject("Op", "ST", "ACT", "动作状态")
        ]
        
        prot_ld.logical_nodes = [ptoc_ln, pdis_ln]
        prot_ied.logical_devices = [prot_ld]
        
        # 创建测控装置
        ctrl_ied = SimpleIED("测控装置_01", "厂商B", "测量控制")
        ctrl_ld = SimpleLogicalDevice("CTRL")
        
        # 创建测量逻辑节点
        mmxu_ln = SimpleLogicalNode("MMXU", "1", "电气量测量")
        mmxu_ln.data_objects = [
            SimpleDataObject("A", "MX", "WYE", "电流"),
            SimpleDataObject("V", "MX", "WYE", "电压"),
            SimpleDataObject("W", "MX", "WYE", "功率")
        ]
        
        ctrl_ld.logical_nodes = [mmxu_ln]
        ctrl_ied.logical_devices = [ctrl_ld]
        
        scd_doc.ieds = [prot_ied, ctrl_ied]
        
        print(f"   ✓ 变电站: {scd_doc.substation.name}")
        print(f"   ✓ IED数量: {len(scd_doc.ieds)}")
        print(f"   ✓ 逻辑节点总数: {sum(len(ld.logical_nodes) for ied in scd_doc.ieds for ld in ied.logical_devices)}")
        
        # 生成虚端子表
        print("\n🔌 生成虚端子表...")
        generator = VirtualTerminalGenerator()
        vt_table = generator.generate_from_scd(
            scd_doc,
            project_name="220kV智能变电站虚端子表",
            options={'include_all': True}
        )
        
        print(f"   ✓ 项目名称: {vt_table.project_name}")
        print(f"   ✓ 变电站: {vt_table.substation_name}")
        print(f"   ✓ 虚端子数量: {len(vt_table.terminals)}")
        
        # 显示统计信息
        if vt_table.statistics:
            print(f"\n📊 统计信息:")
            for key, value in vt_table.statistics.items():
                if isinstance(value, dict):
                    print(f"   {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"     {sub_key}: {sub_value}")
                else:
                    print(f"   {key}: {value}")
        
        # 显示虚端子示例
        if vt_table.terminals:
            print(f"\n🔗 虚端子示例:")
            for i, terminal in enumerate(vt_table.terminals[:5], 1):
                print(f"   {i}. {terminal.ied_name}.{terminal.logical_node}.{terminal.data_object}")
                print(f"      信号类型: {terminal.signal_type}")
                print(f"      方向: {terminal.direction}")
                print(f"      描述: {terminal.description}")
        
        # 导出虚端子表
        print(f"\n📤 导出虚端子表...")
        export_dir = Path("demo_exports")
        export_dir.mkdir(exist_ok=True)
        
        json_path = export_dir / "virtual_terminals_demo.json"
        csv_path = export_dir / "virtual_terminals_demo.csv"
        
        generator.export_to_json(vt_table, str(json_path))
        generator.export_to_csv(vt_table, str(csv_path))
        
        print(f"   ✓ JSON格式: {json_path}")
        print(f"   ✓ CSV格式: {csv_path}")
        
        # 尝试导出Excel格式
        try:
            excel_path = export_dir / "virtual_terminals_demo.xlsx"
            generator.export_to_excel(vt_table, str(excel_path))
            print(f"   ✓ Excel格式: {excel_path}")
        except ImportError:
            print(f"   ⚠️ Excel导出需要pandas库，已跳过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主演示函数"""
    print("🚀 IEC61850设计检查器 - 高级功能演示")
    print("=" * 60)
    print("💡 解决智能变电站工程师的实际工作痛点：")
    print("   • 配置文件版本对比 - 快速识别关键变更")
    print("   • 虚端子表自动生成 - 告别手工制表的繁琐")
    print("   • 专业报告导出 - 标准化的工程交付文档")
    print("=" * 60)
    
    success_count = 0
    total_demos = 2
    
    # 演示配置文件对比
    if demo_comparison_features():
        success_count += 1
    
    # 演示虚端子表生成
    if demo_virtual_terminal_generator():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 演示完成: {success_count}/{total_demos} 项功能正常")
    
    if success_count == total_demos:
        print("🎉 所有高级功能演示成功！")
        print("\n💼 实际应用价值：")
        print("   ✓ 效率提升：配置对比从几小时缩短到几秒钟")
        print("   ✓ 错误减少：自动化减少人工操作错误")
        print("   ✓ 标准化：统一的文档格式和工作流程")
        print("   ✓ 专业化：符合IEC61850标准的专业工具")
        
        print("\n🌐 Web界面访问：")
        print("   • 主页: http://127.0.0.1:5000")
        print("   • 配置对比: http://127.0.0.1:5000/compare")
        print("   • 可视化分析: http://127.0.0.1:5000/visualize")
        
        return 0
    else:
        print("❌ 部分功能演示失败，请检查错误信息")
        return 1


if __name__ == '__main__':
    sys.exit(main())
