"""
Flask Web应用主程序
创建专业的IEC61850设计检查器Web界面
"""

import os
from flask import Flask, render_template
from flask_cors import CORS
import logging

from .api import api_bp
from .views import main_bp


def create_app(config=None):
    """
    创建Flask应用
    
    专为智能变电站工程师设计的专业界面：
    - 直观的文件上传和验证流程
    - 清晰的错误诊断和修复建议
    - 可视化的网络拓扑和设备配置
    - 专业的报告生成和导出功能
    """
    app = Flask(__name__)
    
    # 基础配置
    app.config.update({
        'SECRET_KEY': os.environ.get('SECRET_KEY', 'iec61850-design-checker-dev-key'),
        'MAX_CONTENT_LENGTH': 50 * 1024 * 1024,  # 50MB文件上传限制
        'UPLOAD_FOLDER': os.path.join(os.getcwd(), 'uploads'),
        'REPORTS_FOLDER': os.path.join(os.getcwd(), 'reports'),
        'DEBUG': os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    })
    
    # 应用自定义配置
    if config:
        app.config.update(config)
    
    # 确保上传和报告目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['REPORTS_FOLDER'], exist_ok=True)
    
    # 启用CORS支持
    CORS(app)
    
    # 配置日志
    if not app.debug:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s %(levelname)s: %(message)s'
        )
    
    # 注册蓝图
    app.register_blueprint(main_bp)
    app.register_blueprint(api_bp, url_prefix='/api')
    
    # 错误处理
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        return {
            'error': '文件过大',
            'message': '上传文件不能超过50MB',
            'code': 413
        }, 413
    
    # 应用上下文处理器
    @app.context_processor
    def inject_app_info():
        """注入应用信息到模板"""
        return {
            'app_name': 'IEC61850设计检查器',
            'app_version': '1.0.0',
            'app_description': '智能变电站二次设计验证与分析工具'
        }
    
    # 健康检查端点
    @app.route('/health')
    def health_check():
        """健康检查端点"""
        return {
            'status': 'healthy',
            'service': 'IEC61850 Design Checker',
            'version': '1.0.0'
        }
    
    return app


def run_app(host='127.0.0.1', port=5000, debug=True):
    """
    运行Web应用
    
    Args:
        host: 主机地址
        port: 端口号
        debug: 是否启用调试模式
    """
    app = create_app()
    
    print("=" * 60)
    print("🔧 IEC61850设计检查器 - Web界面启动")
    print("=" * 60)
    print("📋 项目使命：")
    print("   解决智能变电站二次设计的实际痛点")
    print("   • 虚端子连接可视化")
    print("   • 配置错误智能诊断") 
    print("   • 传统回路检查数字化")
    print("   • 设计质量自动保证")
    print("=" * 60)
    print(f"🌐 访问地址: http://{host}:{port}")
    print(f"📊 管理界面: http://{host}:{port}/admin")
    print(f"🔍 API文档: http://{host}:{port}/api/docs")
    print("=" * 60)
    
    try:
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")


if __name__ == '__main__':
    run_app()
