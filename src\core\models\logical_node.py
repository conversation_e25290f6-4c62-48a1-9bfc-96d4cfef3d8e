"""
IEC61850逻辑节点数据模型
定义逻辑节点、数据对象、数据属性等实体
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from enum import Enum

from .base import (
    BaseModel, NamedModel, ReferenceModel, ValidationError, IECDataType,
    validate_required, validate_range, validate_enum,
    validate_list_not_empty, validate_unique_names
)


class FunctionalConstraint(Enum):
    """功能约束枚举"""
    ST = "ST"    # Status information
    MX = "MX"    # Measurands
    CO = "CO"    # Controls
    SP = "SP"    # Setpoint
    SG = "SG"    # Setting group
    SE = "SE"    # Setting group editable
    SV = "SV"    # Substitution
    CF = "CF"    # Configuration
    DC = "DC"    # Description
    EX = "EX"    # Extended definition
    SR = "SR"    # Service response
    OR = "OR"    # Operate received
    BL = "BL"    # Blocked
    RP = "RP"    # Report
    LG = "LG"    # Log
    GO = "GO"    # GOOSE
    GS = "GS"    # GSE settings
    MS = "MS"    # Multicast sampled value settings
    US = "US"    # Unicast sampled value settings


class TriggerOption(Enum):
    """触发选项枚举"""
    DATA_CHANGE = "dchg"        # 数据变化
    QUALITY_CHANGE = "qchg"     # 品质变化
    DATA_UPDATE = "dupd"        # 数据更新
    INTEGRITY = "integrity"     # 完整性
    GENERAL_INTERROGATION = "gi" # 总召


class AccessControl(Enum):
    """访问控制枚举"""
    READ_ONLY = "ReadOnly"
    READ_WRITE = "ReadWrite"
    WRITE_ONLY = "WriteOnly"
    NO_ACCESS = "NoAccess"


@dataclass
class DataAttribute(NamedModel):
    """数据属性"""
    
    # IEC61850标准属性
    fc: str = FunctionalConstraint.ST.value  # 功能约束
    type: str = IECDataType.BOOLEAN.value    # 数据类型
    count: Optional[int] = None              # 数组大小
    val_kind: str = "Set"                    # 值类型 (Set, Conf, RO, Fix)
    val_import: bool = False                 # 值导入
    
    # 值和品质
    value: Optional[Union[str, int, float, bool]] = None
    quality: Optional[str] = None
    timestamp: Optional[str] = None
    
    # 访问控制
    access_control: str = AccessControl.READ_WRITE.value
    
    # 触发选项
    trigger_options: List[str] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证数据属性"""
        super().validate()
        
        # 验证功能约束
        try:
            FunctionalConstraint(self.fc)
        except ValueError:
            raise ValidationError(
                f"无效的功能约束: {self.fc}",
                "fc",
                self.fc
            )
        
        # 验证数据类型
        try:
            IECDataType(self.type)
        except ValueError:
            # 允许自定义类型
            if not self._is_valid_iec_name(self.type):
                raise ValidationError(
                    "数据类型必须符合IEC61850规范",
                    "type",
                    self.type
                )
        
        # 验证值类型
        valid_val_kinds = ["Set", "Conf", "RO", "Fix"]
        if self.val_kind not in valid_val_kinds:
            raise ValidationError(
                f"值类型必须是以下之一: {valid_val_kinds}",
                "val_kind",
                self.val_kind
            )
        
        # 验证访问控制
        try:
            AccessControl(self.access_control)
        except ValueError:
            raise ValidationError(
                f"无效的访问控制: {self.access_control}",
                "access_control",
                self.access_control
            )
        
        # 验证触发选项
        for trigger in self.trigger_options:
            try:
                TriggerOption(trigger)
            except ValueError:
                raise ValidationError(
                    f"无效的触发选项: {trigger}",
                    "trigger_options",
                    trigger
                )
        
        # 验证数组大小
        if self.count is not None:
            validate_range(self.count, 1, 1000, "count")
    
    def set_value(self, value: Union[str, int, float, bool], 
                  quality: Optional[str] = None,
                  timestamp: Optional[str] = None) -> None:
        """设置属性值"""
        # 类型检查
        if not self._is_valid_value_type(value):
            raise ValidationError(
                f"值类型与数据类型 {self.type} 不匹配",
                "value",
                value
            )
        
        self.value = value
        if quality is not None:
            self.quality = quality
        if timestamp is not None:
            self.timestamp = timestamp
        
        self.update_timestamp()
    
    def _is_valid_value_type(self, value: Any) -> bool:
        """检查值类型是否有效"""
        if value is None:
            return True
        
        type_mapping = {
            IECDataType.BOOLEAN.value: bool,
            IECDataType.INT8.value: int,
            IECDataType.INT16.value: int,
            IECDataType.INT32.value: int,
            IECDataType.INT64.value: int,
            IECDataType.INT8U.value: int,
            IECDataType.INT16U.value: int,
            IECDataType.INT24U.value: int,
            IECDataType.INT32U.value: int,
            IECDataType.FLOAT32.value: (int, float),
            IECDataType.FLOAT64.value: (int, float),
            IECDataType.VISIBLE_STRING.value: str,
            IECDataType.UNICODE_STRING.value: str,
            IECDataType.OCTET_STRING.value: str,
        }
        
        expected_type = type_mapping.get(self.type)
        if expected_type is None:
            return True  # 自定义类型，允许任何值
        
        return isinstance(value, expected_type)


@dataclass
class DataObject(NamedModel):
    """数据对象"""
    
    # IEC61850标准属性
    type: str = ""                           # 数据对象类型
    transient: bool = False                  # 是否为瞬态
    fc: str = ""                             # 功能约束 (Functional Constraint)
    cdc: str = ""                            # 通用数据类 (Common Data Class)
    
    # 数据属性
    data_attributes: List[DataAttribute] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证数据对象"""
        super().validate()
        validate_required(self.type, "type")
        
        # 验证数据属性名称唯一性
        if self.data_attributes:
            validate_unique_names(self.data_attributes, "data_attributes")
    
    def add_data_attribute(self, data_attribute: DataAttribute) -> None:
        """添加数据属性"""
        # 检查名称唯一性
        existing_names = [da.name for da in self.data_attributes]
        if data_attribute.name in existing_names:
            raise ValidationError(
                f"数据属性名称 '{data_attribute.name}' 已存在",
                "data_attributes"
            )
        
        self.data_attributes.append(data_attribute)
        self.update_timestamp()
    
    def remove_data_attribute(self, attribute_name: str) -> bool:
        """移除数据属性"""
        for i, da in enumerate(self.data_attributes):
            if da.name == attribute_name:
                del self.data_attributes[i]
                self.update_timestamp()
                return True
        return False
    
    def get_data_attribute(self, attribute_name: str) -> Optional[DataAttribute]:
        """获取指定数据属性"""
        for da in self.data_attributes:
            if da.name == attribute_name:
                return da
        return None
    
    def get_attributes_by_fc(self, fc: str) -> List[DataAttribute]:
        """根据功能约束获取数据属性"""
        return [da for da in self.data_attributes if da.fc == fc]
    
    def get_reference(self, ied_name: str, ld_name: str, ln_name: str) -> str:
        """获取数据对象引用"""
        return f"{ied_name}{ld_name}/{ln_name}.{self.name}"


@dataclass
class LogicalNode(NamedModel):
    """逻辑节点"""
    
    # IEC61850标准属性
    lntype: str = ""                         # 逻辑节点类型
    lninst: str = ""                         # 逻辑节点实例
    lnclass: str = ""                        # 逻辑节点类
    ln_class: str = ""                       # 逻辑节点类（别名）
    inst: str = ""                           # 实例（别名）
    prefix: str = ""                         # 前缀
    
    # 数据对象
    data_objects: List[DataObject] = field(default_factory=list)
    
    def validate(self) -> None:
        """验证逻辑节点"""
        super().validate()
        
        validate_required(self.lntype, "lntype")
        validate_required(self.lnclass, "lnclass")
        
        # 验证逻辑节点类格式 (4个字符)
        if len(self.lnclass) != 4:
            raise ValidationError(
                "逻辑节点类必须是4个字符",
                "lnclass",
                self.lnclass
            )
        
        # 验证逻辑节点类命名规范
        if not self.lnclass.isupper():
            raise ValidationError(
                "逻辑节点类必须是大写字母",
                "lnclass",
                self.lnclass
            )
        
        # 验证数据对象名称唯一性
        if self.data_objects:
            validate_unique_names(self.data_objects, "data_objects")
    
    def add_data_object(self, data_object: DataObject) -> None:
        """添加数据对象"""
        # 检查名称唯一性
        existing_names = [do.name for do in self.data_objects]
        if data_object.name in existing_names:
            raise ValidationError(
                f"数据对象名称 '{data_object.name}' 已存在",
                "data_objects"
            )
        
        self.data_objects.append(data_object)
        self.update_timestamp()
    
    def remove_data_object(self, object_name: str) -> bool:
        """移除数据对象"""
        for i, do in enumerate(self.data_objects):
            if do.name == object_name:
                del self.data_objects[i]
                self.update_timestamp()
                return True
        return False
    
    def get_data_object(self, object_name: str) -> Optional[DataObject]:
        """获取指定数据对象"""
        for do in self.data_objects:
            if do.name == object_name:
                return do
        return None
    
    def get_data_attribute(self, path: str) -> Optional[DataAttribute]:
        """根据路径获取数据属性
        
        Args:
            path: 属性路径，格式为 "data_object.data_attribute"
        
        Returns:
            数据属性对象或None
        """
        if '.' not in path:
            return None
        
        do_name, da_name = path.split('.', 1)
        do = self.get_data_object(do_name)
        if not do:
            return None
        
        return do.get_data_attribute(da_name)
    
    def get_reference(self, ied_name: str, ld_name: str) -> str:
        """获取逻辑节点引用"""
        ln_name = f"{self.prefix}{self.lnclass}{self.lninst}"
        return f"{ied_name}{ld_name}/{ln_name}"
    
    def get_full_name(self) -> str:
        """获取完整的逻辑节点名称"""
        return f"{self.prefix}{self.lnclass}{self.lninst}"
    
    def get_all_data_attributes(self) -> List[DataAttribute]:
        """获取所有数据属性"""
        attributes = []
        for do in self.data_objects:
            attributes.extend(do.data_attributes)
        return attributes
    
    def get_attributes_by_fc(self, fc: str) -> List[DataAttribute]:
        """根据功能约束获取所有数据属性"""
        attributes = []
        for do in self.data_objects:
            attributes.extend(do.get_attributes_by_fc(fc))
        return attributes
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取逻辑节点统计信息"""
        all_attributes = self.get_all_data_attributes()
        
        # 按功能约束统计
        fc_count = {}
        for attr in all_attributes:
            fc_count[attr.fc] = fc_count.get(attr.fc, 0) + 1
        
        # 按数据类型统计
        type_count = {}
        for attr in all_attributes:
            type_count[attr.type] = type_count.get(attr.type, 0) + 1
        
        return {
            "lnclass": self.lnclass,
            "lntype": self.lntype,
            "full_name": self.get_full_name(),
            "data_objects_count": len(self.data_objects),
            "data_attributes_count": len(all_attributes),
            "fc_distribution": fc_count,
            "type_distribution": type_count
        }
