"""
电力系统知识图谱
构建变电站领域的深度知识模型，支持复杂逻辑推理
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
import networkx as nx
from collections import defaultdict

logger = logging.getLogger(__name__)


class EntityType(Enum):
    """实体类型枚举"""
    IED = "IED"
    LD = "LogicalDevice"
    LN = "LogicalNode"
    DO = "DataObject"
    DA = "DataAttribute"
    DATASET = "DataSet"
    REPORT_CONTROL = "ReportControl"
    GSE_CONTROL = "GSEControl"
    SMV_CONTROL = "SampledValueControl"
    GOOSE_MESSAGE = "GOOSEMessage"
    SMV_MESSAGE = "SMVMessage"
    CIRCUIT = "Circuit"
    PROTECTION_LOGIC = "ProtectionLogic"
    # 新增：反事故措施相关实体类型
    ANTI_ACCIDENT_MEASURE = "AntiAccidentMeasure"
    SAFETY_REQUIREMENT = "SafetyRequirement"
    ACCIDENT_CASE = "AccidentCase"
    # 新增：标准相关实体类型
    STANDARD = "Standard"


class RelationType(Enum):
    """关系类型枚举"""
    # 静态关系
    IS_PART_OF = "isPartOf"
    HAS_TYPE = "hasType"
    HAS_VALUE = "hasValue"
    CONTAINS = "contains"
    REFERENCES = "references"
    
    # 通信关系
    SENDS_TO = "sendsTo"
    RECEIVES_FROM = "receivesFrom"
    SUBSCRIBES_TO = "subscribesTo"
    PUBLISHES = "publishes"
    
    # 逻辑关系
    TRIGGERS = "triggers"
    CONTROLS = "controls"
    MEASURES = "measures"
    BLOCKS = "blocks"
    ENABLES = "enables"
    DEPENDS_ON = "dependsOn"
    
    # 保护逻辑关系
    PROTECTS = "protects"
    TRIPS = "trips"
    ALARMS = "alarms"
    INTERLOCKS = "interlocks"
    BACKUP_FOR = "backupFor"
    
    # 新增：反事故措施相关关系类型
    PREVENTS = "prevents"
    COMPLIES_WITH = "compliesWith"
    VIOLATES = "violates"
    RELATED_TO_ACCIDENT = "relatedToAccident"


@dataclass
class KnowledgeEntity:
    """知识实体"""
    id: str
    type: EntityType
    name: str
    properties: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __hash__(self):
        return hash(self.id)


@dataclass
class KnowledgeRelation:
    """知识关系"""
    id: str
    type: RelationType
    source: str  # 源实体ID
    target: str  # 目标实体ID
    properties: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 1.0  # 关系置信度
    
    def __hash__(self):
        return hash(self.id)


@dataclass
class LogicalCircuit:
    """逻辑回路"""
    id: str
    name: str
    type: str  # 如"protection_trip", "alarm", "interlock"
    entities: List[str]  # 参与的实体ID列表
    relations: List[str]  # 涉及的关系ID列表
    start_entity: str  # 起始实体
    end_entity: str  # 终止实体
    is_complete: bool = False
    issues: List[str] = field(default_factory=list)
    # 新增：反事故措施相关属性
    anti_accident_measures: List[str] = field(default_factory=list)  # 相关的反事故措施
    safety_requirements: List[str] = field(default_factory=list)  # 安全要求


class PowerSystemKnowledgeGraph:
    """电力系统知识图谱"""
    
    def __init__(self):
        """初始化知识图谱"""
        self.entities: Dict[str, KnowledgeEntity] = {}
        self.relations: Dict[str, KnowledgeRelation] = {}
        self.graph = nx.MultiDiGraph()  # 使用有向多重图
        self.circuits: Dict[str, LogicalCircuit] = {}
        
        # 专业知识库
        self.ln_patterns = self._load_ln_patterns()
        self.protection_rules = self._load_protection_rules()
        self.design_patterns = self._load_design_patterns()
        # 新增：反事故措施知识库
        self.anti_accident_measures = self._load_anti_accident_measures()
        # 新增：九统一标准知识库
        self.nine_unified_standards = self._load_nine_unified_standards()
        
        logger.info("电力系统知识图谱初始化完成")
    
    def add_entity(self, entity: KnowledgeEntity) -> None:
        """添加实体到知识图谱"""
        self.entities[entity.id] = entity
        self.graph.add_node(entity.id, **{
            'type': entity.type.value,
            'name': entity.name,
            'properties': entity.properties
        })
        logger.debug(f"添加实体: {entity.name} ({entity.type.value})")
    
    def add_relation(self, relation: KnowledgeRelation) -> None:
        """添加关系到知识图谱"""
        if relation.source not in self.entities or relation.target not in self.entities:
            logger.warning(f"关系 {relation.id} 的源或目标实体不存在")
            return
        
        self.relations[relation.id] = relation
        self.graph.add_edge(
            relation.source, 
            relation.target,
            key=relation.id,
            type=relation.type.value,
            properties=relation.properties,
            confidence=relation.confidence
        )
        logger.debug(f"添加关系: {relation.type.value} ({relation.source} -> {relation.target})")
    
    def build_from_scd(self, scd_data: Dict[str, Any]) -> None:
        """从SCD数据构建知识图谱"""
        logger.info("开始从SCD数据构建知识图谱")
        
        # 提取IED实体
        ieds = scd_data.get('ieds', [])
        for ied_data in ieds:
            self._extract_ied_knowledge(ied_data)
        
        # 提取通信配置
        communication = scd_data.get('communication', {})
        if communication:
            self._extract_communication_knowledge(communication)
        
        # 构建逻辑回路
        self._build_logical_circuits()
        
        # 新增：构建反事故措施相关知识
        self._build_anti_accident_knowledge()
        
        # 新增：构建九统一标准相关知识
        self._build_nine_unified_knowledge()
        
        logger.info(f"知识图谱构建完成: {len(self.entities)} 个实体, {len(self.relations)} 个关系")
    
    def _extract_ied_knowledge(self, ied_data: Dict[str, Any]) -> None:
        """提取IED知识"""
        ied_name = ied_data.get('name', '')
        ied_type = ied_data.get('type', '')
        
        # 创建IED实体
        ied_entity = KnowledgeEntity(
            id=f"IED_{ied_name}",
            type=EntityType.IED,
            name=ied_name,
            properties={
                'type': ied_type,
                'manufacturer': ied_data.get('manufacturer', ''),
                'configVersion': ied_data.get('configVersion', '')
            }
        )
        self.add_entity(ied_entity)
        
        # 提取LD知识
        access_points = ied_data.get('AccessPoint', {})
        if isinstance(access_points, dict):
            access_points = [access_points]
        
        for ap in access_points:
            server = ap.get('Server', {})
            ldevices = server.get('LDevice', [])
            if isinstance(ldevices, dict):
                ldevices = [ldevices]
            
            for ld_data in ldevices:
                self._extract_ld_knowledge(ied_name, ld_data)
    
    def _extract_ld_knowledge(self, ied_name: str, ld_data: Dict[str, Any]) -> None:
        """提取LD知识"""
        ld_inst = ld_data.get('inst', '')
        ld_id = f"LD_{ied_name}_{ld_inst}"
        
        # 创建LD实体
        ld_entity = KnowledgeEntity(
            id=ld_id,
            type=EntityType.LD,
            name=f"{ied_name}/{ld_inst}",
            properties={
                'inst': ld_inst,
                'ldName': ld_data.get('ldName', '')
            }
        )
        self.add_entity(ld_entity)
        
        # 创建IED包含LD的关系
        ied_ld_relation = KnowledgeRelation(
            id=f"REL_{ied_name}_contains_{ld_id}",
            type=RelationType.CONTAINS,
            source=f"IED_{ied_name}",
            target=ld_id
        )
        self.add_relation(ied_ld_relation)
        
        # 提取LN知识
        lns = ld_data.get('LN', [])
        if isinstance(lns, dict):
            lns = [lns]
        
        # 处理LN0
        ln0 = ld_data.get('LN0')
        if ln0:
            self._extract_ln_knowledge(ied_name, ld_inst, ln0, is_ln0=True)
        
        # 处理其他LN
        for ln_data in lns:
            self._extract_ln_knowledge(ied_name, ld_inst, ln_data)
    
    def _extract_ln_knowledge(self, ied_name: str, ld_inst: str, ln_data: Dict[str, Any], is_ln0: bool = False) -> None:
        """提取LN知识"""
        ln_class = ln_data.get('lnClass', 'LLN0' if is_ln0 else '')
        ln_inst = ln_data.get('inst', '' if is_ln0 else '1')
        ln_id = f"LN_{ied_name}_{ld_inst}_{ln_class}{ln_inst}"
        
        # 创建LN实体
        ln_entity = KnowledgeEntity(
            id=ln_id,
            type=EntityType.LN,
            name=f"{ied_name}/{ld_inst}/{ln_class}{ln_inst}",
            properties={
                'lnClass': ln_class,
                'inst': ln_inst,
                'lnType': ln_data.get('lnType', ''),
                'desc': ln_data.get('desc', ''),
                'is_ln0': is_ln0
            }
        )
        self.add_entity(ln_entity)
        
        # 创建LD包含LN的关系
        ld_ln_relation = KnowledgeRelation(
            id=f"REL_LD_{ied_name}_{ld_inst}_contains_{ln_id}",
            type=RelationType.CONTAINS,
            source=f"LD_{ied_name}_{ld_inst}",
            target=ln_id
        )
        self.add_relation(ld_ln_relation)
        
        # 根据LN类型添加专业知识
        self._add_ln_professional_knowledge(ln_id, ln_class, ln_data)
        
        # 提取DO知识
        dois = ln_data.get('DOI', [])
        if isinstance(dois, dict):
            dois = [dois]
        
        for doi_data in dois:
            self._extract_do_knowledge(ln_id, doi_data)
    
    def _extract_do_knowledge(self, ln_id: str, doi_data: Dict[str, Any]) -> None:
        """提取DO知识"""
        do_name = doi_data.get('name', '')
        do_id = f"DO_{ln_id}_{do_name}"
        
        # 创建DO实体
        do_entity = KnowledgeEntity(
            id=do_id,
            type=EntityType.DO,
            name=f"{ln_id.split('_', 2)[2]}.{do_name}",
            properties={
                'name': do_name,
                'desc': doi_data.get('desc', '')
            }
        )
        self.add_entity(do_entity)
        
        # 创建LN包含DO的关系
        ln_do_relation = KnowledgeRelation(
            id=f"REL_{ln_id}_contains_{do_id}",
            type=RelationType.CONTAINS,
            source=ln_id,
            target=do_id
        )
        self.add_relation(ln_do_relation)
        
        # 提取DA知识
        dais = doi_data.get('DAI', [])
        if isinstance(dais, dict):
            dais = [dais]
        
        for dai_data in dais:
            self._extract_da_knowledge(do_id, dai_data)
    
    def _extract_da_knowledge(self, do_id: str, dai_data: Dict[str, Any]) -> None:
        """提取DA知识"""
        da_name = dai_data.get('name', '')
        da_id = f"DA_{do_id}_{da_name}"
        
        # 创建DA实体
        da_entity = KnowledgeEntity(
            id=da_id,
            type=EntityType.DA,
            name=f"{do_id.split('_', 3)[3]}.{da_name}",
            properties={
                'name': da_name,
                'value': dai_data.get('Val', ''),
                'desc': dai_data.get('desc', '')
            }
        )
        self.add_entity(da_entity)
        
        # 创建DO包含DA的关系
        do_da_relation = KnowledgeRelation(
            id=f"REL_{do_id}_contains_{da_id}",
            type=RelationType.CONTAINS,
            source=do_id,
            target=da_id
        )
        self.add_relation(do_da_relation)
    
    def _extract_communication_knowledge(self, communication: Dict[str, Any]) -> None:
        """提取通信知识"""
        # 提取GOOSE配置
        subnets = communication.get('SubNetwork', [])
        if isinstance(subnets, dict):
            subnets = [subnets]
        
        for subnet in subnets:
            connected_aps = subnet.get('ConnectedAP', [])
            if isinstance(connected_aps, dict):
                connected_aps = [connected_aps]
            
            for cap in connected_aps:
                self._extract_goose_knowledge(cap)
    
    def _extract_goose_knowledge(self, connected_ap: Dict[str, Any]) -> None:
        """提取GOOSE知识"""
        ied_name = connected_ap.get('iedName', '')
        
        gses = connected_ap.get('GSE', [])
        if isinstance(gses, dict):
            gses = [gses]
        
        for gse in gses:
            ld_inst = gse.get('ldInst', '')
            cb_name = gse.get('cbName', '')
            
            # 创建GOOSE消息实体
            goose_id = f"GOOSE_{ied_name}_{ld_inst}_{cb_name}"
            goose_entity = KnowledgeEntity(
                id=goose_id,
                type=EntityType.GOOSE_MESSAGE,
                name=f"{ied_name}/{ld_inst}${cb_name}",
                properties={
                    'iedName': ied_name,
                    'ldInst': ld_inst,
                    'cbName': cb_name,
                    'appID': gse.get('Address', {}).get('P', [{}])[0].get('value', ''),
                    'macAddress': self._extract_mac_address(gse.get('Address', {}))
                }
            )
            self.add_entity(goose_entity)
            
            # 创建发送关系
            sender_id = f"LN_{ied_name}_{ld_inst}_LLN0"
            if sender_id in self.entities:
                send_relation = KnowledgeRelation(
                    id=f"REL_{sender_id}_publishes_{goose_id}",
                    type=RelationType.PUBLISHES,
                    source=sender_id,
                    target=goose_id
                )
                self.add_relation(send_relation)
    
    def _add_ln_professional_knowledge(self, ln_id: str, ln_class: str, ln_data: Dict[str, Any]) -> None:
        """添加LN专业知识"""
        # 根据LN类型添加专业属性和行为
        if ln_class in self.ln_patterns:
            pattern = self.ln_patterns[ln_class]
            
            # 更新实体属性
            if ln_id in self.entities:
                self.entities[ln_id].properties.update({
                    'function': pattern.get('function', ''),
                    'typical_usage': pattern.get('typical_usage', ''),
                    'key_data_objects': pattern.get('key_data_objects', [])
                })
    
    def _build_logical_circuits(self) -> None:
        """构建逻辑回路"""
        logger.info("开始构建逻辑回路")
        
        # 构建保护跳闸回路
        self._build_protection_trip_circuits()
        
        # 构建告警回路
        self._build_alarm_circuits()
        
        # 构建联锁回路
        self._build_interlock_circuits()
        
        logger.info(f"逻辑回路构建完成: {len(self.circuits)} 个回路")
    
    def _build_protection_trip_circuits(self) -> None:
        """构建保护跳闸回路"""
        # 查找所有保护LN
        protection_lns = [
            entity_id for entity_id, entity in self.entities.items()
            if entity.type == EntityType.LN and 
            entity.properties.get('lnClass', '').startswith(('PTOC', 'PDIF', 'PDIS', 'PTRC'))
        ]
        
        for prot_ln in protection_lns:
            # 查找跳闸输出
            trip_outputs = self._find_trip_outputs(prot_ln)
            
            for trip_output in trip_outputs:
                # 追踪GOOSE发送
                goose_messages = self._find_goose_for_signal(trip_output)
                
                for goose_msg in goose_messages:
                    # 查找订阅者
                    subscribers = self._find_goose_subscribers(goose_msg)
                    
                    for subscriber in subscribers:
                        # 查找断路器控制
                        breaker_controls = self._find_breaker_controls(subscriber)
                        
                        for breaker in breaker_controls:
                            # 创建保护跳闸回路
                            circuit = LogicalCircuit(
                                id=f"CIRCUIT_TRIP_{prot_ln}_{breaker}",
                                name=f"保护跳闸回路: {prot_ln} -> {breaker}",
                                type="protection_trip",
                                entities=[prot_ln, trip_output, goose_msg, subscriber, breaker],
                                relations=[],  # 这里应该填入相关的关系ID
                                start_entity=prot_ln,
                                end_entity=breaker
                            )
                            
                            # 检查回路完整性
                            circuit.is_complete = self._check_circuit_completeness(circuit)
                            if not circuit.is_complete:
                                circuit.issues.append("回路不完整")
                            
                            self.circuits[circuit.id] = circuit
    
    def _build_alarm_circuits(self) -> None:
        """构建告警回路"""
        # 查找所有告警相关的LN
        alarm_lns = [
            entity_id for entity_id, entity in self.entities.items()
            if entity.type == EntityType.LN and 
            entity.properties.get('lnClass', '').startswith(('ALM', 'ANN'))
        ]
        
        for alarm_ln in alarm_lns:
            # 查找告警输出
            alarm_outputs = self._find_alarm_outputs(alarm_ln)
            
            for alarm_output in alarm_outputs:
                # 追踪GOOSE发送
                goose_messages = self._find_goose_for_signal(alarm_output)
                
                for goose_msg in goose_messages:
                    # 查找订阅者
                    subscribers = self._find_goose_subscribers(goose_msg)
                    
                    for subscriber in subscribers:
                        # 创建告警回路
                        circuit = LogicalCircuit(
                            id=f"CIRCUIT_ALARM_{alarm_ln}_{subscriber}",
                            name=f"告警回路: {alarm_ln} -> {subscriber}",
                            type="alarm",
                            entities=[alarm_ln, alarm_output, goose_msg, subscriber],
                            relations=[],
                            start_entity=alarm_ln,
                            end_entity=subscriber
                        )
                        
                        # 检查回路完整性
                        circuit.is_complete = self._check_circuit_completeness(circuit)
                        if not circuit.is_complete:
                            circuit.issues.append("回路不完整")
                        
                        self.circuits[circuit.id] = circuit
    
    def _build_interlock_circuits(self) -> None:
        """构建联锁回路"""
        # 查找所有联锁相关的LN
        interlock_lns = [
            entity_id for entity_id, entity in self.entities.items()
            if entity.type == EntityType.LN and 
            entity.properties.get('lnClass', '') == 'CILO'
        ]
        
        for interlock_ln in interlock_lns:
            # 查找联锁输入输出
            interlock_inputs = self._find_interlock_inputs(interlock_ln)
            interlock_outputs = self._find_interlock_outputs(interlock_ln)
            
            # 创建联锁回路
            circuit = LogicalCircuit(
                id=f"CIRCUIT_INTERLOCK_{interlock_ln}",
                name=f"联锁回路: {interlock_ln}",
                type="interlock",
                entities=[interlock_ln] + interlock_inputs + interlock_outputs,
                relations=[],
                start_entity=interlock_ln,
                end_entity=interlock_ln
            )
            
            # 检查回路完整性
            circuit.is_complete = self._check_circuit_completeness(circuit)
            if not circuit.is_complete:
                circuit.issues.append("回路不完整")
            
            self.circuits[circuit.id] = circuit
    
    def analyze_circuit_logic(self, circuit_type: str = None) -> Dict[str, Any]:
        """分析回路逻辑"""
        logger.info(f"开始分析回路逻辑: {circuit_type or '全部'}")
        
        analysis_result = {
            'total_circuits': len(self.circuits),
            'complete_circuits': 0,
            'incomplete_circuits': 0,
            'issues': [],
            'recommendations': []
        }
        
        circuits_to_analyze = self.circuits.values()
        if circuit_type:
            circuits_to_analyze = [c for c in circuits_to_analyze if c.type == circuit_type]
        
        for circuit in circuits_to_analyze:
            if circuit.is_complete:
                analysis_result['complete_circuits'] += 1
            else:
                analysis_result['incomplete_circuits'] += 1
                analysis_result['issues'].extend([
                    f"{circuit.name}: {issue}" for issue in circuit.issues
                ])
        
        # 生成建议
        if analysis_result['incomplete_circuits'] > 0:
            analysis_result['recommendations'].append(
                f"发现 {analysis_result['incomplete_circuits']} 个不完整的回路，需要检查配置"
            )
        
        return analysis_result
    
    def find_logical_path(self, start_entity: str, end_entity: str) -> List[str]:
        """查找逻辑路径"""
        try:
            path = nx.shortest_path(self.graph, start_entity, end_entity)
            return path
        except nx.NetworkXNoPath:
            return []
    
    def get_entity_neighbors(self, entity_id: str, relation_type: RelationType = None) -> List[str]:
        """获取实体的邻居"""
        if entity_id not in self.graph:
            return []
        
        neighbors = []
        for neighbor in self.graph.neighbors(entity_id):
            if relation_type:
                # 检查边的类型
                edges = self.graph.get_edge_data(entity_id, neighbor)
                for edge_data in edges.values():
                    if edge_data.get('type') == relation_type.value:
                        neighbors.append(neighbor)
                        break
            else:
                neighbors.append(neighbor)
        
        return neighbors
    
    def export_knowledge_graph(self, format: str = 'json') -> str:
        """导出知识图谱"""
        if format == 'json':
            export_data = {
                'entities': {
                    entity_id: {
                        'type': entity.type.value,
                        'name': entity.name,
                        'properties': entity.properties
                    }
                    for entity_id, entity in self.entities.items()
                },
                'relations': {
                    relation_id: {
                        'type': relation.type.value,
                        'source': relation.source,
                        'target': relation.target,
                        'properties': relation.properties,
                        'confidence': relation.confidence
                    }
                    for relation_id, relation in self.relations.items()
                },
                'circuits': {
                    circuit_id: {
                        'name': circuit.name,
                        'type': circuit.type,
                        'entities': circuit.entities,
                        'is_complete': circuit.is_complete,
                        'issues': circuit.issues
                    }
                    for circuit_id, circuit in self.circuits.items()
                }
            }
            return json.dumps(export_data, ensure_ascii=False, indent=2)
        
        return ""
    
    # 辅助方法
    def _load_ln_patterns(self) -> Dict[str, Any]:
        """加载LN模式库"""
        return {
            'PTOC': {
                'function': '过电流保护',
                'typical_usage': '线路、变压器过流保护',
                'key_data_objects': ['Str', 'Op', 'TmASt'],
                'typical_connections': {
                    'input': ['TCTR.Amp'],
                    'output': ['XCBR.Pos', 'PTRC.Tr']
                }
            },
            'XCBR': {
                'function': '断路器',
                'typical_usage': '开关设备控制',
                'key_data_objects': ['Pos', 'BlkOpn', 'BlkCls'],
                'typical_connections': {
                    'input': ['PTOC.Op', 'CSWI.Pos'],
                    'output': ['CILO.EnaOpn', 'CILO.EnaCls']
                }
            },
            'CSWI': {
                'function': '控制开关',
                'typical_usage': '远程控制接口',
                'key_data_objects': ['Pos'],
                'typical_connections': {
                    'input': ['HMI.Cmd'],
                    'output': ['XCBR.Pos']
                }
            },
            'MMXU': {
                'function': '测量',
                'typical_usage': '电气量测量',
                'key_data_objects': ['A', 'PhV', 'W', 'VAr'],
                'typical_connections': {
                    'input': ['TCTR.Amp', 'TVTR.Vol'],
                    'output': ['HMI.Display', 'PTOC.Setting']
                }
            }
        }
    
    def _load_protection_rules(self) -> Dict[str, Any]:
        """加载保护规则库"""
        return {
            'trip_circuit_rules': [
                {
                    'rule_id': 'TRIP_001',
                    'description': '保护跳闸信号必须通过GOOSE发送',
                    'pattern': 'PTOC.Op -> GOOSE -> XCBR.Pos',
                    'mandatory': True
                },
                {
                    'rule_id': 'TRIP_002', 
                    'description': 'GOOSE跳闸信号必须有质量位',
                    'pattern': 'GOOSE.DataSet must include quality bit',
                    'mandatory': True
                }
            ],
            'timing_rules': [
                {
                    'rule_id': 'TIME_001',
                    'description': 'GOOSE Type1A消息传输时间≤4ms',
                    'constraint': 'GOOSE.MinTime <= 4ms',
                    'mandatory': True
                }
            ]
        }
    
    def _load_design_patterns(self) -> Dict[str, Any]:
        """加载设计模式库"""
        return {
            'protection_patterns': {
                'line_protection': {
                    'required_lns': ['PTOC', 'PDIS', 'XCBR'],
                    'typical_logic': 'PTOC.Op OR PDIS.Op -> XCBR.Pos',
                    'backup_logic': 'PTOC.Str -> Timer -> PTRC.Tr'
                },
                'transformer_protection': {
                    'required_lns': ['PDIF', 'PTOC', 'XCBR'],
                    'typical_logic': 'PDIF.Op -> XCBR.Pos',
                    'gas_protection': 'RBDR.Op -> XCBR.Pos'
                }
            }
        }
    
    def _load_anti_accident_measures(self) -> Dict[str, Any]:
        """加载反事故措施知识库"""
        return {
            'protection_accident_prevention': {
                'measure_id': 'AAP001',
                'name': '防止继电保护事故',
                'description': '防止继电保护误动、拒动等事故的技术和管理措施',
                'related_standards': ['StateGrid-Anti-Accident-18', 'GB/T 14285-2023'],
                'key_points': [
                    '保护配置双重化',
                    '二次回路寄生回路检查',
                    '保护定值定期核对',
                    '保护装置定期检验'
                ]
            },
            'switchgear_accident_prevention': {
                'measure_id': 'AAP002',
                'name': '防止开关设备事故',
                'description': '防止断路器、隔离开关等开关设备事故的技术和管理措施',
                'related_standards': ['StateGrid-Anti-Accident-18'],
                'key_points': [
                    '断路器开断容量校核',
                    '隔离开关机械强度检查',
                    '防误闭锁装置完善',
                    '操作票制度严格执行'
                ]
            },
            'transformer_accident_prevention': {
                'measure_id': 'AAP003',
                'name': '防止大型变压器损坏事故',
                'description': '防止变压器绝缘损坏、冷却系统故障等事故的技术和管理措施',
                'related_standards': ['StateGrid-Anti-Accident-18'],
                'key_points': [
                    '变压器绝缘水平校核',
                    '冷却系统冗余配置',
                    '油色谱定期监测',
                    '定期进行状态评估'
                ]
            }
        }
    
    def _load_nine_unified_standards(self) -> Dict[str, Any]:
        """加载九统一标准知识库"""
        return {
            'line_protection_nine_unified': {
                'standard_id': 'NineUnified001',
                'name': '线路保护九统一标准',
                'description': 'Q/GDW 1161-2014线路保护及辅助装置标准化设计规范',
                'related_documents': ['Q/GDW 1161-2014'],
                'key_principles': [
                    '功能配置统一',
                    '端子排布置统一',
                    '原理接线统一',
                    '接口标准统一',
                    '屏柜压板统一',
                    '保护定值统一',
                    '报告格式统一',
                    '图形符号统一',
                    '模型规范统一'
                ],
                'technical_requirements': [
                    '保护功能配置标准化',
                    '二次回路设计规范化',
                    '通信接口标准化',
                    '人机接口统一化'
                ]
            },
            'transformer_protection_nine_unified': {
                'standard_id': 'NineUnified002',
                'name': '变压器保护九统一标准',
                'description': 'Q/GDW 1175-2013变压器、高压并联电抗器和母线保护及辅助装置标准化设计规范',
                'related_documents': ['Q/GDW 1175-2013'],
                'key_principles': [
                    '功能配置统一',
                    '端子排布置统一',
                    '原理接线统一',
                    '接口标准统一',
                    '屏柜压板统一',
                    '保护定值统一',
                    '报告格式统一',
                    '图形符号统一',
                    '模型规范统一'
                ],
                'technical_requirements': [
                    '变压器保护功能配置标准化',
                    '高压并联电抗器保护功能配置标准化',
                    '母线保护功能配置标准化',
                    '二次回路设计规范化'
                ]
            }
        }
    
    def _build_anti_accident_knowledge(self) -> None:
        """构建反事故措施相关知识"""
        logger.info("开始构建反事故措施相关知识")
        
        # 添加反事故措施实体
        for measure_key, measure_data in self.anti_accident_measures.items():
            measure_entity = KnowledgeEntity(
                id=f"MEASURE_{measure_data['measure_id']}",
                type=EntityType.ANTI_ACCIDENT_MEASURE,
                name=measure_data['name'],
                properties={
                    'description': measure_data['description'],
                    'related_standards': measure_data['related_standards'],
                    'key_points': measure_data['key_points']
                }
            )
            self.add_entity(measure_entity)
        
        # 建立回路与反事故措施的关系
        for circuit_id, circuit in self.circuits.items():
            self._link_circuit_to_anti_accident_measures(circuit)
        
        logger.info("反事故措施相关知识构建完成")
    
    def _build_nine_unified_knowledge(self) -> None:
        """构建九统一标准相关知识"""
        logger.info("开始构建九统一标准相关知识")
        
        # 添加九统一标准实体
        for standard_key, standard_data in self.nine_unified_standards.items():
            standard_entity = KnowledgeEntity(
                id=f"STANDARD_{standard_data['standard_id']}",
                type=EntityType.STANDARD,
                name=standard_data['name'],
                properties={
                    'description': standard_data['description'],
                    'related_documents': standard_data['related_documents'],
                    'key_principles': standard_data['key_principles'],
                    'technical_requirements': standard_data['technical_requirements']
                }
            )
            self.add_entity(standard_entity)
        
        # 建立保护装置与九统一标准的关系
        protection_entities = [entity_id for entity_id, entity in self.entities.items() 
                              if entity.type == EntityType.LN and 
                              entity.properties.get('lnClass', '').startswith(('P', 'R'))]
        
        for entity_id in protection_entities:
            # 关联线路保护九统一标准
            if 'line' in self.entities[entity_id].name.lower():
                standard_id = "STANDARD_NineUnified001"
                if standard_id in self.entities:
                    relation = KnowledgeRelation(
                        id=f"REL_{entity_id}_complies_with_{standard_id}",
                        type=RelationType.COMPLIES_WITH,
                        source=entity_id,
                        target=standard_id,
                        confidence=0.9
                    )
                    self.add_relation(relation)
            
            # 关联变压器保护九统一标准
            if 'transformer' in self.entities[entity_id].name.lower():
                standard_id = "STANDARD_NineUnified002"
                if standard_id in self.entities:
                    relation = KnowledgeRelation(
                        id=f"REL_{entity_id}_complies_with_{standard_id}",
                        type=RelationType.COMPLIES_WITH,
                        source=entity_id,
                        target=standard_id,
                        confidence=0.9
                    )
                    self.add_relation(relation)
        
        logger.info("九统一标准相关知识构建完成")
    
    def _link_circuit_to_anti_accident_measures(self, circuit: LogicalCircuit) -> None:
        """建立回路与反事故措施的关系"""
        # 根据回路类型关联相应的反事故措施
        if circuit.type == "protection_trip":
            # 关联保护反事故措施
            measure_id = "MEASURE_AAP001"
            if measure_id in self.entities:
                # 添加防止关系
                relation = KnowledgeRelation(
                    id=f"REL_{circuit.id}_prevents_{measure_id}",
                    type=RelationType.PREVENTS,
                    source=circuit.id,
                    target=measure_id,
                    confidence=0.9
                )
                self.add_relation(relation)
                
                # 更新回路信息
                circuit.anti_accident_measures.append(measure_id)
        
        elif circuit.type == "control":
            # 关联开关设备反事故措施
            measure_id = "MEASURE_AAP002"
            if measure_id in self.entities:
                relation = KnowledgeRelation(
                    id=f"REL_{circuit.id}_prevents_{measure_id}",
                    type=RelationType.PREVENTS,
                    source=circuit.id,
                    target=measure_id,
                    confidence=0.8
                )
                self.add_relation(relation)
                
                # 更新回路信息
                circuit.anti_accident_measures.append(measure_id)
    
    def analyze_circuit_safety(self, circuit_type: str = None) -> Dict[str, Any]:
        """分析回路安全性（结合反事故措施）"""
        logger.info(f"开始分析回路安全性: {circuit_type or '全部'}")
        
        safety_analysis = {
            'total_circuits': len(self.circuits),
            'safety_compliant_circuits': 0,
            'safety_violation_circuits': 0,
            'anti_accident_measures_coverage': 0,
            'issues': [],
            'recommendations': []
        }
        
        circuits_to_analyze = self.circuits.values()
        if circuit_type:
            circuits_to_analyze = [c for c in circuits_to_analyze if c.type == circuit_type]
        
        total_anti_accident_measures = 0
        covered_anti_accident_measures = 0
        
        for circuit in circuits_to_analyze:
            # 检查是否关联了反事故措施
            if circuit.anti_accident_measures:
                safety_analysis['safety_compliant_circuits'] += 1
                covered_anti_accident_measures += len(circuit.anti_accident_measures)
            else:
                safety_analysis['safety_violation_circuits'] += 1
                safety_analysis['issues'].append(f"回路 {circuit.name} 未关联反事故措施")
            
            total_anti_accident_measures += len(circuit.anti_accident_measures)
        
        # 计算反事故措施覆盖率
        if total_anti_accident_measures > 0:
            safety_analysis['anti_accident_measures_coverage'] = (
                covered_anti_accident_measures / total_anti_accident_measures * 100
            )
        
        # 生成安全建议
        if safety_analysis['safety_violation_circuits'] > 0:
            safety_analysis['recommendations'].append(
                f"发现 {safety_analysis['safety_violation_circuits']} 个回路未关联反事故措施，需要完善"
            )
        
        return safety_analysis
    
    def get_anti_accident_recommendations(self, circuit_id: str) -> List[Dict[str, Any]]:
        """获取针对特定回路的反事故措施建议"""
        recommendations = []
        
        if circuit_id not in self.circuits:
            return recommendations
        
        circuit = self.circuits[circuit_id]
        
        # 根据回路类型提供反事故措施建议
        if circuit.type == "protection_trip":
            recommendations.append({
                'measure': '防止继电保护事故',
                'details': '确保保护配置双重化，检查二次回路是否存在寄生回路',
                'priority': 'high'
            })
            recommendations.append({
                'measure': '保护定值管理',
                'details': '定期核对保护定值，确保定值正确',
                'priority': 'medium'
            })
        elif circuit.type == "control":
            recommendations.append({
                'measure': '防止开关设备事故',
                'details': '完善防误闭锁装置，严格执行操作票制度',
                'priority': 'high'
            })
        elif circuit.type == "measurement":
            recommendations.append({
                'measure': '防止互感器事故',
                'details': '定期检查互感器二次回路，防止开路或短路',
                'priority': 'medium'
            })
        
        return recommendations
    
    def _extract_mac_address(self, address: Dict[str, Any]) -> str:
        """提取MAC地址"""
        p_elements = address.get('P', [])
        for p in p_elements:
            if p.get('type') == 'MAC-Address':
                return p.get('value', '')
        return ''
    
    def _find_trip_outputs(self, ln_id: str) -> List[str]:
        """查找跳闸输出"""
        # 简化实现，实际应该查找Op、Tr等跳闸相关的DO
        trip_outputs = []
        for entity_id, entity in self.entities.items():
            if (entity.type == EntityType.DO and 
                entity_id.startswith(f"DO_{ln_id}") and
                entity.properties.get('name') in ['Op', 'Tr']):
                trip_outputs.append(entity_id)
        return trip_outputs
    
    def _find_goose_for_signal(self, signal_id: str) -> List[str]:
        """查找信号对应的GOOSE消息"""
        # 简化实现，实际应该通过DataSet关联查找
        goose_messages = []
        for entity_id, entity in self.entities.items():
            if entity.type == EntityType.GOOSE_MESSAGE:
                goose_messages.append(entity_id)
        return goose_messages
    
    def _find_goose_subscribers(self, goose_id: str) -> List[str]:
        """查找GOOSE订阅者"""
        # 简化实现，实际应该通过Inputs配置查找
        subscribers = []
        # 这里需要实现复杂的订阅关系查找逻辑
        return subscribers
    
    def _find_breaker_controls(self, subscriber_id: str) -> List[str]:
        """查找断路器控制"""
        breaker_controls = []
        for entity_id, entity in self.entities.items():
            if (entity.type == EntityType.LN and 
                entity.properties.get('lnClass') == 'XCBR'):
                breaker_controls.append(entity_id)
        return breaker_controls
    
    def _check_circuit_completeness(self, circuit: LogicalCircuit) -> bool:
        """检查回路完整性"""
        # 简化实现，实际应该检查所有必要的连接
        return len(circuit.entities) >= 3  # 至少包含保护、GOOSE、断路器
    
    def _find_alarm_outputs(self, ln_id: str) -> List[str]:
        """查找告警输出"""
        # 简化实现，实际应该查找Alarm相关的DO
        alarm_outputs = []
        for entity_id, entity in self.entities.items():
            if (entity.type == EntityType.DO and 
                entity_id.startswith(f"DO_{ln_id}") and
                entity.properties.get('name') in ['Alm', 'Ind']):
                alarm_outputs.append(entity_id)
        return alarm_outputs
    
    def _find_interlock_inputs(self, ln_id: str) -> List[str]:
        """查找联锁输入"""
        interlock_inputs = []
        for entity_id, entity in self.entities.items():
            if (entity.type == EntityType.DO and 
                entity_id.startswith(f"DO_{ln_id}") and
                entity.properties.get('name') in ['EnaOpn', 'EnaCls']):
                interlock_inputs.append(entity_id)
        return interlock_inputs
    
    def _find_interlock_outputs(self, ln_id: str) -> List[str]:
        """查找联锁输出"""
        interlock_outputs = []
        for entity_id, entity in self.entities.items():
            if (entity.type == EntityType.DO and 
                entity_id.startswith(f"DO_{ln_id}") and
                entity.properties.get('name') in ['EnaOpn', 'EnaCls']):
                interlock_outputs.append(entity_id)
        return interlock_outputs
    
    def analyze_circuit_relationships(self) -> Dict[str, Any]:
        """分析回路间的逻辑关系"""
        relationship_analysis = {
            'inter_circuit_relationships': [],
            'shared_entities': [],
            'conflicting_relationships': [],
            'redundant_relationships': []
        }
        
        # 查找共享实体
        entity_usage = defaultdict(list)
        for circuit_id, circuit in self.circuits.items():
            for entity_id in circuit.entities:
                entity_usage[entity_id].append(circuit_id)
        
        # 识别共享实体（被多个回路使用）
        shared_entities = {entity_id: circuits for entity_id, circuits in entity_usage.items() if len(circuits) > 1}
        relationship_analysis['shared_entities'] = shared_entities
        
        # 分析回路间的关系
        circuit_ids = list(self.circuits.keys())
        for i, circuit_id1 in enumerate(circuit_ids):
            for circuit_id2 in circuit_ids[i+1:]:
                circuit1 = self.circuits[circuit_id1]
                circuit2 = self.circuits[circuit_id2]
                
                # 查找共同实体
                common_entities = set(circuit1.entities) & set(circuit2.entities)
                if common_entities:
                    relationship = {
                        'circuit1': circuit_id1,
                        'circuit2': circuit_id2,
                        'common_entities': list(common_entities),
                        'relationship_type': self._determine_relationship_type(circuit1, circuit2, common_entities)
                    }
                    relationship_analysis['inter_circuit_relationships'].append(relationship)
        
        return relationship_analysis
    
    def _determine_relationship_type(self, circuit1: LogicalCircuit, circuit2: LogicalCircuit, common_entities: Set[str]) -> str:
        """确定回路间关系类型"""
        # 检查是否为依赖关系（一个回路的输出是另一个回路的输入）
        if circuit1.end_entity in circuit2.entities:
            return "dependency"
        elif circuit2.end_entity in circuit1.entities:
            return "dependency"
        
        # 检查是否为并行关系（共享输入但输出不同）
        if circuit1.start_entity == circuit2.start_entity and circuit1.end_entity != circuit2.end_entity:
            return "parallel"
        
        # 检查是否为串行关系（一个回路的输出是另一个回路的输入）
        if circuit1.end_entity == circuit2.start_entity:
            return "serial"
        elif circuit2.end_entity == circuit1.start_entity:
            return "serial"
        
        # 默认为共享关系
        return "shared"
    
    def validate_circuit_logic(self, circuit_id: str) -> List[Dict[str, Any]]:
        """验证特定回路的逻辑正确性"""
        validation_issues = []
        
        if circuit_id not in self.circuits:
            validation_issues.append({
                'type': 'error',
                'description': f'回路 {circuit_id} 不存在',
                'severity': 'critical'
            })
            return validation_issues
        
        circuit = self.circuits[circuit_id]
        
        # 验证回路完整性
        if not circuit.is_complete:
            validation_issues.append({
                'type': 'error',
                'description': '回路不完整',
                'severity': 'error',
                'details': circuit.issues
            })
        
        # 验证逻辑节点顺序
        logic_validation = self._validate_logic_node_sequence(circuit)
        validation_issues.extend(logic_validation)
        
        # 验证通信路径
        communication_validation = self._validate_communication_path(circuit)
        validation_issues.extend(communication_validation)
        
        return validation_issues
    
    def _validate_logic_node_sequence(self, circuit: LogicalCircuit) -> List[Dict[str, Any]]:
        """验证逻辑节点顺序"""
        issues = []
        
        # 获取回路中的逻辑节点
        ln_entities = [entity_id for entity_id in circuit.entities 
                      if entity_id in self.entities and self.entities[entity_id].type == EntityType.LN]
        
        if len(ln_entities) < 2:
            return issues
        
        # 验证逻辑顺序（例如保护->跳闸）
        expected_sequence = {
            'PTOC': ['PTRC', 'XCBR'],  # 过流保护应该连接到跳闸逻辑和断路器
            'PDIF': ['PTRC', 'XCBR'],  # 差动保护应该连接到跳闸逻辑和断路器
            'PTRC': ['XCBR']           # 跳闸逻辑应该连接到断路器
        }
        
        for i in range(len(ln_entities) - 1):
            current_ln = self.entities[ln_entities[i]]
            next_ln = self.entities[ln_entities[i+1]]
            
            current_class = current_ln.properties.get('lnClass', '')
            next_class = next_ln.properties.get('lnClass', '')
            
            if current_class in expected_sequence:
                expected_next = expected_sequence[current_class]
                if next_class not in expected_next:
                    issues.append({
                        'type': 'warning',
                        'description': f'逻辑节点顺序异常: {current_class} 后面不应该是 {next_class}',
                        'severity': 'warning',
                        'details': f'期望的后续节点: {expected_next}'
                    })
        
        return issues
    
    def _validate_communication_path(self, circuit: LogicalCircuit) -> List[Dict[str, Any]]:
        """验证通信路径"""
        issues = []
        
        # 检查GOOSE消息是否正确配置
        goose_entities = [entity_id for entity_id in circuit.entities 
                         if entity_id in self.entities and self.entities[entity_id].type == EntityType.GOOSE_MESSAGE]
        
        for goose_id in goose_entities:
            goose_entity = self.entities[goose_id]
            
            # 检查GOOSE消息是否有发布者
            publisher_relations = [rel_id for rel_id, rel in self.relations.items() 
                                 if rel.type == RelationType.PUBLISHES and rel.target == goose_id]
            
            if not publisher_relations:
                issues.append({
                    'type': 'error',
                    'description': f'GOOSE消息 {goose_entity.name} 缺少发布者',
                    'severity': 'error'
                })
            
            # 检查GOOSE消息是否有订阅者
            subscriber_relations = [rel_id for rel_id, rel in self.relations.items() 
                                  if rel.type == RelationType.SUBSCRIBES_TO and rel.target == goose_id]
            
            if not subscriber_relations:
                issues.append({
                    'type': 'warning',
                    'description': f'GOOSE消息 {goose_entity.name} 缺少订阅者',
                    'severity': 'warning'
                })
        
        return issues


def main():
    """主函数 - 演示知识图谱功能"""
    print("电力系统知识图谱演示")
    print("=" * 50)
    
    # 创建知识图谱
    kg = PowerSystemKnowledgeGraph()
    
    # 模拟SCD数据
    test_scd_data = {
        'ieds': [
            {
                'name': 'PROT_IED_001',
                'type': 'Protection',
                'manufacturer': 'TestManufacturer',
                'AccessPoint': {
                    'name': 'AP1',
                    'Server': {
                        'LDevice': {
                            'inst': 'LD1',
                            'LN0': {
                                'lnClass': 'LLN0',
                                'inst': ''
                            },
                            'LN': [
                                {
                                    'lnClass': 'PTOC',
                                    'inst': '1',
                                    'DOI': [
                                        {
                                            'name': 'Str',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        },
                                        {
                                            'name': 'Op',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            }
        ],
        'communication': {
            'SubNetwork': {
                'name': 'SubNet1',
                'ConnectedAP': {
                    'iedName': 'PROT_IED_001',
                    'GSE': {
                        'ldInst': 'LD1',
                        'cbName': 'GC1',
                        'Address': {
                            'P': [
                                {'type': 'MAC-Address', 'value': '01-0C-CD-01-00-01'},
                                {'type': 'APPID', 'value': '0001'}
                            ]
                        }
                    }
                }
            }
        }
    }
    
    # 构建知识图谱
    kg.build_from_scd(test_scd_data)
    
    print(f"知识图谱构建完成:")
    print(f"  实体数量: {len(kg.entities)}")
    print(f"  关系数量: {len(kg.relations)}")
    print(f"  回路数量: {len(kg.circuits)}")
    
    # 分析回路逻辑
    analysis = kg.analyze_circuit_logic()
    print(f"\n回路逻辑分析:")
    print(f"  总回路数: {analysis['total_circuits']}")
    print(f"  完整回路: {analysis['complete_circuits']}")
    print(f"  不完整回路: {analysis['incomplete_circuits']}")
    
    if analysis['issues']:
        print(f"  发现问题:")
        for issue in analysis['issues'][:3]:
            print(f"    - {issue}")
    
    print("\n知识图谱演示完成！")


if __name__ == "__main__":
    main()