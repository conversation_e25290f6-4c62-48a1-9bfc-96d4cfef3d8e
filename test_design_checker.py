#!/usr/bin/env python3
"""
测试设计检查器
"""

import xml.etree.ElementTree as ET
import os

def test_design_checker():
    """测试设计检查器"""
    
    print("🔍 设计问题检查演示")
    print("=" * 50)
    
    # 检查配置文件是否存在
    config_file = "test_project/problematic_substation.scd"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    print(f"✅ 找到配置文件: {config_file}")
    
    try:
        # 解析XML文件
        tree = ET.parse(config_file)
        root = tree.getroot()
        print("✅ XML文件解析成功")
        
        # 检查断路器Terminal连接
        issues = []
        breakers = root.findall(".//ConductingEquipment[@type='CBR']")
        print(f"📊 找到 {len(breakers)} 个断路器")
        
        for breaker in breakers:
            breaker_name = breaker.get('name', '未知断路器')
            terminals = breaker.findall("Terminal")
            
            print(f"   • 断路器 {breaker_name}: {len(terminals)} 个Terminal")
            
            if len(terminals) == 0:
                issue = {
                    'title': f"断路器{breaker_name}缺少Terminal连接",
                    'description': f"断路器{breaker_name}没有定义任何Terminal连接，导致回路不通",
                    'severity': "critical",
                    'category': "回路连接",
                    'suggestion': f"为断路器{breaker_name}添加至少两个Terminal连接"
                }
                issues.append(issue)
        
        # 检查电流互感器
        current_transformers = root.findall(".//ConductingEquipment[@type='CTR']")
        print(f"📊 找到 {len(current_transformers)} 个电流互感器")
        
        for ct in current_transformers:
            ct_name = ct.get('name', '未知电流互感器')
            terminals = ct.findall("Terminal")
            
            print(f"   • 电流互感器 {ct_name}: {len(terminals)} 个Terminal")
            
            if len(terminals) < 2:
                issue = {
                    'title': f"电流互感器{ct_name}缺少二次侧连接",
                    'description': f"电流互感器{ct_name}缺少二次侧Terminal连接",
                    'severity': "error",
                    'category': "虚端子连接",
                    'suggestion': f"为电流互感器{ct_name}添加二次侧Terminal连接"
                }
                issues.append(issue)
        
        # 检查IP地址冲突
        ip_addresses = {}
        connected_aps = root.findall(".//ConnectedAP")
        print(f"📊 找到 {len(connected_aps)} 个网络连接点")
        
        for ap in connected_aps:
            ied_name = ap.get('iedName', '未知IED')
            address = ap.find("Address")
            if address is not None:
                ip_elem = address.find("P[@type='IP']")
                if ip_elem is not None:
                    ip_addr = ip_elem.text
                    print(f"   • IED {ied_name}: IP地址 {ip_addr}")
                    
                    if ip_addr in ip_addresses:
                        issue = {
                            'title': f"IP地址冲突: {ip_addr}",
                            'description': f"IP地址{ip_addr}被多个IED使用: {ip_addresses[ip_addr]} 和 {ied_name}",
                            'severity': "critical",
                            'category': "通信配置",
                            'suggestion': f"为IED {ied_name}分配唯一的IP地址"
                        }
                        issues.append(issue)
                    else:
                        ip_addresses[ip_addr] = ied_name
        
        # 检查数据集引用
        datasets = root.findall(".//DataSet")
        print(f"📊 找到 {len(datasets)} 个数据集")
        
        for dataset in datasets:
            dataset_name = dataset.get('name', '未知数据集')
            fcdas = dataset.findall("FCDA")
            print(f"   • 数据集 {dataset_name}: {len(fcdas)} 个FCDA引用")
            
            for fcda in fcdas:
                ln_class = fcda.get('lnClass')
                ln_inst = fcda.get('lnInst')
                
                # 查找对应的逻辑节点
                ln_xpath = f".//LN[@lnClass='{ln_class}'][@inst='{ln_inst}']"
                logical_nodes = root.findall(ln_xpath)
                
                if not logical_nodes:
                    issue = {
                        'title': f"数据集{dataset_name}引用不存在的逻辑节点",
                        'description': f"数据集{dataset_name}中引用了不存在的逻辑节点: {ln_class}{ln_inst}",
                        'severity': "error",
                        'category': "数据集配置",
                        'suggestion': f"检查逻辑节点{ln_class}{ln_inst}是否正确定义"
                    }
                    issues.append(issue)
        
        # 显示检查结果
        print("\n🔍 设计问题检查结果:")
        print("=" * 50)
        
        if issues:
            print(f"📊 发现 {len(issues)} 个设计问题:")
            
            for i, issue in enumerate(issues, 1):
                severity_icon = {
                    'critical': '🔴',
                    'error': '🟠', 
                    'warning': '🟡',
                    'info': '🔵'
                }.get(issue.get('severity', 'info'), '⚪')
                
                print(f"\n{i}. {severity_icon} [{issue.get('severity', 'info').upper()}] {issue.get('title', '未知问题')}")
                print(f"   📝 描述: {issue.get('description', '无描述')}")
                print(f"   💡 建议: {issue.get('suggestion', '无建议')}")
                print(f"   📋 类别: {issue.get('category', '未知类别')}")
        else:
            print("✅ 未发现设计问题")
        
        print("\n🎯 检查总结:")
        print("=" * 50)
        print("✅ 成功检查了以下设计要素:")
        print("   • 断路器Terminal连接完整性")
        print("   • 电流互感器二次侧连接")
        print("   • 通信网络IP地址冲突")
        print("   • 数据集逻辑节点引用完整性")
        
        print("\n💡 这些检查能够发现:")
        print("   • 回路不通的问题")
        print("   • 虚端子连接缺失")
        print("   • 网络配置冲突")
        print("   • 数据引用错误")
        
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_design_checker()
