"""
逻辑设备模型
简化的逻辑设备定义，用于测试和基本功能
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any

from .base import BaseModel


@dataclass
class LogicalDevice(BaseModel):
    """
    逻辑设备
    简化版本，用于测试和基本功能
    """
    
    # 基本属性
    inst: str = ""                          # 实例名
    name: str = ""                          # 设备名称
    desc: str = ""                          # 描述
    
    # 逻辑节点
    logical_nodes: List['LogicalNode'] = field(default_factory=list)
    
    def add_logical_node(self, ln: 'LogicalNode'):
        """添加逻辑节点"""
        if ln not in self.logical_nodes:
            self.logical_nodes.append(ln)
    
    def get_logical_node_by_class(self, ln_class: str) -> Optional['LogicalNode']:
        """根据类获取逻辑节点"""
        for ln in self.logical_nodes:
            if getattr(ln, 'ln_class', '') == ln_class or getattr(ln, 'lnclass', '') == ln_class:
                return ln
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'inst': self.inst,
            'name': self.name,
            'desc': self.desc,
            'logical_nodes': [ln.to_dict() for ln in self.logical_nodes]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LogicalDevice':
        """从字典创建逻辑设备"""
        ld = cls()
        ld.inst = data.get('inst', '')
        ld.name = data.get('name', '')
        ld.desc = data.get('desc', '')
        
        # 逻辑节点需要在运行时导入以避免循环导入
        if data.get('logical_nodes'):
            from .logical_node import LogicalNode
            ld.logical_nodes = [LogicalNode.from_dict(ln_data) 
                              for ln_data in data['logical_nodes']]
        
        return ld
