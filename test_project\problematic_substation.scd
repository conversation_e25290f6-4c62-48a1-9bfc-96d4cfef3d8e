<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" version="2007" revision="B" release="4">
  <Header id="ProblematicSubstation" version="1.0" revision="A" toolID="DesignTool" nameStructure="IEDName">
    <Text>某220kV变电站配置文件 - 包含设计问题</Text>
    <History>
      <Hitem version="1.0" revision="A" when="2024-08-17T10:00:00Z" who="设计工程师" what="初始设计" why="新建变电站"/>
    </History>
  </Header>
  
  <Substation name="TestSubstation220kV" desc="测试220kV变电站">
    <VoltageLevel name="220kV" desc="220kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">220</Voltage>
      <Bay name="220kV_Line1" desc="220kV出线1">
        <ConductingEquipment name="QF1" type="CBR" desc="220kV出线1断路器">
          <!-- 问题1: 缺少Terminal连接 -->
        </ConductingEquipment>
        <ConductingEquipment name="QS1" type="DIS" desc="220kV出线1隔离开关">
          <Terminal name="T1" connectivityNode="TestSubstation220kV/220kV/L1" substationName="TestSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line1" cNodeName="L1"/>
        </ConductingEquipment>
        <!-- 问题2: 缺少电流互感器 -->
      </Bay>
      
      <Bay name="220kV_Line2" desc="220kV出线2">
        <ConductingEquipment name="QF2" type="CBR" desc="220kV出线2断路器">
          <Terminal name="T1" connectivityNode="TestSubstation220kV/220kV/L2" substationName="TestSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line2" cNodeName="L2"/>
          <Terminal name="T2" connectivityNode="TestSubstation220kV/220kV/L2" substationName="TestSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line2" cNodeName="L2"/>
        </ConductingEquipment>
        <ConductingEquipment name="TA2" type="CTR" desc="220kV出线2电流互感器">
          <Terminal name="T1" connectivityNode="TestSubstation220kV/220kV/L2" substationName="TestSubstation220kV" voltageLevelName="220kV" bayName="220kV_Line2" cNodeName="L2"/>
          <!-- 问题3: 电流互感器缺少二次侧Terminal -->
        </ConductingEquipment>
      </Bay>
    </VoltageLevel>
    
    <VoltageLevel name="110kV" desc="110kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">110</Voltage>
      <!-- 问题4: 110kV侧完全没有设备配置 -->
    </VoltageLevel>
  </Substation>
  
  <Communication>
    <SubNetwork name="StationBus" desc="站控层网络" type="8-MMS">
      <BitRate unit="b/s" multiplier="M">100</BitRate>
      <ConnectedAP iedName="ProtectionIED1" apName="S1">
        <Address>
          <P type="IP">***********0</P>
          <P type="IP-SUBNET">*************</P>
          <!-- 问题5: 缺少网关配置 -->
        </Address>
      </ConnectedAP>
      <ConnectedAP iedName="ProtectionIED2" apName="S1">
        <Address>
          <P type="IP">***********0</P>  <!-- 问题6: IP地址冲突 -->
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
        </Address>
      </ConnectedAP>
    </SubNetwork>
  </Communication>
  
  <IED name="ProtectionIED1" desc="220kV线路保护装置1" type="Protection" manufacturer="某厂商" configVersion="1.0">
    <AccessPoint name="S1" desc="站控层接入点">
      <Server desc="服务器配置">
        <Authentication none="true"/>
        <LDevice inst="PROT" desc="保护逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type">
            <DataSet name="Events" desc="事件数据集">
              <FCDA ldInst="PROT" prefix="" lnClass="PTRC" lnInst="1" doName="Str" fc="ST"/>
              <FCDA ldInst="PROT" prefix="" lnClass="PTRC" lnInst="1" doName="Op" fc="ST"/>
              <!-- 问题7: 数据集中引用了不存在的逻辑节点 -->
              <FCDA ldInst="PROT" prefix="" lnClass="PTRC" lnInst="2" doName="Str" fc="ST"/>
            </DataSet>
            <ReportControl name="EventsRpt" desc="事件报告" datSet="Events" rptID="Events01" buffered="true" bufTime="50" intgPd="1000">
              <TrgOps period="true" gi="true" dchg="true" qchg="true"/>
              <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="true" entryID="true" configRef="true"/>
              <RptEnabled max="5"/>
            </ReportControl>
          </LN0>
          <LN lnClass="PTRC" inst="1" lnType="PTRC_Type" desc="距离保护">
            <!-- 问题8: 保护逻辑节点缺少必要的数据对象 -->
          </LN>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  
  <IED name="ProtectionIED2" desc="220kV线路保护装置2" type="Protection" manufacturer="某厂商" configVersion="1.0">
    <AccessPoint name="S1" desc="站控层接入点">
      <Server desc="服务器配置">
        <Authentication none="true"/>
        <LDevice inst="PROT" desc="保护逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
          <!-- 问题9: 缺少保护逻辑节点 -->
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  
  <!-- 问题10: 缺少MeasurementIED的定义，但在通信网络中被引用 -->
  
  <DataTypeTemplates>
    <LNodeType id="LLN0_Type" lnClass="LLN0">
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
      <!-- 问题11: 缺少必要的NamPlt数据对象 -->
    </LNodeType>
    
    <LNodeType id="PTRC_Type" lnClass="PTRC">
      <!-- 问题12: 保护逻辑节点类型定义不完整，缺少关键数据对象 -->
      <DO name="Mod" type="ENC_Mod"/>
      <DO name="Beh" type="ENS_Beh"/>
      <DO name="Health" type="ENS_Health"/>
    </LNodeType>
    
    <DOType id="ENC_Mod" cdc="ENC">
      <DA name="stVal" fc="ST" dchg="true" bType="Enum" type="Mod"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="ENS_Beh" cdc="ENS">
      <DA name="stVal" fc="ST" dchg="true" bType="Enum" type="Beh"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <DOType id="ENS_Health" cdc="ENS">
      <DA name="stVal" fc="ST" dchg="true" bType="Enum" type="Health"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality"/>
      <DA name="t" fc="ST" bType="Timestamp"/>
    </DOType>
    
    <EnumType id="Mod">
      <EnumVal ord="1">on</EnumVal>
      <EnumVal ord="2">blocked</EnumVal>
      <EnumVal ord="3">test</EnumVal>
      <EnumVal ord="4">test/blocked</EnumVal>
      <EnumVal ord="5">off</EnumVal>
    </EnumType>
    
    <EnumType id="Beh">
      <EnumVal ord="1">on</EnumVal>
      <EnumVal ord="2">blocked</EnumVal>
      <EnumVal ord="3">test</EnumVal>
      <EnumVal ord="4">test/blocked</EnumVal>
      <EnumVal ord="5">off</EnumVal>
    </EnumType>
    
    <EnumType id="Health">
      <EnumVal ord="1">Ok</EnumVal>
      <EnumVal ord="2">Warning</EnumVal>
      <EnumVal ord="3">Alarm</EnumVal>
    </EnumType>
    
    <!-- 问题13: 缺少Dir枚举类型定义，但在其他地方被引用 -->
  </DataTypeTemplates>
</SCL>
