<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .device-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
            .terminal-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
            .current-line { stroke: red; stroke-width: 2; fill: none; }
            .voltage-line { stroke: blue; stroke-width: 2; fill: none; }
            .control-line { stroke: green; stroke-width: 1.5; fill: none; stroke-dasharray: 5,5; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="900" height="700" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="450" y="30" text-anchor="middle" class="title-text">电流回路图</text>
    <text x="450" y="50" text-anchor="middle" font-size="12" fill="#666">基于虚端子连接关系生成</text>
    
    <!-- 图例 -->
    <g transform="translate(20, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">图例:</text>
        <line x1="0" y1="15" x2="30" y2="15" class="current-line"/>
        <text x="35" y="19" font-size="10">电流回路</text>
        <line x1="0" y1="30" x2="30" y2="30" class="voltage-line"/>
        <text x="35" y="34" font-size="10">电压回路</text>
        <line x1="0" y1="45" x2="30" y2="45" class="control-line"/>
        <text x="35" y="49" font-size="10">控制回路</text>
    </g>
    <!-- 220kV出线1电流互感器 -->
    <g transform="translate(100,150)">
                <g id="ct_TA1">
                    <circle cx="50" cy="50" r="25" fill="none" stroke="black" stroke-width="2"/>
                    <circle cx="50" cy="50" r="15" fill="none" stroke="black" stroke-width="1"/>
                    <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TA</text>
                    <text x="50" y="85" text-anchor="middle" font-size="8">TA1</text>
                    <!-- 一次侧端子 -->
                    <circle cx="25" cy="50" r="2" fill="black"/>
                    <text x="20" y="45" font-size="8">P1</text>
                    <circle cx="75" cy="50" r="2" fill="black"/>
                    <text x="80" y="45" font-size="8">P2</text>
                    <!-- 二次侧端子 -->
                    <circle cx="35" cy="75" r="2" fill="red"/>
                    <text x="30" y="90" font-size="8">S1</text>
                    <circle cx="65" cy="75" r="2" fill="red"/>
                    <text x="70" y="90" font-size="8">S2</text>
                </g>
    </g>
    <!-- 220kV出线2电流互感器 -->
    <g transform="translate(100,300)">
                <g id="ct_TA2">
                    <circle cx="50" cy="50" r="25" fill="none" stroke="black" stroke-width="2"/>
                    <circle cx="50" cy="50" r="15" fill="none" stroke="black" stroke-width="1"/>
                    <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TA</text>
                    <text x="50" y="85" text-anchor="middle" font-size="8">TA2</text>
                    <!-- 一次侧端子 -->
                    <circle cx="25" cy="50" r="2" fill="black"/>
                    <text x="20" y="45" font-size="8">P1</text>
                    <circle cx="75" cy="50" r="2" fill="black"/>
                    <text x="80" y="45" font-size="8">P2</text>
                    <!-- 二次侧端子 -->
                    <circle cx="35" cy="75" r="2" fill="red"/>
                    <text x="30" y="90" font-size="8">S1</text>
                    <circle cx="65" cy="75" r="2" fill="red"/>
                    <text x="70" y="90" font-size="8">S2</text>
                </g>
    </g>
    <!-- 220kV母线电压互感器 -->
    <g transform="translate(100,450)">
                <g id="vt_TV1">
                    <circle cx="50" cy="50" r="25" fill="none" stroke="blue" stroke-width="2"/>
                    <circle cx="50" cy="50" r="15" fill="none" stroke="blue" stroke-width="1"/>
                    <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TV</text>
                    <text x="50" y="85" text-anchor="middle" font-size="8">TV1</text>
                    <!-- 一次侧端子 -->
                    <circle cx="25" cy="50" r="2" fill="black"/>
                    <text x="20" y="45" font-size="8">P1</text>
                    <circle cx="75" cy="50" r="2" fill="black"/>
                    <text x="80" y="45" font-size="8">P2</text>
                    <!-- 二次侧端子 -->
                    <circle cx="35" cy="75" r="2" fill="blue"/>
                    <text x="30" y="90" font-size="8">S1</text>
                    <circle cx="65" cy="75" r="2" fill="blue"/>
                    <text x="70" y="90" font-size="8">S2</text>
                </g>
    </g>
    <!-- 220kV出线1保护装置 -->
    <g transform="translate(350,200)">
                <g id="prot_PROT1">
                    <rect x="10" y="20" width="80" height="60" fill="lightgray" stroke="black" stroke-width="2"/>
                    <text x="50" y="45" text-anchor="middle" font-size="10" font-weight="bold">保护装置</text>
                    <text x="50" y="60" text-anchor="middle" font-size="8">PROT1</text>
                    <!-- 模拟量输入端子 -->
                    <circle cx="5" cy="35" r="2" fill="red"/>
                    <text x="0" y="30" font-size="6">AI1</text>
                    <circle cx="5" cy="45" r="2" fill="red"/>
                    <text x="0" y="40" font-size="6">AI2</text>
                    <circle cx="5" cy="55" r="2" fill="red"/>
                    <text x="0" y="50" font-size="6">AI3</text>
                    <!-- 数字量输出端子 -->
                    <circle cx="95" cy="35" r="2" fill="green"/>
                    <text x="100" y="30" font-size="6">DO1</text>
                    <circle cx="95" cy="45" r="2" fill="green"/>
                    <text x="100" y="40" font-size="6">DO2</text>
                </g>
    </g>
    <!-- 220kV出线1断路器 -->
    <g transform="translate(600,150)">
                <g id="cb_QF1">
                    <rect x="20" y="30" width="60" height="40" fill="none" stroke="black" stroke-width="2"/>
                    <line x1="30" y1="30" x2="70" y2="70" stroke="black" stroke-width="3"/>
                    <text x="50" y="85" text-anchor="middle" font-size="8">QF1</text>
                    <!-- 主触头端子 -->
                    <circle cx="50" cy="20" r="2" fill="black"/>
                    <circle cx="50" cy="80" r="2" fill="black"/>
                    <!-- 跳闸线圈端子 -->
                    <circle cx="10" cy="50" r="2" fill="red"/>
                    <text x="5" y="45" font-size="6">TC</text>
                    <!-- 合闸线圈端子 -->
                    <circle cx="90" cy="50" r="2" fill="green"/>
                    <text x="95" y="45" font-size="6">CC</text>
                </g>
    </g>
    <!-- 跳闸继电器 -->
    <g transform="translate(500,350)">
                <g id="relay_K1">
                    <rect x="20" y="20" width="60" height="40" fill="lightblue" stroke="black" stroke-width="1"/>
                    <text x="50" y="45" text-anchor="middle" font-size="10" font-weight="bold">K</text>
                    <text x="50" y="75" text-anchor="middle" font-size="8">K1</text>
                    <!-- 线圈端子 -->
                    <circle cx="15" cy="40" r="2" fill="black"/>
                    <text x="10" y="35" font-size="6">1</text>
                    <circle cx="85" cy="40" r="2" fill="black"/>
                    <text x="90" y="35" font-size="6">2</text>
                    <!-- 触点端子 -->
                    <circle cx="35" cy="15" r="2" fill="black"/>
                    <text x="30" y="10" font-size="6">3</text>
                    <circle cx="65" cy="15" r="2" fill="black"/>
                    <text x="70" y="10" font-size="6">4</text>
                </g>
    </g>
    <!-- 连接线 -->
    <line x1="150" y1="200" x2="400" y2="250" class="current-line"/>
    <text x="275.0" y="220.0" font-size="8" text-anchor="middle" fill="red">S1→AI1</text>
    <line x1="150" y1="200" x2="400" y2="250" class="current-line"/>
    <text x="275.0" y="220.0" font-size="8" text-anchor="middle" fill="red">S2→AI2</text>
    <line x1="150" y1="350" x2="400" y2="250" class="current-line"/>
    <text x="275.0" y="295.0" font-size="8" text-anchor="middle" fill="red">S1→AI3</text>
    <line x1="150" y1="500" x2="400" y2="250" class="voltage-line"/>
    <text x="275.0" y="370.0" font-size="8" text-anchor="middle" fill="blue">S1→VI1</text>
    <line x1="150" y1="500" x2="400" y2="250" class="voltage-line"/>
    <text x="275.0" y="370.0" font-size="8" text-anchor="middle" fill="blue">S2→VI2</text>
    <line x1="400" y1="250" x2="650" y2="200" class="control-line"/>
    <text x="525.0" y="220.0" font-size="8" text-anchor="middle" fill="green">DO1→TC</text>
    <line x1="400" y1="250" x2="550" y2="400" class="control-line"/>
    <text x="475.0" y="320.0" font-size="8" text-anchor="middle" fill="green">DO2→1</text>
    <line x1="550" y1="400" x2="650" y2="200" class="control-line"/>
    <text x="600.0" y="295.0" font-size="8" text-anchor="middle" fill="orange">3→TC</text>
    
    <!-- 说明文字 -->
    <g transform="translate(20, 600)">
        <text x="0" y="0" font-size="14" font-weight="bold">回路说明:</text>
        <text x="0" y="20" font-size="11">1. 电流互感器TA1、TA2将一次电流转换为二次电流信号</text>
        <text x="0" y="35" font-size="11">2. 保护装置PROT1接收电流信号进行保护判断</text>
        <text x="0" y="50" font-size="11">3. 保护动作时通过数字输出DO1直接跳闸或通过继电器K1跳闸</text>
        <text x="0" y="65" font-size="11">4. 虚端子技术实现了设备间的数字化连接</text>
    </g>
    
</svg>