<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一审查 - IEC61850智能设计检查器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .file-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        .file-type-config {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .file-type-drawing {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .issue-item {
            border-left: 4px solid #dee2e6;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 0 8px 8px 0;
        }
        .issue-critical { border-left-color: #dc3545; background-color: #f8d7da; }
        .issue-error { border-left-color: #fd7e14; background-color: #fff3cd; }
        .issue-warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .issue-info { border-left-color: #0dcaf0; background-color: #d1ecf1; }
        .source-badge {
            font-size: 0.75em;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .source-config {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        .source-drawing {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .progress-container {
            display: none;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-cogs me-2"></i>
                IEC61850智能设计检查器
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">首页</a>
                <a class="nav-link" href="/validation">配置验证</a>
                <a class="nav-link" href="/drawing-review">图纸审查</a>
                <a class="nav-link active" href="/unified-review">统一审查</a>
                <a class="nav-link" href="/comparison">文件对比</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2><i class="fas fa-search-plus me-2"></i>统一审查</h2>
                <p class="text-muted">
                    智能识别文件类型，统一审查IEC61850配置文件和二次图纸，
                    支持SCD、ICD、CID、DWG、DXF、PDF等格式
                </p>
            </div>
        </div>

        <!-- 文件上传区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload me-2"></i>上传文件</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到此处或点击选择文件</h5>
                            <p class="text-muted">
                                支持配置文件: SCD、ICD、CID、XML<br>
                                支持图纸文件: DWG、DXF、PDF<br>
                                最大 50MB
                            </p>
                            <input type="file" id="fileInput" class="d-none" 
                                   accept=".scd,.icd,.cid,.xml,.dwg,.dxf,.pdf">
                            <button class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open me-2"></i>选择文件
                            </button>
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="progress-container mt-3">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted mt-2 d-block" id="progressText">准备上传...</small>
                        </div>

                        <!-- 文件信息 -->
                        <div id="fileInfo" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>文件信息</h6>
                                <p class="mb-1"><strong>文件名:</strong> <span id="fileName"></span></p>
                                <p class="mb-1"><strong>文件大小:</strong> <span id="fileSize"></span></p>
                                <p class="mb-1"><strong>文件类型:</strong> <span id="fileType"></span></p>
                                <p class="mb-0"><strong>检测类型:</strong> <span id="detectedType"></span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 检查选项 -->
        <div class="row mt-4" id="checkOptionsRow" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>检查选项</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">检查分类</label>
                                <div id="categoryFilters">
                                    <!-- 动态加载检查分类 -->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">严重程度过滤</label>
                                <select class="form-select" id="severityFilter">
                                    <option value="">全部</option>
                                    <option value="critical">仅关键问题</option>
                                    <option value="error">错误及以上</option>
                                    <option value="warning">警告及以上</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" id="startReviewBtn">
                                <i class="fas fa-play me-2"></i>开始审查
                            </button>
                            <button class="btn btn-outline-secondary ms-2" id="resetBtn">
                                <i class="fas fa-redo me-2"></i>重置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 审查结果 -->
        <div class="row mt-4" id="resultsRow" style="display: none;">
            <!-- 统计概览 -->
            <div class="col-12 mb-4">
                <div class="card stats-card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <h3 id="totalIssues">0</h3>
                                <small>总问题数</small>
                            </div>
                            <div class="col-md-2">
                                <h3 id="complianceScore">0</h3>
                                <small>合规性评分</small>
                            </div>
                            <div class="col-md-2">
                                <h3 id="criticalIssues">0</h3>
                                <small>关键问题</small>
                            </div>
                            <div class="col-md-2">
                                <h3 id="configIssues">0</h3>
                                <small>配置问题</small>
                            </div>
                            <div class="col-md-2">
                                <h3 id="drawingIssues">0</h3>
                                <small>图纸问题</small>
                            </div>
                            <div class="col-md-2">
                                <h3 id="autoFixable">0</h3>
                                <small>可自动修复</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 问题列表 -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>问题列表</h5>
                        <div>
                            <div class="btn-group btn-group-sm me-2" role="group">
                                <button type="button" class="btn btn-outline-primary active" data-filter="all">全部</button>
                                <button type="button" class="btn btn-outline-primary" data-filter="config">配置</button>
                                <button type="button" class="btn btn-outline-primary" data-filter="drawing">图纸</button>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" id="exportReportBtn">
                                <i class="fas fa-download me-1"></i>导出报告
                            </button>
                        </div>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="issuesList">
                            <!-- 动态加载问题列表 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal fade" id="reportModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">统一审查报告</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reportContent"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="downloadReportBtn">下载报告</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        class UnifiedReviewApp {
            constructor() {
                this.currentFile = null;
                this.reviewResult = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadCheckCategories();
            }

            setupEventListeners() {
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');

                // 文件拖拽
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        this.handleFileSelect(files[0]);
                    }
                });

                // 文件选择
                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.handleFileSelect(e.target.files[0]);
                    }
                });

                // 开始审查
                document.getElementById('startReviewBtn').addEventListener('click', () => {
                    this.startReview();
                });

                // 重置
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.reset();
                });

                // 导出报告
                document.getElementById('exportReportBtn').addEventListener('click', () => {
                    this.exportReport();
                });

                // 问题过滤
                document.querySelectorAll('[data-filter]').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('[data-filter]').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.filterIssues(e.target.dataset.filter);
                    });
                });
            }

            async loadCheckCategories() {
                try {
                    const response = await fetch('/api/unified-review/categories');
                    const result = await response.json();
                    
                    if (result.success) {
                        this.renderCategoryFilters(result.data.categories, result.data.descriptions);
                    }
                } catch (error) {
                    console.error('加载检查分类失败:', error);
                }
            }

            renderCategoryFilters(categories, descriptions) {
                const container = document.getElementById('categoryFilters');
                container.innerHTML = '';

                // 配置分类
                if (categories.config_categories) {
                    const configDiv = document.createElement('div');
                    configDiv.innerHTML = '<h6 class="text-primary">配置检查</h6>';
                    categories.config_categories.forEach(category => {
                        const div = document.createElement('div');
                        div.className = 'form-check';
                        div.innerHTML = `
                            <input class="form-check-input" type="checkbox" value="${category}" id="config_${category}" checked>
                            <label class="form-check-label" for="config_${category}" title="${descriptions[category] || ''}">
                                ${category}
                            </label>
                        `;
                        configDiv.appendChild(div);
                    });
                    container.appendChild(configDiv);
                }

                // 图纸分类
                if (categories.drawing_categories) {
                    const drawingDiv = document.createElement('div');
                    drawingDiv.innerHTML = '<h6 class="text-purple mt-3">图纸检查</h6>';
                    categories.drawing_categories.forEach(category => {
                        const div = document.createElement('div');
                        div.className = 'form-check';
                        div.innerHTML = `
                            <input class="form-check-input" type="checkbox" value="${category}" id="drawing_${category}" checked>
                            <label class="form-check-label" for="drawing_${category}" title="${descriptions[category] || ''}">
                                ${category}
                            </label>
                        `;
                        drawingDiv.appendChild(div);
                    });
                    container.appendChild(drawingDiv);
                }
            }

            async handleFileSelect(file) {
                // 验证文件类型
                const allowedTypes = ['.scd', '.icd', '.cid', '.xml', '.dwg', '.dxf', '.pdf'];
                const fileExt = '.' + file.name.split('.').pop().toLowerCase();
                
                if (!allowedTypes.includes(fileExt)) {
                    alert('不支持的文件格式，请选择支持的文件类型');
                    return;
                }

                // 验证文件大小
                if (file.size > 50 * 1024 * 1024) {
                    alert('文件过大，请选择小于 50MB 的文件');
                    return;
                }

                this.currentFile = file;
                await this.uploadFile(file);
            }

            async uploadFile(file) {
                const progressContainer = document.querySelector('.progress-container');
                const progressBar = document.querySelector('.progress-bar');
                const progressText = document.getElementById('progressText');

                progressContainer.style.display = 'block';
                progressText.textContent = '正在上传文件...';

                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await fetch('/api/unified-review/upload', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        progressBar.style.width = '100%';
                        progressText.textContent = '文件上传成功';
                        
                        this.currentFile = result.data;
                        this.displayFileInfo(result.data);
                        document.getElementById('checkOptionsRow').style.display = 'block';
                        
                        setTimeout(() => {
                            progressContainer.style.display = 'none';
                        }, 1000);
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    progressText.textContent = '上传失败: ' + error.message;
                    progressBar.classList.add('bg-danger');
                }
            }

            displayFileInfo(fileData) {
                document.getElementById('fileName').textContent = fileData.filename;
                document.getElementById('fileSize').textContent = this.formatFileSize(fileData.file_size);
                document.getElementById('fileType').textContent = fileData.file_extension;
                
                const detectedTypeSpan = document.getElementById('detectedType');
                detectedTypeSpan.textContent = fileData.detected_type === 'config' ? '配置文件' : '图纸文件';
                detectedTypeSpan.className = `file-type-badge file-type-${fileData.detected_type}`;
                
                document.getElementById('fileInfo').style.display = 'block';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            async startReview() {
                if (!this.currentFile) {
                    alert('请先上传文件');
                    return;
                }

                const startBtn = document.getElementById('startReviewBtn');
                startBtn.disabled = true;
                startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>审查中...';

                try {
                    // 获取选中的检查分类
                    const selectedCategories = Array.from(document.querySelectorAll('#categoryFilters input:checked'))
                        .map(input => input.value);

                    const severityFilter = document.getElementById('severityFilter').value;

                    const response = await fetch('/api/unified-review/review', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            file_path: this.currentFile.file_path,
                            check_categories: selectedCategories,
                            severity_filter: severityFilter || null
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.reviewResult = result.data;
                        this.displayResults();
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    alert('审查失败: ' + error.message);
                } finally {
                    startBtn.disabled = false;
                    startBtn.innerHTML = '<i class="fas fa-play me-2"></i>开始审查';
                }
            }

            displayResults() {
                const summary = this.reviewResult.summary;
                
                // 更新统计数据
                document.getElementById('totalIssues').textContent = summary.total_issues;
                document.getElementById('complianceScore').textContent = summary.compliance_score?.toFixed(1) || '0';
                document.getElementById('criticalIssues').textContent = summary.critical_issues;
                document.getElementById('configIssues').textContent = summary.config_issues;
                document.getElementById('drawingIssues').textContent = summary.drawing_issues;
                document.getElementById('autoFixable').textContent = summary.auto_fixable_issues;

                // 显示问题列表
                this.renderIssuesList();

                // 显示结果区域
                document.getElementById('resultsRow').style.display = 'block';
            }

            renderIssuesList() {
                const container = document.getElementById('issuesList');
                container.innerHTML = '';

                if (this.reviewResult.issues.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted py-4">未发现问题，文件符合规范！</div>';
                    return;
                }

                this.reviewResult.issues.forEach(issue => {
                    const div = document.createElement('div');
                    div.className = `issue-item issue-${issue.severity}`;
                    div.dataset.source = issue.source_type;
                    div.innerHTML = `
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    ${issue.title}
                                    <span class="source-badge source-${issue.source_type} ms-2">${issue.source_type === 'config' ? '配置' : '图纸'}</span>
                                </h6>
                                <p class="mb-1 small">${issue.description}</p>
                                ${issue.suggestion ? `<p class="mb-1 small text-success"><i class="fas fa-lightbulb me-1"></i>${issue.suggestion}</p>` : ''}
                                <small class="text-muted">
                                    ${issue.standard_reference}
                                    ${issue.auto_fixable ? '<span class="badge bg-success ms-2">可自动修复</span>' : ''}
                                </small>
                            </div>
                            <span class="badge bg-${this.getSeverityColor(issue.severity)}">${this.getSeverityText(issue.severity)}</span>
                        </div>
                    `;
                    container.appendChild(div);
                });
            }

            filterIssues(filter) {
                const issues = document.querySelectorAll('.issue-item');
                issues.forEach(issue => {
                    if (filter === 'all' || issue.dataset.source === filter) {
                        issue.style.display = 'block';
                    } else {
                        issue.style.display = 'none';
                    }
                });
            }

            getSeverityColor(severity) {
                const colors = {
                    'critical': 'danger',
                    'error': 'warning',
                    'warning': 'warning',
                    'info': 'info'
                };
                return colors[severity] || 'secondary';
            }

            getSeverityText(severity) {
                const texts = {
                    'critical': '关键',
                    'error': '错误',
                    'warning': '警告',
                    'info': '信息'
                };
                return texts[severity] || severity;
            }

            async exportReport() {
                if (!this.currentFile) {
                    alert('请先进行审查');
                    return;
                }

                try {
                    const response = await fetch('/api/unified-review/report', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            file_path: this.currentFile.file_path,
                            format: 'html'
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        document.getElementById('reportContent').innerHTML = result.data.content;
                        new bootstrap.Modal(document.getElementById('reportModal')).show();
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    alert('导出报告失败: ' + error.message);
                }
            }

            reset() {
                this.currentFile = null;
                this.reviewResult = null;
                document.getElementById('checkOptionsRow').style.display = 'none';
                document.getElementById('resultsRow').style.display = 'none';
                document.getElementById('fileInfo').style.display = 'none';
                document.querySelector('.progress-container').style.display = 'none';
                document.getElementById('fileInput').value = '';
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new UnifiedReviewApp();
        });
    </script>
</body>
</html>
