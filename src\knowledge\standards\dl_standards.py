"""
DL电力行业标准知识库
包含中国电力行业标准的详细知识
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from ..base.knowledge_entity import (
    StandardEntity, RuleEntity, RequirementEntity,
    EntityType, ConfidenceLevel
)


logger = logging.getLogger(__name__)


class DLStandardsKnowledge:
    """DL电力行业标准知识库"""
    
    def __init__(self):
        """初始化DL标准知识库"""
        self.standards = {}
        self.rules = {}
        self.requirements = {}
        
        self._initialize_knowledge()
        logger.info("DL标准知识库初始化完成")
    
    def _initialize_knowledge(self):
        """初始化知识库内容"""
        self._init_dl_standards()
        self._init_dl_rules()
        self._init_dl_requirements()
    
    def _init_dl_standards(self):
        """初始化DL标准"""
        
        # DL/T 5136-2012 火力发电厂、变电站二次接线设计技术规程
        dl5136 = StandardEntity(
            standard_number="DL/T 5136-2012",
            standard_title="火力发电厂、变电站二次接线设计技术规程",
            name="DL/T 5136-2012 二次接线设计技术规程",
            description="规定了火力发电厂和变电站二次接线设计应遵循的原则和标准，适用于单机额定容量12MW～1000MW等级新建火力发电厂和额定电压35kV～1000kV新建变电站的二次接线设计",
            content="""
            本标准规定了：
            1. 二次接线设计的基本原则
            2. 继电保护配置原则
            3. 自动装置配置要求
            4. 测量仪表配置
            5. 信号回路设计
            6. 操作回路设计
            7. 直流系统设计
            8. 通信系统设计
            9. 计算机监控系统设计
            
            设计原则：
            - 安全可靠
            - 技术先进
            - 经济合理
            - 便于运行维护
            
            适用范围：
            - 单机额定容量12MW～1000MW等级新建火力发电厂
            - 额定电压35kV～1000kV新建变电站
            - 扩建和技术改造工程可参照执行
            """,
            source="DL/T 5136-2012",
            issuing_organization="国家能源局",
            effective_date=datetime(2012, 1, 1),
            category="electrical",
            domain="secondary_system",
            scope="火力发电厂和变电站二次接线设计",
            supersedes=["DL/T 5136-2001"],
            confidence=1.0,
            attributes={
                "classification": "P60",
                "word_count": 205272,
                "quoted_standards": [
                    "GB/T 50063", "GB 50217", "GB 50227", "GB/T 7064",
                    "GB/T 14285", "DL/T 621", "DL/T 866"
                ]
            }
        )
        self.standards["dl5136"] = dl5136
        
        # DL/T 5218 220kV～750kV变电站设计技术规程
        dl5218 = StandardEntity(
            standard_number="DL/T 5218",
            standard_title="220kV～750kV变电站设计技术规程",
            name="DL/T 5218 变电站设计技术规程",
            description="规定了220kV～750kV变电站设计的技术要求和规范",
            content="""
            本规程规定了220kV～750kV变电站设计的：
            1. 站址选择原则
            2. 总平面布置要求
            3. 电气主接线设计
            4. 高压设备选择
            5. 配电装置设计
            6. 继电保护和自动化
            7. 通信系统
            8. 建筑结构设计
            9. 辅助设施设计
            
            设计原则：
            - 满足电力系统安全稳定运行要求
            - 技术先进、经济合理
            - 节约用地、保护环境
            - 便于施工、运行和维护
            """,
            source="DL/T 5218",
            issuing_organization="国家能源局",
            category="electrical",
            domain="substation",
            scope="220kV～750kV变电站设计",
            confidence=0.9
        )
        self.standards["dl5218"] = dl5218
        
        # DL/T 5056 变电站总布置设计技术规程
        dl5056 = StandardEntity(
            standard_number="DL/T 5056",
            standard_title="变电站总布置设计技术规程",
            name="DL/T 5056 变电站总布置设计技术规程",
            description="规定了变电站总布置设计的技术要求",
            content="""
            本规程规定了变电站总布置设计的：
            1. 设计原则和要求
            2. 站址选择条件
            3. 总平面布置
            4. 竖向设计
            5. 道路设计
            6. 围墙和大门设计
            7. 绿化设计
            8. 环境保护要求
            
            布置原则：
            - 满足生产工艺要求
            - 节约用地
            - 便于施工和运行维护
            - 安全可靠
            - 环境协调
            """,
            source="DL/T 5056",
            issuing_organization="国家能源局",
            category="electrical",
            domain="substation",
            scope="变电站总布置设计",
            confidence=0.9
        )
        self.standards["dl5056"] = dl5056
    
    def _init_dl_rules(self):
        """初始化DL标准规则"""
        
        # 二次接线设计规则
        secondary_wiring_rule = RuleEntity(
            name="Secondary_Wiring_Design_Rule",
            description="二次接线设计必须符合DL/T 5136-2012技术规程",
            content="二次接线设计应遵循安全可靠、技术先进、经济合理、便于运行维护的原则",
            source="DL/T 5136-2012",
            rule_type="business",
            severity="error",
            category="design_compliance",
            condition="进行二次接线设计时",
            action="检查设计方案是否符合规程要求",
            message_template="二次接线设计不符合DL/T 5136-2012要求: {details}",
            applicable_standards=["DL/T 5136-2012"],
            applicable_devices=["secondary_system"],
            fix_suggestions=[
                "确保设计满足安全可靠性要求",
                "采用技术先进的设备和方案",
                "进行经济性比较分析",
                "考虑运行维护便利性"
            ],
            confidence=0.95
        )
        self.rules["secondary_wiring_design"] = secondary_wiring_rule
        
        # 变电站布置规则
        substation_layout_rule = RuleEntity(
            name="Substation_Layout_Rule",
            description="变电站总布置必须符合DL/T 5056技术规程",
            content="变电站总布置应满足生产工艺要求，节约用地，便于施工和运行维护，确保安全可靠，与环境协调",
            source="DL/T 5056",
            rule_type="business",
            severity="warning",
            category="layout_compliance",
            condition="进行变电站总布置设计时",
            action="检查布置方案的合理性",
            message_template="变电站总布置不符合DL/T 5056要求: {details}",
            applicable_standards=["DL/T 5056"],
            applicable_devices=["substation"],
            fix_suggestions=[
                "优化设备布置方案",
                "合理利用站址地形",
                "考虑扩建预留空间",
                "满足安全距离要求"
            ],
            confidence=0.9
        )
        self.rules["substation_layout"] = substation_layout_rule
        
        # 高压设备选择规则
        hv_equipment_selection_rule = RuleEntity(
            name="HV_Equipment_Selection_Rule",
            description="高压设备选择必须符合DL/T 5218技术规程",
            content="高压设备选择应根据系统条件、环境条件、技术经济比较确定，确保设备性能满足系统要求",
            source="DL/T 5218",
            rule_type="business",
            severity="error",
            category="equipment_compliance",
            condition="选择高压设备时",
            action="验证设备选型的正确性",
            message_template="高压设备选择不符合DL/T 5218要求: {details}",
            applicable_standards=["DL/T 5218"],
            applicable_devices=["hv_equipment"],
            fix_suggestions=[
                "核实系统技术条件",
                "考虑环境条件影响",
                "进行技术经济比较",
                "验证设备技术参数"
            ],
            confidence=0.9
        )
        self.rules["hv_equipment_selection"] = hv_equipment_selection_rule
    
    def _init_dl_requirements(self):
        """初始化DL标准技术要求"""
        
        # 二次系统可靠性要求
        secondary_reliability_req = RequirementEntity(
            name="Secondary_System_Reliability_Requirement",
            description="二次系统的可靠性要求",
            content="二次系统应具有足够的可靠性，关键回路应有冗余配置，确保在单一故障情况下系统仍能正常运行",
            source="DL/T 5136-2012",
            requirement_type="safety",
            priority="high",
            mandatory=True,
            verification_method="可靠性分析",
            acceptance_criteria="满足N-1准则",
            source_standard="DL/T 5136-2012",
            clause_reference="基本原则章节",
            confidence=0.95
        )
        self.requirements["secondary_reliability"] = secondary_reliability_req
        
        # 变电站安全距离要求
        safety_distance_req = RequirementEntity(
            name="Substation_Safety_Distance_Requirement",
            description="变电站设备间安全距离要求",
            content="变电站内各设备间应保持足够的安全距离，满足电气安全、消防安全、运行维护等要求",
            source="DL/T 5218",
            requirement_type="safety",
            priority="high",
            mandatory=True,
            verification_method="距离测量",
            acceptance_criteria="符合标准规定的最小安全距离",
            source_standard="DL/T 5218",
            clause_reference="配电装置设计章节",
            confidence=0.95
        )
        self.requirements["safety_distance"] = safety_distance_req
        
        # 环境适应性要求
        environmental_req = RequirementEntity(
            name="Environmental_Adaptability_Requirement",
            description="设备环境适应性要求",
            content="变电站设备应能适应当地的气候条件、海拔高度、地震烈度等环境条件",
            source="DL/T 5218",
            requirement_type="environmental",
            priority="medium",
            mandatory=True,
            verification_method="环境试验",
            acceptance_criteria="满足当地环境条件要求",
            source_standard="DL/T 5218",
            clause_reference="设备选择章节",
            confidence=0.9
        )
        self.requirements["environmental"] = environmental_req
    
    def get_all_standards(self) -> List[StandardEntity]:
        """获取所有DL标准"""
        return list(self.standards.values())
    
    def get_all_rules(self) -> List[RuleEntity]:
        """获取所有DL规则"""
        return list(self.rules.values())
    
    def get_all_requirements(self) -> List[RequirementEntity]:
        """获取所有DL技术要求"""
        return list(self.requirements.values())
