"""
数据映射检查器
检查IED设备间的数据映射和绑定关系
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.rules.base_rule import ValidationResult


logger = logging.getLogger(__name__)


class MappingType(Enum):
    """映射类型"""
    GOOSE_SUBSCRIPTION = "goose_subscription"
    SMV_SUBSCRIPTION = "smv_subscription"
    REPORT_SUBSCRIPTION = "report_subscription"
    CLIENT_SERVER = "client_server"


class DataDirection(Enum):
    """数据方向"""
    INPUT = "input"
    OUTPUT = "output"
    BIDIRECTIONAL = "bidirectional"


@dataclass
class DataPoint:
    """数据点"""
    device_name: str
    logical_device: str
    logical_node: str
    data_object: str
    data_attribute: Optional[str] = None
    data_type: Optional[str] = None
    functional_constraint: Optional[str] = None
    description: Optional[str] = None


@dataclass
class DataMapping:
    """数据映射"""
    mapping_id: str
    mapping_type: MappingType
    source: DataPoint
    target: DataPoint
    direction: DataDirection
    configuration: Dict[str, Any]
    is_valid: bool = True
    issues: List[str] = None


@dataclass
class MappingValidationResult:
    """映射验证结果"""
    mapping: DataMapping
    is_valid: bool
    issues: List[ValidationResult]
    recommendations: List[str]


class DataMappingChecker:
    """数据映射检查器"""
    
    def __init__(self):
        """初始化数据映射检查器"""
        self.data_type_compatibility = self._load_data_type_compatibility()
        self.fc_rules = self._load_functional_constraint_rules()
        
        logger.info("数据映射检查器初始化完成")
    
    def validate_data_mapping(self, mapping: DataMapping) -> MappingValidationResult:
        """
        验证数据映射
        
        Args:
            mapping: 数据映射
            
        Returns:
            MappingValidationResult: 映射验证结果
        """
        try:
            logger.debug(f"验证数据映射: {mapping.mapping_id}")
            
            issues = []
            
            # 1. 验证数据类型兼容性
            type_issues = self._validate_data_type_compatibility(mapping)
            issues.extend(type_issues)
            
            # 2. 验证功能约束
            fc_issues = self._validate_functional_constraints(mapping)
            issues.extend(fc_issues)
            
            # 3. 验证映射方向
            direction_issues = self._validate_mapping_direction(mapping)
            issues.extend(direction_issues)
            
            # 4. 验证映射配置
            config_issues = self._validate_mapping_configuration(mapping)
            issues.extend(config_issues)
            
            # 5. 映射类型特定验证
            type_specific_issues = self._validate_mapping_type_specific(mapping)
            issues.extend(type_specific_issues)
            
            # 生成推荐
            recommendations = self._generate_mapping_recommendations(mapping, issues)
            
            is_valid = len([i for i in issues if i.severity == "error"]) == 0
            
            result = MappingValidationResult(
                mapping=mapping,
                is_valid=is_valid,
                issues=issues,
                recommendations=recommendations
            )
            
            logger.debug(f"映射验证完成: {mapping.mapping_id}, 有效: {is_valid}")
            return result
            
        except Exception as e:
            logger.error(f"数据映射验证失败: {e}")
            return MappingValidationResult(
                mapping=mapping,
                is_valid=False,
                issues=[],
                recommendations=[]
            )
    
    def validate_system_mappings(self, mappings: List[DataMapping]) -> List[MappingValidationResult]:
        """
        验证系统中的所有数据映射
        
        Args:
            mappings: 数据映射列表
            
        Returns:
            List[MappingValidationResult]: 映射验证结果列表
        """
        results = []
        
        try:
            logger.info(f"开始验证 {len(mappings)} 个数据映射")
            
            # 验证每个映射
            for mapping in mappings:
                result = self.validate_data_mapping(mapping)
                results.append(result)
            
            # 验证映射间的冲突
            conflict_issues = self._validate_mapping_conflicts(mappings)
            
            # 将冲突问题添加到相关映射的结果中
            for issue in conflict_issues:
                mapping_id = issue.details.get('mapping_id')
                for result in results:
                    if result.mapping.mapping_id == mapping_id:
                        result.issues.append(issue)
                        result.is_valid = result.is_valid and issue.severity != "error"
            
            valid_count = len([r for r in results if r.is_valid])
            logger.info(f"映射验证完成: {valid_count}/{len(mappings)} 个映射有效")
            
            return results
            
        except Exception as e:
            logger.error(f"系统映射验证失败: {e}")
            return []
    
    def extract_mappings_from_scd(self, scd_data: Dict[str, Any]) -> List[DataMapping]:
        """
        从SCD文件中提取数据映射
        
        Args:
            scd_data: SCD文件数据
            
        Returns:
            List[DataMapping]: 数据映射列表
        """
        mappings = []
        
        try:
            logger.info("从SCD文件提取数据映射")
            
            # 提取GOOSE订阅映射
            goose_mappings = self._extract_goose_mappings(scd_data)
            mappings.extend(goose_mappings)
            
            # 提取SMV订阅映射
            smv_mappings = self._extract_smv_mappings(scd_data)
            mappings.extend(smv_mappings)
            
            # 提取报告订阅映射
            report_mappings = self._extract_report_mappings(scd_data)
            mappings.extend(report_mappings)
            
            # 提取客户端-服务器映射
            client_server_mappings = self._extract_client_server_mappings(scd_data)
            mappings.extend(client_server_mappings)
            
            logger.info(f"提取到 {len(mappings)} 个数据映射")
            return mappings
            
        except Exception as e:
            logger.error(f"提取数据映射失败: {e}")
            return []
    
    def analyze_mapping_coverage(self, 
                                mappings: List[DataMapping],
                                devices: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析映射覆盖率
        
        Args:
            mappings: 数据映射列表
            devices: 设备列表
            
        Returns:
            Dict[str, Any]: 覆盖率分析结果
        """
        try:
            # 收集所有可用的数据点
            all_data_points = self._collect_all_data_points(devices)
            
            # 收集已映射的数据点
            mapped_sources = set()
            mapped_targets = set()
            
            for mapping in mappings:
                source_key = self._get_data_point_key(mapping.source)
                target_key = self._get_data_point_key(mapping.target)
                mapped_sources.add(source_key)
                mapped_targets.add(target_key)
            
            # 计算覆盖率
            total_outputs = len([dp for dp in all_data_points if self._is_output_data_point(dp)])
            total_inputs = len([dp for dp in all_data_points if self._is_input_data_point(dp)])
            
            mapped_outputs = len(mapped_sources)
            mapped_inputs = len(mapped_targets)
            
            output_coverage = mapped_outputs / total_outputs if total_outputs > 0 else 0
            input_coverage = mapped_inputs / total_inputs if total_inputs > 0 else 0
            
            # 找出未映射的数据点
            all_output_keys = {self._get_data_point_key(dp) for dp in all_data_points 
                              if self._is_output_data_point(dp)}
            all_input_keys = {self._get_data_point_key(dp) for dp in all_data_points 
                             if self._is_input_data_point(dp)}
            
            unmapped_outputs = all_output_keys - mapped_sources
            unmapped_inputs = all_input_keys - mapped_targets
            
            return {
                'total_mappings': len(mappings),
                'valid_mappings': len([m for m in mappings if m.is_valid]),
                'output_coverage': output_coverage,
                'input_coverage': input_coverage,
                'total_outputs': total_outputs,
                'total_inputs': total_inputs,
                'mapped_outputs': mapped_outputs,
                'mapped_inputs': mapped_inputs,
                'unmapped_outputs': list(unmapped_outputs),
                'unmapped_inputs': list(unmapped_inputs)
            }
            
        except Exception as e:
            logger.error(f"映射覆盖率分析失败: {e}")
            return {}
    
    def _validate_data_type_compatibility(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证数据类型兼容性"""
        issues = []
        
        source_type = mapping.source.data_type
        target_type = mapping.target.data_type
        
        if source_type and target_type:
            if not self._are_data_types_compatible(source_type, target_type):
                issue = ValidationResult(
                    rule_id="DATA_MAPPING_TYPE_COMPAT",
                    passed=False,
                    message=f"数据类型不兼容: {source_type} -> {target_type}",
                    severity="error",
                    details={
                        'mapping_id': mapping.mapping_id,
                        'source_type': source_type,
                        'target_type': target_type,
                        'source_point': self._get_data_point_key(mapping.source),
                        'target_point': self._get_data_point_key(mapping.target)
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_functional_constraints(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证功能约束"""
        issues = []
        
        source_fc = mapping.source.functional_constraint
        target_fc = mapping.target.functional_constraint
        
        if source_fc and target_fc:
            # 检查功能约束兼容性
            if not self._are_functional_constraints_compatible(source_fc, target_fc, mapping.mapping_type):
                issue = ValidationResult(
                    rule_id="DATA_MAPPING_FC_COMPAT",
                    passed=False,
                    message=f"功能约束不兼容: {source_fc} -> {target_fc}",
                    severity="warning",
                    details={
                        'mapping_id': mapping.mapping_id,
                        'source_fc': source_fc,
                        'target_fc': target_fc,
                        'mapping_type': mapping.mapping_type.value
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_mapping_direction(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证映射方向"""
        issues = []
        
        # 检查数据方向是否合理
        if mapping.mapping_type in [MappingType.GOOSE_SUBSCRIPTION, MappingType.SMV_SUBSCRIPTION]:
            if mapping.direction != DataDirection.INPUT:
                issue = ValidationResult(
                    rule_id="DATA_MAPPING_DIRECTION",
                    passed=False,
                    message=f"{mapping.mapping_type.value}应该是输入方向",
                    severity="warning",
                    details={
                        'mapping_id': mapping.mapping_id,
                        'mapping_type': mapping.mapping_type.value,
                        'direction': mapping.direction.value
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_mapping_configuration(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证映射配置"""
        issues = []
        
        config = mapping.configuration
        
        # 验证必需的配置参数
        required_params = self._get_required_config_params(mapping.mapping_type)
        
        for param in required_params:
            if param not in config:
                issue = ValidationResult(
                    rule_id="DATA_MAPPING_CONFIG",
                    passed=False,
                    message=f"缺少必需的配置参数: {param}",
                    severity="error",
                    details={
                        'mapping_id': mapping.mapping_id,
                        'missing_parameter': param,
                        'mapping_type': mapping.mapping_type.value
                    }
                )
                issues.append(issue)
        
        return issues
    
    def _validate_mapping_type_specific(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证映射类型特定规则"""
        issues = []
        
        if mapping.mapping_type == MappingType.GOOSE_SUBSCRIPTION:
            issues.extend(self._validate_goose_mapping(mapping))
        elif mapping.mapping_type == MappingType.SMV_SUBSCRIPTION:
            issues.extend(self._validate_smv_mapping(mapping))
        elif mapping.mapping_type == MappingType.REPORT_SUBSCRIPTION:
            issues.extend(self._validate_report_mapping(mapping))
        elif mapping.mapping_type == MappingType.CLIENT_SERVER:
            issues.extend(self._validate_client_server_mapping(mapping))
        
        return issues
    
    def _validate_goose_mapping(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证GOOSE映射"""
        issues = []
        
        # 检查GOOSE特定的约束
        config = mapping.configuration
        
        # 检查数据集配置
        if 'dataset' not in config:
            issue = ValidationResult(
                rule_id="GOOSE_MAPPING_DATASET",
                passed=False,
                message="GOOSE映射缺少数据集配置",
                severity="error",
                details={
                    'mapping_id': mapping.mapping_id,
                    'configuration': config
                }
            )
            issues.append(issue)
        
        return issues
    
    def _validate_smv_mapping(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证SMV映射"""
        issues = []
        
        # 检查SMV特定的约束
        config = mapping.configuration
        
        # 检查采样率配置
        if 'sample_rate' not in config:
            issue = ValidationResult(
                rule_id="SMV_MAPPING_SAMPLE_RATE",
                passed=False,
                message="SMV映射缺少采样率配置",
                severity="error",
                details={
                    'mapping_id': mapping.mapping_id,
                    'configuration': config
                }
            )
            issues.append(issue)
        
        return issues
    
    def _validate_report_mapping(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证报告映射"""
        issues = []
        
        # 检查报告特定的约束
        config = mapping.configuration
        
        # 检查报告控制块配置
        if 'report_control_block' not in config:
            issue = ValidationResult(
                rule_id="REPORT_MAPPING_RCB",
                passed=False,
                message="报告映射缺少报告控制块配置",
                severity="error",
                details={
                    'mapping_id': mapping.mapping_id,
                    'configuration': config
                }
            )
            issues.append(issue)
        
        return issues
    
    def _validate_client_server_mapping(self, mapping: DataMapping) -> List[ValidationResult]:
        """验证客户端-服务器映射"""
        issues = []
        
        # 检查客户端-服务器特定的约束
        config = mapping.configuration
        
        # 检查服务类型
        if 'service_type' not in config:
            issue = ValidationResult(
                rule_id="CLIENT_SERVER_MAPPING_SERVICE",
                passed=False,
                message="客户端-服务器映射缺少服务类型配置",
                severity="error",
                details={
                    'mapping_id': mapping.mapping_id,
                    'configuration': config
                }
            )
            issues.append(issue)
        
        return issues
    
    def _validate_mapping_conflicts(self, mappings: List[DataMapping]) -> List[ValidationResult]:
        """验证映射冲突"""
        issues = []
        
        # 检查目标数据点的重复映射
        target_mappings = {}
        
        for mapping in mappings:
            target_key = self._get_data_point_key(mapping.target)
            
            if target_key in target_mappings:
                # 发现冲突
                conflicting_mapping = target_mappings[target_key]
                
                issue = ValidationResult(
                    rule_id="DATA_MAPPING_CONFLICT",
                    passed=False,
                    message=f"目标数据点存在多个映射: {target_key}",
                    severity="error",
                    details={
                        'mapping_id': mapping.mapping_id,
                        'conflicting_mapping_id': conflicting_mapping.mapping_id,
                        'target_point': target_key
                    }
                )
                issues.append(issue)
            else:
                target_mappings[target_key] = mapping
        
        return issues
    
    def _extract_goose_mappings(self, scd_data: Dict[str, Any]) -> List[DataMapping]:
        """提取GOOSE映射"""
        mappings = []
        
        # 这里应该根据实际的SCD结构来提取GOOSE映射
        # 简化实现
        
        return mappings
    
    def _extract_smv_mappings(self, scd_data: Dict[str, Any]) -> List[DataMapping]:
        """提取SMV映射"""
        mappings = []
        
        # 这里应该根据实际的SCD结构来提取SMV映射
        # 简化实现
        
        return mappings
    
    def _extract_report_mappings(self, scd_data: Dict[str, Any]) -> List[DataMapping]:
        """提取报告映射"""
        mappings = []
        
        # 这里应该根据实际的SCD结构来提取报告映射
        # 简化实现
        
        return mappings
    
    def _extract_client_server_mappings(self, scd_data: Dict[str, Any]) -> List[DataMapping]:
        """提取客户端-服务器映射"""
        mappings = []
        
        # 这里应该根据实际的SCD结构来提取客户端-服务器映射
        # 简化实现
        
        return mappings
    
    def _collect_all_data_points(self, devices: List[Dict[str, Any]]) -> List[DataPoint]:
        """收集所有数据点"""
        data_points = []
        
        for device in devices:
            device_name = device.get('name', 'Unknown')
            
            # 遍历逻辑设备
            logical_devices = device.get('logical_devices', [])
            for ld in logical_devices:
                ld_name = ld.get('name', 'Unknown')
                
                # 遍历逻辑节点
                logical_nodes = ld.get('logical_nodes', [])
                for ln in logical_nodes:
                    ln_name = ln.get('name', 'Unknown')
                    
                    # 遍历数据对象
                    data_objects = ln.get('data_objects', [])
                    for do in data_objects:
                        do_name = do.get('name', 'Unknown')
                        
                        data_point = DataPoint(
                            device_name=device_name,
                            logical_device=ld_name,
                            logical_node=ln_name,
                            data_object=do_name,
                            data_type=do.get('type'),
                            functional_constraint=do.get('fc'),
                            description=do.get('desc')
                        )
                        data_points.append(data_point)
        
        return data_points
    
    def _get_data_point_key(self, data_point: DataPoint) -> str:
        """获取数据点键值"""
        parts = [
            data_point.device_name,
            data_point.logical_device,
            data_point.logical_node,
            data_point.data_object
        ]
        
        if data_point.data_attribute:
            parts.append(data_point.data_attribute)
        
        return "/".join(parts)
    
    def _is_output_data_point(self, data_point: DataPoint) -> bool:
        """判断是否为输出数据点"""
        # 基于功能约束判断
        output_fcs = ['ST', 'MX', 'CO']
        return data_point.functional_constraint in output_fcs
    
    def _is_input_data_point(self, data_point: DataPoint) -> bool:
        """判断是否为输入数据点"""
        # 基于功能约束判断
        input_fcs = ['SP', 'SV', 'CF']
        return data_point.functional_constraint in input_fcs
    
    def _generate_mapping_recommendations(self, 
                                        mapping: DataMapping,
                                        issues: List[ValidationResult]) -> List[str]:
        """生成映射推荐"""
        recommendations = []
        
        for issue in issues:
            if issue.severity == "error":
                if "数据类型不兼容" in issue.message:
                    recommendations.append("使用数据类型转换或选择兼容的数据点")
                elif "缺少必需的配置参数" in issue.message:
                    recommendations.append(f"添加缺少的配置参数: {issue.details.get('missing_parameter')}")
                elif "数据集配置" in issue.message:
                    recommendations.append("配置正确的GOOSE数据集")
        
        return recommendations[:3]  # 限制推荐数量
    
    def _load_data_type_compatibility(self) -> Dict[str, List[str]]:
        """加载数据类型兼容性规则"""
        return {
            'BOOLEAN': ['BOOLEAN'],
            'INT8': ['INT8', 'INT16', 'INT32'],
            'INT16': ['INT16', 'INT32'],
            'INT32': ['INT32'],
            'FLOAT32': ['FLOAT32', 'FLOAT64'],
            'FLOAT64': ['FLOAT64'],
            'VISIBLE_STRING': ['VISIBLE_STRING'],
            'TIMESTAMP': ['TIMESTAMP'],
            'QUALITY': ['QUALITY']
        }
    
    def _load_functional_constraint_rules(self) -> Dict[str, Dict[str, List[str]]]:
        """加载功能约束规则"""
        return {
            'goose_subscription': {
                'allowed_source': ['ST', 'MX', 'CO'],
                'allowed_target': ['SP', 'SV']
            },
            'smv_subscription': {
                'allowed_source': ['MX'],
                'allowed_target': ['MX']
            },
            'report_subscription': {
                'allowed_source': ['ST', 'MX', 'CO'],
                'allowed_target': ['SP', 'SV']
            }
        }
    
    def _are_data_types_compatible(self, source_type: str, target_type: str) -> bool:
        """检查数据类型兼容性"""
        compatible_types = self.data_type_compatibility.get(source_type, [])
        return target_type in compatible_types
    
    def _are_functional_constraints_compatible(self, 
                                             source_fc: str,
                                             target_fc: str,
                                             mapping_type: MappingType) -> bool:
        """检查功能约束兼容性"""
        rules = self.fc_rules.get(mapping_type.value, {})
        
        allowed_source = rules.get('allowed_source', [])
        allowed_target = rules.get('allowed_target', [])
        
        return source_fc in allowed_source and target_fc in allowed_target
    
    def _get_required_config_params(self, mapping_type: MappingType) -> List[str]:
        """获取必需的配置参数"""
        required_params = {
            MappingType.GOOSE_SUBSCRIPTION: ['dataset', 'goose_id'],
            MappingType.SMV_SUBSCRIPTION: ['sample_rate', 'smv_id'],
            MappingType.REPORT_SUBSCRIPTION: ['report_control_block'],
            MappingType.CLIENT_SERVER: ['service_type']
        }
        
        return required_params.get(mapping_type, [])
