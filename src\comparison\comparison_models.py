"""
对比分析数据模型
定义对比结果、差异项等核心数据结构
"""

import logging
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)


class DiffType(Enum):
    """差异类型"""
    ADDED = "added"         # 新增
    REMOVED = "removed"     # 删除
    MODIFIED = "modified"   # 修改
    MOVED = "moved"         # 移动
    RENAMED = "renamed"     # 重命名


class DiffCategory(Enum):
    """差异分类"""
    HEADER = "header"                   # 文件头
    IED = "ied"                        # IED设备
    LOGICAL_DEVICE = "logical_device"   # 逻辑设备
    LOGICAL_NODE = "logical_node"       # 逻辑节点
    DATA_OBJECT = "data_object"         # 数据对象
    DATA_ATTRIBUTE = "data_attribute"   # 数据属性
    COMMUNICATION = "communication"     # 通信配置
    SUBSTATION = "substation"          # 变电站
    DATA_TYPE_TEMPLATE = "data_type_template"  # 数据类型模板
    GOOSE = "goose"                    # GOOSE配置
    SMV = "smv"                        # SMV配置
    REPORT = "report"                  # 报告配置


class DiffSeverity(Enum):
    """差异严重程度"""
    CRITICAL = "critical"   # 关键差异
    MAJOR = "major"         # 重要差异
    MINOR = "minor"         # 次要差异
    INFO = "info"           # 信息性差异


@dataclass
class DiffItem:
    """差异项"""
    diff_id: str                        # 差异ID
    diff_type: DiffType                 # 差异类型
    category: DiffCategory              # 差异分类
    severity: DiffSeverity              # 严重程度
    path: str                           # 差异路径
    description: str                    # 差异描述
    old_value: Optional[Any] = None     # 旧值
    new_value: Optional[Any] = None     # 新值
    context: Dict[str, Any] = field(default_factory=dict)  # 上下文信息
    impact_analysis: Optional[str] = None  # 影响分析
    recommendations: List[str] = field(default_factory=list)  # 推荐操作
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.diff_id:
            self.diff_id = self._generate_diff_id()
    
    def _generate_diff_id(self) -> str:
        """生成差异ID"""
        import hashlib
        content = f"{self.diff_type.value}_{self.category.value}_{self.path}"
        return hashlib.md5(content.encode()).hexdigest()[:8]
    
    def get_summary(self) -> str:
        """获取差异摘要"""
        type_desc = {
            DiffType.ADDED: "新增",
            DiffType.REMOVED: "删除", 
            DiffType.MODIFIED: "修改",
            DiffType.MOVED: "移动",
            DiffType.RENAMED: "重命名"
        }
        
        return f"{type_desc.get(self.diff_type, self.diff_type.value)}: {self.description}"
    
    def is_structural_change(self) -> bool:
        """判断是否为结构性变更"""
        structural_categories = [
            DiffCategory.IED,
            DiffCategory.LOGICAL_DEVICE,
            DiffCategory.LOGICAL_NODE,
            DiffCategory.SUBSTATION
        ]
        
        structural_types = [
            DiffType.ADDED,
            DiffType.REMOVED,
            DiffType.MOVED
        ]
        
        return (self.category in structural_categories and 
                self.diff_type in structural_types)
    
    def is_configuration_change(self) -> bool:
        """判断是否为配置变更"""
        config_categories = [
            DiffCategory.COMMUNICATION,
            DiffCategory.GOOSE,
            DiffCategory.SMV,
            DiffCategory.REPORT
        ]
        
        return self.category in config_categories
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'diff_id': self.diff_id,
            'diff_type': self.diff_type.value,
            'category': self.category.value,
            'severity': self.severity.value,
            'path': self.path,
            'description': self.description,
            'old_value': self.old_value,
            'new_value': self.new_value,
            'context': self.context,
            'impact_analysis': self.impact_analysis,
            'recommendations': self.recommendations,
            'summary': self.get_summary(),
            'is_structural': self.is_structural_change(),
            'is_configuration': self.is_configuration_change()
        }


@dataclass
class ComparisonResult:
    """对比结果"""
    comparison_id: str                  # 对比ID
    source_file: str                    # 源文件路径
    target_file: str                    # 目标文件路径
    comparison_time: datetime = field(default_factory=datetime.now)
    diff_items: List[DiffItem] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_diff(self, diff_item: DiffItem):
        """添加差异项"""
        self.diff_items.append(diff_item)
    
    def get_diffs_by_type(self, diff_type: DiffType) -> List[DiffItem]:
        """根据类型获取差异"""
        return [diff for diff in self.diff_items if diff.diff_type == diff_type]
    
    def get_diffs_by_category(self, category: DiffCategory) -> List[DiffItem]:
        """根据分类获取差异"""
        return [diff for diff in self.diff_items if diff.category == category]
    
    def get_diffs_by_severity(self, severity: DiffSeverity) -> List[DiffItem]:
        """根据严重程度获取差异"""
        return [diff for diff in self.diff_items if diff.severity == severity]
    
    def get_structural_changes(self) -> List[DiffItem]:
        """获取结构性变更"""
        return [diff for diff in self.diff_items if diff.is_structural_change()]
    
    def get_configuration_changes(self) -> List[DiffItem]:
        """获取配置变更"""
        return [diff for diff in self.diff_items if diff.is_configuration_change()]
    
    def get_critical_diffs(self) -> List[DiffItem]:
        """获取关键差异"""
        return self.get_diffs_by_severity(DiffSeverity.CRITICAL)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'total_diffs': len(self.diff_items),
            'by_type': {},
            'by_category': {},
            'by_severity': {},
            'structural_changes': len(self.get_structural_changes()),
            'configuration_changes': len(self.get_configuration_changes())
        }
        
        # 按类型统计
        for diff_type in DiffType:
            stats['by_type'][diff_type.value] = len(self.get_diffs_by_type(diff_type))
        
        # 按分类统计
        for category in DiffCategory:
            stats['by_category'][category.value] = len(self.get_diffs_by_category(category))
        
        # 按严重程度统计
        for severity in DiffSeverity:
            stats['by_severity'][severity.value] = len(self.get_diffs_by_severity(severity))
        
        return stats
    
    def has_breaking_changes(self) -> bool:
        """判断是否有破坏性变更"""
        # 检查是否有关键差异或重要的结构性变更
        critical_diffs = self.get_critical_diffs()
        major_structural = [diff for diff in self.get_structural_changes() 
                          if diff.severity in [DiffSeverity.CRITICAL, DiffSeverity.MAJOR]]
        
        return len(critical_diffs) > 0 or len(major_structural) > 0
    
    def get_compatibility_score(self) -> float:
        """计算兼容性分数"""
        if not self.diff_items:
            return 1.0
        
        # 根据差异的严重程度和类型计算分数
        penalty = 0.0
        
        for diff in self.diff_items:
            if diff.severity == DiffSeverity.CRITICAL:
                penalty += 0.3
            elif diff.severity == DiffSeverity.MAJOR:
                penalty += 0.2
            elif diff.severity == DiffSeverity.MINOR:
                penalty += 0.1
            else:  # INFO
                penalty += 0.05
            
            # 结构性变更额外惩罚
            if diff.is_structural_change():
                penalty += 0.1
        
        score = max(0.0, 1.0 - penalty / len(self.diff_items))
        return score
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'comparison_id': self.comparison_id,
            'source_file': self.source_file,
            'target_file': self.target_file,
            'comparison_time': self.comparison_time.isoformat(),
            'diff_items': [diff.to_dict() for diff in self.diff_items],
            'metadata': self.metadata,
            'statistics': self.get_statistics(),
            'has_breaking_changes': self.has_breaking_changes(),
            'compatibility_score': self.get_compatibility_score()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ComparisonResult':
        """从字典创建"""
        result = cls(
            comparison_id=data['comparison_id'],
            source_file=data['source_file'],
            target_file=data['target_file'],
            metadata=data.get('metadata', {})
        )
        
        # 解析时间
        if 'comparison_time' in data:
            result.comparison_time = datetime.fromisoformat(data['comparison_time'])
        
        # 解析差异项
        for diff_data in data.get('diff_items', []):
            diff_item = DiffItem(
                diff_id=diff_data['diff_id'],
                diff_type=DiffType(diff_data['diff_type']),
                category=DiffCategory(diff_data['category']),
                severity=DiffSeverity(diff_data['severity']),
                path=diff_data['path'],
                description=diff_data['description'],
                old_value=diff_data.get('old_value'),
                new_value=diff_data.get('new_value'),
                context=diff_data.get('context', {}),
                impact_analysis=diff_data.get('impact_analysis'),
                recommendations=diff_data.get('recommendations', [])
            )
            result.add_diff(diff_item)
        
        return result


@dataclass
class MergeConflict:
    """合并冲突"""
    conflict_id: str                    # 冲突ID
    path: str                           # 冲突路径
    description: str                    # 冲突描述
    source_value: Any                   # 源值
    target_value: Any                   # 目标值
    conflict_type: str                  # 冲突类型
    resolution_options: List[str] = field(default_factory=list)  # 解决选项
    recommended_resolution: Optional[str] = None  # 推荐解决方案
    context: Dict[str, Any] = field(default_factory=dict)  # 上下文
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.conflict_id:
            self.conflict_id = self._generate_conflict_id()
    
    def _generate_conflict_id(self) -> str:
        """生成冲突ID"""
        import hashlib
        content = f"{self.conflict_type}_{self.path}"
        return hashlib.md5(content.encode()).hexdigest()[:8]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'conflict_id': self.conflict_id,
            'path': self.path,
            'description': self.description,
            'source_value': self.source_value,
            'target_value': self.target_value,
            'conflict_type': self.conflict_type,
            'resolution_options': self.resolution_options,
            'recommended_resolution': self.recommended_resolution,
            'context': self.context
        }


@dataclass
class MergeResult:
    """合并结果"""
    merge_id: str                       # 合并ID
    source_file: str                    # 源文件
    target_file: str                    # 目标文件
    output_file: str                    # 输出文件
    merge_time: datetime = field(default_factory=datetime.now)
    conflicts: List[MergeConflict] = field(default_factory=list)
    resolved_conflicts: List[MergeConflict] = field(default_factory=list)
    merge_statistics: Dict[str, Any] = field(default_factory=dict)
    success: bool = True
    error_message: Optional[str] = None
    
    def add_conflict(self, conflict: MergeConflict):
        """添加冲突"""
        self.conflicts.append(conflict)
    
    def resolve_conflict(self, conflict_id: str, resolution: str) -> bool:
        """解决冲突"""
        for conflict in self.conflicts:
            if conflict.conflict_id == conflict_id:
                conflict.recommended_resolution = resolution
                self.resolved_conflicts.append(conflict)
                self.conflicts.remove(conflict)
                return True
        return False
    
    def has_unresolved_conflicts(self) -> bool:
        """是否有未解决的冲突"""
        return len(self.conflicts) > 0
    
    def get_conflict_summary(self) -> Dict[str, int]:
        """获取冲突摘要"""
        conflict_types = {}
        for conflict in self.conflicts + self.resolved_conflicts:
            conflict_type = conflict.conflict_type
            conflict_types[conflict_type] = conflict_types.get(conflict_type, 0) + 1
        
        return {
            'total_conflicts': len(self.conflicts) + len(self.resolved_conflicts),
            'unresolved_conflicts': len(self.conflicts),
            'resolved_conflicts': len(self.resolved_conflicts),
            'by_type': conflict_types
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'merge_id': self.merge_id,
            'source_file': self.source_file,
            'target_file': self.target_file,
            'output_file': self.output_file,
            'merge_time': self.merge_time.isoformat(),
            'conflicts': [c.to_dict() for c in self.conflicts],
            'resolved_conflicts': [c.to_dict() for c in self.resolved_conflicts],
            'merge_statistics': self.merge_statistics,
            'success': self.success,
            'error_message': self.error_message,
            'conflict_summary': self.get_conflict_summary()
        }
