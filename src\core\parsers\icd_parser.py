"""
ICD (IED Capability Description) 文件解析器
解析IEC61850 IED能力描述文件
"""

from typing import List, Optional, Dict, Any
from lxml import etree

from .scd_parser import SCDParser, SCLDocument, SCLHeader
from .base_parser import ParseError
from ..models import IED


class ICDParser(SCDParser):
    """ICD文件解析器"""
    
    def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型"""
        return ['.icd', '.xml']
    
    def parse_root_element(self, root: etree.Element) -> 'ICDDocument':
        """解析ICD根元素"""
        # ICD文件主要包含单个IED的详细描述
        icd_doc = ICDDocument()
        
        # 解析Header
        header_elem = self.find_child_element(root, 'Header', required=True)
        icd_doc.header = self.parse_header(header_elem)
        
        # 解析IED（ICD文件通常只包含一个IED）
        ied_elements = self.find_child_elements(root, 'IED')
        if not ied_elements:
            raise ParseError("ICD文件必须包含至少一个IED元素")
        
        # 通常ICD只包含一个IED，但也可能包含多个
        for ied_elem in ied_elements:
            ied = self.parse_ied(ied_elem)
            icd_doc.ieds.append(ied)
        
        # 解析DataTypeTemplates（ICD文件的重要部分）
        templates_elem = self.find_child_element(root, 'DataTypeTemplates')
        if templates_elem is not None:
            icd_doc.data_type_templates = self.parse_data_type_templates(templates_elem)
        else:
            # ICD文件通常应该包含DataTypeTemplates
            self.logger.warning("ICD文件缺少DataTypeTemplates元素")
        
        # 设置SCL属性
        icd_doc.version = self.get_required_attribute(root, 'version')
        icd_doc.revision = self.get_element_attribute(root, 'revision', 'A')
        
        return icd_doc
    
    def validate_icd_specific(self, icd_doc: 'ICDDocument') -> List[str]:
        """ICD特定的验证"""
        warnings = []
        
        # 检查是否有多个IED
        if len(icd_doc.ieds) > 1:
            warnings.append(f"ICD文件包含{len(icd_doc.ieds)}个IED，通常应该只包含一个")
        
        # 检查是否有DataTypeTemplates
        if not icd_doc.data_type_templates:
            warnings.append("ICD文件缺少DataTypeTemplates，这可能影响类型定义的完整性")
        
        # 检查IED是否有AccessPoint
        for ied in icd_doc.ieds:
            if not ied.access_points:
                warnings.append(f"IED '{ied.name}' 没有AccessPoint定义")
            
            # 检查IED的基本属性
            if not ied.manufacturer:
                warnings.append(f"IED '{ied.name}' 缺少manufacturer属性")
            
            if not ied.type:
                warnings.append(f"IED '{ied.name}' 缺少type属性")
        
        return warnings


class ICDDocument(SCLDocument):
    """ICD文档"""
    
    def validate(self) -> None:
        """ICD文档验证"""
        super().validate()
        
        # ICD特定验证
        if not self.ieds:
            from ..models.base import ValidationError
            raise ValidationError("ICD文档必须包含至少一个IED", "ieds")
    
    def get_primary_ied(self) -> Optional[IED]:
        """获取主要的IED（通常是第一个）"""
        return self.ieds[0] if self.ieds else None
    
    def get_ied_capabilities(self) -> Dict[str, Any]:
        """获取IED能力信息"""
        capabilities = {
            'ieds_count': len(self.ieds),
            'ieds': []
        }
        
        for ied in self.ieds:
            ied_caps = ied.get_capabilities()
            capabilities['ieds'].append(ied_caps)
        
        return capabilities
    
    def get_supported_logical_nodes(self) -> List[str]:
        """获取支持的逻辑节点类型"""
        ln_classes = set()
        
        if self.data_type_templates:
            for lnode_type in self.data_type_templates.lnode_types:
                ln_classes.add(lnode_type.lnclass)
        
        return sorted(list(ln_classes))
    
    def get_communication_capabilities(self) -> Dict[str, Any]:
        """获取通信能力"""
        capabilities = {
            'protocols': set(),
            'services': set(),
            'access_points': []
        }
        
        for ied in self.ieds:
            for ap in ied.access_points:
                ap_info = {
                    'name': ap.name,
                    'router': ap.router,
                    'clock': ap.clock,
                    'services': []
                }
                
                if ap.services:
                    services_dict = ap.services.to_dict()
                    for service, supported in services_dict.items():
                        if supported and service not in ["id", "created_at", "updated_at", "desc"]:
                            capabilities['services'].add(service)
                            ap_info['services'].append(service)
                
                capabilities['access_points'].append(ap_info)
        
        # 转换set为list以便JSON序列化
        capabilities['protocols'] = list(capabilities['protocols'])
        capabilities['services'] = list(capabilities['services'])
        
        return capabilities
