/**
 * IEC61850设计检查器 - 主JavaScript文件
 * 提供通用的前端功能和交互逻辑
 */

// 全局配置
const AppConfig = {
    apiBaseUrl: '/api',
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedFileTypes: ['.scd', '.icd', '.cid', '.xml'],
    toastDuration: 5000
};

// 工具函数类
class Utils {
    /**
     * 格式化文件大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * 格式化时间长度
     */
    static formatDuration(seconds) {
        if (seconds < 1) {
            return `${(seconds * 1000).toFixed(0)} ms`;
        } else if (seconds < 60) {
            return `${seconds.toFixed(1)} s`;
        } else {
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${minutes}m ${secs.toFixed(0)}s`;
        }
    }

    /**
     * 格式化日期时间
     */
    static formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * 验证文件类型
     */
    static isValidFileType(filename) {
        const ext = '.' + filename.split('.').pop().toLowerCase();
        return AppConfig.allowedFileTypes.includes(ext);
    }

    /**
     * 验证文件大小
     */
    static isValidFileSize(size) {
        return size <= AppConfig.maxFileSize;
    }

    /**
     * 生成唯一ID
     */
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 防抖函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Toast通知类
class ToastManager {
    constructor() {
        this.container = document.querySelector('.toast-container');
        if (!this.container) {
            this.createContainer();
        }
    }

    createContainer() {
        this.container = document.createElement('div');
        this.container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(this.container);
    }

    show(title, message, type = 'info', duration = AppConfig.toastDuration) {
        const toastId = Utils.generateId();
        const iconMap = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };

        const colorMap = {
            success: 'text-success',
            error: 'text-danger',
            warning: 'text-warning',
            info: 'text-primary'
        };

        const toastHtml = `
            <div id="toast-${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-${iconMap[type]} ${colorMap[type]} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <small class="text-muted">刚刚</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        this.container.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(`toast-${toastId}`);
        const bsToast = new bootstrap.Toast(toastElement, {
            delay: duration
        });
        
        bsToast.show();

        // 自动清理DOM
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });

        return bsToast;
    }

    success(title, message) {
        return this.show(title, message, 'success');
    }

    error(title, message) {
        return this.show(title, message, 'error');
    }

    warning(title, message) {
        return this.show(title, message, 'warning');
    }

    info(title, message) {
        return this.show(title, message, 'info');
    }
}

// 加载指示器类
class LoadingManager {
    constructor() {
        this.overlay = document.getElementById('loadingOverlay');
        if (!this.overlay) {
            this.createOverlay();
        }
    }

    createOverlay() {
        this.overlay = document.createElement('div');
        this.overlay.id = 'loadingOverlay';
        this.overlay.className = 'loading-overlay d-none';
        this.overlay.innerHTML = `
            <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-3 text-primary">正在处理，请稍候...</p>
            </div>
        `;
        document.body.appendChild(this.overlay);
    }

    show(message = '正在处理，请稍候...') {
        const messageElement = this.overlay.querySelector('p');
        if (messageElement) {
            messageElement.textContent = message;
        }
        this.overlay.classList.remove('d-none');
    }

    hide() {
        this.overlay.classList.add('d-none');
    }
}

// API客户端类
class ApiClient {
    constructor(baseUrl = AppConfig.apiBaseUrl) {
        this.baseUrl = baseUrl;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    async get(endpoint) {
        return this.request(endpoint, { method: 'GET' });
    }

    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        return fetch(`${this.baseUrl}/upload`, {
            method: 'POST',
            body: formData
        }).then(response => response.json());
    }

    async validateFile(fileId, config = {}) {
        return this.post(`/validate/${fileId}`, config);
    }

    async getReport(reportId) {
        return this.get(`/report/${reportId}`);
    }

    async getStatus() {
        return this.get('/status');
    }
}

// 全局实例
const toast = new ToastManager();
const loading = new LoadingManager();
const api = new ApiClient();

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Bootstrap组件
    initializeBootstrapComponents();
    
    // 初始化全局事件监听器
    initializeGlobalEventListeners();
    
    // 初始化页面特定功能
    initializePageSpecificFeatures();
    
    // 显示欢迎信息
    showWelcomeMessage();
});

// 初始化Bootstrap组件
function initializeBootstrapComponents() {
    // 初始化所有工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化所有弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// 初始化全局事件监听器
function initializeGlobalEventListeners() {
    // 全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
        toast.error('系统错误', '发生了意外错误，请刷新页面重试');
    });

    // 全局未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
        toast.error('网络错误', '请检查网络连接并重试');
    });

    // 键盘快捷键
    document.addEventListener('keydown', function(event) {
        // Ctrl+/ 显示帮助
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            window.location.href = '/help';
        }
    });
}

// 初始化页面特定功能
function initializePageSpecificFeatures() {
    const currentPage = window.location.pathname;
    
    switch (currentPage) {
        case '/':
            initializeHomePage();
            break;
        case '/upload':
            initializeUploadPage();
            break;
        case '/validate':
            initializeValidatePage();
            break;
        case '/visualize':
            initializeVisualizePage();
            break;
        default:
            // 通用页面初始化
            break;
    }
}

// 主页初始化
function initializeHomePage() {
    // 添加动画效果
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
}

// 上传页面初始化
function initializeUploadPage() {
    // 在upload.html中已经实现
}

// 验证页面初始化
function initializeValidatePage() {
    // 将在validate.html中实现
}

// 可视化页面初始化
function initializeVisualizePage() {
    // 将在visualize.html中实现
}

// 显示欢迎信息
function showWelcomeMessage() {
    // 检查是否是首次访问
    if (!localStorage.getItem('iec61850_visited')) {
        setTimeout(() => {
            toast.info(
                '欢迎使用IEC61850设计检查器',
                '专为解决智能变电站虚端子连接检查难题而设计'
            );
            localStorage.setItem('iec61850_visited', 'true');
        }, 1000);
    }
}

// 导出全局对象
window.IEC61850 = {
    Utils,
    ToastManager,
    LoadingManager,
    ApiClient,
    toast,
    loading,
    api
};
