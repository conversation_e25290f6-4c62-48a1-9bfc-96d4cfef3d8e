"""
GB国家标准知识库
包含中国国家标准的详细知识
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from ..base.knowledge_entity import (
    StandardEntity, RuleEntity, RequirementEntity,
    EntityType, ConfidenceLevel
)


logger = logging.getLogger(__name__)


class GBStandardsKnowledge:
    """GB国家标准知识库"""
    
    def __init__(self):
        """初始化GB标准知识库"""
        self.standards = {}
        self.rules = {}
        self.requirements = {}
        
        self._initialize_knowledge()
        logger.info("GB标准知识库初始化完成")
    
    def _initialize_knowledge(self):
        """初始化知识库内容"""
        self._init_gb_standards()
        self._init_gb_rules()
        self._init_gb_requirements()
    
    def _init_gb_standards(self):
        """初始化GB标准"""
        
        # GB/T 51072-2014 智能变电站设计规范
        gb51072 = StandardEntity(
            standard_number="GB/T 51072-2014",
            standard_title="110(66)kV～220kV智能变电站设计规范",
            name="GB/T 51072-2014 智能变电站设计规范",
            description="规范110(66)kV～220kV智能变电站的设计技术原则，确保变电站设计符合安全可靠、先进适用、经济合理、节能环保的要求",
            content="""
            本规范适用于交流电压110(66)kV～220kV智能变电站(开关站)新建工程设计。
            
            主要内容包括：
            1. 总则 - 标准化设计技术原则
            2. 术语 - 智能变电站相关术语定义
            3. 站址选择和总平面布置
            4. 电气一次 - 电气主接线、高压设备选择
            5. 电气二次 - 继电保护、自动化系统、通信系统
            6. 建筑结构 - 建筑设计要求
            7. 给排水、暖通、消防等辅助系统
            
            智能变电站应具备以下技术特征：
            - 信息采集数字化
            - 通信平台网络化  
            - 信息共享标准化
            - 系统功能集成化
            - 结构设计紧凑化
            - 高压设备智能化
            - 运行状态可视化
            """,
            source="GB/T 51072-2014",
            issuing_organization="住房和城乡建设部、国家质量监督检验检疫总局",
            effective_date=datetime(2015, 8, 1),
            category="electrical",
            domain="substation",
            scope="110(66)kV～220kV智能变电站设计",
            confidence=1.0,
            attributes={
                "classification": "J15",
                "word_count": 62618,
                "quoted_standards": [
                    "GB 50016", "GB 50019", "GB 50034", "GB 50054", 
                    "GB/T 14285", "GB 15707", "GB 20840.7"
                ]
            }
        )
        self.standards["gb51072"] = gb51072
        
        # GB/T 14285-2023 继电保护和安全自动装置技术规程
        gb14285 = StandardEntity(
            standard_number="GB/T 14285-2023",
            standard_title="继电保护和安全自动装置技术规程",
            name="GB/T 14285-2023 继电保护和安全自动装置技术规程",
            description="规定了电力系统继电保护和安全自动装置的总体要求、交流继电保护、直流输电系统保护、安全自动装置等技术要求",
            content="""
            本文件规定了电力系统继电保护和安全自动装置的：
            1. 总体要求
            2. 交流继电保护
            3. 直流输电系统保护  
            4. 安全自动装置
            5. 继电保护和安全自动装置、相关回路和设备要求
            6. 故障录波、故障信息管理和在线监测分析
            
            主要技术变化：
            - 新增同步调相机保护
            - 新增750kV和1000kV电压等级的交流继电保护
            - 新增柔性输电设备保护
            - 新增串联电抗器保护
            - 新增柔性直流输电保护和特高压直流输电保护
            - 新增分布式电源保护、微电网保护
            - 新增低频振荡监控装置、次同步振荡监控装置
            """,
            source="GB/T 14285-2023",
            issuing_organization="国家市场监督管理总局、国家标准化管理委员会",
            effective_date=datetime(2024, 3, 1),
            category="electrical", 
            domain="protection",
            scope="继电保护和安全自动装置",
            supersedes=["GB/T 14285-2006"],
            confidence=1.0,
            attributes={
                "classification": "K45",
                "word_count": 96911
            }
        )
        self.standards["gb14285"] = gb14285
        
        # GB/T 50976 继电保护及二次回路安装及验收规范
        gb50976 = StandardEntity(
            standard_number="GB/T 50976",
            standard_title="继电保护及二次回路安装及验收规范",
            name="GB/T 50976 继电保护及二次回路安装及验收规范",
            description="规定了继电保护及二次回路的安装和验收技术要求",
            content="""
            本规范规定了继电保护及二次回路的：
            1. 设备安装要求
            2. 回路连接规范
            3. 调试程序和方法
            4. 验收标准和程序
            5. 质量控制要求
            
            适用于：
            - 发电厂继电保护装置
            - 变电站继电保护装置  
            - 输电线路保护装置
            - 安全自动装置
            - 故障录波装置
            """,
            source="GB/T 50976",
            issuing_organization="住房和城乡建设部",
            category="electrical",
            domain="protection",
            scope="继电保护及二次回路安装验收",
            confidence=0.9
        )
        self.standards["gb50976"] = gb50976
    
    def _init_gb_rules(self):
        """初始化GB标准规则"""
        
        # 智能变电站设计规则
        smart_substation_design_rule = RuleEntity(
            name="Smart_Substation_Design_Rule",
            description="智能变电站设计必须符合GB/T 51072-2014规范要求",
            content="智能变电站设计应采用可靠、经济、集成、节能、环保的技术和设备，满足易扩展、易升级、易改造、易维护的工业应用要求",
            source="GB/T 51072-2014",
            rule_type="business",
            severity="error",
            category="design_compliance",
            condition="进行智能变电站设计时",
            action="检查设计方案是否符合标准要求",
            message_template="智能变电站设计不符合GB/T 51072-2014要求: {details}",
            applicable_standards=["GB/T 51072-2014"],
            applicable_devices=["substation"],
            fix_suggestions=[
                "采用数字化信息采集技术",
                "建立网络化通信平台",
                "实现信息共享标准化",
                "确保系统功能集成化"
            ],
            confidence=0.95
        )
        self.rules["smart_substation_design"] = smart_substation_design_rule
        
        # 继电保护配置规则
        protection_config_rule = RuleEntity(
            name="Protection_Configuration_Rule", 
            description="继电保护装置配置必须符合GB/T 14285-2023技术规程",
            content="继电保护装置应按照电力系统安全稳定运行要求进行配置，确保选择性、速动性、灵敏性和可靠性",
            source="GB/T 14285-2023",
            rule_type="business",
            severity="error",
            category="protection_compliance",
            condition="配置继电保护装置时",
            action="验证保护配置的正确性",
            message_template="继电保护配置不符合GB/T 14285-2023要求: {details}",
            applicable_standards=["GB/T 14285-2023"],
            applicable_devices=["protection"],
            fix_suggestions=[
                "检查保护配置的选择性",
                "验证动作时间配合",
                "确保灵敏度满足要求",
                "验证可靠性指标"
            ],
            confidence=0.95
        )
        self.rules["protection_config"] = protection_config_rule
        
        # 二次回路安装规则
        secondary_circuit_rule = RuleEntity(
            name="Secondary_Circuit_Installation_Rule",
            description="二次回路安装必须符合GB/T 50976验收规范",
            content="二次回路的安装应符合设计要求，接线正确，绝缘良好，标识清晰",
            source="GB/T 50976",
            rule_type="business",
            severity="warning",
            category="installation_compliance",
            condition="进行二次回路安装时",
            action="检查安装质量",
            message_template="二次回路安装不符合GB/T 50976要求: {details}",
            applicable_standards=["GB/T 50976"],
            fix_suggestions=[
                "检查接线正确性",
                "验证绝缘电阻",
                "完善回路标识",
                "进行通电试验"
            ],
            confidence=0.9
        )
        self.rules["secondary_circuit"] = secondary_circuit_rule
    
    def _init_gb_requirements(self):
        """初始化GB标准技术要求"""
        
        # 智能变电站技术要求
        smart_substation_req = RequirementEntity(
            name="Smart_Substation_Technical_Requirement",
            description="智能变电站的技术特征要求",
            content="智能变电站应具备信息采集数字化、通信平台网络化、信息共享标准化、系统功能集成化、结构设计紧凑化、高压设备智能化、运行状态可视化等技术特征",
            source="GB/T 51072-2014",
            requirement_type="functional",
            priority="high",
            mandatory=True,
            verification_method="技术方案审查",
            acceptance_criteria="满足七个技术特征要求",
            source_standard="GB/T 51072-2014",
            clause_reference="1.0.3条",
            confidence=1.0
        )
        self.requirements["smart_substation_tech"] = smart_substation_req
        
        # 继电保护性能要求
        protection_performance_req = RequirementEntity(
            name="Protection_Performance_Requirement",
            description="继电保护装置的性能要求",
            content="继电保护装置应满足选择性、速动性、灵敏性、可靠性的基本要求，动作时间、返回时间应符合系统稳定要求",
            source="GB/T 14285-2023",
            requirement_type="performance",
            priority="high", 
            mandatory=True,
            verification_method="性能测试",
            acceptance_criteria="满足四性要求和时间指标",
            source_standard="GB/T 14285-2023",
            clause_reference="总体要求章节",
            confidence=0.95
        )
        self.requirements["protection_performance"] = protection_performance_req
    
    def get_all_standards(self) -> List[StandardEntity]:
        """获取所有GB标准"""
        return list(self.standards.values())
    
    def get_all_rules(self) -> List[RuleEntity]:
        """获取所有GB规则"""
        return list(self.rules.values())
    
    def get_all_requirements(self) -> List[RequirementEntity]:
        """获取所有GB技术要求"""
        return list(self.requirements.values())
