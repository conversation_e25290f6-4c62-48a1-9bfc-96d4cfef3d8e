<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #333; }
            .close-line { stroke: green; stroke-width: 4; fill: none; }
            .trip-line { stroke: red; stroke-width: 4; fill: none; }
            .control-line { stroke: blue; stroke-width: 2; fill: none; }
            .correction-box { fill: #d4edda; stroke: #28a745; stroke-width: 3; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1600" height="1200" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="800" y="40" text-anchor="middle" class="title-text">修正后的控制回路图</text>
    <text x="800" y="65" text-anchor="middle" font-size="14" fill="#666">合闸分闸独立、防误操作、远方就地切换</text>
    
    <!-- 修正说明 -->
    <g transform="translate(50, 90)">
        <rect x="0" y="0" width="1500" height="80" class="correction-box"/>
        <text x="750" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#155724">✅ 控制回路重要修正</text>
        <text x="30" y="50" font-size="12" fill="#155724">1. 合闸和分闸回路完全独立，各有专用电源和控制开关</text>
        <text x="30" y="70" font-size="12" fill="#155724">2. 完整的五防逻辑：防误分、防误合、防误入、防误碰、防误操作</text>
    </g>
    
    <!-- 远方就地切换 -->
    <g transform="translate(100, 200)">
        <text x="0" y="0" font-size="14" font-weight="bold">远方/就地切换:</text>
        <rect x="0" y="20" width="80" height="40" fill="lightgray" stroke="black" stroke-width="2"/>
        <text x="40" y="35" text-anchor="middle" font-size="10">SA0</text>
        <text x="40" y="50" text-anchor="middle" font-size="8">远方/就地</text>
        
        <!-- 远方控制路径 -->
        <text x="100" y="30" font-size="10">远方控制</text>
        <rect x="100" y="40" width="100" height="30" fill="lightblue" stroke="black" stroke-width="1"/>
        <text x="150" y="60" text-anchor="middle" font-size="9">SCADA系统</text>
        
        <!-- 就地控制路径 -->
        <text x="100" y="90" font-size="10">就地控制</text>
        <rect x="100" y="100" width="100" height="30" fill="lightgreen" stroke="black" stroke-width="1"/>
        <text x="150" y="120" text-anchor="middle" font-size="9">就地控制屏</text>
    </g>
    
    <!-- 合闸回路（独立） -->
    <g transform="translate(100, 350)">
        <text x="0" y="0" font-size="14" font-weight="bold" fill="green">合闸回路（独立）:</text>
        
        <!-- 合闸电源 -->
        <rect x="0" y="20" width="80" height="40" fill="lightgreen" stroke="black" stroke-width="2"/>
        <text x="40" y="35" text-anchor="middle" font-size="10">DC 220V</text>
        <text x="40" y="50" text-anchor="middle" font-size="8">合闸专用</text>
        
        <!-- 合闸控制开关 -->
        <rect x="120" y="20" width="60" height="40" fill="lightcyan" stroke="black" stroke-width="2"/>
        <text x="150" y="35" text-anchor="middle" font-size="10">SA1</text>
        <text x="150" y="50" text-anchor="middle" font-size="8">合闸开关</text>
        
        <!-- 防误操作逻辑 -->
        <rect x="220" y="20" width="120" height="40" fill="yellow" stroke="black" stroke-width="2"/>
        <text x="280" y="35" text-anchor="middle" font-size="10">防误操作逻辑</text>
        <text x="280" y="50" text-anchor="middle" font-size="8">分位确认+无故障</text>
        
        <!-- 合闸线圈 -->
        <rect x="380" y="20" width="60" height="40" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="410" y="35" text-anchor="middle" font-size="10">YC</text>
        <text x="410" y="50" text-anchor="middle" font-size="8">合闸线圈</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="120" y2="40" class="close-line"/>
        <line x1="180" y1="40" x2="220" y2="40" class="close-line"/>
        <line x1="340" y1="40" x2="380" y2="40" class="close-line"/>
    </g>
    
    <!-- 分闸回路（独立） -->
    <g transform="translate(100, 500)">
        <text x="0" y="0" font-size="14" font-weight="bold" fill="red">分闸回路（独立）:</text>
        
        <!-- 分闸电源 -->
        <rect x="0" y="20" width="80" height="40" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="40" y="35" text-anchor="middle" font-size="10">DC 220V</text>
        <text x="40" y="50" text-anchor="middle" font-size="8">分闸专用</text>
        
        <!-- 分闸控制开关 -->
        <rect x="120" y="20" width="60" height="40" fill="lightcyan" stroke="black" stroke-width="2"/>
        <text x="150" y="35" text-anchor="middle" font-size="10">SA2</text>
        <text x="150" y="50" text-anchor="middle" font-size="8">分闸开关</text>
        
        <!-- 保护跳闸 -->
        <rect x="220" y="20" width="120" height="40" fill="orange" stroke="black" stroke-width="2"/>
        <text x="280" y="35" text-anchor="middle" font-size="10">保护跳闸</text>
        <text x="280" y="50" text-anchor="middle" font-size="8">硬接线直跳</text>
        
        <!-- 跳闸线圈 -->
        <rect x="380" y="20" width="60" height="40" fill="red" stroke="black" stroke-width="2"/>
        <text x="410" y="35" text-anchor="middle" font-size="10">YT</text>
        <text x="410" y="50" text-anchor="middle" font-size="8">跳闸线圈</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="120" y2="40" class="trip-line"/>
        <line x1="180" y1="40" x2="220" y2="40" class="trip-line"/>
        <line x1="340" y1="40" x2="380" y2="40" class="trip-line"/>
    </g>
    
    <!-- 断路器本体 -->
    <g transform="translate(600, 400)">
        <rect x="0" y="0" width="80" height="80" fill="none" stroke="black" stroke-width="4"/>
        <line x1="10" y1="10" x2="70" y2="70" stroke="black" stroke-width="6"/>
        <text x="40" y="100" text-anchor="middle" font-size="12">QF1</text>
        <text x="40" y="120" text-anchor="middle" font-size="10">220kV断路器</text>
        
        <!-- 位置信号 -->
        <circle cx="90" cy="20" r="8" fill="green"/>
        <text x="90" y="25" text-anchor="middle" font-size="6" fill="white">合</text>
        <text x="110" y="15" font-size="8">合位信号</text>
        
        <circle cx="90" cy="60" r="8" fill="red"/>
        <text x="90" y="65" text-anchor="middle" font-size="6" fill="white">分</text>
        <text x="110" y="55" font-size="8">分位信号</text>
    </g>
    
    <!-- 五防逻辑详细说明 -->
    <g transform="translate(800, 200)">
        <text x="0" y="0" font-size="14" font-weight="bold">五防逻辑详细说明:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">防误分:</text>
            <text x="0" y="20" font-size="10">• 确认断路器在合闸位置</text>
            <text x="0" y="35" font-size="10">• 确认无负荷或已转移负荷</text>
            <text x="0" y="50" font-size="10">• 操作票确认</text>
        </g>
        
        <g transform="translate(0, 110)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">防误合:</text>
            <text x="0" y="20" font-size="10">• 确认断路器在分闸位置</text>
            <text x="0" y="35" font-size="10">• 确认保护装置正常</text>
            <text x="0" y="50" font-size="10">• 确认系统条件允许</text>
        </g>
        
        <g transform="translate(0, 190)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">防误入:</text>
            <text x="0" y="20" font-size="10">• 断路器分闸</text>
            <text x="0" y="35" font-size="10">• 隔离开关分闸</text>
            <text x="0" y="50" font-size="10">• 接地开关合闸</text>
        </g>
        
        <g transform="translate(400, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#6f42c1">防误碰:</text>
            <text x="0" y="20" font-size="10">• 验电确认无电</text>
            <text x="0" y="35" font-size="10">• 挂接地线</text>
            <text x="0" y="50" font-size="10">• 设置安全围栏</text>
        </g>
        
        <g transform="translate(400, 110)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#fd7e14">防误操作:</text>
            <text x="0" y="20" font-size="10">• 操作票制度</text>
            <text x="0" y="35" font-size="10">• 监护制度</text>
            <text x="0" y="50" font-size="10">• 操作前核对</text>
        </g>
    </g>
    
    <!-- 控制回路连接到断路器 -->
    <line x1="540" y1="390" x2="600" y2="420" class="close-line"/>
    <line x1="540" y1="540" x2="600" y2="460" class="trip-line"/>
    
    <!-- 技术要求 -->
    <g transform="translate(100, 700)">
        <text x="0" y="0" font-size="14" font-weight="bold">修正后的技术要求:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">独立性要求:</text>
            <text x="0" y="20" font-size="10">✅ 合闸和分闸回路完全独立</text>
            <text x="0" y="35" font-size="10">✅ 各有专用的直流电源</text>
            <text x="0" y="50" font-size="10">✅ 各有独立的控制开关</text>
            <text x="0" y="65" font-size="10">✅ 互不影响，提高可靠性</text>
        </g>
        
        <g transform="translate(400, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">安全性要求:</text>
            <text x="0" y="20" font-size="10">✅ 完整的五防逻辑</text>
            <text x="0" y="35" font-size="10">✅ 防误操作闭锁</text>
            <text x="0" y="50" font-size="10">✅ 操作条件检查</text>
            <text x="0" y="65" font-size="10">✅ 紧急情况处理</text>
        </g>
        
        <g transform="translate(800, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">控制方式:</text>
            <text x="0" y="20" font-size="10">✅ 远方/就地明确切换</text>
            <text x="0" y="35" font-size="10">✅ 就地控制优先</text>
            <text x="0" y="50" font-size="10">✅ 控制权限管理</text>
            <text x="0" y="65" font-size="10">✅ 操作记录完整</text>
        </g>
    </g>
    
</svg>