<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" version="2007" revision="B" release="4">
    <Header id="test_scd_correct" version="1.0" revision="A" toolID="TestTool" nameStructure="IEDName">
        <Text>测试用SCD文件 - 正确格式</Text>
        <History>
            <Hitem version="1.0" revision="A" when="2025-08-26T21:00:00Z" who="TestUser" what="Initial creation"/>
        </History>
    </Header>
    
    <Substation name="TestSubstation" desc="测试变电站">
        <VoltageLevel name="110kV" desc="110kV电压等级" nomFreq="50" numPhases="3">
            <Voltage unit="kV" multiplier="k">110</Voltage>
            <Bay name="Line1" desc="线路1">
                <ConductingEquipment name="CB1" type="CBR" desc="断路器1">
                    <Terminal name="T1" connectivityNode="TestSubstation/110kV/Line1/CN1" substationName="TestSubstation" voltageLevelName="110kV" bayName="Line1" cNodeName="CN1"/>
                </ConductingEquipment>
                <ConnectivityNode name="CN1" desc="连接节点1" pathName="TestSubstation/110kV/Line1/CN1"/>
            </Bay>
        </VoltageLevel>
    </Substation>
    
    <IED name="IED1" type="Protection" manufacturer="TestManufacturer" configVersion="1.0" desc="保护装置1">
        <Services>
            <DynAssociation/>
            <GetDirectory/>
            <GetDataObjectDefinition/>
            <DataObjectDirectory/>
            <GetDataSetValue/>
            <SetDataSetValue/>
            <DataSetDirectory/>
            <ConfDataSet modify="true" max="10"/>
            <DynDataSet max="5"/>
            <ReadWrite/>
            <ConfReportControl max="10"/>
            <GetCBValues/>
            <ConfLogControl max="5"/>
            <ReportSettings cbName="Fix" datSet="Fix"/>
            <LogSettings cbName="Fix" datSet="Fix"/>
            <GSESettings appID="Fix" datLabel="Fix"/>
            <SMVSettings cbName="Fix" datSet="Fix"/>
            <GSEDir/>
            <GOOSE max="10"/>
            <GSSE max="5"/>
            <SMVsc max="10"/>
            <FileHandling/>
            <ConfLNs fixPrefix="true" fixLnInst="true"/>
        </Services>
        
        <AccessPoint name="AP1" desc="访问点1">
            <Server desc="服务器">
                <Authentication none="true"/>
                <LDevice inst="LD1" desc="逻辑设备1" ldName="IED1LD1">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type" desc="逻辑节点0">
                        <DataSet name="DS1" desc="数据集1">
                            <FCDA ldInst="LD1" lnClass="XCBR" lnInst="1" doName="Pos" daName="stVal" fc="ST"/>
                            <FCDA ldInst="LD1" lnClass="XCBR" lnInst="1" doName="Pos" daName="q" fc="ST"/>
                            <FCDA ldInst="LD1" lnClass="XCBR" lnInst="1" doName="Pos" daName="t" fc="ST"/>
                        </DataSet>
                        
                        <ReportControl name="RC1" desc="报告控制1" datSet="DS1" rptID="IED1LD1/LLN0.RC1" confRev="1" buffered="true" bufTime="50">
                            <TrgOps dchg="true" qchg="true" dupd="false" period="false" gi="true"/>
                            <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="true" entryID="false" configRef="false"/>
                            <RptEnabled max="5"/>
                        </ReportControl>
                        
                        <GSEControl name="GC1" desc="GOOSE控制1" type="GOOSE" appID="IED1LD1/LLN0$GO$GC1" datSet="DS1" confRev="1">
                            <IEDName>IED1</IEDName>
                        </GSEControl>
                    </LN0>
                    
                    <LN lnClass="XCBR" inst="1" lnType="XCBR_Type" desc="断路器逻辑节点">
                        <DOI name="Pos" desc="位置">
                            <DAI name="stVal" desc="状态值">
                                <Val>intermediate-state</Val>
                            </DAI>
                            <DAI name="q" desc="质量">
                                <Val>good</Val>
                            </DAI>
                            <DAI name="t" desc="时间戳">
                                <Val>2025-08-26T21:00:00.000Z</Val>
                            </DAI>
                        </DOI>
                        <DOI name="BlkOpn" desc="分闸闭锁">
                            <DAI name="stVal">
                                <Val>false</Val>
                            </DAI>
                        </DOI>
                        <DOI name="BlkCls" desc="合闸闭锁">
                            <DAI name="stVal">
                                <Val>false</Val>
                            </DAI>
                        </DOI>
                    </LN>
                    
                    <LN lnClass="CSWI" inst="1" lnType="CSWI_Type" desc="控制开关逻辑节点">
                        <DOI name="Pos" desc="位置">
                            <DAI name="stVal">
                                <Val>intermediate-state</Val>
                            </DAI>
                        </DOI>
                    </LN>
                    
                    <LN lnClass="PTOC" inst="1" lnType="PTOC_Type" desc="过电流保护逻辑节点">
                        <DOI name="Str" desc="启动">
                            <DAI name="general">
                                <Val>false</Val>
                            </DAI>
                        </DOI>
                        <DOI name="Op" desc="动作">
                            <DAI name="general">
                                <Val>false</Val>
                            </DAI>
                        </DOI>
                    </LN>
                </LDevice>
            </Server>
        </AccessPoint>
    </IED>
    
    <Communication>
        <SubNetwork name="SubNet1" type="8-MMS" desc="MMS子网">
            <BitRate unit="b/s" multiplier="M">100</BitRate>
            <ConnectedAP iedName="IED1" apName="AP1" desc="IED1连接">
                <Address>
                    <P type="IP">***********0</P>
                    <P type="IP-SUBNET">*************</P>
                    <P type="IP-GATEWAY">***********</P>
                    <P type="OSI-TSEL">0001</P>
                    <P type="OSI-SSEL">0001</P>
                    <P type="OSI-PSEL">00000001</P>
                </Address>
                
                <GSE ldInst="LD1" cbName="GC1" desc="GOOSE发送">
                    <Address>
                        <P type="MAC-Address">01-0C-CD-01-00-01</P>
                        <P type="APPID">0001</P>
                        <P type="VLAN-ID">100</P>
                        <P type="VLAN-PRIORITY">4</P>
                    </Address>
                    <MinTime unit="ms" multiplier="m">4</MinTime>
                    <MaxTime unit="ms" multiplier="m">1000</MaxTime>
                </GSE>
            </ConnectedAP>
        </SubNetwork>
    </Communication>
    
    <DataTypeTemplates>
        <LNodeType id="LLN0_Type" lnClass="LLN0" desc="逻辑节点0类型">
            <DO name="Mod" type="ENC_Mod" desc="模式"/>
            <DO name="Beh" type="ENS_Beh" desc="行为"/>
            <DO name="Health" type="ENS_Health" desc="健康状态"/>
            <DO name="NamPlt" type="LPL_NamPlt" desc="铭牌"/>
        </LNodeType>
        
        <LNodeType id="XCBR_Type" lnClass="XCBR" desc="断路器类型">
            <DO name="Mod" type="ENC_Mod" desc="模式"/>
            <DO name="Beh" type="ENS_Beh" desc="行为"/>
            <DO name="Health" type="ENS_Health" desc="健康状态"/>
            <DO name="NamPlt" type="LPL_NamPlt" desc="铭牌"/>
            <DO name="Pos" type="DPC_Pos" desc="位置"/>
            <DO name="BlkOpn" type="SPC_BlkOpn" desc="分闸闭锁"/>
            <DO name="BlkCls" type="SPC_BlkCls" desc="合闸闭锁"/>
        </LNodeType>
        
        <LNodeType id="CSWI_Type" lnClass="CSWI" desc="控制开关类型">
            <DO name="Mod" type="ENC_Mod" desc="模式"/>
            <DO name="Beh" type="ENS_Beh" desc="行为"/>
            <DO name="Health" type="ENS_Health" desc="健康状态"/>
            <DO name="NamPlt" type="LPL_NamPlt" desc="铭牌"/>
            <DO name="Pos" type="DPC_Pos" desc="位置"/>
        </LNodeType>
        
        <LNodeType id="PTOC_Type" lnClass="PTOC" desc="过电流保护类型">
            <DO name="Mod" type="ENC_Mod" desc="模式"/>
            <DO name="Beh" type="ENS_Beh" desc="行为"/>
            <DO name="Health" type="ENS_Health" desc="健康状态"/>
            <DO name="NamPlt" type="LPL_NamPlt" desc="铭牌"/>
            <DO name="Str" type="ACD_Str" desc="启动"/>
            <DO name="Op" type="ACT_Op" desc="动作"/>
        </LNodeType>
        
        <DOType id="DPC_Pos" cdc="DPC" desc="双点控制">
            <DA name="stVal" bType="Dbpos" fc="ST" desc="状态值"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
            <DA name="ctlModel" bType="Enum" type="CtlModelEnum" fc="CF" desc="控制模型"/>
        </DOType>
        
        <DOType id="SPC_BlkOpn" cdc="SPC" desc="单点控制">
            <DA name="stVal" bType="BOOLEAN" fc="ST" desc="状态值"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
        </DOType>
        
        <DOType id="SPC_BlkCls" cdc="SPC" desc="单点控制">
            <DA name="stVal" bType="BOOLEAN" fc="ST" desc="状态值"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
        </DOType>
        
        <DOType id="ACD_Str" cdc="ACD" desc="一般检测">
            <DA name="general" bType="BOOLEAN" fc="ST" desc="一般"/>
            <DA name="dirGeneral" bType="Enum" type="DirEnum" fc="ST" desc="方向一般"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
        </DOType>
        
        <DOType id="ACT_Op" cdc="ACT" desc="保护动作">
            <DA name="general" bType="BOOLEAN" fc="ST" desc="一般"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
        </DOType>
        
        <DOType id="ENC_Mod" cdc="ENC" desc="可控枚举">
            <DA name="stVal" bType="Enum" type="ModEnum" fc="ST" desc="状态值"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
            <DA name="ctlModel" bType="Enum" type="CtlModelEnum" fc="CF" desc="控制模型"/>
        </DOType>
        
        <DOType id="ENS_Beh" cdc="ENS" desc="状态枚举">
            <DA name="stVal" bType="Enum" type="BehEnum" fc="ST" desc="状态值"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
        </DOType>
        
        <DOType id="ENS_Health" cdc="ENS" desc="状态枚举">
            <DA name="stVal" bType="Enum" type="HealthEnum" fc="ST" desc="状态值"/>
            <DA name="q" bType="Quality" fc="ST" desc="质量"/>
            <DA name="t" bType="Timestamp" fc="ST" desc="时间戳"/>
        </DOType>
        
        <DOType id="LPL_NamPlt" cdc="LPL" desc="铭牌">
            <DA name="vendor" bType="VisString255" fc="DC" desc="厂商"/>
            <DA name="swRev" bType="VisString255" fc="DC" desc="软件版本"/>
            <DA name="d" bType="VisString255" fc="DC" desc="描述"/>
        </DOType>
        
        <EnumType id="CtlModelEnum">
            <EnumVal ord="0">status-only</EnumVal>
            <EnumVal ord="1">direct-with-normal-security</EnumVal>
            <EnumVal ord="2">sbo-with-normal-security</EnumVal>
            <EnumVal ord="3">direct-with-enhanced-security</EnumVal>
            <EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
        </EnumType>
        
        <EnumType id="ModEnum">
            <EnumVal ord="1">on</EnumVal>
            <EnumVal ord="2">blocked</EnumVal>
            <EnumVal ord="3">test</EnumVal>
            <EnumVal ord="4">test/blocked</EnumVal>
            <EnumVal ord="5">off</EnumVal>
        </EnumType>
        
        <EnumType id="BehEnum">
            <EnumVal ord="1">on</EnumVal>
            <EnumVal ord="2">blocked</EnumVal>
            <EnumVal ord="3">test</EnumVal>
            <EnumVal ord="4">test/blocked</EnumVal>
            <EnumVal ord="5">off</EnumVal>
        </EnumType>
        
        <EnumType id="HealthEnum">
            <EnumVal ord="1">Ok</EnumVal>
            <EnumVal ord="2">Warning</EnumVal>
            <EnumVal ord="3">Alarm</EnumVal>
        </EnumType>
        
        <EnumType id="DirEnum">
            <EnumVal ord="0">unknown</EnumVal>
            <EnumVal ord="1">forward</EnumVal>
            <EnumVal ord="2">backward</EnumVal>
            <EnumVal ord="3">both</EnumVal>
        </EnumType>
    </DataTypeTemplates>
</SCL>