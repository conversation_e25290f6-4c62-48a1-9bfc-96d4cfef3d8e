<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1400" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 20px; font-weight: bold; fill: #333; }
            .device-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
            .model-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
            .protection-a { stroke: red; stroke-width: 3; fill: none; }
            .protection-b { stroke: blue; stroke-width: 3; fill: none; }
            .measurement { stroke: green; stroke-width: 2; fill: none; }
            .control { stroke: purple; stroke-width: 2; fill: none; }
            .goose { stroke: orange; stroke-width: 2; fill: none; stroke-dasharray: 8,4; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1800" height="1400" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="900" y="40" text-anchor="middle" class="title-text">220kV某某线完整逻辑回路</text>
    <text x="900" y="65" text-anchor="middle" font-size="14" fill="#666">真实厂家设备配置实例</text>
    
    <!-- 一次设备 -->
    <g transform="translate(100, 120)">
        <text x="0" y="0" font-size="16" font-weight="bold">一次设备配置:</text>
        
        <!-- 线路 -->
        <line x1="0" y1="40" x2="400" y2="40" stroke="black" stroke-width="8"/>
        <text x="200" y="30" text-anchor="middle" font-size="12" font-weight="bold">220kV某某线</text>
        
        <!-- 电流互感器 -->
        <g transform="translate(80, 20)">
            <circle cx="0" cy="20" r="18" fill="none" stroke="red" stroke-width="3"/>
            <text x="0" y="50" text-anchor="middle" class="device-text" font-weight="bold">TA</text>
            <text x="0" y="65" text-anchor="middle" class="model-text">大连北方</text>
            <text x="0" y="75" text-anchor="middle" class="model-text">LZZBJ9-252</text>
            <text x="0" y="85" text-anchor="middle" class="model-text">2000/5A</text>
            
            <!-- 二次绕组 -->
            <circle cx="-25" cy="95" r="3" fill="red"/>
            <text x="-35" y="90" class="model-text">1S1-1S2</text>
            <text x="-35" y="100" class="model-text">保护A</text>
            
            <circle cx="-8" cy="95" r="3" fill="blue"/>
            <text x="-18" y="90" class="model-text">2S1-2S2</text>
            <text x="-18" y="100" class="model-text">保护B</text>
            
            <circle cx="8" cy="95" r="3" fill="green"/>
            <text x="18" y="90" class="model-text">3S1-3S2</text>
            <text x="18" y="100" class="model-text">测量</text>
            
            <circle cx="25" cy="95" r="3" fill="orange"/>
            <text x="35" y="90" class="model-text">4S1-4S2</text>
            <text x="35" y="100" class="model-text">计量</text>
        </g>
        
        <!-- 断路器 -->
        <g transform="translate(300, 20)">
            <rect x="0" y="0" width="40" height="40" fill="none" stroke="black" stroke-width="4"/>
            <line x1="5" y1="5" x2="35" y2="35" stroke="black" stroke-width="6"/>
            <text x="20" y="55" text-anchor="middle" class="device-text" font-weight="bold">QF</text>
            <text x="20" y="70" text-anchor="middle" class="model-text">西安西电</text>
            <text x="20" y="80" text-anchor="middle" class="model-text">LW36-252</text>
            <text x="20" y="90" text-anchor="middle" class="model-text">252kV/4000A</text>
            
            <!-- 跳闸线圈 -->
            <rect x="-15" y="100" width="20" height="15" fill="red" stroke="black"/>
            <text x="-5" y="110" text-anchor="middle" class="model-text" fill="white">YT1</text>
            <text x="-5" y="125" text-anchor="middle" class="model-text">主跳</text>
            
            <rect x="35" y="100" width="20" height="15" fill="blue" stroke="black"/>
            <text x="45" y="110" text-anchor="middle" class="model-text" fill="white">YT2</text>
            <text x="45" y="125" text-anchor="middle" class="model-text">备跳</text>
            
            <!-- 合闸线圈 -->
            <rect x="10" y="130" width="20" height="15" fill="green" stroke="black"/>
            <text x="20" y="140" text-anchor="middle" class="model-text" fill="white">YC</text>
            <text x="20" y="155" text-anchor="middle" class="model-text">合闸</text>
        </g>
        
        <!-- 电压互感器 -->
        <g transform="translate(500, 20)">
            <rect x="0" y="0" width="30" height="40" fill="none" stroke="blue" stroke-width="3"/>
            <text x="15" y="25" text-anchor="middle" class="device-text" font-weight="bold">TV</text>
            <text x="15" y="55" text-anchor="middle" class="device-text" font-weight="bold">PT</text>
            <text x="15" y="70" text-anchor="middle" class="model-text">大连北方</text>
            <text x="15" y="80" text-anchor="middle" class="model-text">JDZ10-252</text>
            <text x="15" y="90" text-anchor="middle" class="model-text">220kV/100V</text>
        </g>
    </g>
    
    <!-- 保护装置A -->
    <g transform="translate(100, 350)">
        <rect x="0" y="0" width="200" height="120" fill="lightcoral" stroke="black" stroke-width="3"/>
        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">线路保护A</text>
        <text x="100" y="45" text-anchor="middle" class="device-text" font-weight="bold">南京南瑞继保</text>
        <text x="100" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-978A</text>
        <text x="100" y="75" text-anchor="middle" class="model-text">220kV线路成套保护</text>
        <text x="100" y="90" text-anchor="middle" class="model-text">距离+零序+重合闸</text>
        <text x="100" y="105" text-anchor="middle" class="model-text">IEC 61850通信</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="30" r="4" fill="red"/>
        <text x="-25" y="25" class="model-text">CT-A</text>
        <circle cx="-10" cy="50" r="4" fill="blue"/>
        <text x="-25" y="45" class="model-text">PT</text>
        <circle cx="-10" cy="70" r="4" fill="black"/>
        <text x="-30" y="65" class="model-text">DC220V-I</text>
        
        <circle cx="210" cy="30" r="4" fill="red"/>
        <text x="220" y="25" class="model-text">跳闸YT1</text>
        <circle cx="210" cy="50" r="4" fill="orange"/>
        <text x="220" y="45" class="model-text">GOOSE</text>
    </g>
    
    <!-- 保护装置B -->
    <g transform="translate(100, 520)">
        <rect x="0" y="0" width="200" height="120" fill="lightblue" stroke="black" stroke-width="3"/>
        <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="bold">线路保护B</text>
        <text x="100" y="45" text-anchor="middle" class="device-text" font-weight="bold">北京四方继保</text>
        <text x="100" y="60" text-anchor="middle" class="device-text" font-weight="bold">CSC-278A</text>
        <text x="100" y="75" text-anchor="middle" class="model-text">220kV线路成套保护</text>
        <text x="100" y="90" text-anchor="middle" class="model-text">距离+零序+重合闸</text>
        <text x="100" y="105" text-anchor="middle" class="model-text">IEC 61850通信</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="30" r="4" fill="blue"/>
        <text x="-25" y="25" class="model-text">CT-B</text>
        <circle cx="-10" cy="50" r="4" fill="blue"/>
        <text x="-25" y="45" class="model-text">PT</text>
        <circle cx="-10" cy="70" r="4" fill="black"/>
        <text x="-30" y="65" class="model-text">DC220V-II</text>
        
        <circle cx="210" cy="30" r="4" fill="blue"/>
        <text x="220" y="25" class="model-text">跳闸YT2</text>
        <circle cx="210" cy="50" r="4" fill="orange"/>
        <text x="220" y="45" class="model-text">GOOSE</text>
    </g>
    
    <!-- 测控装置 -->
    <g transform="translate(400, 350)">
        <rect x="0" y="0" width="180" height="100" fill="lightgreen" stroke="black" stroke-width="3"/>
        <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold">测控装置</text>
        <text x="90" y="45" text-anchor="middle" class="device-text" font-weight="bold">国电南瑞</text>
        <text x="90" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-9611CS</text>
        <text x="90" y="75" text-anchor="middle" class="model-text">测量+控制+通信</text>
        <text x="90" y="90" text-anchor="middle" class="model-text">遥测+遥控+遥信</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="30" r="4" fill="green"/>
        <text x="-30" y="25" class="model-text">CT测量</text>
        <circle cx="-10" cy="50" r="4" fill="blue"/>
        <text x="-25" y="45" class="model-text">PT</text>
        
        <circle cx="190" cy="40" r="4" fill="purple"/>
        <text x="200" y="35" class="model-text">控制</text>
    </g>
    
    <!-- 智能终端 -->
    <g transform="translate(400, 500)">
        <rect x="0" y="0" width="180" height="80" fill="lightyellow" stroke="black" stroke-width="3"/>
        <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="bold">智能终端</text>
        <text x="90" y="45" text-anchor="middle" class="device-text" font-weight="bold">国电南瑞</text>
        <text x="90" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-9611IT</text>
        <text x="90" y="75" text-anchor="middle" class="model-text">智能操作箱</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="40" r="4" fill="orange"/>
        <text x="-30" y="35" class="model-text">GOOSE</text>
        
        <circle cx="190" cy="30" r="4" fill="green"/>
        <text x="200" y="25" class="model-text">合闸</text>
        <circle cx="190" cy="50" r="4" fill="red"/>
        <text x="200" y="45" class="model-text">跳闸</text>
    </g>
    
    <!-- 合并单元 -->
    <g transform="translate(700, 350)">
        <rect x="0" y="0" width="160" height="100" fill="lightcyan" stroke="black" stroke-width="3"/>
        <text x="80" y="25" text-anchor="middle" font-size="14" font-weight="bold">合并单元</text>
        <text x="80" y="45" text-anchor="middle" class="device-text" font-weight="bold">国电南瑞</text>
        <text x="80" y="60" text-anchor="middle" class="device-text" font-weight="bold">RCS-9611MU</text>
        <text x="80" y="75" text-anchor="middle" class="model-text">4000Hz采样</text>
        <text x="80" y="90" text-anchor="middle" class="model-text">SV输出</text>
        
        <!-- 输入输出接口 -->
        <circle cx="-10" cy="40" r="4" fill="red"/>
        <text x="-25" y="35" class="model-text">CT</text>
        <circle cx="-10" cy="60" r="4" fill="blue"/>
        <text x="-25" y="55" class="model-text">PT</text>
        
        <circle cx="170" cy="50" r="4" fill="orange"/>
        <text x="180" y="45" class="model-text">SV</text>
    </g>
    
    <!-- 直流电源系统 -->
    <g transform="translate(100, 750)">
        <text x="0" y="0" font-size="16" font-weight="bold">直流电源系统:</text>
        
        <!-- 直流电源I -->
        <rect x="0" y="20" width="150" height="80" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="75" y="40" text-anchor="middle" font-size="12" font-weight="bold">直流电源I系统</text>
        <text x="75" y="55" text-anchor="middle" class="device-text">许继电气</text>
        <text x="75" y="70" text-anchor="middle" class="device-text">XJZG-220/110</text>
        <text x="75" y="85" text-anchor="middle" class="model-text">DC 220V/600Ah</text>
        
        <!-- 直流电源II -->
        <rect x="200" y="20" width="150" height="80" fill="lightblue" stroke="black" stroke-width="2"/>
        <text x="275" y="40" text-anchor="middle" font-size="12" font-weight="bold">直流电源II系统</text>
        <text x="275" y="55" text-anchor="middle" class="device-text">许继电气</text>
        <text x="275" y="70" text-anchor="middle" class="device-text">XJZG-220/110</text>
        <text x="275" y="85" text-anchor="middle" class="model-text">DC 220V/600Ah</text>
        
        <!-- 分电屏 -->
        <rect x="400" y="20" width="200" height="80" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="500" y="35" text-anchor="middle" font-size="12" font-weight="bold">直流分电屏</text>
        <text x="420" y="55" class="model-text">保护A: DC220V-I</text>
        <text x="420" y="70" class="model-text">保护B: DC220V-II</text>
        <text x="420" y="85" class="model-text">控制: DC220V-I/II</text>
        <text x="520" y="55" class="model-text">测控: DC220V</text>
        <text x="520" y="70" class="model-text">信号: DC110V</text>
        <text x="520" y="85" class="model-text">通信: DC48V</text>
    </g>
    
    <!-- 连接线 -->
    <!-- CT到保护装置 -->
    <line x1="155" y1="215" x2="90" y2="380" class="protection-a"/>
    <line x1="172" y1="215" x2="90" y2="550" class="protection-b"/>
    <line x1="188" y1="215" x2="390" y2="380" class="measurement"/>
    
    <!-- PT到各装置 -->
    <line x1="615" y1="160" x2="90" y2="400" stroke="blue" stroke-width="2"/>
    <line x1="615" y1="160" x2="90" y2="570" stroke="blue" stroke-width="2"/>
    <line x1="615" y1="160" x2="390" y2="400" stroke="blue" stroke-width="2"/>
    
    <!-- 保护装置到断路器跳闸线圈 -->
    <line x1="310" y1="380" x2="385" y2="220" class="protection-a"/>
    <line x1="310" y1="550" x2="445" y2="220" class="protection-b"/>
    
    <!-- 测控到智能终端 -->
    <line x1="590" y1="390" x2="390" y2="540" class="control"/>
    
    <!-- 智能终端到断路器 -->
    <line x1="590" y1="530" x2="410" y2="250" stroke="green" stroke-width="3"/>
    <line x1="590" y1="550" x2="420" y2="220" stroke="red" stroke-width="3"/>
    
    <!-- GOOSE连接 -->
    <line x1="310" y1="400" x2="390" y2="540" class="goose"/>
    <line x1="310" y1="570" x2="390" y2="540" class="goose"/>
    
    <!-- 直流电源连接 -->
    <line x1="75" y1="750" x2="90" y2="420" stroke="red" stroke-width="2"/>
    <line x1="275" y1="750" x2="90" y2="590" stroke="blue" stroke-width="2"/>
    <line x1="500" y1="750" x2="490" y2="450" stroke="black" stroke-width="2"/>
    
    <!-- 技术参数说明 -->
    <g transform="translate(1000, 200)">
        <text x="0" y="0" font-size="16" font-weight="bold">设备技术参数:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">保护装置:</text>
            <text x="0" y="20" class="device-text">• RCS-978A: 距离保护≤20ms</text>
            <text x="0" y="35" class="device-text">• CSC-278A: 零序保护≤30ms</text>
            <text x="0" y="50" class="device-text">• 重合闸: 0.5-300s可调</text>
            <text x="0" y="65" class="device-text">• 通信: IEC 61850</text>
        </g>
        
        <g transform="translate(0, 120)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">断路器:</text>
            <text x="0" y="20" class="device-text">• LW36-252: SF6断路器</text>
            <text x="0" y="35" class="device-text">• 分闸时间: ≤40ms</text>
            <text x="0" y="50" class="device-text">• 合闸时间: ≤100ms</text>
            <text x="0" y="65" class="device-text">• 机械寿命: 10000次</text>
        </g>
        
        <g transform="translate(0, 210)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">互感器:</text>
            <text x="0" y="20" class="device-text">• CT: 2000/5A, 5P20</text>
            <text x="0" y="35" class="device-text">• PT: 220kV/100V, 3P</text>
            <text x="0" y="50" class="device-text">• 热稳定: 4s/80kA</text>
            <text x="0" y="65" class="device-text">• 动稳定: 200kA</text>
        </g>
        
        <g transform="translate(0, 300)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#6f42c1">通信性能:</text>
            <text x="0" y="20" class="device-text">• GOOSE: ≤4ms</text>
            <text x="0" y="35" class="device-text">• SV: 4000Hz, ≤3ms</text>
            <text x="0" y="50" class="device-text">• 同步精度: ±1μs</text>
            <text x="0" y="65" class="device-text">• 网络: 双网冗余</text>
        </g>
    </g>
    
    <!-- 保护逻辑说明 -->
    <g transform="translate(1000, 650)">
        <text x="0" y="0" font-size="16" font-weight="bold">保护配置逻辑:</text>
        
        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">主保护:</text>
            <text x="0" y="20" class="device-text">• 距离I段: 瞬时, 85%线路</text>
            <text x="0" y="35" class="device-text">• 距离II段: 0.5s, 120%线路</text>
            <text x="0" y="50" class="device-text">• 零序I段: 瞬时, 85%线路</text>
            <text x="0" y="65" class="device-text">• 纵差保护: 光纤通道</text>
        </g>
        
        <g transform="translate(0, 120)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">后备保护:</text>
            <text x="0" y="20" class="device-text">• 距离III段: 1.2s, 相邻线路</text>
            <text x="0" y="35" class="device-text">• 零序II段: 0.5s, 相邻线路</text>
            <text x="0" y="50" class="device-text">• 过负荷: 告警+跳闸</text>
            <text x="0" y="65" class="device-text">• 失灵保护: 150ms</text>
        </g>
        
        <g transform="translate(0, 210)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">自动装置:</text>
            <text x="0" y="20" class="device-text">• 重合闸: 单相+三相</text>
            <text x="0" y="35" class="device-text">• 重合时间: 1s可调</text>
            <text x="0" y="50" class="device-text">• 重合次数: 1次</text>
            <text x="0" y="65" class="device-text">• 检同期: 电压+频率</text>
        </g>
    </g>
    
</svg>