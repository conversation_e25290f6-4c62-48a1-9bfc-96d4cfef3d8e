<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .section-text { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #333; }
            .device-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
            .model-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
            .protection-a { stroke: red; stroke-width: 2; fill: none; }
            .protection-b { stroke: blue; stroke-width: 2; fill: none; }
            .measurement { stroke: green; stroke-width: 2; fill: none; }
            .control { stroke: purple; stroke-width: 2; fill: none; }
            .power { stroke: black; stroke-width: 2; fill: none; }
            .goose { stroke: orange; stroke-width: 1.5; fill: none; stroke-dasharray: 6,3; }
            .section-box { fill: #f8f9fa; stroke: #dee2e6; stroke-width: 1; }
        </style>
    </defs>

    <!-- 背景 -->
    <rect width="1600" height="1200" fill="white" stroke="#ddd"/>

    <!-- 标题 -->
    <text x="800" y="30" text-anchor="middle" class="title-text">220kV某某线完整逻辑回路</text>
    <text x="800" y="50" text-anchor="middle" font-size="12" fill="#666">真实厂家设备配置实例</text>

    <!-- 一次设备区域 -->
    <rect x="50" y="80" width="1500" height="120" class="section-box"/>
    <text x="60" y="100" class="section-text">一次设备配置</text>

    <!-- 线路 -->
    <line x1="100" y1="140" x2="1450" y2="140" stroke="black" stroke-width="6"/>
    <text x="775" y="130" text-anchor="middle" font-size="12" font-weight="bold">220kV某某线</text>

    <!-- 电流互感器 -->
    <g transform="translate(300, 120)">
        <circle cx="0" cy="20" r="15" fill="none" stroke="red" stroke-width="2"/>
        <text x="0" y="45" text-anchor="middle" class="device-text" font-weight="bold">TA</text>
        <text x="0" y="58" text-anchor="middle" class="model-text">大连北方 LZZBJ9-252</text>
        <text x="0" y="70" text-anchor="middle" class="model-text">2000/5A</text>

        <!-- 二次绕组接线端子 -->
        <rect x="-40" y="80" width="15" height="8" fill="red" stroke="black"/>
        <text x="-32" y="86" text-anchor="middle" class="model-text" fill="white">1S</text>
        <text x="-32" y="98" text-anchor="middle" class="model-text">保护A</text>

        <rect x="-20" y="80" width="15" height="8" fill="blue" stroke="black"/>
        <text x="-12" y="86" text-anchor="middle" class="model-text" fill="white">2S</text>
        <text x="-12" y="98" text-anchor="middle" class="model-text">保护B</text>

        <rect x="5" y="80" width="15" height="8" fill="green" stroke="black"/>
        <text x="12" y="86" text-anchor="middle" class="model-text" fill="white">3S</text>
        <text x="12" y="98" text-anchor="middle" class="model-text">测量</text>

        <rect x="25" y="80" width="15" height="8" fill="orange" stroke="black"/>
        <text x="32" y="86" text-anchor="middle" class="model-text" fill="white">4S</text>
        <text x="32" y="98" text-anchor="middle" class="model-text">计量</text>
    </g>

    <!-- 断路器 -->
    <g transform="translate(750, 120)">
        <rect x="0" y="0" width="50" height="40" fill="none" stroke="black" stroke-width="3"/>
        <line x1="8" y1="8" x2="42" y2="32" stroke="black" stroke-width="4"/>
        <text x="25" y="55" text-anchor="middle" class="device-text" font-weight="bold">QF</text>
        <text x="25" y="68" text-anchor="middle" class="model-text">西安西电 LW36-252</text>
        <text x="25" y="80" text-anchor="middle" class="model-text">252kV/4000A/50kA</text>

        <!-- 操作机构 -->
        <rect x="-20" y="90" width="15" height="10" fill="red" stroke="black"/>
        <text x="-12" y="98" text-anchor="middle" class="model-text" fill="white">YT1</text>

        <rect x="55" y="90" width="15" height="10" fill="blue" stroke="black"/>
        <text x="62" y="98" text-anchor="middle" class="model-text" fill="white">YT2</text>

        <rect x="17" y="110" width="15" height="10" fill="green" stroke="black"/>
        <text x="25" y="118" text-anchor="middle" class="model-text" fill="white">YC</text>
    </g>

    <!-- 电压互感器 -->
    <g transform="translate(1200, 120)">
        <rect x="0" y="0" width="40" height="40" fill="none" stroke="blue" stroke-width="2"/>
        <text x="20" y="25" text-anchor="middle" class="device-text" font-weight="bold">PT</text>
        <text x="20" y="55" text-anchor="middle" class="model-text">大连北方 JDZ10-252</text>
        <text x="20" y="68" text-anchor="middle" class="model-text">220kV/100V</text>

        <!-- 二次绕组 -->
        <rect x="-10" y="80" width="60" height="8" fill="blue" stroke="black"/>
        <text x="20" y="86" text-anchor="middle" class="model-text" fill="white">a-x, b-y, c-z</text>
    </g>

    <!-- 保护装置区域 -->
    <rect x="50" y="250" width="700" height="200" class="section-box"/>
    <text x="60" y="270" class="section-text">保护装置</text>

    <!-- 保护装置A -->
    <g transform="translate(100, 290)">
        <rect x="0" y="0" width="250" height="80" fill="#ffe6e6" stroke="red" stroke-width="2"/>
        <text x="125" y="20" text-anchor="middle" font-size="12" font-weight="bold">线路保护A</text>
        <text x="125" y="35" text-anchor="middle" class="device-text">南京南瑞继保 RCS-978A</text>
        <text x="125" y="50" text-anchor="middle" class="model-text">距离+零序+重合闸+失灵</text>
        <text x="125" y="65" text-anchor="middle" class="model-text">IEC 61850通信</text>

        <!-- 输入端子 -->
        <rect x="-15" y="10" width="10" height="8" fill="red" stroke="black"/>
        <text x="-25" y="5" class="model-text">CT-1S</text>

        <rect x="-15" y="25" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="20" class="model-text">PT</text>

        <rect x="-15" y="40" width="10" height="8" fill="black" stroke="black"/>
        <text x="-35" y="35" class="model-text">DC220V-I</text>

        <!-- 输出端子 -->
        <rect x="255" y="15" width="10" height="8" fill="red" stroke="black"/>
        <text x="275" y="10" class="model-text">跳闸YT1</text>

        <rect x="255" y="35" width="10" height="8" fill="orange" stroke="black"/>
        <text x="275" y="30" class="model-text">GOOSE</text>
    </g>

    <!-- 保护装置B -->
    <g transform="translate(400, 290)">
        <rect x="0" y="0" width="250" height="80" fill="#e6f3ff" stroke="blue" stroke-width="2"/>
        <text x="125" y="20" text-anchor="middle" font-size="12" font-weight="bold">线路保护B</text>
        <text x="125" y="35" text-anchor="middle" class="device-text">北京四方继保 CSC-278A</text>
        <text x="125" y="50" text-anchor="middle" class="model-text">距离+零序+重合闸+失灵</text>
        <text x="125" y="65" text-anchor="middle" class="model-text">IEC 61850通信</text>

        <!-- 输入端子 -->
        <rect x="-15" y="10" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="5" class="model-text">CT-2S</text>

        <rect x="-15" y="25" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="20" class="model-text">PT</text>

        <rect x="-15" y="40" width="10" height="8" fill="black" stroke="black"/>
        <text x="-35" y="35" class="model-text">DC220V-II</text>

        <!-- 输出端子 -->
        <rect x="255" y="15" width="10" height="8" fill="blue" stroke="black"/>
        <text x="275" y="10" class="model-text">跳闸YT2</text>

        <rect x="255" y="35" width="10" height="8" fill="orange" stroke="black"/>
        <text x="275" y="30" class="model-text">GOOSE</text>
    </g>

    <!-- 测控通信区域 -->
    <rect x="800" y="250" width="750" height="200" class="section-box"/>
    <text x="810" y="270" class="section-text">测控通信装置</text>

    <!-- 测控装置 -->
    <g transform="translate(850, 290)">
        <rect x="0" y="0" width="200" height="80" fill="#e6ffe6" stroke="green" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">测控装置</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">国电南瑞 RCS-9611CS</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">测量+控制+通信</text>
        <text x="100" y="65" text-anchor="middle" class="model-text">遥测+遥控+遥信</text>

        <!-- 输入端子 -->
        <rect x="-15" y="15" width="10" height="8" fill="green" stroke="black"/>
        <text x="-30" y="10" class="model-text">CT-3S</text>

        <rect x="-15" y="30" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="25" class="model-text">PT</text>

        <!-- 输出端子 -->
        <rect x="205" y="25" width="10" height="8" fill="purple" stroke="black"/>
        <text x="225" y="20" class="model-text">控制</text>
    </g>

    <!-- 智能终端 -->
    <g transform="translate(1100, 290)">
        <rect x="0" y="0" width="200" height="80" fill="#fff9e6" stroke="#ffc107" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">智能终端</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">国电南瑞 RCS-9611IT</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">智能操作箱</text>
        <text x="100" y="65" text-anchor="middle" class="model-text">GOOSE接收+硬接点输出</text>

        <!-- 输入端子 -->
        <rect x="-15" y="20" width="10" height="8" fill="orange" stroke="black"/>
        <text x="-35" y="15" class="model-text">GOOSE</text>

        <rect x="-15" y="35" width="10" height="8" fill="purple" stroke="black"/>
        <text x="-30" y="30" class="model-text">控制</text>

        <!-- 输出端子 -->
        <rect x="205" y="15" width="10" height="8" fill="green" stroke="black"/>
        <text x="225" y="10" class="model-text">合闸</text>

        <rect x="205" y="35" width="10" height="8" fill="red" stroke="black"/>
        <text x="225" y="30" class="model-text">跳闸</text>
    </g>

    <!-- 合并单元 -->
    <g transform="translate(850, 390)">
        <rect x="0" y="0" width="200" height="50" fill="#e6ffff" stroke="cyan" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">合并单元</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">国电南瑞 RCS-9611MU (4000Hz采样)</text>

        <!-- 输入端子 -->
        <rect x="-15" y="15" width="10" height="8" fill="red" stroke="black"/>
        <text x="-25" y="10" class="model-text">CT</text>

        <rect x="-15" y="30" width="10" height="8" fill="blue" stroke="black"/>
        <text x="-25" y="25" class="model-text">PT</text>

        <!-- 输出端子 -->
        <rect x="205" y="20" width="10" height="8" fill="orange" stroke="black"/>
        <text x="225" y="15" class="model-text">SV</text>
    </g>

    <!-- 直流电源系统 -->
    <rect x="50" y="500" width="1500" height="120" class="section-box"/>
    <text x="60" y="520" class="section-text">直流电源系统</text>

    <!-- 直流电源I -->
    <g transform="translate(150, 540)">
        <rect x="0" y="0" width="200" height="60" fill="#ffe6e6" stroke="red" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">直流电源I系统</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">许继电气 XJZG-220/110</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">DC 220V/600Ah</text>

        <!-- 输出端子 -->
        <rect x="205" y="20" width="10" height="8" fill="red" stroke="black"/>
        <text x="225" y="15" class="model-text">+220V-I</text>

        <rect x="205" y="35" width="10" height="8" fill="black" stroke="black"/>
        <text x="225" y="30" class="model-text">-220V-I</text>
    </g>

    <!-- 直流电源II -->
    <g transform="translate(450, 540)">
        <rect x="0" y="0" width="200" height="60" fill="#e6f3ff" stroke="blue" stroke-width="2"/>
        <text x="100" y="20" text-anchor="middle" font-size="12" font-weight="bold">直流电源II系统</text>
        <text x="100" y="35" text-anchor="middle" class="device-text">许继电气 XJZG-220/110</text>
        <text x="100" y="50" text-anchor="middle" class="model-text">DC 220V/600Ah</text>

        <!-- 输出端子 -->
        <rect x="205" y="20" width="10" height="8" fill="blue" stroke="black"/>
        <text x="225" y="15" class="model-text">+220V-II</text>

        <rect x="205" y="35" width="10" height="8" fill="black" stroke="black"/>
        <text x="225" y="30" class="model-text">-220V-II</text>
    </g>

    <!-- 直流分电屏 -->
    <g transform="translate(800, 540)">
        <rect x="0" y="0" width="300" height="60" fill="#fff9e6" stroke="#ffc107" stroke-width="2"/>
        <text x="150" y="20" text-anchor="middle" font-size="12" font-weight="bold">直流分电屏</text>
        <text x="80" y="35" text-anchor="middle" class="model-text">保护A: DC220V-I</text>
        <text x="220" y="35" text-anchor="middle" class="model-text">保护B: DC220V-II</text>
        <text x="80" y="50" text-anchor="middle" class="model-text">测控: DC220V</text>
        <text x="220" y="50" text-anchor="middle" class="model-text">信号: DC110V</text>
    </g>

    <!-- 连接线 - 垂直和水平布局 -->

    <!-- CT二次绕组到各装置 -->
    <!-- 1S绕组到保护A -->
    <line x1="268" y1="208" x2="268" y2="290" class="protection-a"/>
    <line x1="268" y1="290" x2="85" y2="290" class="protection-a"/>
    <line x1="85" y1="290" x2="85" y2="300" class="protection-a"/>

    <!-- 2S绕组到保护B -->
    <line x1="288" y1="208" x2="288" y2="240" class="protection-b"/>
    <line x1="288" y1="240" x2="385" y2="240" class="protection-b"/>
    <line x1="385" y1="240" x2="385" y2="300" class="protection-b"/>

    <!-- 3S绕组到测控装置 -->
    <line x1="312" y1="208" x2="312" y2="230" class="measurement"/>
    <line x1="312" y1="230" x2="835" y2="230" class="measurement"/>
    <line x1="835" y1="230" x2="835" y2="305" class="measurement"/>

    <!-- PT到各装置 -->
    <line x1="1220" y1="208" x2="1220" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="1220" y1="220" x2="85" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="85" y1="220" x2="85" y2="315" stroke="blue" stroke-width="2"/>

    <line x1="1220" y1="220" x2="385" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="385" y1="220" x2="385" y2="315" stroke="blue" stroke-width="2"/>

    <line x1="1220" y1="220" x2="835" y2="220" stroke="blue" stroke-width="2"/>
    <line x1="835" y1="220" x2="835" y2="320" stroke="blue" stroke-width="2"/>

    <!-- 保护装置到断路器跳闸线圈 -->
    <line x1="365" y1="305" x2="730" y2="305" class="protection-a"/>
    <line x1="730" y1="305" x2="730" y2="210" class="protection-a"/>

    <line x1="665" y1="305" x2="770" y2="305" class="protection-b"/>
    <line x1="770" y1="305" x2="770" y2="210" class="protection-b"/>

    <!-- 测控到智能终端 -->
    <line x1="1065" y1="315" x2="1085" y2="315" class="control"/>

    <!-- 智能终端到断路器 -->
    <line x1="1315" y1="305" x2="1400" y2="305" stroke="green" stroke-width="2"/>
    <line x1="1400" y1="305" x2="1400" y2="250" stroke="green" stroke-width="2"/>
    <line x1="1400" y1="250" x2="775" y2="250" stroke="green" stroke-width="2"/>
    <line x1="775" y1="250" x2="775" y2="230" stroke="green" stroke-width="2"/>

    <line x1="1315" y1="325" x2="1420" y2="325" stroke="red" stroke-width="2"/>
    <line x1="1420" y1="325" x2="1420" y2="260" stroke="red" stroke-width="2"/>
    <line x1="1420" y1="260" x2="730" y2="260" stroke="red" stroke-width="2"/>
    <line x1="730" y1="260" x2="730" y2="210" stroke="red" stroke-width="2"/>

    <!-- GOOSE连接 -->
    <line x1="365" y1="325" x2="1085" y2="325" class="goose"/>
    <line x1="665" y1="325" x2="1085" y2="325" class="goose"/>

    <!-- 直流电源连接 -->
    <line x1="365" y1="330" x2="365" y2="540" class="power"/>
    <line x1="365" y1="540" x2="365" y2="560" class="power"/>

    <line x1="665" y1="330" x2="665" y2="540" class="power"/>
    <line x1="665" y1="540" x2="665" y2="560" class="power"/>

    <line x1="1065" y1="330" x2="1065" y2="540" class="power"/>
    <line x1="1065" y1="540" x2="950" y2="540" class="power"/>
    <line x1="950" y1="540" x2="950" y2="560" class="power"/>

    <!-- 技术参数说明 -->
    <rect x="50" y="680" width="750" height="200" class="section-box"/>
    <text x="60" y="700" class="section-text">设备技术参数</text>

    <g transform="translate(80, 720)">
        <text x="0" y="0" font-size="11" font-weight="bold" fill="#dc3545">保护装置:</text>
        <text x="0" y="15" class="model-text">• RCS-978A: 距离保护≤20ms, 零序保护≤30ms</text>
        <text x="0" y="30" class="model-text">• CSC-278A: 距离保护≤20ms, 零序保护≤30ms</text>
        <text x="0" y="45" class="model-text">• 重合闸: 0.5-300s可调, 单相+三相</text>
        <text x="0" y="60" class="model-text">• 通信: IEC 61850, GOOSE≤4ms</text>

        <text x="300" y="0" font-size="11" font-weight="bold" fill="#198754">断路器:</text>
        <text x="300" y="15" class="model-text">• LW36-252: SF6断路器, 252kV/4000A/50kA</text>
        <text x="300" y="30" class="model-text">• 分闸时间: ≤40ms, 合闸时间: ≤100ms</text>
        <text x="300" y="45" class="model-text">• 机械寿命: 10000次</text>
        <text x="300" y="60" class="model-text">• 操动机构: CD17弹簧操动</text>

        <text x="0" y="90" font-size="11" font-weight="bold" fill="#0d6efd">互感器:</text>
        <text x="0" y="105" class="model-text">• CT: LZZBJ9-252, 2000/5A, 保护5P20</text>
        <text x="0" y="120" class="model-text">• PT: JDZ10-252, 220kV/100V, 保护3P</text>
        <text x="0" y="135" class="model-text">• 热稳定: 4s/80kA, 动稳定: 200kA</text>

        <text x="300" y="90" font-size="11" font-weight="bold" fill="#6f42c1">通信性能:</text>
        <text x="300" y="105" class="model-text">• GOOSE: ≤4ms, SV: 4000Hz采样≤3ms</text>
        <text x="300" y="120" class="model-text">• 同步精度: ±1μs (IEEE 1588v2)</text>
        <text x="300" y="135" class="model-text">• 网络: 双网冗余, 光纤连接</text>
    </g>

    <!-- 保护逻辑说明 -->
    <rect x="850" y="680" width="700" height="200" class="section-box"/>
    <text x="860" y="700" class="section-text">保护配置逻辑</text>

    <g transform="translate(880, 720)">
        <text x="0" y="0" font-size="11" font-weight="bold" fill="#dc3545">主保护:</text>
        <text x="0" y="15" class="model-text">• 距离I段: 瞬时, 85%线路长度</text>
        <text x="0" y="30" class="model-text">• 距离II段: 0.5s, 120%线路长度</text>
        <text x="0" y="45" class="model-text">• 零序I段: 瞬时, 85%线路长度</text>
        <text x="0" y="60" class="model-text">• 纵差保护: 光纤通道, ≤10ms</text>

        <text x="300" y="0" font-size="11" font-weight="bold" fill="#198754">后备保护:</text>
        <text x="300" y="15" class="model-text">• 距离III段: 1.2s, 覆盖相邻线路</text>
        <text x="300" y="30" class="model-text">• 零序II段: 0.5s, 覆盖相邻线路</text>
        <text x="300" y="45" class="model-text">• 过负荷: 告警+延时跳闸</text>
        <text x="300" y="60" class="model-text">• 失灵保护: 150ms启动</text>

        <text x="0" y="90" font-size="11" font-weight="bold" fill="#0d6efd">自动装置:</text>
        <text x="0" y="105" class="model-text">• 重合闸: 单相0.5s, 三相1.0s</text>
        <text x="0" y="120" class="model-text">• 重合次数: 1次, 检同期功能</text>
        <text x="0" y="135" class="model-text">• 检无压: 电压<30V, 频率±0.5Hz</text>

        <text x="300" y="90" font-size="11" font-weight="bold" fill="#ffc107">回路特点:</text>
        <text x="300" y="105" class="model-text">• 双重化保护完全独立</text>
        <text x="300" y="120" class="model-text">• 硬接线跳闸+GOOSE信息</text>
        <text x="300" y="135" class="model-text">• CT/PT专用绕组配置</text>
    </g>
    
</svg>