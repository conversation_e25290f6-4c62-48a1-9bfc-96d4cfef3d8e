<?xml version="1.0" encoding="UTF-8"?>
<svg width="1300" height="1000" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .signal-line { stroke: orange; stroke-width: 2; fill: none; }
            .alarm-line { stroke: red; stroke-width: 2; fill: none; stroke-dasharray: 5,5; }
            .status-line { stroke: blue; stroke-width: 1.5; fill: none; }
            .control-line { stroke: green; stroke-width: 1; fill: none; stroke-dasharray: 3,3; }
        </style>
    </defs>

    <!-- 背景 -->
    <rect width="1300" height="1000" fill="#f8f9fa" stroke="#ddd"/>

    <!-- 标题 -->
    <text x="650" y="30" text-anchor="middle" class="title-text">信号回路图</text>
    <text x="650" y="50" text-anchor="middle" font-size="12" fill="#666">状态信号、告警信号、故障信号系统</text>

    <!-- 信号分类说明 -->
    <g transform="translate(50, 80)">
        <text x="0" y="0" font-size="14" font-weight="bold">信号回路分类:</text>
        <text x="0" y="20" font-size="11">1. 位置信号 - 反映设备运行状态（断路器、隔离开关位置）</text>
        <text x="0" y="35" font-size="11">2. 保护动作信号 - 反映保护装置动作情况</text>
        <text x="0" y="50" font-size="11">3. 异常信号（预告信号） - 设备异常但未达到跳闸条件</text>
        <text x="0" y="65" font-size="11">4. 故障信号 - 设备故障需要立即处理</text>
    </g>

    <!-- 位置信号回路 -->
    <g transform="translate(100, 180)">
        <text x="0" y="0" font-size="12" font-weight="bold">1. 位置信号回路:</text>

        <!-- 断路器本体 -->
        <rect x="0" y="20" width="60" height="60" fill="none" stroke="black" stroke-width="2"/>
        <line x1="10" y1="30" x2="50" y2="70" stroke="black" stroke-width="3"/>
        <text x="30" y="95" text-anchor="middle" font-size="8">QF1</text>
        <text x="30" y="110" text-anchor="middle" font-size="8">220kV出线</text>

        <!-- 辅助触点 -->
        <circle cx="70" cy="35" r="3" fill="green"/>
        <text x="80" y="30" font-size="8">合位触点</text>
        <circle cx="70" cy="65" r="3" fill="red"/>
        <text x="80" y="60" font-size="8">分位触点</text>

        <!-- 合位信号灯 -->
        <circle cx="150" cy="35" r="12" fill="green" stroke="black" stroke-width="2"/>
        <text x="150" y="40" text-anchor="middle" font-size="8" fill="white">合</text>
        <text x="150" y="60" text-anchor="middle" font-size="8">HL1-合位灯</text>

        <!-- 分位信号灯 -->
        <circle cx="150" cy="80" r="12" fill="red" stroke="black" stroke-width="2"/>
        <text x="150" y="85" text-anchor="middle" font-size="8" fill="white">分</text>
        <text x="150" y="105" text-anchor="middle" font-size="8">HL2-分位灯</text>

        <!-- 远传信号 -->
        <rect x="220" y="40" width="60" height="40" fill="lightblue" stroke="black" stroke-width="1"/>
        <text x="250" y="55" text-anchor="middle" font-size="8">远传装置</text>
        <text x="250" y="70" text-anchor="middle" font-size="8">遥信</text>

        <!-- 连接线 -->
        <line x1="70" y1="35" x2="138" y2="35" class="status-line"/>
        <line x1="70" y1="65" x2="138" y2="80" class="status-line"/>
        <line x1="162" y1="50" x2="220" y2="55" class="signal-line"/>

        <!-- 电源连接 -->
        <line x1="150" y1="23" x2="150" y2="10" class="signal-line"/>
        <text x="155" y="15" font-size="8">+KM</text>
        <line x1="150" y1="68" x2="150" y2="10" class="signal-line"/>
    </g>

    <!-- 保护动作信号回路 -->
    <g transform="translate(500, 180)">
        <text x="0" y="0" font-size="12" font-weight="bold">2. 保护动作信号回路:</text>

        <!-- 保护装置 -->
        <rect x="0" y="20" width="80" height="60" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="40" y="40" text-anchor="middle" font-size="10" font-weight="bold">保护装置</text>
        <text x="40" y="55" text-anchor="middle" font-size="8">KA1</text>
        <text x="40" y="70" text-anchor="middle" font-size="8">线路保护</text>

        <!-- 保护出口继电器 -->
        <rect x="120" y="30" width="40" height="30" fill="lightcoral" stroke="black" stroke-width="1"/>
        <text x="140" y="50" text-anchor="middle" font-size="8">KJ1</text>
        <text x="140" y="75" text-anchor="middle" font-size="8">跳闸继电器</text>

        <!-- 信号继电器 -->
        <rect x="120" y="90" width="40" height="30" fill="lightgreen" stroke="black" stroke-width="1"/>
        <text x="140" y="110" text-anchor="middle" font-size="8">KS1</text>
        <text x="140" y="135" text-anchor="middle" font-size="8">信号继电器</text>

        <!-- 跳闸信号灯 -->
        <circle cx="220" cy="45" r="12" fill="red" stroke="black" stroke-width="2"/>
        <text x="220" y="50" text-anchor="middle" font-size="8" fill="white">跳</text>
        <text x="220" y="70" text-anchor="middle" font-size="8">HL3-跳闸灯</text>

        <!-- 保护动作信号灯 -->
        <circle cx="220" cy="105" r="12" fill="yellow" stroke="black" stroke-width="2"/>
        <text x="220" y="110" text-anchor="middle" font-size="8">保护</text>
        <text x="220" y="130" text-anchor="middle" font-size="8">HL4-保护灯</text>

        <!-- 音响信号 -->
        <rect x="280" y="60" width="50" height="30" fill="orange" stroke="black" stroke-width="2"/>
        <text x="305" y="80" text-anchor="middle" font-size="8">音响</text>
        <text x="305" y="105" text-anchor="middle" font-size="8">HA1</text>

        <!-- 连接线 -->
        <line x1="80" y1="40" x2="120" y2="45" class="signal-line"/>
        <line x1="80" y1="60" x2="120" y2="105" class="signal-line"/>
        <line x1="160" y1="45" x2="208" y2="45" class="alarm-line"/>
        <line x1="160" y1="105" x2="208" y2="105" class="signal-line"/>
        <line x1="232" y1="75" x2="280" y2="75" class="alarm-line"/>
    </g>

    <!-- 异常信号回路 -->
    <g transform="translate(100, 350)">
        <text x="0" y="0" font-size="12" font-weight="bold">3. 异常信号回路（预告信号）:</text>

        <!-- 变压器本体 -->
        <rect x="0" y="20" width="60" height="80" fill="lightsteelblue" stroke="black" stroke-width="2"/>
        <text x="30" y="55" text-anchor="middle" font-size="10">变压器</text>
        <text x="30" y="70" text-anchor="middle" font-size="8">T1</text>
        <text x="30" y="85" text-anchor="middle" font-size="8">220/110kV</text>

        <!-- 温度监测 -->
        <circle cx="80" cy="40" r="8" fill="orange" stroke="black"/>
        <text x="80" y="45" text-anchor="middle" font-size="6">T</text>
        <text x="95" y="35" font-size="8">温度</text>

        <!-- 油位监测 -->
        <circle cx="80" cy="60" r="8" fill="blue" stroke="black"/>
        <text x="80" y="65" text-anchor="middle" font-size="6">L</text>
        <text x="95" y="55" font-size="8">油位</text>

        <!-- 压力监测 -->
        <circle cx="80" cy="80" r="8" fill="green" stroke="black"/>
        <text x="80" y="85" text-anchor="middle" font-size="6">P</text>
        <text x="95" y="75" font-size="8">压力</text>

        <!-- 预告信号灯 -->
        <circle cx="150" cy="40" r="10" fill="yellow" stroke="black" stroke-width="2"/>
        <text x="150" y="45" text-anchor="middle" font-size="6">温度高</text>
        <text x="150" y="65" text-anchor="middle" font-size="8">HL5</text>

        <circle cx="150" cy="80" r="10" fill="blue" stroke="black" stroke-width="2"/>
        <text x="150" y="85" text-anchor="middle" font-size="6">油位低</text>
        <text x="150" y="105" text-anchor="middle" font-size="8">HL6</text>

        <!-- 预告音响 -->
        <rect x="200" y="50" width="40" height="25" fill="yellow" stroke="black" stroke-width="1"/>
        <text x="220" y="67" text-anchor="middle" font-size="8">预告</text>
        <text x="220" y="85" text-anchor="middle" font-size="8">HA2</text>

        <!-- 连接线 -->
        <line x1="88" y1="40" x2="140" y2="40" class="signal-line"/>
        <line x1="88" y1="80" x2="140" y2="80" class="signal-line"/>
        <line x1="160" y1="60" x2="200" y2="62" class="signal-line"/>
    </g>

    <!-- 控制回路断线信号 -->
    <g transform="translate(500, 350)">
        <text x="0" y="0" font-size="12" font-weight="bold">4. 控制回路断线信号:</text>

        <!-- 控制回路 -->
        <rect x="0" y="20" width="80" height="40" fill="lightgreen" stroke="black" stroke-width="2"/>
        <text x="40" y="35" text-anchor="middle" font-size="8">控制回路</text>
        <text x="40" y="50" text-anchor="middle" font-size="8">监测装置</text>

        <!-- 断线检测继电器 -->
        <rect x="120" y="25" width="40" height="30" fill="lightpink" stroke="black" stroke-width="1"/>
        <text x="140" y="45" text-anchor="middle" font-size="8">KV1</text>
        <text x="140" y="70" text-anchor="middle" font-size="8">断线继电器</text>

        <!-- 断线信号灯 -->
        <circle cx="220" cy="40" r="10" fill="red" stroke="black" stroke-width="2"/>
        <text x="220" y="45" text-anchor="middle" font-size="6">断线</text>
        <text x="220" y="65" text-anchor="middle" font-size="8">HL7</text>

        <!-- 连接线 -->
        <line x1="80" y1="40" x2="120" y2="40" class="control-line"/>
        <line x1="160" y1="40" x2="210" y2="40" class="alarm-line"/>
    </g>

    <!-- 直流系统信号 -->
    <g transform="translate(800, 350)">
        <text x="0" y="0" font-size="12" font-weight="bold">5. 直流系统信号:</text>

        <!-- 直流系统 -->
        <rect x="0" y="20" width="60" height="60" fill="lightsteelblue" stroke="black" stroke-width="2"/>
        <text x="30" y="40" text-anchor="middle" font-size="8">直流系统</text>
        <text x="30" y="55" text-anchor="middle" font-size="8">DC 220V</text>
        <text x="30" y="70" text-anchor="middle" font-size="8">监测装置</text>

        <!-- 绝缘监测信号 -->
        <circle cx="120" cy="35" r="10" fill="red" stroke="black" stroke-width="2"/>
        <text x="120" y="40" text-anchor="middle" font-size="6">接地</text>
        <text x="120" y="55" text-anchor="middle" font-size="8">HL8</text>

        <!-- 欠压信号 -->
        <circle cx="120" cy="65" r="10" fill="yellow" stroke="black" stroke-width="2"/>
        <text x="120" y="70" text-anchor="middle" font-size="6">欠压</text>
        <text x="120" y="85" text-anchor="middle" font-size="8">HL9</text>

        <!-- 连接线 -->
        <line x1="60" y1="40" x2="110" y2="35" class="alarm-line"/>
        <line x1="60" y1="60" x2="110" y2="65" class="signal-line"/>
    </g>

    <!-- 信号回路电源 -->
    <g transform="translate(50, 550)">
        <text x="0" y="0" font-size="12" font-weight="bold">信号回路电源系统:</text>

        <!-- 直流电源 -->
        <rect x="0" y="20" width="80" height="40" fill="lightcoral" stroke="black" stroke-width="2"/>
        <text x="40" y="35" text-anchor="middle" font-size="8">直流电源</text>
        <text x="40" y="50" text-anchor="middle" font-size="8">DC 220V</text>

        <!-- 交流电源 -->
        <rect x="120" y="20" width="80" height="40" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="160" y="35" text-anchor="middle" font-size="8">交流电源</text>
        <text x="160" y="50" text-anchor="middle" font-size="8">AC 220V</text>

        <!-- 信号母线 -->
        <line x1="0" y1="80" x2="1000" y2="80" stroke="red" stroke-width="3"/>
        <text x="10" y="75" font-size="10" font-weight="bold" fill="red">信号电源母线</text>

        <!-- 各信号回路分支 -->
        <line x1="150" y1="80" x2="150" y2="100" class="signal-line"/>
        <text x="155" y="95" font-size="8">位置信号</text>

        <line x1="300" y1="80" x2="300" y2="100" class="signal-line"/>
        <text x="305" y="95" font-size="8">保护信号</text>

        <line x1="450" y1="80" x2="450" y2="100" class="signal-line"/>
        <text x="455" y="95" font-size="8">异常信号</text>

        <line x1="600" y1="80" x2="600" y2="100" class="signal-line"/>
        <text x="605" y="95" font-size="8">故障信号</text>
    </g>

    <!-- 信号处理与传输 -->
    <g transform="translate(50, 700)">
        <text x="0" y="0" font-size="12" font-weight="bold">信号处理与传输:</text>

        <!-- 信号采集装置 -->
        <rect x="0" y="20" width="100" height="50" fill="lightcyan" stroke="black" stroke-width="2"/>
        <text x="50" y="40" text-anchor="middle" font-size="10">信号采集装置</text>
        <text x="50" y="55" text-anchor="middle" font-size="8">遥信单元</text>

        <!-- 通信处理器 -->
        <rect x="150" y="20" width="100" height="50" fill="lightgreen" stroke="black" stroke-width="2"/>
        <text x="200" y="40" text-anchor="middle" font-size="10">通信处理器</text>
        <text x="200" y="55" text-anchor="middle" font-size="8">协议转换</text>

        <!-- 监控系统 -->
        <rect x="300" y="20" width="100" height="50" fill="lightyellow" stroke="black" stroke-width="2"/>
        <text x="350" y="40" text-anchor="middle" font-size="10">监控系统</text>
        <text x="350" y="55" text-anchor="middle" font-size="8">SCADA</text>

        <!-- 远程终端 -->
        <rect x="450" y="20" width="100" height="50" fill="lightsteelblue" stroke="black" stroke-width="2"/>
        <text x="500" y="40" text-anchor="middle" font-size="10">远程终端</text>
        <text x="500" y="55" text-anchor="middle" font-size="8">调度中心</text>

        <!-- 连接线 -->
        <line x1="100" y1="45" x2="150" y2="45" class="signal-line"/>
        <text x="125" y="40" text-anchor="middle" font-size="8">RS485</text>

        <line x1="250" y1="45" x2="300" y2="45" class="signal-line"/>
        <text x="275" y="40" text-anchor="middle" font-size="8">以太网</text>

        <line x1="400" y1="45" x2="450" y2="45" class="signal-line"/>
        <text x="425" y="40" text-anchor="middle" font-size="8">光纤</text>
    </g>

    <!-- 技术要求和说明 -->
    <g transform="translate(50, 850)">
        <text x="0" y="0" font-size="14" font-weight="bold">信号回路技术要求:</text>

        <g transform="translate(0, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#dc3545">电源要求:</text>
            <text x="0" y="20" font-size="10">• 直流信号：DC 220V、110V（高可靠性）</text>
            <text x="0" y="35" font-size="10">• 交流信号：AC 220V（照明信号）</text>
            <text x="0" y="50" font-size="10">• 低压信号：DC 24V、48V（通信信号）</text>
            <text x="0" y="65" font-size="10">• 电源独立：与控制电源分开供电</text>
        </g>

        <g transform="translate(300, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#0d6efd">信号分类:</text>
            <text x="0" y="20" font-size="10">• 位置信号：设备状态（合/分位）</text>
            <text x="0" y="35" font-size="10">• 保护信号：保护动作、跳闸</text>
            <text x="0" y="50" font-size="10">• 预告信号：异常但未跳闸</text>
            <text x="0" y="65" font-size="10">• 故障信号：设备故障告警</text>
        </g>

        <g transform="translate(600, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#198754">技术特点:</text>
            <text x="0" y="20" font-size="10">• 快速响应：毫秒级信号传输</text>
            <text x="0" y="35" font-size="10">• 可靠性高：冗余设计</text>
            <text x="0" y="50" font-size="10">• 远程传输：光纤通信</text>
            <text x="0" y="65" font-size="10">• 智能化：自诊断功能</text>
        </g>

        <g transform="translate(900, 30)">
            <text x="0" y="0" font-size="12" font-weight="bold" fill="#6f42c1">虚端子应用:</text>
            <text x="0" y="20" font-size="10">• GOOSE信号：快速传输</text>
            <text x="0" y="35" font-size="10">• 数字化信号：减少硬接线</text>
            <text x="0" y="50" font-size="10">• 网络化：统一管理</text>
            <text x="0" y="65" font-size="10">• IEC 61850：标准化</text>
        </g>
    </g>

</svg>