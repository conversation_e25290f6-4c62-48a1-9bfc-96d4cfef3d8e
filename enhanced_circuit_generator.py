#!/usr/bin/env python3
"""
增强版虚端子回路图生成系统
基于IEC61850配置文件生成传统二次回路图
"""

import os
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class EnhancedCircuitGenerator:
    """增强版回路图生成器"""
    
    def __init__(self):
        # 基于GB/T 4728标准的电气符号定义
        self.symbols = {
            'current_transformer': {
                'name': '电流互感器',
                'symbol': 'TA',
                'svg_template': '''
                <g id="ct_{name}">
                    <circle cx="50" cy="50" r="25" fill="none" stroke="black" stroke-width="2"/>
                    <circle cx="50" cy="50" r="15" fill="none" stroke="black" stroke-width="1"/>
                    <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TA</text>
                    <text x="50" y="85" text-anchor="middle" font-size="8">{name}</text>
                    <!-- 一次侧端子 -->
                    <circle cx="25" cy="50" r="2" fill="black"/>
                    <text x="20" y="45" font-size="8">P1</text>
                    <circle cx="75" cy="50" r="2" fill="black"/>
                    <text x="80" y="45" font-size="8">P2</text>
                    <!-- 二次侧端子 -->
                    <circle cx="35" cy="75" r="2" fill="red"/>
                    <text x="30" y="90" font-size="8">S1</text>
                    <circle cx="65" cy="75" r="2" fill="red"/>
                    <text x="70" y="90" font-size="8">S2</text>
                </g>''',
                'width': 100,
                'height': 100
            },
            'voltage_transformer': {
                'name': '电压互感器',
                'symbol': 'TV',
                'svg_template': '''
                <g id="vt_{name}">
                    <circle cx="50" cy="50" r="25" fill="none" stroke="blue" stroke-width="2"/>
                    <circle cx="50" cy="50" r="15" fill="none" stroke="blue" stroke-width="1"/>
                    <text x="50" y="55" text-anchor="middle" font-size="10" font-weight="bold">TV</text>
                    <text x="50" y="85" text-anchor="middle" font-size="8">{name}</text>
                    <!-- 一次侧端子 -->
                    <circle cx="25" cy="50" r="2" fill="black"/>
                    <text x="20" y="45" font-size="8">P1</text>
                    <circle cx="75" cy="50" r="2" fill="black"/>
                    <text x="80" y="45" font-size="8">P2</text>
                    <!-- 二次侧端子 -->
                    <circle cx="35" cy="75" r="2" fill="blue"/>
                    <text x="30" y="90" font-size="8">S1</text>
                    <circle cx="65" cy="75" r="2" fill="blue"/>
                    <text x="70" y="90" font-size="8">S2</text>
                </g>''',
                'width': 100,
                'height': 100
            },
            'protection_device': {
                'name': '保护装置',
                'symbol': 'PROT',
                'svg_template': '''
                <g id="prot_{name}">
                    <rect x="10" y="20" width="80" height="60" fill="lightgray" stroke="black" stroke-width="2"/>
                    <text x="50" y="45" text-anchor="middle" font-size="10" font-weight="bold">保护装置</text>
                    <text x="50" y="60" text-anchor="middle" font-size="8">{name}</text>
                    <!-- 模拟量输入端子 -->
                    <circle cx="5" cy="35" r="2" fill="red"/>
                    <text x="0" y="30" font-size="6">AI1</text>
                    <circle cx="5" cy="45" r="2" fill="red"/>
                    <text x="0" y="40" font-size="6">AI2</text>
                    <circle cx="5" cy="55" r="2" fill="red"/>
                    <text x="0" y="50" font-size="6">AI3</text>
                    <!-- 数字量输出端子 -->
                    <circle cx="95" cy="35" r="2" fill="green"/>
                    <text x="100" y="30" font-size="6">DO1</text>
                    <circle cx="95" cy="45" r="2" fill="green"/>
                    <text x="100" y="40" font-size="6">DO2</text>
                </g>''',
                'width': 120,
                'height': 100
            },
            'breaker': {
                'name': '断路器',
                'symbol': 'QF',
                'svg_template': '''
                <g id="cb_{name}">
                    <rect x="20" y="30" width="60" height="40" fill="none" stroke="black" stroke-width="2"/>
                    <line x1="30" y1="30" x2="70" y2="70" stroke="black" stroke-width="3"/>
                    <text x="50" y="85" text-anchor="middle" font-size="8">{name}</text>
                    <!-- 主触头端子 -->
                    <circle cx="50" cy="20" r="2" fill="black"/>
                    <circle cx="50" cy="80" r="2" fill="black"/>
                    <!-- 跳闸线圈端子 -->
                    <circle cx="10" cy="50" r="2" fill="red"/>
                    <text x="5" y="45" font-size="6">TC</text>
                    <!-- 合闸线圈端子 -->
                    <circle cx="90" cy="50" r="2" fill="green"/>
                    <text x="95" y="45" font-size="6">CC</text>
                </g>''',
                'width': 100,
                'height': 100
            },
            'relay': {
                'name': '继电器',
                'symbol': 'K',
                'svg_template': '''
                <g id="relay_{name}">
                    <rect x="20" y="20" width="60" height="40" fill="lightblue" stroke="black" stroke-width="1"/>
                    <text x="50" y="45" text-anchor="middle" font-size="10" font-weight="bold">K</text>
                    <text x="50" y="75" text-anchor="middle" font-size="8">{name}</text>
                    <!-- 线圈端子 -->
                    <circle cx="15" cy="40" r="2" fill="black"/>
                    <text x="10" y="35" font-size="6">1</text>
                    <circle cx="85" cy="40" r="2" fill="black"/>
                    <text x="90" y="35" font-size="6">2</text>
                    <!-- 触点端子 -->
                    <circle cx="35" cy="15" r="2" fill="black"/>
                    <text x="30" y="10" font-size="6">3</text>
                    <circle cx="65" cy="15" r="2" fill="black"/>
                    <text x="70" y="10" font-size="6">4</text>
                </g>''',
                'width': 100,
                'height': 80
            }
        }
    
    def create_demo_circuit_data(self) -> Dict:
        """创建演示用的回路数据"""
        
        return {
            'devices': {
                'TA1': {
                    'type': 'current_transformer',
                    'name': 'TA1',
                    'description': '220kV出线1电流互感器',
                    'position': {'x': 100, 'y': 150},
                    'terminals': {
                        'P1': {'type': 'primary', 'connection': 'Line1_P1'},
                        'P2': {'type': 'primary', 'connection': 'Line1_P2'},
                        'S1': {'type': 'secondary', 'connection': 'PROT1_AI1'},
                        'S2': {'type': 'secondary', 'connection': 'PROT1_AI2'}
                    }
                },
                'TA2': {
                    'type': 'current_transformer',
                    'name': 'TA2',
                    'description': '220kV出线2电流互感器',
                    'position': {'x': 100, 'y': 300},
                    'terminals': {
                        'P1': {'type': 'primary', 'connection': 'Line2_P1'},
                        'P2': {'type': 'primary', 'connection': 'Line2_P2'},
                        'S1': {'type': 'secondary', 'connection': 'PROT2_AI1'},
                        'S2': {'type': 'secondary', 'connection': 'PROT2_AI2'}
                    }
                },
                'TV1': {
                    'type': 'voltage_transformer',
                    'name': 'TV1',
                    'description': '220kV母线电压互感器',
                    'position': {'x': 100, 'y': 450},
                    'terminals': {
                        'P1': {'type': 'primary', 'connection': 'Bus_A'},
                        'P2': {'type': 'primary', 'connection': 'Bus_N'},
                        'S1': {'type': 'secondary', 'connection': 'PROT1_VI1'},
                        'S2': {'type': 'secondary', 'connection': 'PROT1_VI2'}
                    }
                },
                'PROT1': {
                    'type': 'protection_device',
                    'name': 'PROT1',
                    'description': '220kV出线1保护装置',
                    'position': {'x': 350, 'y': 200},
                    'terminals': {
                        'AI1': {'type': 'analog_input', 'connection': 'TA1_S1'},
                        'AI2': {'type': 'analog_input', 'connection': 'TA1_S2'},
                        'AI3': {'type': 'analog_input', 'connection': 'TA2_S1'},
                        'VI1': {'type': 'voltage_input', 'connection': 'TV1_S1'},
                        'VI2': {'type': 'voltage_input', 'connection': 'TV1_S2'},
                        'DO1': {'type': 'digital_output', 'connection': 'QF1_TC'},
                        'DO2': {'type': 'digital_output', 'connection': 'K1_1'}
                    }
                },
                'QF1': {
                    'type': 'breaker',
                    'name': 'QF1',
                    'description': '220kV出线1断路器',
                    'position': {'x': 600, 'y': 150},
                    'terminals': {
                        'TC': {'type': 'trip_coil', 'connection': 'PROT1_DO1'},
                        'CC': {'type': 'close_coil', 'connection': 'Control_CC1'}
                    }
                },
                'K1': {
                    'type': 'relay',
                    'name': 'K1',
                    'description': '跳闸继电器',
                    'position': {'x': 500, 'y': 350},
                    'terminals': {
                        '1': {'type': 'coil', 'connection': 'PROT1_DO2'},
                        '2': {'type': 'coil', 'connection': 'Ground'},
                        '3': {'type': 'contact', 'connection': 'QF1_TC'},
                        '4': {'type': 'contact', 'connection': 'Control_+'}
                    }
                }
            },
            'connections': [
                {'from': 'TA1.S1', 'to': 'PROT1.AI1', 'type': 'current', 'color': 'red'},
                {'from': 'TA1.S2', 'to': 'PROT1.AI2', 'type': 'current', 'color': 'red'},
                {'from': 'TA2.S1', 'to': 'PROT1.AI3', 'type': 'current', 'color': 'red'},
                {'from': 'TV1.S1', 'to': 'PROT1.VI1', 'type': 'voltage', 'color': 'blue'},
                {'from': 'TV1.S2', 'to': 'PROT1.VI2', 'type': 'voltage', 'color': 'blue'},
                {'from': 'PROT1.DO1', 'to': 'QF1.TC', 'type': 'control', 'color': 'green'},
                {'from': 'PROT1.DO2', 'to': 'K1.1', 'type': 'control', 'color': 'green'},
                {'from': 'K1.3', 'to': 'QF1.TC', 'type': 'control', 'color': 'orange'}
            ]
        }
    
    def generate_current_circuit_svg(self, circuit_data: Dict) -> str:
        """生成电流回路SVG图"""
        
        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .device-text { font-family: Arial, sans-serif; font-size: 10px; fill: #333; }
            .terminal-text { font-family: Arial, sans-serif; font-size: 8px; fill: #666; }
            .current-line { stroke: red; stroke-width: 2; fill: none; }
            .voltage-line { stroke: blue; stroke-width: 2; fill: none; }
            .control-line { stroke: green; stroke-width: 1.5; fill: none; stroke-dasharray: 5,5; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="900" height="700" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="450" y="30" text-anchor="middle" class="title-text">电流回路图</text>
    <text x="450" y="50" text-anchor="middle" font-size="12" fill="#666">基于虚端子连接关系生成</text>
    
    <!-- 图例 -->
    <g transform="translate(20, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">图例:</text>
        <line x1="0" y1="15" x2="30" y2="15" class="current-line"/>
        <text x="35" y="19" font-size="10">电流回路</text>
        <line x1="0" y1="30" x2="30" y2="30" class="voltage-line"/>
        <text x="35" y="34" font-size="10">电压回路</text>
        <line x1="0" y1="45" x2="30" y2="45" class="control-line"/>
        <text x="35" y="49" font-size="10">控制回路</text>
    </g>'''
        
        # 添加设备符号
        for device_name, device in circuit_data['devices'].items():
            if device['type'] in self.symbols:
                symbol = self.symbols[device['type']]
                x = device['position']['x']
                y = device['position']['y']
                
                # 替换模板中的名称
                device_svg = symbol['svg_template'].format(name=device_name)
                svg_content += f'\n    <!-- {device["description"]} -->'
                svg_content += f'\n    <g transform="translate({x},{y})">'
                svg_content += device_svg
                svg_content += '\n    </g>'
        
        # 添加连接线
        svg_content += '\n    <!-- 连接线 -->'
        for connection in circuit_data['connections']:
            from_device, from_terminal = connection['from'].split('.')
            to_device, to_terminal = connection['to'].split('.')
            
            # 获取起点和终点坐标
            from_pos = circuit_data['devices'][from_device]['position']
            to_pos = circuit_data['devices'][to_device]['position']
            
            # 计算连接点坐标（简化处理）
            x1 = from_pos['x'] + 50
            y1 = from_pos['y'] + 50
            x2 = to_pos['x'] + 50
            y2 = to_pos['y'] + 50
            
            # 添加连接线
            line_class = f"{connection['type']}-line"
            svg_content += f'\n    <line x1="{x1}" y1="{y1}" x2="{x2}" y2="{y2}" class="{line_class}"/>'
            
            # 添加连接标注
            mid_x = (x1 + x2) / 2
            mid_y = (y1 + y2) / 2
            svg_content += f'\n    <text x="{mid_x}" y="{mid_y-5}" font-size="8" text-anchor="middle" fill="{connection["color"]}">{from_terminal}→{to_terminal}</text>'
        
        # 添加说明文字
        svg_content += '''
    
    <!-- 说明文字 -->
    <g transform="translate(20, 600)">
        <text x="0" y="0" font-size="14" font-weight="bold">回路说明:</text>
        <text x="0" y="20" font-size="11">1. 电流互感器TA1、TA2将一次电流转换为二次电流信号</text>
        <text x="0" y="35" font-size="11">2. 保护装置PROT1接收电流信号进行保护判断</text>
        <text x="0" y="50" font-size="11">3. 保护动作时通过数字输出DO1直接跳闸或通过继电器K1跳闸</text>
        <text x="0" y="65" font-size="11">4. 虚端子技术实现了设备间的数字化连接</text>
    </g>
    
</svg>'''
        
        return svg_content
    
    def generate_protection_circuit_svg(self, circuit_data: Dict) -> str:
        """生成保护回路SVG图"""
        
        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .goose-line { stroke: purple; stroke-width: 2; fill: none; stroke-dasharray: 10,5; }
            .sv-line { stroke: orange; stroke-width: 2; fill: none; stroke-dasharray: 15,5; }
            .hardwire-line { stroke: gray; stroke-width: 1; fill: none; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1000" height="800" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="500" y="30" text-anchor="middle" class="title-text">保护回路图</text>
    <text x="500" y="50" text-anchor="middle" font-size="12" fill="#666">基于GOOSE和SV虚端子连接</text>
    
    <!-- 图例 -->
    <g transform="translate(20, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">图例:</text>
        <line x1="0" y1="15" x2="40" y2="15" class="goose-line"/>
        <text x="45" y="19" font-size="10">GOOSE连接</text>
        <line x1="0" y1="30" x2="40" y2="30" class="sv-line"/>
        <text x="45" y="34" font-size="10">SV采样值</text>
        <line x1="0" y1="45" x2="40" y2="45" class="hardwire-line"/>
        <text x="45" y="49" font-size="10">硬接线</text>
    </g>'''
        
        # 添加保护功能框图
        svg_content += '''
    
    <!-- 保护功能框图 -->
    <g transform="translate(200, 150)">
        <!-- 采样值接收 -->
        <rect x="0" y="0" width="120" height="60" fill="lightblue" stroke="black"/>
        <text x="60" y="35" text-anchor="middle" font-size="10" font-weight="bold">SV采样值接收</text>
        
        <!-- 保护算法 -->
        <rect x="200" y="0" width="120" height="60" fill="lightgreen" stroke="black"/>
        <text x="260" y="25" text-anchor="middle" font-size="10" font-weight="bold">保护算法</text>
        <text x="260" y="40" text-anchor="middle" font-size="8">距离保护</text>
        <text x="260" y="50" text-anchor="middle" font-size="8">差动保护</text>
        
        <!-- GOOSE发送 -->
        <rect x="400" y="0" width="120" height="60" fill="lightyellow" stroke="black"/>
        <text x="460" y="35" text-anchor="middle" font-size="10" font-weight="bold">GOOSE发送</text>
        
        <!-- 连接线 -->
        <line x1="120" y1="30" x2="200" y2="30" stroke="black" stroke-width="2"/>
        <line x1="320" y1="30" x2="400" y2="30" stroke="black" stroke-width="2"/>
        
        <!-- 标注 -->
        <text x="160" y="25" text-anchor="middle" font-size="8">电流/电压</text>
        <text x="360" y="25" text-anchor="middle" font-size="8">跳闸命令</text>
    </g>'''
        
        # 添加虚端子连接示意
        svg_content += '''
    
    <!-- 虚端子连接示意 -->
    <g transform="translate(100, 300)">
        <text x="0" y="0" font-size="14" font-weight="bold">虚端子连接关系:</text>
        
        <!-- 发送端 -->
        <rect x="0" y="20" width="150" height="80" fill="lightcoral" stroke="black"/>
        <text x="75" y="40" text-anchor="middle" font-size="10" font-weight="bold">发送端IED</text>
        <text x="75" y="55" text-anchor="middle" font-size="8">ProtectionIED1</text>
        <text x="75" y="70" text-anchor="middle" font-size="8">GOOSE: Trip_Out</text>
        <text x="75" y="85" text-anchor="middle" font-size="8">SV: Current_A</text>
        
        <!-- 接收端 -->
        <rect x="300" y="20" width="150" height="80" fill="lightsteelblue" stroke="black"/>
        <text x="375" y="40" text-anchor="middle" font-size="10" font-weight="bold">接收端IED</text>
        <text x="375" y="55" text-anchor="middle" font-size="8">ProtectionIED2</text>
        <text x="375" y="70" text-anchor="middle" font-size="8">GOOSE: Trip_In</text>
        <text x="375" y="85" text-anchor="middle" font-size="8">SV: Current_B</text>
        
        <!-- 虚端子连接 -->
        <line x1="150" y1="50" x2="300" y2="50" class="goose-line"/>
        <text x="225" y="45" text-anchor="middle" font-size="8">GOOSE虚端子</text>
        
        <line x1="150" y1="70" x2="300" y2="70" class="sv-line"/>
        <text x="225" y="85" text-anchor="middle" font-size="8">SV虚端子</text>
    </g>'''
        
        # 添加说明
        svg_content += '''
    
    <!-- 说明文字 -->
    <g transform="translate(20, 500)">
        <text x="0" y="0" font-size="14" font-weight="bold">虚端子技术优势:</text>
        <text x="0" y="25" font-size="11">1. 减少硬接线，提高系统可靠性</text>
        <text x="0" y="45" font-size="11">2. 实现设备间的数字化通信</text>
        <text x="0" y="65" font-size="11">3. 支持复杂的保护逻辑配置</text>
        <text x="0" y="85" font-size="11">4. 便于系统维护和扩展</text>
        
        <text x="400" y="0" font-size="14" font-weight="bold">传统回路对比:</text>
        <text x="400" y="25" font-size="11">1. 传统硬接线复杂，故障率高</text>
        <text x="400" y="45" font-size="11">2. 虚端子实现点对点数字连接</text>
        <text x="400" y="65" font-size="11">3. 配置灵活，易于修改</text>
        <text x="400" y="85" font-size="11">4. 支持远程监控和诊断</text>
    </g>
    
</svg>'''
        
        return svg_content
    
    def generate_trip_circuit_svg(self, circuit_data: Dict) -> str:
        """生成跳闸回路SVG图"""
        
        svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .trip-line { stroke: red; stroke-width: 3; fill: none; }
            .close-line { stroke: green; stroke-width: 2; fill: none; }
            .aux-line { stroke: blue; stroke-width: 1; fill: none; stroke-dasharray: 3,3; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="900" height="600" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="450" y="30" text-anchor="middle" class="title-text">跳闸回路图</text>
    <text x="450" y="50" text-anchor="middle" font-size="12" fill="#666">断路器控制回路</text>
    
    <!-- 跳闸回路 -->
    <g transform="translate(100, 100)">
        <text x="0" y="0" font-size="14" font-weight="bold">跳闸回路:</text>
        
        <!-- 保护装置输出 -->
        <rect x="0" y="20" width="80" height="40" fill="lightgreen" stroke="black"/>
        <text x="40" y="45" text-anchor="middle" font-size="9">保护装置</text>
        
        <!-- 跳闸继电器 -->
        <rect x="150" y="20" width="60" height="40" fill="lightblue" stroke="black"/>
        <text x="180" y="45" text-anchor="middle" font-size="9">跳闸继电器</text>
        
        <!-- 断路器跳闸线圈 -->
        <rect x="280" y="20" width="80" height="40" fill="lightcoral" stroke="black"/>
        <text x="320" y="45" text-anchor="middle" font-size="9">跳闸线圈</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="150" y2="40" class="trip-line"/>
        <line x1="210" y1="40" x2="280" y2="40" class="trip-line"/>
        
        <!-- 标注 -->
        <text x="115" y="35" text-anchor="middle" font-size="8">GOOSE</text>
        <text x="245" y="35" text-anchor="middle" font-size="8">触点</text>
    </g>
    
    <!-- 合闸回路 -->
    <g transform="translate(100, 200)">
        <text x="0" y="0" font-size="14" font-weight="bold">合闸回路:</text>
        
        <!-- 控制开关 -->
        <rect x="0" y="20" width="80" height="40" fill="lightyellow" stroke="black"/>
        <text x="40" y="45" text-anchor="middle" font-size="9">控制开关</text>
        
        <!-- 合闸继电器 -->
        <rect x="150" y="20" width="60" height="40" fill="lightblue" stroke="black"/>
        <text x="180" y="45" text-anchor="middle" font-size="9">合闸继电器</text>
        
        <!-- 断路器合闸线圈 -->
        <rect x="280" y="20" width="80" height="40" fill="lightgreen" stroke="black"/>
        <text x="320" y="45" text-anchor="middle" font-size="9">合闸线圈</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="150" y2="40" class="close-line"/>
        <line x1="210" y1="40" x2="280" y2="40" class="close-line"/>
        
        <!-- 标注 -->
        <text x="115" y="35" text-anchor="middle" font-size="8">手动</text>
        <text x="245" y="35" text-anchor="middle" font-size="8">触点</text>
    </g>
    
    <!-- 辅助回路 -->
    <g transform="translate(100, 300)">
        <text x="0" y="0" font-size="14" font-weight="bold">辅助回路:</text>
        
        <!-- 位置信号 -->
        <rect x="0" y="20" width="80" height="40" fill="lightsteelblue" stroke="black"/>
        <text x="40" y="45" text-anchor="middle" font-size="9">位置信号</text>
        
        <!-- 信号继电器 -->
        <rect x="150" y="20" width="60" height="40" fill="lightblue" stroke="black"/>
        <text x="180" y="45" text-anchor="middle" font-size="9">信号继电器</text>
        
        <!-- 指示灯 -->
        <circle cx="320" cy="40" r="20" fill="yellow" stroke="black"/>
        <text x="320" y="45" text-anchor="middle" font-size="8">指示灯</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="150" y2="40" class="aux-line"/>
        <line x1="210" y1="40" x2="300" y2="40" class="aux-line"/>
    </g>
    
    <!-- 说明 -->
    <g transform="translate(500, 150)">
        <text x="0" y="0" font-size="14" font-weight="bold">虚端子实现:</text>
        <text x="0" y="25" font-size="11">1. 保护装置通过GOOSE发送跳闸命令</text>
        <text x="0" y="45" font-size="11">2. 断路器IED接收GOOSE信号</text>
        <text x="0" y="65" font-size="11">3. 直接驱动跳闸线圈动作</text>
        <text x="0" y="85" font-size="11">4. 减少中间继电器环节</text>
        
        <text x="0" y="120" font-size="14" font-weight="bold">传统回路特点:</text>
        <text x="0" y="145" font-size="11">• 硬接线连接，可靠性高</text>
        <text x="0" y="165" font-size="11">• 中间继电器多，故障点多</text>
        <text x="0" y="185" font-size="11">• 接线复杂，维护困难</text>
        
        <text x="0" y="220" font-size="14" font-weight="bold">虚端子优势:</text>
        <text x="0" y="245" font-size="11">• 数字化连接，速度快</text>
        <text x="0" y="265" font-size="11">• 减少硬接线，提高可靠性</text>
        <text x="0" y="285" font-size="11">• 配置灵活，易于扩展</text>
    </g>
    
</svg>'''
        
        return svg_content
    
    def generate_all_circuits(self, output_dir: str) -> Dict:
        """生成所有回路图"""
        
        print("🎨 生成传统二次回路图")
        print("=" * 50)
        
        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取演示数据
        circuit_data = self.create_demo_circuit_data()
        
        # 生成各种回路图
        circuits = {
            'current': {
                'name': '电流回路图',
                'generator': self.generate_current_circuit_svg,
                'description': '基于电流互感器的电流测量回路'
            },
            'protection': {
                'name': '保护回路图',
                'generator': self.generate_protection_circuit_svg,
                'description': '基于GOOSE和SV的保护通信回路'
            },
            'trip': {
                'name': '跳闸回路图',
                'generator': self.generate_trip_circuit_svg,
                'description': '断路器控制和跳闸回路'
            }
        }
        
        generated_files = []
        
        for circuit_type, circuit_info in circuits.items():
            print(f"🎨 生成{circuit_info['name']}...")
            
            # 生成SVG内容
            svg_content = circuit_info['generator'](circuit_data)
            
            # 保存SVG文件
            svg_file = output_path / f"{circuit_type}_circuit_enhanced.svg"
            with open(svg_file, 'w', encoding='utf-8') as f:
                f.write(svg_content)
            
            generated_files.append(str(svg_file))
            print(f"✅ {circuit_info['name']}已保存: {svg_file}")
        
        # 生成HTML查看器
        html_content = self.generate_html_viewer(circuits, generated_files)
        html_file = output_path / "circuit_viewer.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"🌐 HTML查看器已保存: {html_file}")
        
        # 生成报告
        report = {
            'generation_time': datetime.now().isoformat(),
            'output_directory': str(output_path),
            'generated_circuits': list(circuits.keys()),
            'circuit_files': generated_files,
            'html_viewer': str(html_file),
            'description': '基于虚端子连接关系生成的传统二次回路图'
        }
        
        report_file = output_path / "enhanced_circuit_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📋 生成报告已保存: {report_file}")
        
        return {
            'circuits': circuits,
            'generated_files': generated_files,
            'html_viewer': str(html_file),
            'report': report,
            'output_directory': str(output_path)
        }
    
    def generate_html_viewer(self, circuits: Dict, svg_files: List[str]) -> str:
        """生成HTML查看器"""
        
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚端子回路图查看器</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 20px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .tabs { display: flex; border-bottom: 2px solid #ddd; margin-bottom: 20px; }
        .tab { padding: 10px 20px; cursor: pointer; border: none; background: none; font-size: 14px; }
        .tab.active { background-color: #667eea; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .circuit-container { text-align: center; padding: 20px; }
        .circuit-description { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: left; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 虚端子回路图查看器</h1>
            <p>基于IEC61850虚端子连接关系生成的传统二次回路图</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('current')">电流回路</button>
            <button class="tab" onclick="showTab('protection')">保护回路</button>
            <button class="tab" onclick="showTab('trip')">跳闸回路</button>
        </div>'''
        
        # 添加各个回路图的内容
        for circuit_type, circuit_info in circuits.items():
            html_content += f'''
        <div id="{circuit_type}" class="tab-content {'active' if circuit_type == 'current' else ''}">
            <div class="circuit-description">
                <h3>{circuit_info['name']}</h3>
                <p>{circuit_info['description']}</p>
            </div>
            <div class="circuit-container">
                <object data="{circuit_type}_circuit_enhanced.svg" type="image/svg+xml" width="100%" height="600">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>'''
        
        html_content += '''
    </div>
    
    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            var contents = document.getElementsByClassName('tab-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].classList.remove('active');
            }
            
            // 移除所有标签的active类
            var tabs = document.getElementsByClassName('tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>'''
        
        return html_content


def main():
    """主函数"""
    
    print("🎨 增强版虚端子回路图生成系统")
    print("=" * 60)
    
    # 创建生成器
    generator = EnhancedCircuitGenerator()
    
    # 输出目录
    output_dir = "design_reports/circuit_analysis"
    
    # 生成回路图
    try:
        result = generator.generate_all_circuits(output_dir)
        
        print("\n🎉 回路图生成完成！")
        print(f"📁 输出目录: {result['output_directory']}")
        print(f"📊 生成的回路图: {len(result['circuits'])} 个")
        print(f"🌐 HTML查看器: {result['html_viewer']}")
        
        print("\n💡 使用建议:")
        print("   1. 在浏览器中打开HTML查看器查看所有回路图")
        print("   2. SVG文件可以在支持矢量图的软件中编辑")
        print("   3. 回路图展示了虚端子技术的应用优势")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
