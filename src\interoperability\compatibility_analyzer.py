"""
兼容性分析器
分析设备间的详细兼容性信息
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict

from .interop_checker import CompatibilityIssue, InteroperabilityResult


logger = logging.getLogger(__name__)


@dataclass
class CompatibilityMatrix:
    """兼容性矩阵"""
    devices: List[str]
    matrix: Dict[Tuple[str, str], float]  # (device1, device2) -> compatibility_score
    issues_matrix: Dict[Tuple[str, str], List[CompatibilityIssue]]


@dataclass
class SystemCompatibilityReport:
    """系统兼容性报告"""
    overall_compatibility: float
    device_count: int
    total_connections: int
    compatible_connections: int
    incompatible_connections: int
    critical_issues: List[CompatibilityIssue]
    recommendations: List[str]
    compatibility_matrix: CompatibilityMatrix


class CompatibilityAnalyzer:
    """兼容性分析器"""
    
    def __init__(self):
        """初始化兼容性分析器"""
        self.compatibility_thresholds = {
            'full': 0.9,
            'partial': 0.7,
            'limited': 0.5,
            'incompatible': 0.0
        }
        
        logger.info("兼容性分析器初始化完成")
    
    def analyze_system_compatibility(self, 
                                   interop_results: List[InteroperabilityResult]) -> SystemCompatibilityReport:
        """
        分析系统整体兼容性
        
        Args:
            interop_results: 互操作性检查结果列表
            
        Returns:
            SystemCompatibilityReport: 系统兼容性报告
        """
        try:
            logger.info("开始分析系统兼容性")
            
            # 构建兼容性矩阵
            compatibility_matrix = self._build_compatibility_matrix(interop_results)
            
            # 计算整体兼容性
            overall_compatibility = self._calculate_overall_compatibility(interop_results)
            
            # 统计连接信息
            total_connections = len(interop_results)
            compatible_connections = len([r for r in interop_results 
                                        if r.overall_score >= self.compatibility_thresholds['partial']])
            incompatible_connections = total_connections - compatible_connections
            
            # 收集关键问题
            critical_issues = self._collect_critical_issues(interop_results)
            
            # 生成系统级推荐
            recommendations = self._generate_system_recommendations(interop_results, critical_issues)
            
            # 获取设备数量
            devices = set()
            for result in interop_results:
                devices.add(result.source_device)
                devices.add(result.target_device)
            device_count = len(devices)
            
            report = SystemCompatibilityReport(
                overall_compatibility=overall_compatibility,
                device_count=device_count,
                total_connections=total_connections,
                compatible_connections=compatible_connections,
                incompatible_connections=incompatible_connections,
                critical_issues=critical_issues,
                recommendations=recommendations,
                compatibility_matrix=compatibility_matrix
            )
            
            logger.info(f"系统兼容性分析完成: 整体兼容性 {overall_compatibility:.2f}")
            return report
            
        except Exception as e:
            logger.error(f"系统兼容性分析失败: {e}")
            return SystemCompatibilityReport(
                overall_compatibility=0.0,
                device_count=0,
                total_connections=0,
                compatible_connections=0,
                incompatible_connections=0,
                critical_issues=[],
                recommendations=[],
                compatibility_matrix=CompatibilityMatrix([], {}, {})
            )
    
    def analyze_device_compatibility(self, 
                                   device_name: str,
                                   interop_results: List[InteroperabilityResult]) -> Dict[str, Any]:
        """
        分析特定设备的兼容性
        
        Args:
            device_name: 设备名称
            interop_results: 互操作性检查结果列表
            
        Returns:
            Dict[str, Any]: 设备兼容性分析结果
        """
        try:
            # 找到与该设备相关的所有结果
            device_results = [
                r for r in interop_results 
                if r.source_device == device_name or r.target_device == device_name
            ]
            
            if not device_results:
                return {
                    'device_name': device_name,
                    'compatibility_score': 0.0,
                    'compatible_devices': [],
                    'incompatible_devices': [],
                    'issues': [],
                    'recommendations': []
                }
            
            # 计算设备兼容性分数
            total_score = sum(r.overall_score for r in device_results)
            compatibility_score = total_score / len(device_results)
            
            # 分类兼容和不兼容的设备
            compatible_devices = []
            incompatible_devices = []
            
            for result in device_results:
                other_device = (result.target_device if result.source_device == device_name 
                              else result.source_device)
                
                if result.overall_score >= self.compatibility_thresholds['partial']:
                    compatible_devices.append({
                        'device': other_device,
                        'score': result.overall_score,
                        'level': result.compatibility_level.value
                    })
                else:
                    incompatible_devices.append({
                        'device': other_device,
                        'score': result.overall_score,
                        'level': result.compatibility_level.value
                    })
            
            # 收集设备相关的问题
            device_issues = []
            for result in device_results:
                device_issues.extend(result.issues)
            
            # 生成设备级推荐
            device_recommendations = self._generate_device_recommendations(device_name, device_issues)
            
            return {
                'device_name': device_name,
                'compatibility_score': compatibility_score,
                'compatible_devices': compatible_devices,
                'incompatible_devices': incompatible_devices,
                'issues': device_issues,
                'recommendations': device_recommendations,
                'total_connections': len(device_results),
                'successful_connections': len(compatible_devices)
            }
            
        except Exception as e:
            logger.error(f"设备兼容性分析失败: {e}")
            return {}
    
    def find_compatibility_bottlenecks(self, 
                                     interop_results: List[InteroperabilityResult]) -> List[Dict[str, Any]]:
        """
        查找兼容性瓶颈
        
        Args:
            interop_results: 互操作性检查结果列表
            
        Returns:
            List[Dict[str, Any]]: 兼容性瓶颈列表
        """
        bottlenecks = []
        
        try:
            # 统计每个设备的兼容性问题
            device_issues = defaultdict(list)
            device_scores = defaultdict(list)
            
            for result in interop_results:
                device_issues[result.source_device].extend(result.issues)
                device_issues[result.target_device].extend(result.issues)
                device_scores[result.source_device].append(result.overall_score)
                device_scores[result.target_device].append(result.overall_score)
            
            # 识别问题最多的设备
            for device, issues in device_issues.items():
                if len(issues) > 0:
                    avg_score = sum(device_scores[device]) / len(device_scores[device])
                    error_count = len([i for i in issues if i.severity == "error"])
                    warning_count = len([i for i in issues if i.severity == "warning"])
                    
                    if error_count > 2 or avg_score < self.compatibility_thresholds['limited']:
                        bottlenecks.append({
                            'device': device,
                            'type': 'device_bottleneck',
                            'severity': 'high' if error_count > 2 else 'medium',
                            'avg_compatibility_score': avg_score,
                            'error_count': error_count,
                            'warning_count': warning_count,
                            'description': f"设备 {device} 存在多个兼容性问题",
                            'recommendations': [
                                f"检查设备 {device} 的配置",
                                "考虑升级设备固件",
                                "验证设备支持的协议版本"
                            ]
                        })
            
            # 识别协议瓶颈
            protocol_issues = defaultdict(int)
            for result in interop_results:
                for issue in result.issues:
                    if 'protocol' in issue.details:
                        protocol = issue.details['protocol']
                        protocol_issues[protocol] += 1
            
            for protocol, count in protocol_issues.items():
                if count > 3:  # 如果某个协议问题出现超过3次
                    bottlenecks.append({
                        'protocol': protocol,
                        'type': 'protocol_bottleneck',
                        'severity': 'high' if count > 5 else 'medium',
                        'issue_count': count,
                        'description': f"协议 {protocol} 存在广泛的兼容性问题",
                        'recommendations': [
                            f"统一 {protocol} 协议版本",
                            f"检查 {protocol} 配置参数",
                            "考虑使用协议转换器"
                        ]
                    })
            
            # 按严重程度排序
            bottlenecks.sort(key=lambda x: (
                x['severity'] == 'high',
                x.get('error_count', 0) + x.get('issue_count', 0)
            ), reverse=True)
            
            logger.info(f"发现 {len(bottlenecks)} 个兼容性瓶颈")
            return bottlenecks
            
        except Exception as e:
            logger.error(f"查找兼容性瓶颈失败: {e}")
            return []
    
    def generate_compatibility_improvement_plan(self, 
                                               interop_results: List[InteroperabilityResult]) -> Dict[str, Any]:
        """
        生成兼容性改进计划
        
        Args:
            interop_results: 互操作性检查结果列表
            
        Returns:
            Dict[str, Any]: 兼容性改进计划
        """
        try:
            # 分析当前状态
            current_report = self.analyze_system_compatibility(interop_results)
            bottlenecks = self.find_compatibility_bottlenecks(interop_results)
            
            # 生成改进步骤
            improvement_steps = []
            
            # 1. 解决关键问题
            critical_issues = current_report.critical_issues
            if critical_issues:
                improvement_steps.append({
                    'phase': 1,
                    'title': '解决关键兼容性问题',
                    'priority': 'high',
                    'estimated_effort': 'high',
                    'actions': [
                        f"修复 {len(critical_issues)} 个关键兼容性问题",
                        "优先处理错误级别的问题",
                        "验证修复效果"
                    ],
                    'expected_improvement': 0.2
                })
            
            # 2. 解决瓶颈设备
            device_bottlenecks = [b for b in bottlenecks if b['type'] == 'device_bottleneck']
            if device_bottlenecks:
                improvement_steps.append({
                    'phase': 2,
                    'title': '优化瓶颈设备',
                    'priority': 'high',
                    'estimated_effort': 'medium',
                    'actions': [
                        f"优化 {len(device_bottlenecks)} 个瓶颈设备",
                        "升级设备固件或配置",
                        "验证设备间通信"
                    ],
                    'expected_improvement': 0.15
                })
            
            # 3. 统一协议版本
            protocol_bottlenecks = [b for b in bottlenecks if b['type'] == 'protocol_bottleneck']
            if protocol_bottlenecks:
                improvement_steps.append({
                    'phase': 3,
                    'title': '统一协议版本',
                    'priority': 'medium',
                    'estimated_effort': 'medium',
                    'actions': [
                        "统一关键协议版本",
                        "更新协议配置参数",
                        "测试协议兼容性"
                    ],
                    'expected_improvement': 0.1
                })
            
            # 4. 优化配置
            improvement_steps.append({
                'phase': 4,
                'title': '优化系统配置',
                'priority': 'low',
                'estimated_effort': 'low',
                'actions': [
                    "优化网络配置",
                    "调整时序参数",
                    "完善安全配置"
                ],
                'expected_improvement': 0.05
            })
            
            # 计算预期改进效果
            total_expected_improvement = sum(step['expected_improvement'] for step in improvement_steps)
            target_compatibility = min(1.0, current_report.overall_compatibility + total_expected_improvement)
            
            plan = {
                'current_compatibility': current_report.overall_compatibility,
                'target_compatibility': target_compatibility,
                'total_expected_improvement': total_expected_improvement,
                'improvement_steps': improvement_steps,
                'estimated_timeline': f"{len(improvement_steps) * 2} 周",
                'success_metrics': [
                    f"整体兼容性从 {current_report.overall_compatibility:.2f} 提升到 {target_compatibility:.2f}",
                    f"减少关键问题数量 {len(critical_issues)} 个",
                    f"解决瓶颈设备 {len(device_bottlenecks)} 个"
                ]
            }
            
            logger.info("兼容性改进计划生成完成")
            return plan
            
        except Exception as e:
            logger.error(f"生成兼容性改进计划失败: {e}")
            return {}
    
    def _build_compatibility_matrix(self, 
                                  interop_results: List[InteroperabilityResult]) -> CompatibilityMatrix:
        """构建兼容性矩阵"""
        devices = set()
        matrix = {}
        issues_matrix = {}
        
        # 收集所有设备
        for result in interop_results:
            devices.add(result.source_device)
            devices.add(result.target_device)
        
        devices = sorted(list(devices))
        
        # 构建矩阵
        for result in interop_results:
            key = (result.source_device, result.target_device)
            matrix[key] = result.overall_score
            issues_matrix[key] = result.issues
            
            # 添加反向关系（假设兼容性是对称的）
            reverse_key = (result.target_device, result.source_device)
            matrix[reverse_key] = result.overall_score
            issues_matrix[reverse_key] = result.issues
        
        # 设备与自身的兼容性为1.0
        for device in devices:
            matrix[(device, device)] = 1.0
            issues_matrix[(device, device)] = []
        
        return CompatibilityMatrix(devices, matrix, issues_matrix)
    
    def _calculate_overall_compatibility(self, 
                                       interop_results: List[InteroperabilityResult]) -> float:
        """计算整体兼容性"""
        if not interop_results:
            return 0.0
        
        total_score = sum(result.overall_score for result in interop_results)
        return total_score / len(interop_results)
    
    def _collect_critical_issues(self, 
                               interop_results: List[InteroperabilityResult]) -> List[CompatibilityIssue]:
        """收集关键问题"""
        critical_issues = []
        
        for result in interop_results:
            for issue in result.issues:
                if issue.severity == "error" or issue.impact_level == "high":
                    critical_issues.append(issue)
        
        # 按影响级别和严重程度排序
        critical_issues.sort(key=lambda x: (
            x.severity == "error",
            x.impact_level == "high",
            x.impact_level == "medium"
        ), reverse=True)
        
        return critical_issues[:20]  # 限制数量
    
    def _generate_system_recommendations(self, 
                                       interop_results: List[InteroperabilityResult],
                                       critical_issues: List[CompatibilityIssue]) -> List[str]:
        """生成系统级推荐"""
        recommendations = []
        
        # 基于关键问题生成推荐
        issue_types = defaultdict(int)
        for issue in critical_issues:
            issue_types[issue.issue_type] += 1
        
        for issue_type, count in issue_types.items():
            if count > 2:  # 如果某类问题出现多次
                if issue_type.value == "protocol_version":
                    recommendations.append("统一系统中的协议版本")
                elif issue_type.value == "configuration_conflict":
                    recommendations.append("标准化设备配置参数")
                elif issue_type.value == "service_unavailable":
                    recommendations.append("确保关键服务在所有设备上可用")
        
        # 基于整体兼容性生成推荐
        overall_compatibility = self._calculate_overall_compatibility(interop_results)
        if overall_compatibility < 0.7:
            recommendations.append("考虑进行系统级兼容性升级")
            recommendations.append("建立设备兼容性测试流程")
        
        return recommendations[:10]
    
    def _generate_device_recommendations(self, 
                                       device_name: str,
                                       issues: List[CompatibilityIssue]) -> List[str]:
        """生成设备级推荐"""
        recommendations = []
        
        # 统计问题类型
        issue_types = defaultdict(int)
        for issue in issues:
            issue_types[issue.issue_type] += 1
        
        # 基于问题类型生成推荐
        for issue_type, count in issue_types.items():
            if issue_type.value == "protocol_version":
                recommendations.append(f"升级设备 {device_name} 的协议版本")
            elif issue_type.value == "configuration_conflict":
                recommendations.append(f"检查设备 {device_name} 的配置参数")
            elif issue_type.value == "service_unavailable":
                recommendations.append(f"启用设备 {device_name} 的必要服务")
        
        return recommendations[:5]
