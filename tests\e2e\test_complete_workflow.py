"""
端到端测试套件
测试完整的用户工作流程
"""

import pytest
import os
import tempfile
import json
from pathlib import Path
import time
from unittest.mock import patch, MagicMock

# 导入核心模块
from src.core.parser.scd_parser import SCDParser
from src.core.validator.validation_engine import ValidationEngine
from src.core.rules.rule_engine import RuleEngine
from src.comparison.file_comparator import FileComparator
from src.comparison.diff_analyzer import DiffAnalyzer
from src.comparison.merge_tool import MergeTool
from src.virtual_terminal.vt_generator import VirtualTerminalGenerator
from src.virtual_terminal.vt_analyzer import VirtualTerminalAnalyzer
from src.virtual_terminal.vt_exporter import VirtualTerminalExporter


class TestCompleteWorkflow:
    """完整工作流程测试"""
    
    @pytest.fixture
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试SCD文件
        self.test_scd_content = self._create_test_scd_content()
        self.test_scd_file = os.path.join(self.temp_dir, "test.scd")
        
        with open(self.test_scd_file, 'w', encoding='utf-8') as f:
            f.write(self.test_scd_content)
        
        # 创建第二个测试文件用于对比
        self.test_scd_content_v2 = self._create_test_scd_content_v2()
        self.test_scd_file_v2 = os.path.join(self.temp_dir, "test_v2.scd")
        
        with open(self.test_scd_file_v2, 'w', encoding='utf-8') as f:
            f.write(self.test_scd_content_v2)
        
        yield
        
        # 清理
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_validation_workflow(self, setup_test_environment):
        """测试完整的验证工作流程"""
        # 1. 文件解析
        parser = SCDParser()
        parsed_data = parser.parse_file(self.test_scd_file)
        
        assert parsed_data is not None
        assert 'header' in parsed_data
        assert 'ieds' in parsed_data
        
        # 2. 验证执行
        validation_engine = ValidationEngine()
        validation_result = validation_engine.validate(parsed_data)
        
        assert validation_result is not None
        assert hasattr(validation_result, 'rule_results')
        assert len(validation_result.rule_results) > 0
        
        # 3. 报告生成
        from src.core.rules.report_generator import ReportGenerator
        report_generator = ReportGenerator()
        report = report_generator.generate_report(validation_result)
        
        assert report is not None
        assert 'summary' in report
        assert 'details' in report
        
        print(f"✅ 完整验证工作流程测试通过")
    
    def test_complete_comparison_workflow(self, setup_test_environment):
        """测试完整的文件对比工作流程"""
        # 1. 文件对比
        comparator = FileComparator()
        comparison_result = comparator.compare_files(
            self.test_scd_file, 
            self.test_scd_file_v2
        )
        
        assert comparison_result is not None
        assert len(comparison_result.diff_items) > 0
        
        # 2. 差异分析
        analyzer = DiffAnalyzer()
        analysis = analyzer.analyze_comparison_result(comparison_result)
        
        assert analysis is not None
        assert 'summary' in analysis
        assert 'impact_analysis' in analysis
        
        # 3. 文件合并
        merge_tool = MergeTool()
        output_file = os.path.join(self.temp_dir, "merged.scd")
        
        merge_result = merge_tool.merge_files(
            self.test_scd_file,
            self.test_scd_file_v2,
            output_file
        )
        
        assert merge_result is not None
        assert merge_result.success or merge_result.has_unresolved_conflicts()
        
        print(f"✅ 完整对比工作流程测试通过")
    
    def test_complete_virtual_terminal_workflow(self, setup_test_environment):
        """测试完整的虚端子表工作流程"""
        # 1. 解析SCD文件
        parser = SCDParser()
        parsed_data = parser.parse_file(self.test_scd_file)
        
        # 2. 生成虚端子表
        vt_generator = VirtualTerminalGenerator()
        vt_table = vt_generator.generate_from_scd(parsed_data, "测试虚端子表")
        
        assert vt_table is not None
        assert len(vt_table.terminals) > 0
        
        # 3. 分析虚端子表
        vt_analyzer = VirtualTerminalAnalyzer()
        analysis = vt_analyzer.analyze_table(vt_table)
        
        assert analysis is not None
        assert 'terminal_analysis' in analysis
        assert 'connection_analysis' in analysis
        
        # 4. 导出虚端子表
        vt_exporter = VirtualTerminalExporter()
        output_file = os.path.join(self.temp_dir, "vt_table.json")
        
        success = vt_exporter.export_table(vt_table, output_file, 'json')
        assert success
        assert os.path.exists(output_file)
        
        print(f"✅ 完整虚端子表工作流程测试通过")
    
    def test_performance_with_large_file(self, setup_test_environment):
        """测试大文件处理性能"""
        # 创建大型测试文件
        large_scd_content = self._create_large_scd_content()
        large_scd_file = os.path.join(self.temp_dir, "large_test.scd")
        
        with open(large_scd_file, 'w', encoding='utf-8') as f:
            f.write(large_scd_content)
        
        # 测试解析性能
        start_time = time.time()
        
        parser = SCDParser()
        parsed_data = parser.parse_file(large_scd_file)
        
        parse_time = time.time() - start_time
        
        assert parsed_data is not None
        assert parse_time < 30  # 应该在30秒内完成
        
        # 测试验证性能
        start_time = time.time()
        
        validation_engine = ValidationEngine()
        validation_result = validation_engine.validate(parsed_data)
        
        validation_time = time.time() - start_time
        
        assert validation_result is not None
        assert validation_time < 60  # 应该在60秒内完成
        
        print(f"✅ 大文件性能测试通过 - 解析: {parse_time:.2f}s, 验证: {validation_time:.2f}s")
    
    def test_error_handling_workflow(self, setup_test_environment):
        """测试错误处理工作流程"""
        # 创建有错误的SCD文件
        invalid_scd_content = self._create_invalid_scd_content()
        invalid_scd_file = os.path.join(self.temp_dir, "invalid.scd")
        
        with open(invalid_scd_file, 'w', encoding='utf-8') as f:
            f.write(invalid_scd_content)
        
        # 测试解析错误处理
        parser = SCDParser()
        
        try:
            parsed_data = parser.parse_file(invalid_scd_file)
            # 应该能够处理错误并返回部分结果或错误信息
            assert parsed_data is not None or parser.has_errors()
        except Exception as e:
            # 应该有适当的错误处理
            assert str(e) is not None
        
        print(f"✅ 错误处理工作流程测试通过")
    
    def test_concurrent_processing(self, setup_test_environment):
        """测试并发处理能力"""
        import threading
        import concurrent.futures
        
        def validate_file(file_path):
            parser = SCDParser()
            parsed_data = parser.parse_file(file_path)
            
            validation_engine = ValidationEngine()
            result = validation_engine.validate(parsed_data)
            return result
        
        # 并发验证多个文件
        files = [self.test_scd_file, self.test_scd_file_v2]
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(validate_file, f) for f in files]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        assert len(results) == 2
        assert all(result is not None for result in results)
        
        print(f"✅ 并发处理测试通过")
    
    def test_memory_usage(self, setup_test_environment):
        """测试内存使用情况"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多次验证操作
        parser = SCDParser()
        validation_engine = ValidationEngine()
        
        for i in range(10):
            parsed_data = parser.parse_file(self.test_scd_file)
            validation_result = validation_engine.validate(parsed_data)
            
            # 强制垃圾回收
            del parsed_data
            del validation_result
            gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该控制在合理范围内
        assert memory_increase < 100  # 不应该增长超过100MB
        
        print(f"✅ 内存使用测试通过 - 增长: {memory_increase:.2f}MB")
    
    def _create_test_scd_content(self):
        """创建测试SCD文件内容"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL">
    <Header id="TestSCD" version="1.0" revision="A" toolID="IEC61850Validator" nameStructure="IEDName">
        <Text>Test SCD File</Text>
    </Header>
    <Substation name="TestSubstation">
        <VoltageLevel name="110kV">
            <Bay name="Bay1">
                <ConductingEquipment name="CB1" type="CBR"/>
            </Bay>
        </VoltageLevel>
    </Substation>
    <IED name="TestIED1" type="Protection" manufacturer="TestManufacturer">
        <Services>
            <DynAssociation/>
            <GetDirectory/>
            <GetDataObjectDefinition/>
            <DataObjectDirectory/>
            <GetDataSetValue/>
            <SetDataSetValue/>
            <DataSetDirectory/>
            <ConfDataSet modify="true"/>
            <DynDataSet maxAttributes="100"/>
            <ReadWrite/>
            <ConfReportControl bufMode="both" bufConf="true"/>
            <GetCBValues/>
            <ConfLogControl bufMode="both"/>
            <ReportSettings cbName="Conf" datSet="Conf" rptID="Conf" optFields="Conf" bufTime="Conf" trgOps="Conf" intgPd="Conf" resvTms="true" owner="true"/>
            <LogSettings cbName="Conf" datSet="Conf" logEna="Conf" trgOps="Conf" intgPd="Conf"/>
            <GSESettings cbName="Conf" datSet="Conf" appID="Conf" dataLabel="Conf"/>
            <SMVSettings cbName="Conf" datSet="Conf" svID="Conf" optFields="Conf" smpRate="Conf" samplesPerSec="true" pdcTimeStamp="true"/>
        </Services>
        <AccessPoint name="AP1">
            <Server>
                <Authentication/>
                <LDevice inst="LD1">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type">
                        <DataSet name="DS1">
                            <FCDA ldInst="LD1" lnClass="PTOC" lnInst="1" doName="Str" fc="ST"/>
                        </DataSet>
                        <ReportControl name="RC1" datSet="DS1" rptID="RC1" buffered="true" bufTime="1000">
                            <TrgOps dchg="true" qchg="true" dupd="false" period="false"/>
                            <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="true" entryID="false" configRef="false"/>
                        </ReportControl>
                    </LN0>
                    <LN lnClass="PTOC" inst="1" lnType="PTOC_Type"/>
                </LDevice>
            </Server>
        </AccessPoint>
    </IED>
    <DataTypeTemplates>
        <LNodeType id="LLN0_Type" lnClass="LLN0">
            <DO name="Mod" type="INC_1"/>
            <DO name="Beh" type="INS_1"/>
            <DO name="Health" type="INS_1"/>
        </LNodeType>
        <LNodeType id="PTOC_Type" lnClass="PTOC">
            <DO name="Str" type="ACD_1"/>
            <DO name="Op" type="ACT_1"/>
        </LNodeType>
        <DOType id="INC_1" cdc="INC">
            <DA name="stVal" fc="ST" dchg="true" bType="INT32"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <DOType id="INS_1" cdc="INS">
            <DA name="stVal" fc="ST" dchg="true" bType="INT32"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <DOType id="ACD_1" cdc="ACD">
            <DA name="general" fc="ST" dchg="true" bType="BOOLEAN"/>
            <DA name="dirGeneral" fc="ST" dchg="true" bType="Enum" type="dir"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <DOType id="ACT_1" cdc="ACT">
            <DA name="general" fc="ST" dchg="true" bType="BOOLEAN"/>
            <DA name="q" fc="ST" qchg="true" bType="Quality"/>
            <DA name="t" fc="ST" bType="Timestamp"/>
        </DOType>
        <EnumType id="dir">
            <EnumVal ord="0">unknown</EnumVal>
            <EnumVal ord="1">forward</EnumVal>
            <EnumVal ord="2">backward</EnumVal>
            <EnumVal ord="3">both</EnumVal>
        </EnumType>
    </DataTypeTemplates>
    <Communication>
        <SubNetwork name="SubNetwork1" type="8-MMS">
            <ConnectedAP iedName="TestIED1" apName="AP1">
                <Address>
                    <P type="IP">*************</P>
                    <P type="IP-SUBNET">*************</P>
                    <P type="IP-GATEWAY">***********</P>
                    <P type="OSI-TSEL">0001</P>
                    <P type="OSI-SSEL">0001</P>
                    <P type="OSI-PSEL">00000001</P>
                </Address>
            </ConnectedAP>
        </SubNetwork>
    </Communication>
</SCL>'''
    
    def _create_test_scd_content_v2(self):
        """创建测试SCD文件内容版本2（用于对比测试）"""
        content = self._create_test_scd_content()
        # 修改一些内容用于对比测试
        content = content.replace('version="1.0"', 'version="1.1"')
        content = content.replace('TestIED1', 'TestIED1_Modified')
        content = content.replace('*************', '*************')
        return content
    
    def _create_large_scd_content(self):
        """创建大型SCD文件内容"""
        base_content = self._create_test_scd_content()
        
        # 在基础内容中添加更多IED和配置
        additional_ieds = ""
        for i in range(2, 21):  # 添加19个额外的IED
            additional_ieds += f'''
    <IED name="TestIED{i}" type="Protection" manufacturer="TestManufacturer">
        <Services>
            <DynAssociation/>
            <GetDirectory/>
            <GetDataObjectDefinition/>
            <DataObjectDirectory/>
            <GetDataSetValue/>
            <SetDataSetValue/>
            <DataSetDirectory/>
            <ConfDataSet modify="true"/>
            <DynDataSet maxAttributes="100"/>
            <ReadWrite/>
        </Services>
        <AccessPoint name="AP{i}">
            <Server>
                <Authentication/>
                <LDevice inst="LD{i}">
                    <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type"/>
                    <LN lnClass="PTOC" inst="1" lnType="PTOC_Type"/>
                    <LN lnClass="PTOC" inst="2" lnType="PTOC_Type"/>
                    <LN lnClass="PTOC" inst="3" lnType="PTOC_Type"/>
                </LDevice>
            </Server>
        </AccessPoint>
    </IED>'''
        
        # 插入额外的IED到原始内容中
        insert_pos = base_content.find('</IED>') + 6
        large_content = base_content[:insert_pos] + additional_ieds + base_content[insert_pos:]
        
        return large_content
    
    def _create_invalid_scd_content(self):
        """创建无效的SCD文件内容"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL">
    <Header id="InvalidSCD" version="1.0">
        <!-- 缺少必要的属性 -->
    </Header>
    <IED name="InvalidIED">
        <!-- 缺少必要的子元素 -->
        <AccessPoint name="AP1">
            <Server>
                <LDevice inst="LD1">
                    <!-- 无效的逻辑节点引用 -->
                    <LN lnClass="INVALID" inst="1" lnType="NonExistentType"/>
                </LDevice>
            </Server>
        </AccessPoint>
    </IED>
    <!-- 缺少DataTypeTemplates -->
</SCL>'''


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
