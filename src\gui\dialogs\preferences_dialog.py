"""
首选项对话框
应用程序设置和配置界面
"""

import logging
from typing import Dict, Any

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QGroupBox, QLabel, QLineEdit, QSpinBox, QDoubleSpinBox,
    QCheckBox, QComboBox, QPushButton, QFileDialog, QColorDialog,
    QSlider, QTextEdit, QDialogButtonBox, QFormLayout, QGridLayout
)
from PySide6.QtCore import Qt, QSettings
from PySide6.QtGui import QColor, QPalette

logger = logging.getLogger(__name__)


class PreferencesDialog(QDialog):
    """首选项对话框"""
    
    def __init__(self, parent=None):
        """初始化首选项对话框"""
        super().__init__(parent)
        
        self.settings = QSettings("IEC61850Checker", "DesignChecker")
        self.preferences = {}
        
        self._init_ui()
        self._load_preferences()
        
        logger.debug("首选项对话框初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("首选项")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 通用设置标签页
        self.general_tab = self._create_general_tab()
        self.tab_widget.addTab(self.general_tab, "通用")
        
        # 验证设置标签页
        self.validation_tab = self._create_validation_tab()
        self.tab_widget.addTab(self.validation_tab, "验证")
        
        # 界面设置标签页
        self.ui_tab = self._create_ui_tab()
        self.tab_widget.addTab(self.ui_tab, "界面")
        
        # 知识库设置标签页
        self.knowledge_tab = self._create_knowledge_tab()
        self.tab_widget.addTab(self.knowledge_tab, "知识库")
        
        # 高级设置标签页
        self.advanced_tab = self._create_advanced_tab()
        self.tab_widget.addTab(self.advanced_tab, "高级")
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_preferences)
        layout.addWidget(button_box)
    
    def _create_general_tab(self) -> QWidget:
        """创建通用设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 语言设置
        language_group = QGroupBox("语言设置")
        language_layout = QFormLayout(language_group)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        language_layout.addRow("界面语言:", self.language_combo)
        
        layout.addWidget(language_group)
        
        # 文件设置
        file_group = QGroupBox("文件设置")
        file_layout = QFormLayout(file_group)
        
        # 默认打开路径
        default_path_layout = QHBoxLayout()
        self.default_path_edit = QLineEdit()
        default_path_layout.addWidget(self.default_path_edit)
        
        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self._browse_default_path)
        default_path_layout.addWidget(browse_button)
        
        file_layout.addRow("默认打开路径:", default_path_layout)
        
        # 自动保存
        self.auto_save_checkbox = QCheckBox("启用自动保存")
        file_layout.addRow(self.auto_save_checkbox)
        
        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(1, 60)
        self.auto_save_interval.setSuffix(" 分钟")
        file_layout.addRow("自动保存间隔:", self.auto_save_interval)
        
        layout.addWidget(file_group)
        
        # 最近文件
        recent_group = QGroupBox("最近文件")
        recent_layout = QFormLayout(recent_group)
        
        self.recent_files_count = QSpinBox()
        self.recent_files_count.setRange(1, 20)
        recent_layout.addRow("最近文件数量:", self.recent_files_count)
        
        clear_recent_button = QPushButton("清除最近文件")
        clear_recent_button.clicked.connect(self._clear_recent_files)
        recent_layout.addRow(clear_recent_button)
        
        layout.addWidget(recent_group)
        
        layout.addStretch()
        return widget
    
    def _create_validation_tab(self) -> QWidget:
        """创建验证设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 验证选项
        validation_group = QGroupBox("验证选项")
        validation_layout = QFormLayout(validation_group)
        
        self.enable_syntax_validation = QCheckBox("启用语法验证")
        validation_layout.addRow(self.enable_syntax_validation)
        
        self.enable_semantic_validation = QCheckBox("启用语义验证")
        validation_layout.addRow(self.enable_semantic_validation)
        
        self.enable_business_validation = QCheckBox("启用业务规则验证")
        validation_layout.addRow(self.enable_business_validation)
        
        self.enable_smart_validation = QCheckBox("启用智能验证")
        validation_layout.addRow(self.enable_smart_validation)
        
        layout.addWidget(validation_group)
        
        # 验证严重程度
        severity_group = QGroupBox("严重程度过滤")
        severity_layout = QFormLayout(severity_group)
        
        self.show_errors = QCheckBox("显示错误")
        severity_layout.addRow(self.show_errors)
        
        self.show_warnings = QCheckBox("显示警告")
        severity_layout.addRow(self.show_warnings)
        
        self.show_info = QCheckBox("显示信息")
        severity_layout.addRow(self.show_info)
        
        layout.addWidget(severity_group)
        
        # 智能验证设置
        smart_group = QGroupBox("智能验证设置")
        smart_layout = QFormLayout(smart_group)
        
        self.confidence_threshold = QDoubleSpinBox()
        self.confidence_threshold.setRange(0.0, 1.0)
        self.confidence_threshold.setSingleStep(0.1)
        self.confidence_threshold.setDecimals(2)
        smart_layout.addRow("置信度阈值:", self.confidence_threshold)
        
        self.max_recommendations = QSpinBox()
        self.max_recommendations.setRange(1, 50)
        smart_layout.addRow("最大推荐数量:", self.max_recommendations)
        
        layout.addWidget(smart_group)
        
        layout.addStretch()
        return widget
    
    def _create_ui_tab(self) -> QWidget:
        """创建界面设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 主题设置
        theme_group = QGroupBox("主题设置")
        theme_layout = QFormLayout(theme_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["默认", "深色", "浅色"])
        theme_layout.addRow("主题:", self.theme_combo)
        
        layout.addWidget(theme_group)
        
        # 字体设置
        font_group = QGroupBox("字体设置")
        font_layout = QFormLayout(font_group)
        
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(["Arial", "Microsoft YaHei", "SimSun", "Consolas"])
        font_layout.addRow("字体族:", self.font_family_combo)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        font_layout.addRow("字体大小:", self.font_size_spin)
        
        layout.addWidget(font_group)
        
        # 颜色设置
        color_group = QGroupBox("颜色设置")
        color_layout = QGridLayout(color_group)
        
        # 错误颜色
        color_layout.addWidget(QLabel("错误颜色:"), 0, 0)
        self.error_color_button = QPushButton()
        self.error_color_button.clicked.connect(lambda: self._choose_color('error'))
        color_layout.addWidget(self.error_color_button, 0, 1)
        
        # 警告颜色
        color_layout.addWidget(QLabel("警告颜色:"), 1, 0)
        self.warning_color_button = QPushButton()
        self.warning_color_button.clicked.connect(lambda: self._choose_color('warning'))
        color_layout.addWidget(self.warning_color_button, 1, 1)
        
        # 信息颜色
        color_layout.addWidget(QLabel("信息颜色:"), 2, 0)
        self.info_color_button = QPushButton()
        self.info_color_button.clicked.connect(lambda: self._choose_color('info'))
        color_layout.addWidget(self.info_color_button, 2, 1)
        
        layout.addWidget(color_group)
        
        # 界面选项
        ui_options_group = QGroupBox("界面选项")
        ui_options_layout = QFormLayout(ui_options_group)
        
        self.show_toolbar = QCheckBox("显示工具栏")
        ui_options_layout.addRow(self.show_toolbar)
        
        self.show_statusbar = QCheckBox("显示状态栏")
        ui_options_layout.addRow(self.show_statusbar)
        
        self.show_line_numbers = QCheckBox("显示行号")
        ui_options_layout.addRow(self.show_line_numbers)
        
        layout.addWidget(ui_options_group)
        
        layout.addStretch()
        return widget
    
    def _create_knowledge_tab(self) -> QWidget:
        """创建知识库设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 知识库路径
        kb_path_group = QGroupBox("知识库路径")
        kb_path_layout = QFormLayout(kb_path_group)
        
        kb_path_input_layout = QHBoxLayout()
        self.kb_path_edit = QLineEdit()
        kb_path_input_layout.addWidget(self.kb_path_edit)
        
        kb_browse_button = QPushButton("浏览...")
        kb_browse_button.clicked.connect(self._browse_kb_path)
        kb_path_input_layout.addWidget(kb_browse_button)
        
        kb_path_layout.addRow("知识库文件:", kb_path_input_layout)
        
        layout.addWidget(kb_path_group)
        
        # 知识库选项
        kb_options_group = QGroupBox("知识库选项")
        kb_options_layout = QFormLayout(kb_options_group)
        
        self.auto_update_kb = QCheckBox("自动更新知识库")
        kb_options_layout.addRow(self.auto_update_kb)
        
        self.enable_learning = QCheckBox("启用学习功能")
        kb_options_layout.addRow(self.enable_learning)
        
        self.cache_knowledge = QCheckBox("缓存知识数据")
        kb_options_layout.addRow(self.cache_knowledge)
        
        layout.addWidget(kb_options_group)
        
        # 推理设置
        inference_group = QGroupBox("推理设置")
        inference_layout = QFormLayout(inference_group)
        
        self.inference_depth = QSpinBox()
        self.inference_depth.setRange(1, 10)
        inference_layout.addRow("推理深度:", self.inference_depth)
        
        self.inference_timeout = QSpinBox()
        self.inference_timeout.setRange(1, 60)
        self.inference_timeout.setSuffix(" 秒")
        inference_layout.addRow("推理超时:", self.inference_timeout)
        
        layout.addWidget(inference_group)
        
        layout.addStretch()
        return widget
    
    def _create_advanced_tab(self) -> QWidget:
        """创建高级设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 性能设置
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        self.max_memory_usage = QSpinBox()
        self.max_memory_usage.setRange(100, 2000)
        self.max_memory_usage.setSuffix(" MB")
        performance_layout.addRow("最大内存使用:", self.max_memory_usage)
        
        self.thread_count = QSpinBox()
        self.thread_count.setRange(1, 16)
        performance_layout.addRow("线程数量:", self.thread_count)
        
        layout.addWidget(performance_group)
        
        # 日志设置
        logging_group = QGroupBox("日志设置")
        logging_layout = QFormLayout(logging_group)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        logging_layout.addRow("日志级别:", self.log_level_combo)
        
        self.enable_file_logging = QCheckBox("启用文件日志")
        logging_layout.addRow(self.enable_file_logging)
        
        log_path_layout = QHBoxLayout()
        self.log_path_edit = QLineEdit()
        log_path_layout.addWidget(self.log_path_edit)
        
        log_browse_button = QPushButton("浏览...")
        log_browse_button.clicked.connect(self._browse_log_path)
        log_path_layout.addWidget(log_browse_button)
        
        logging_layout.addRow("日志文件路径:", log_path_layout)
        
        layout.addWidget(logging_group)
        
        # 调试设置
        debug_group = QGroupBox("调试设置")
        debug_layout = QFormLayout(debug_group)
        
        self.enable_debug_mode = QCheckBox("启用调试模式")
        debug_layout.addRow(self.enable_debug_mode)
        
        self.show_debug_info = QCheckBox("显示调试信息")
        debug_layout.addRow(self.show_debug_info)
        
        layout.addWidget(debug_group)
        
        # 重置按钮
        reset_button = QPushButton("重置为默认设置")
        reset_button.clicked.connect(self._reset_to_defaults)
        layout.addWidget(reset_button)
        
        layout.addStretch()
        return widget
    
    def _load_preferences(self):
        """加载首选项"""
        try:
            # 通用设置
            self.language_combo.setCurrentText(self.settings.value("language", "简体中文"))
            self.default_path_edit.setText(self.settings.value("defaultPath", ""))
            self.auto_save_checkbox.setChecked(self.settings.value("autoSave", True, type=bool))
            self.auto_save_interval.setValue(self.settings.value("autoSaveInterval", 5, type=int))
            self.recent_files_count.setValue(self.settings.value("recentFilesCount", 10, type=int))
            
            # 验证设置
            self.enable_syntax_validation.setChecked(self.settings.value("enableSyntaxValidation", True, type=bool))
            self.enable_semantic_validation.setChecked(self.settings.value("enableSemanticValidation", True, type=bool))
            self.enable_business_validation.setChecked(self.settings.value("enableBusinessValidation", True, type=bool))
            self.enable_smart_validation.setChecked(self.settings.value("enableSmartValidation", True, type=bool))
            
            self.show_errors.setChecked(self.settings.value("showErrors", True, type=bool))
            self.show_warnings.setChecked(self.settings.value("showWarnings", True, type=bool))
            self.show_info.setChecked(self.settings.value("showInfo", True, type=bool))
            
            self.confidence_threshold.setValue(self.settings.value("confidenceThreshold", 0.6, type=float))
            self.max_recommendations.setValue(self.settings.value("maxRecommendations", 10, type=int))
            
            # 界面设置
            self.theme_combo.setCurrentText(self.settings.value("theme", "默认"))
            self.font_family_combo.setCurrentText(self.settings.value("fontFamily", "Microsoft YaHei"))
            self.font_size_spin.setValue(self.settings.value("fontSize", 10, type=int))
            
            self.show_toolbar.setChecked(self.settings.value("showToolbar", True, type=bool))
            self.show_statusbar.setChecked(self.settings.value("showStatusbar", True, type=bool))
            self.show_line_numbers.setChecked(self.settings.value("showLineNumbers", True, type=bool))
            
            # 知识库设置
            self.kb_path_edit.setText(self.settings.value("knowledgeBasePath", "data/knowledge.db"))
            self.auto_update_kb.setChecked(self.settings.value("autoUpdateKB", True, type=bool))
            self.enable_learning.setChecked(self.settings.value("enableLearning", True, type=bool))
            self.cache_knowledge.setChecked(self.settings.value("cacheKnowledge", True, type=bool))
            
            self.inference_depth.setValue(self.settings.value("inferenceDepth", 3, type=int))
            self.inference_timeout.setValue(self.settings.value("inferenceTimeout", 10, type=int))
            
            # 高级设置
            self.max_memory_usage.setValue(self.settings.value("maxMemoryUsage", 500, type=int))
            self.thread_count.setValue(self.settings.value("threadCount", 4, type=int))
            
            self.log_level_combo.setCurrentText(self.settings.value("logLevel", "INFO"))
            self.enable_file_logging.setChecked(self.settings.value("enableFileLogging", False, type=bool))
            self.log_path_edit.setText(self.settings.value("logPath", "logs/app.log"))
            
            self.enable_debug_mode.setChecked(self.settings.value("enableDebugMode", False, type=bool))
            self.show_debug_info.setChecked(self.settings.value("showDebugInfo", False, type=bool))
            
        except Exception as e:
            logger.error(f"加载首选项失败: {e}")
    
    def _save_preferences(self):
        """保存首选项"""
        try:
            # 通用设置
            self.settings.setValue("language", self.language_combo.currentText())
            self.settings.setValue("defaultPath", self.default_path_edit.text())
            self.settings.setValue("autoSave", self.auto_save_checkbox.isChecked())
            self.settings.setValue("autoSaveInterval", self.auto_save_interval.value())
            self.settings.setValue("recentFilesCount", self.recent_files_count.value())
            
            # 验证设置
            self.settings.setValue("enableSyntaxValidation", self.enable_syntax_validation.isChecked())
            self.settings.setValue("enableSemanticValidation", self.enable_semantic_validation.isChecked())
            self.settings.setValue("enableBusinessValidation", self.enable_business_validation.isChecked())
            self.settings.setValue("enableSmartValidation", self.enable_smart_validation.isChecked())
            
            self.settings.setValue("showErrors", self.show_errors.isChecked())
            self.settings.setValue("showWarnings", self.show_warnings.isChecked())
            self.settings.setValue("showInfo", self.show_info.isChecked())
            
            self.settings.setValue("confidenceThreshold", self.confidence_threshold.value())
            self.settings.setValue("maxRecommendations", self.max_recommendations.value())
            
            # 界面设置
            self.settings.setValue("theme", self.theme_combo.currentText())
            self.settings.setValue("fontFamily", self.font_family_combo.currentText())
            self.settings.setValue("fontSize", self.font_size_spin.value())
            
            self.settings.setValue("showToolbar", self.show_toolbar.isChecked())
            self.settings.setValue("showStatusbar", self.show_statusbar.isChecked())
            self.settings.setValue("showLineNumbers", self.show_line_numbers.isChecked())
            
            # 知识库设置
            self.settings.setValue("knowledgeBasePath", self.kb_path_edit.text())
            self.settings.setValue("autoUpdateKB", self.auto_update_kb.isChecked())
            self.settings.setValue("enableLearning", self.enable_learning.isChecked())
            self.settings.setValue("cacheKnowledge", self.cache_knowledge.isChecked())
            
            self.settings.setValue("inferenceDepth", self.inference_depth.value())
            self.settings.setValue("inferenceTimeout", self.inference_timeout.value())
            
            # 高级设置
            self.settings.setValue("maxMemoryUsage", self.max_memory_usage.value())
            self.settings.setValue("threadCount", self.thread_count.value())
            
            self.settings.setValue("logLevel", self.log_level_combo.currentText())
            self.settings.setValue("enableFileLogging", self.enable_file_logging.isChecked())
            self.settings.setValue("logPath", self.log_path_edit.text())
            
            self.settings.setValue("enableDebugMode", self.enable_debug_mode.isChecked())
            self.settings.setValue("showDebugInfo", self.show_debug_info.isChecked())
            
        except Exception as e:
            logger.error(f"保存首选项失败: {e}")
    
    def get_preferences(self) -> Dict[str, Any]:
        """获取首选项字典"""
        return {
            # 通用设置
            'language': self.language_combo.currentText(),
            'default_path': self.default_path_edit.text(),
            'auto_save': self.auto_save_checkbox.isChecked(),
            'auto_save_interval': self.auto_save_interval.value(),
            'recent_files_count': self.recent_files_count.value(),
            
            # 验证设置
            'enable_syntax_validation': self.enable_syntax_validation.isChecked(),
            'enable_semantic_validation': self.enable_semantic_validation.isChecked(),
            'enable_business_validation': self.enable_business_validation.isChecked(),
            'enable_smart_validation': self.enable_smart_validation.isChecked(),
            
            'show_errors': self.show_errors.isChecked(),
            'show_warnings': self.show_warnings.isChecked(),
            'show_info': self.show_info.isChecked(),
            
            'confidence_threshold': self.confidence_threshold.value(),
            'max_recommendations': self.max_recommendations.value(),
            
            # 界面设置
            'theme': self.theme_combo.currentText(),
            'font_family': self.font_family_combo.currentText(),
            'font_size': self.font_size_spin.value(),
            
            'show_toolbar': self.show_toolbar.isChecked(),
            'show_statusbar': self.show_statusbar.isChecked(),
            'show_line_numbers': self.show_line_numbers.isChecked(),
            
            # 知识库设置
            'knowledge_base_path': self.kb_path_edit.text(),
            'auto_update_kb': self.auto_update_kb.isChecked(),
            'enable_learning': self.enable_learning.isChecked(),
            'cache_knowledge': self.cache_knowledge.isChecked(),
            
            'inference_depth': self.inference_depth.value(),
            'inference_timeout': self.inference_timeout.value(),
            
            # 高级设置
            'max_memory_usage': self.max_memory_usage.value(),
            'thread_count': self.thread_count.value(),
            
            'log_level': self.log_level_combo.currentText(),
            'enable_file_logging': self.enable_file_logging.isChecked(),
            'log_path': self.log_path_edit.text(),
            
            'enable_debug_mode': self.enable_debug_mode.isChecked(),
            'show_debug_info': self.show_debug_info.isChecked()
        }
    
    def accept(self):
        """接受对话框"""
        self._save_preferences()
        super().accept()
    
    def _apply_preferences(self):
        """应用首选项"""
        self._save_preferences()
    
    def _browse_default_path(self):
        """浏览默认路径"""
        path = QFileDialog.getExistingDirectory(self, "选择默认打开路径")
        if path:
            self.default_path_edit.setText(path)
    
    def _browse_kb_path(self):
        """浏览知识库路径"""
        path, _ = QFileDialog.getOpenFileName(
            self, "选择知识库文件", "", "数据库文件 (*.db);;所有文件 (*.*)"
        )
        if path:
            self.kb_path_edit.setText(path)
    
    def _browse_log_path(self):
        """浏览日志路径"""
        path, _ = QFileDialog.getSaveFileName(
            self, "选择日志文件", "", "日志文件 (*.log);;所有文件 (*.*)"
        )
        if path:
            self.log_path_edit.setText(path)
    
    def _choose_color(self, color_type: str):
        """选择颜色"""
        color = QColorDialog.getColor(Qt.white, self)
        if color.isValid():
            button = getattr(self, f"{color_type}_color_button")
            button.setStyleSheet(f"background-color: {color.name()}")
    
    def _clear_recent_files(self):
        """清除最近文件"""
        self.settings.remove("recentFiles")
    
    def _reset_to_defaults(self):
        """重置为默认设置"""
        # 清除所有设置
        self.settings.clear()
        
        # 重新加载默认值
        self._load_preferences()
