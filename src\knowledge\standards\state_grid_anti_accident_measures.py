"""
国家电网公司十八项电网重大反事故措施知识库
包含国家电网公司十八项电网重大反事故措施的详细知识
"""

import logging
from typing import Dict, List, Any
from datetime import datetime

from ..base.knowledge_entity import (
    StandardEntity, RuleEntity, RequirementEntity,
    EntityType, ConfidenceLevel
)


logger = logging.getLogger(__name__)


class StateGridAntiAccidentMeasures:
    """国家电网公司十八项电网重大反事故措施知识库"""
    
    def __init__(self):
        """初始化国家电网公司十八项电网重大反事故措施知识库"""
        self.standards = {}
        self.rules = {}
        self.requirements = {}
        
        self._initialize_knowledge()
        logger.info("国家电网公司十八项电网重大反事故措施知识库初始化完成")
    
    def _initialize_knowledge(self):
        """初始化知识库内容"""
        self._init_anti_accident_standards()
        self._init_anti_accident_rules()
        self._init_anti_accident_requirements()
    
    def _init_anti_accident_standards(self):
        """初始化反事故措施标准"""
        
        # 国家电网公司十八项电网重大反事故措施
        anti_accident_measures = StandardEntity(
            standard_number="StateGrid-Anti-Accident-18",
            standard_title="国家电网公司十八项电网重大反事故措施",
            name="国家电网公司十八项电网重大反事故措施",
            description="国家电网公司为防范电网重大事故而制定的十八项关键技术措施，涵盖电网规划设计、设备选型、运行维护等各个环节",
            content="""
            国家电网公司十八项电网重大反事故措施是国家电网公司为防范电网重大事故而制定的关键技术措施，
            涵盖电网规划设计、设备选型、运行维护等各个环节，是保障电网安全稳定运行的重要技术文件。
            
            十八项反事故措施包括：
            
            1. 防止人身伤亡事故
            2. 防止系统稳定破坏事故
            3. 防止机网协调及风电、光伏、分布式电源大规模脱网事故
            4. 防止电气误操作事故
            5. 防止枢纽变电站全停事故
            6. 防止输电线路事故
            7. 防止输变电设备污闪事故
            8. 防止直流输电系统事故
            9. 防止大型变压器损坏事故
            10. 防止互感器损坏事故
            11. 防止开关设备事故
            12. 防止接地网事故
            13. 防止直流系统事故
            14. 防止继电保护事故
            15. 防止电网调度自动化系统与电力通信网事故
            16. 防止串补控制保护事故
            17. 防止电力通信网事故
            18. 防止信息安全事故
            
            每项措施都包含具体的预防措施、技术要求、管理规定等内容。
            """,
            source="国家电网公司",
            issuing_organization="国家电网公司",
            effective_date=datetime(2019, 1, 1),
            category="electrical",
            domain="grid_operation",
            scope="电网安全运行",
            confidence=1.0,
            attributes={
                "classification": "Safety",
                "version": "2018版",
                "applicable_voltage_levels": ["110kV", "220kV", "500kV", "750kV", "1000kV"]
            }
        )
        self.standards["state_grid_anti_accident_measures"] = anti_accident_measures
    
    def _init_anti_accident_rules(self):
        """初始化反事故措施规则"""
        
        # 防止继电保护事故规则
        prevent_protection_accident_rule = RuleEntity(
            name="Prevent_Protection_Accident_Rule",
            description="防止继电保护事故的技术和管理措施",
            content="""
            防止继电保护事故的主要措施包括：
            
            1. 继电保护配置要求：
               - 保护配置应满足选择性、速动性、灵敏性、可靠性要求
               - 重要设备和线路应配置主保护和后备保护
               - 保护装置应双重化配置，互为备用
               
            2. 保护装置技术要求：
               - 保护装置应采用成熟、可靠的硬件和软件
               - 保护装置应具备自检功能
               - 保护装置应具备事件记录功能
               
            3. 二次回路要求：
               - 二次回路应简单可靠，避免寄生回路
               - 二次电缆应采用屏蔽电缆
               - 二次回路应定期检查维护
               
            4. 运行管理要求：
               - 保护定值应定期核对
               - 保护装置应定期检验
               - 保护动作应详细分析
            """,
            source="国家电网公司十八项电网重大反事故措施第14项",
            rule_type="business",
            severity="critical",
            category="protection_safety",
            condition="继电保护系统设计、配置、运行和维护",
            action="检查继电保护系统是否符合反事故措施要求",
            message_template="继电保护系统不符合国家电网公司反事故措施要求: {details}",
            applicable_standards=["StateGrid-Anti-Accident-18"],
            applicable_devices=["protection", "substation"],
            fix_suggestions=[
                "检查保护配置的双重化配置是否满足要求",
                "验证保护装置的自检功能是否正常",
                "检查二次回路是否存在寄生回路",
                "确认保护定值是否定期核对"
            ],
            confidence=0.95,
            keywords=["继电保护", "反事故措施", "保护配置", "双重化"]
        )
        self.rules["prevent_protection_accident"] = prevent_protection_accident_rule
        
        # 防止开关设备事故规则
        prevent_switchgear_accident_rule = RuleEntity(
            name="Prevent_Switchgear_Accident_Rule",
            description="防止开关设备事故的技术和管理措施",
            content="""
            防止开关设备事故的主要措施包括：
            
            1. 设备选型要求：
               - 断路器应选择开断容量足够的产品
               - 隔离开关应选择机械强度足够的产品
               - 开关设备应具备完善的机械联锁装置
               
            2. 设备安装要求：
               - 开关设备安装应符合相关标准要求
               - 开关设备应有良好的通风散热条件
               - 开关设备应有完善的接地系统
               
            3. 运行维护要求：
               - 定期检查开关设备的机械特性
               - 定期检查开关设备的电气特性
               - 定期检查开关设备的绝缘状况
               
            4. 操作管理要求：
               - 严格执行操作票制度
               - 操作前应检查设备状态
               - 操作后应确认设备位置
            """,
            source="国家电网公司十八项电网重大反事故措施第11项",
            rule_type="business",
            severity="critical",
            category="switchgear_safety",
            condition="开关设备选型、安装、运行和维护",
            action="检查开关设备是否符合反事故措施要求",
            message_template="开关设备不符合国家电网公司反事故措施要求: {details}",
            applicable_standards=["StateGrid-Anti-Accident-18"],
            applicable_devices=["switchgear", "circuit_breaker"],
            fix_suggestions=[
                "检查断路器开断容量是否满足系统要求",
                "验证隔离开关机械强度是否足够",
                "检查机械联锁装置是否完善",
                "确认操作票制度是否严格执行"
            ],
            confidence=0.95,
            keywords=["开关设备", "反事故措施", "断路器", "隔离开关"]
        )
        self.rules["prevent_switchgear_accident"] = prevent_switchgear_accident_rule
        
        # 防止大型变压器损坏事故规则
        prevent_transformer_accident_rule = RuleEntity(
            name="Prevent_Transformer_Accident_Rule",
            description="防止大型变压器损坏事故的技术和管理措施",
            content="""
            防止大型变压器损坏事故的主要措施包括：
            
            1. 设计选型要求：
               - 变压器应选择技术成熟、结构可靠的产品
               - 变压器绝缘水平应满足运行要求
               - 变压器冷却系统应冗余配置
               
            2. 安装调试要求：
               - 变压器安装应符合相关标准要求
               - 变压器投运前应进行充分的试验
               - 变压器保护应正确配置和整定
               
            3. 运行维护要求：
               - 定期监测变压器油色谱
               - 定期检查变压器温度
               - 定期检查变压器绝缘状况
               
            4. 技术监督要求：
               - 建立完善的变压器技术档案
               - 定期进行变压器状态评估
               - 及时处理变压器缺陷
            """,
            source="国家电网公司十八项电网重大反事故措施第9项",
            rule_type="business",
            severity="critical",
            category="transformer_safety",
            condition="变压器设计、选型、安装、运行和维护",
            action="检查变压器是否符合反事故措施要求",
            message_template="变压器不符合国家电网公司反事故措施要求: {details}",
            applicable_standards=["StateGrid-Anti-Accident-18"],
            applicable_devices=["transformer"],
            fix_suggestions=[
                "检查变压器绝缘水平是否满足要求",
                "验证冷却系统是否冗余配置",
                "确认变压器保护是否正确配置",
                "检查变压器油色谱监测是否正常"
            ],
            confidence=0.95,
            keywords=["变压器", "反事故措施", "绝缘", "冷却系统"]
        )
        self.rules["prevent_transformer_accident"] = prevent_transformer_accident_rule
        
        # 防止电气误操作事故规则
        prevent_electrical_misoperation_rule = RuleEntity(
            name="Prevent_Electrical_Misoperation_Rule",
            description="防止电气误操作事故的技术和管理措施",
            content="""
            防止电气误操作事故的主要措施包括：
            
            1. 技术措施：
               - 严格执行操作票制度
               - 操作前应进行模拟预演
               - 操作中应逐项确认设备状态
               - 操作后应检查设备实际位置
               
            2. 防误闭锁装置：
               - 重要电气设备应配置防误闭锁装置
               - 防误闭锁装置应完善可靠
               - 防误闭锁装置应定期检查维护
               
            3. 人员培训：
               - 操作人员应经过专业培训
               - 操作人员应熟悉设备性能
               - 操作人员应掌握操作程序
               
            4. 管理制度：
               - 建立完善的操作管理制度
               - 建立操作监护制度
               - 建立操作记录制度
            """,
            source="国家电网公司十八项电网重大反事故措施第4项",
            rule_type="business",
            severity="critical",
            category="operation_safety",
            condition="电气设备操作",
            action="检查电气操作是否符合反事故措施要求",
            message_template="电气操作不符合国家电网公司反事故措施要求: {details}",
            applicable_standards=["StateGrid-Anti-Accident-18"],
            applicable_devices=["all_electrical"],
            fix_suggestions=[
                "检查操作票制度是否严格执行",
                "验证防误闭锁装置是否完善",
                "确认操作人员是否经过培训",
                "检查操作监护制度是否落实"
            ],
            confidence=0.95,
            keywords=["电气误操作", "反事故措施", "操作票", "防误闭锁"]
        )
        self.rules["prevent_electrical_misoperation"] = prevent_electrical_misoperation_rule
    
    def _init_anti_accident_requirements(self):
        """初始化反事故措施要求"""
        
        # 继电保护反事故要求
        protection_anti_accident_req = RequirementEntity(
            name="Protection_Anti_Accident_Requirement",
            description="继电保护反事故技术要求",
            content="""
            继电保护反事故技术要求：
            
            1. 保护配置要求：
               - 220kV及以上系统保护应双重化配置
               - 保护装置应采用主后一体化设计
               - 保护通道应具备自愈功能
               
            2. 保护装置要求：
               - 保护装置应具备差动保护功能
               - 保护装置应具备距离保护功能
               - 保护装置应具备零序保护功能
               
            3. 二次回路要求：
               - 二次电缆应采用阻燃屏蔽电缆
               - 二次回路应避免交直流混用
               - 二次回路应有完善的接地系统
               
            4. 运行维护要求：
               - 保护定值应每年核对一次
               - 保护装置应每6年进行一次全检
               - 保护动作应100%分析原因
            """,
            source="国家电网公司十八项电网重大反事故措施第14项",
            requirement_type="safety",
            priority="high",
            mandatory=True,
            verification_method="检查和测试",
            acceptance_criteria="满足技术要求",
            source_standard="StateGrid-Anti-Accident-18",
            clause_reference="第14项防止继电保护事故",
            confidence=0.95,
            attributes={
                "voltage_level": "220kV及以上",
                "application_scope": "继电保护系统"
            }
        )
        self.requirements["protection_anti_accident"] = protection_anti_accident_req
        
        # 开关设备反事故要求
        switchgear_anti_accident_req = RequirementEntity(
            name="Switchgear_Anti_Accident_Requirement",
            description="开关设备反事故技术要求",
            content="""
            开关设备反事故技术要求：
            
            1. 设备选型要求：
               - 断路器开断容量应有20%裕度
               - 隔离开关机械寿命应大于3000次
               - 接地开关应具备快速分合能力
               
            2. 设备安装要求：
               - 开关设备安装应有防震措施
               - 开关设备应有良好的通风条件
               - 开关设备应有完善的防潮措施
               
            3. 运行维护要求：
               - 断路器机械特性应每年测试一次
               - 隔离开关接触电阻应每年测试一次
               - 开关设备红外测温应每月进行一次
               
            4. 技术监督要求：
               - 建立开关设备技术档案
               - 定期进行开关设备状态评估
               - 及时处理开关设备缺陷
            """,
            source="国家电网公司十八项电网重大反事故措施第11项",
            requirement_type="safety",
            priority="high",
            mandatory=True,
            verification_method="检查和测试",
            acceptance_criteria="满足技术要求",
            source_standard="StateGrid-Anti-Accident-18",
            clause_reference="第11项防止开关设备事故",
            confidence=0.95,
            attributes={
                "voltage_level": "110kV及以上",
                "application_scope": "开关设备"
            }
        )
        self.requirements["switchgear_anti_accident"] = switchgear_anti_accident_req
    
    def get_all_standards(self) -> List[StandardEntity]:
        """获取所有标准"""
        return list(self.standards.values())
    
    def get_all_rules(self) -> List[RuleEntity]:
        """获取所有规则"""
        return list(self.rules.values())
    
    def get_all_requirements(self) -> List[RequirementEntity]:
        """获取所有技术要求"""
        return list(self.requirements.values())
    
    def get_rules_for_standard(self, standard_id: str) -> List[RuleEntity]:
        """获取特定标准的相关规则"""
        rules = []
        for rule in self.rules.values():
            if standard_id in rule.applicable_standards:
                rules.append(rule)
        return rules
    
    def get_applicable_rules(self, device_type: str = None, category: str = None) -> List[RuleEntity]:
        """获取适用的规则"""
        applicable_rules = []
        
        for rule in self.rules.values():
            # 检查设备类型
            if device_type and rule.applicable_devices:
                if device_type not in rule.applicable_devices:
                    continue
            
            # 检查类别
            if category and rule.category:
                if category != rule.category:
                    continue
            
            applicable_rules.append(rule)
        
        return applicable_rules