"""
IEC61850 XML解析器基类
提供通用的XML解析功能和接口定义
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Type
from datetime import datetime
import xml.etree.ElementTree as ET
from lxml import etree
import logging

from ..models.base import BaseModel


class ParseError(Exception):
    """解析错误异常"""
    
    def __init__(self, message: str, line_number: Optional[int] = None, 
                 column_number: Optional[int] = None, element_name: Optional[str] = None):
        self.message = message
        self.line_number = line_number
        self.column_number = column_number
        self.element_name = element_name
        super().__init__(self.format_message())
    
    def format_message(self) -> str:
        """格式化错误消息"""
        msg = self.message
        if self.element_name:
            msg = f"元素 '{self.element_name}': {msg}"
        if self.line_number:
            msg = f"第{self.line_number}行: {msg}"
            if self.column_number:
                msg = f"第{self.line_number}行第{self.column_number}列: {msg}"
        return msg


@dataclass
class ParseResult:
    """解析结果"""
    
    success: bool = False
    data: Optional[BaseModel] = None
    errors: List[ParseError] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    parse_time: Optional[datetime] = None
    file_path: Optional[str] = None
    
    def add_error(self, error: Union[str, ParseError]) -> None:
        """添加错误"""
        if isinstance(error, str):
            error = ParseError(error)
        self.errors.append(error)
        self.success = False
    
    def add_warning(self, warning: str) -> None:
        """添加警告"""
        self.warnings.append(warning)
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
    
    def get_summary(self) -> str:
        """获取解析结果摘要"""
        if self.success:
            summary = "解析成功"
            if self.has_warnings():
                summary += f"，{len(self.warnings)}个警告"
        else:
            summary = f"解析失败，{len(self.errors)}个错误"
            if self.has_warnings():
                summary += f"，{len(self.warnings)}个警告"
        return summary


class BaseParser(ABC):
    """XML解析器基类"""
    
    def __init__(self, validate_schema: bool = True, strict_mode: bool = False):
        """
        初始化解析器
        
        Args:
            validate_schema: 是否进行Schema验证
            strict_mode: 是否启用严格模式（严格模式下警告也会导致解析失败）
        """
        self.validate_schema = validate_schema
        self.strict_mode = strict_mode
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 解析统计
        self.parse_count = 0
        self.success_count = 0
        self.error_count = 0
    
    @abstractmethod
    def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型"""
        pass
    
    @abstractmethod
    def get_root_element_name(self) -> str:
        """获取根元素名称"""
        pass
    
    @abstractmethod
    def parse_root_element(self, root: etree.Element) -> BaseModel:
        """解析根元素"""
        pass
    
    def parse_file(self, file_path: Union[str, Path]) -> ParseResult:
        """
        解析XML文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            解析结果
        """
        start_time = datetime.now()
        result = ParseResult(
            parse_time=start_time,
            file_path=str(file_path)
        )
        
        try:
            # 统计更新
            self.parse_count += 1
            
            # 检查文件是否存在
            path = Path(file_path)
            if not path.exists():
                result.add_error(f"文件不存在: {file_path}")
                return result
            
            # 检查文件扩展名
            if path.suffix.lower() not in ['.xml', '.scd', '.icd', '.cid']:
                result.add_warning(f"文件扩展名可能不正确: {path.suffix}")
            
            # 解析XML文件
            try:
                tree = etree.parse(str(path))
                root = tree.getroot()
            except etree.XMLSyntaxError as e:
                result.add_error(ParseError(
                    f"XML语法错误: {e.msg}",
                    line_number=e.lineno,
                    column_number=e.offset
                ))
                return result
            except Exception as e:
                result.add_error(f"文件读取错误: {str(e)}")
                return result
            
            # 验证根元素（处理命名空间）
            expected_root = self.get_root_element_name()
            root_tag = root.tag

            # 如果根元素包含命名空间，提取本地名称
            if '}' in root_tag:
                root_tag = root_tag.split('}')[1]

            if root_tag != expected_root:
                result.add_error(f"根元素错误，期望 '{expected_root}'，实际 '{root_tag}'")
                return result
            
            # Schema验证
            if self.validate_schema:
                schema_errors = self._validate_schema(tree)
                for error in schema_errors:
                    result.add_error(error)
                
                if result.has_errors():
                    return result
            
            # 解析内容
            try:
                data = self.parse_root_element(root)
                result.data = data
                result.success = True
                self.success_count += 1
                
                # 添加元数据
                result.metadata.update({
                    'file_size': path.stat().st_size,
                    'parse_duration': (datetime.now() - start_time).total_seconds(),
                    'element_count': len(root.xpath('.//*')),
                    'namespace': root.nsmap if hasattr(root, 'nsmap') else {}
                })
                
            except Exception as e:
                result.add_error(f"内容解析错误: {str(e)}")
                self.error_count += 1
                return result
            
            # 严格模式检查
            if self.strict_mode and result.has_warnings():
                result.success = False
                result.add_error("严格模式下不允许警告")
            
        except Exception as e:
            result.add_error(f"解析过程发生未知错误: {str(e)}")
            self.error_count += 1
        
        return result
    
    def parse_string(self, xml_content: str) -> ParseResult:
        """
        解析XML字符串
        
        Args:
            xml_content: XML内容字符串
            
        Returns:
            解析结果
        """
        start_time = datetime.now()
        result = ParseResult(parse_time=start_time)
        
        try:
            # 解析XML字符串
            try:
                root = etree.fromstring(xml_content.encode('utf-8'))
            except etree.XMLSyntaxError as e:
                result.add_error(ParseError(
                    f"XML语法错误: {e.msg}",
                    line_number=e.lineno,
                    column_number=e.offset
                ))
                return result
            
            # 验证根元素（处理命名空间）
            expected_root = self.get_root_element_name()
            root_tag = root.tag

            # 如果根元素包含命名空间，提取本地名称
            if '}' in root_tag:
                root_tag = root_tag.split('}')[1]

            if root_tag != expected_root:
                result.add_error(f"根元素错误，期望 '{expected_root}'，实际 '{root_tag}'")
                return result
            
            # 解析内容
            data = self.parse_root_element(root)
            result.data = data
            result.success = True
            
            # 添加元数据
            result.metadata.update({
                'content_length': len(xml_content),
                'parse_duration': (datetime.now() - start_time).total_seconds(),
                'element_count': len(root.xpath('.//*'))
            })
            
        except Exception as e:
            result.add_error(f"解析错误: {str(e)}")
        
        return result
    
    def _validate_schema(self, tree: etree.ElementTree) -> List[ParseError]:
        """
        Schema验证（子类可重写）
        
        Args:
            tree: XML树
            
        Returns:
            验证错误列表
        """
        # 基类默认不进行Schema验证
        return []
    
    def get_element_text(self, element: etree.Element, default: str = "") -> str:
        """获取元素文本内容"""
        return element.text.strip() if element.text else default
    
    def get_element_attribute(self, element: etree.Element, attr_name: str, 
                            default: Optional[str] = None) -> Optional[str]:
        """获取元素属性值"""
        return element.get(attr_name, default)
    
    def get_required_attribute(self, element: etree.Element, attr_name: str) -> str:
        """获取必需的属性值"""
        value = element.get(attr_name)
        if value is None:
            raise ParseError(
                f"缺少必需属性 '{attr_name}'",
                element_name=element.tag
            )
        return value
    
    def find_child_element(self, parent: etree.Element, tag_name: str,
                          required: bool = False) -> Optional[etree.Element]:
        """查找子元素（支持命名空间）"""
        # 首先尝试直接查找
        child = parent.find(tag_name)

        # 如果没找到且父元素有命名空间，尝试使用命名空间查找
        if child is None and hasattr(parent, 'nsmap') and parent.nsmap:
            # 获取默认命名空间
            default_ns = parent.nsmap.get(None)
            if default_ns:
                namespaced_tag = f"{{{default_ns}}}{tag_name}"
                child = parent.find(namespaced_tag)

        if required and child is None:
            raise ParseError(
                f"缺少必需子元素 '{tag_name}'",
                element_name=self._get_local_tag_name(parent.tag)
            )
        return child
    
    def find_child_elements(self, parent: etree.Element, tag_name: str) -> List[etree.Element]:
        """查找所有匹配的子元素（支持命名空间）"""
        # 首先尝试直接查找
        children = parent.findall(tag_name)

        # 如果没找到且父元素有命名空间，尝试使用命名空间查找
        if not children and hasattr(parent, 'nsmap') and parent.nsmap:
            # 获取默认命名空间
            default_ns = parent.nsmap.get(None)
            if default_ns:
                namespaced_tag = f"{{{default_ns}}}{tag_name}"
                children = parent.findall(namespaced_tag)

        return children
    
    def parse_boolean_attribute(self, element: etree.Element, attr_name: str, 
                               default: bool = False) -> bool:
        """解析布尔属性"""
        value = element.get(attr_name)
        if value is None:
            return default
        return value.lower() in ('true', '1', 'yes', 'on')
    
    def parse_int_attribute(self, element: etree.Element, attr_name: str, 
                           default: Optional[int] = None) -> Optional[int]:
        """解析整数属性"""
        value = element.get(attr_name)
        if value is None:
            return default
        try:
            return int(value)
        except ValueError:
            raise ParseError(
                f"属性 '{attr_name}' 的值 '{value}' 不是有效整数",
                element_name=element.tag
            )
    
    def _get_local_tag_name(self, tag: str) -> str:
        """获取本地标签名（去除命名空间）"""
        if '}' in tag:
            return tag.split('}')[1]
        return tag

    def get_statistics(self) -> Dict[str, Any]:
        """获取解析统计信息"""
        return {
            'total_parses': self.parse_count,
            'successful_parses': self.success_count,
            'failed_parses': self.error_count,
            'success_rate': self.success_count / self.parse_count if self.parse_count > 0 else 0
        }
