"""
PDF文档提取器
从PDF文档中提取文本和结构信息
"""

import logging
from typing import Dict, List, Optional, Any
from pathlib import Path
import re

try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    logging.warning("PyMuPDF未安装，PDF提取功能将受限")

from .text_analyzer import TextAnalyzer


logger = logging.getLogger(__name__)


class PDFExtractor:
    """PDF文档提取器"""
    
    def __init__(self):
        """初始化PDF提取器"""
        self.text_analyzer = TextAnalyzer()
        
        if not PYMUPDF_AVAILABLE:
            logger.warning("PDF提取器初始化失败：缺少PyMuPDF依赖")
        else:
            logger.info("PDF提取器初始化完成")
    
    def extract_from_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """
        从PDF文件中提取内容
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            Dict: 提取的内容
        """
        if not PYMUPDF_AVAILABLE:
            logger.error("PyMuPDF未安装，无法提取PDF内容")
            return self._empty_result()
        
        try:
            pdf_path = Path(pdf_path)
            if not pdf_path.exists():
                logger.error(f"PDF文件不存在: {pdf_path}")
                return self._empty_result()
            
            doc = fitz.open(str(pdf_path))
            
            result = {
                'metadata': self._extract_metadata(doc),
                'text': self._extract_text(doc),
                'structure': self._extract_structure(doc),
                'pages': self._extract_pages(doc),
                'images': self._extract_images(doc),
                'tables': self._extract_tables(doc)
            }
            
            doc.close()
            
            logger.info(f"成功提取PDF内容: {pdf_path.name}, {len(result['pages'])}页")
            return result
            
        except Exception as e:
            logger.error(f"PDF提取失败: {e}")
            return self._empty_result()
    
    def _extract_metadata(self, doc) -> Dict[str, Any]:
        """提取PDF元数据"""
        try:
            metadata = doc.metadata
            return {
                'title': metadata.get('title', ''),
                'author': metadata.get('author', ''),
                'subject': metadata.get('subject', ''),
                'creator': metadata.get('creator', ''),
                'producer': metadata.get('producer', ''),
                'creation_date': metadata.get('creationDate', ''),
                'modification_date': metadata.get('modDate', ''),
                'page_count': doc.page_count,
                'encrypted': doc.needs_pass
            }
        except Exception as e:
            logger.warning(f"元数据提取失败: {e}")
            return {}
    
    def _extract_text(self, doc) -> str:
        """提取全文本"""
        try:
            full_text = ""
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text = page.get_text()
                full_text += f"\n--- 第{page_num + 1}页 ---\n{text}\n"
            
            return full_text
        except Exception as e:
            logger.warning(f"文本提取失败: {e}")
            return ""
    
    def _extract_structure(self, doc) -> Dict[str, Any]:
        """提取文档结构"""
        try:
            structure = {
                'toc': [],  # 目录
                'headings': [],  # 标题
                'sections': []  # 章节
            }
            
            # 提取目录
            toc = doc.get_toc()
            structure['toc'] = toc
            
            # 提取标题和章节
            for page_num in range(doc.page_count):
                page = doc[page_num]
                blocks = page.get_text("dict")["blocks"]
                
                for block in blocks:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text = span["text"].strip()
                                font_size = span["size"]
                                
                                # 根据字体大小判断是否为标题
                                if font_size > 12 and len(text) > 0:
                                    if self._is_heading(text):
                                        structure['headings'].append({
                                            'text': text,
                                            'page': page_num + 1,
                                            'font_size': font_size,
                                            'level': self._determine_heading_level(font_size)
                                        })
            
            # 根据标题划分章节
            structure['sections'] = self._create_sections(structure['headings'])
            
            return structure
            
        except Exception as e:
            logger.warning(f"结构提取失败: {e}")
            return {'toc': [], 'headings': [], 'sections': []}
    
    def _extract_pages(self, doc) -> List[Dict[str, Any]]:
        """提取页面信息"""
        pages = []
        
        try:
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                page_info = {
                    'page_number': page_num + 1,
                    'text': page.get_text(),
                    'word_count': len(page.get_text().split()),
                    'rect': page.rect,
                    'rotation': page.rotation,
                    'links': page.get_links(),
                    'annotations': self._extract_annotations(page)
                }
                
                pages.append(page_info)
                
        except Exception as e:
            logger.warning(f"页面信息提取失败: {e}")
        
        return pages
    
    def _extract_images(self, doc) -> List[Dict[str, Any]]:
        """提取图像信息"""
        images = []
        
        try:
            for page_num in range(doc.page_count):
                page = doc[page_num]
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    try:
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)
                        
                        image_info = {
                            'page': page_num + 1,
                            'index': img_index,
                            'xref': xref,
                            'width': pix.width,
                            'height': pix.height,
                            'colorspace': pix.colorspace.name if pix.colorspace else 'unknown',
                            'size': len(pix.tobytes())
                        }
                        
                        images.append(image_info)
                        pix = None  # 释放内存
                        
                    except Exception as e:
                        logger.warning(f"图像提取失败: {e}")
                        continue
                        
        except Exception as e:
            logger.warning(f"图像信息提取失败: {e}")
        
        return images
    
    def _extract_tables(self, doc) -> List[Dict[str, Any]]:
        """提取表格信息（简化版）"""
        tables = []
        
        try:
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                # 简单的表格检测：查找规律性的文本排列
                text = page.get_text()
                lines = text.split('\n')
                
                potential_tables = []
                current_table = []
                
                for line in lines:
                    # 检测是否为表格行（包含多个制表符或空格分隔的列）
                    if self._is_table_row(line):
                        current_table.append(line)
                    else:
                        if len(current_table) >= 3:  # 至少3行才认为是表格
                            potential_tables.append(current_table)
                        current_table = []
                
                # 添加最后一个表格
                if len(current_table) >= 3:
                    potential_tables.append(current_table)
                
                # 处理检测到的表格
                for i, table_lines in enumerate(potential_tables):
                    table_info = {
                        'page': page_num + 1,
                        'table_index': i,
                        'rows': len(table_lines),
                        'content': table_lines,
                        'estimated_columns': self._estimate_columns(table_lines[0])
                    }
                    tables.append(table_info)
                    
        except Exception as e:
            logger.warning(f"表格提取失败: {e}")
        
        return tables
    
    def _extract_annotations(self, page) -> List[Dict[str, Any]]:
        """提取页面注释"""
        annotations = []
        
        try:
            for annot in page.annots():
                annot_info = {
                    'type': annot.type[1],  # 注释类型
                    'content': annot.info.get('content', ''),
                    'author': annot.info.get('title', ''),
                    'rect': annot.rect,
                    'page': page.number + 1
                }
                annotations.append(annot_info)
                
        except Exception as e:
            logger.warning(f"注释提取失败: {e}")
        
        return annotations
    
    def _is_heading(self, text: str) -> bool:
        """判断是否为标题"""
        # 标题特征：
        # 1. 包含章节编号
        # 2. 长度适中
        # 3. 不包含过多标点符号
        
        if len(text) > 100 or len(text) < 3:
            return False
        
        # 检查章节编号模式
        heading_patterns = [
            r'^\d+\.?\s+',  # 1. 或 1
            r'^第[一二三四五六七八九十\d]+[章节条款]',  # 第一章
            r'^[一二三四五六七八九十]+[、．]',  # 一、
            r'^\([一二三四五六七八九十\d]+\)',  # (一)
            r'^[A-Z]\.\s+',  # A.
            r'^\d+\.\d+',  # 1.1
        ]
        
        for pattern in heading_patterns:
            if re.match(pattern, text):
                return True
        
        # 检查是否为常见标题词汇
        title_keywords = ['概述', '总则', '术语', '定义', '要求', '规范', '方法', '附录']
        if any(keyword in text for keyword in title_keywords):
            return True
        
        return False
    
    def _determine_heading_level(self, font_size: float) -> int:
        """根据字体大小确定标题级别"""
        if font_size >= 18:
            return 1
        elif font_size >= 16:
            return 2
        elif font_size >= 14:
            return 3
        else:
            return 4
    
    def _create_sections(self, headings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据标题创建章节结构"""
        sections = []
        current_section = None
        
        for heading in headings:
            if heading['level'] == 1:
                # 新的一级标题，开始新章节
                if current_section:
                    sections.append(current_section)
                
                current_section = {
                    'title': heading['text'],
                    'level': 1,
                    'page': heading['page'],
                    'subsections': []
                }
            elif current_section and heading['level'] > current_section['level']:
                # 子章节
                current_section['subsections'].append({
                    'title': heading['text'],
                    'level': heading['level'],
                    'page': heading['page']
                })
        
        # 添加最后一个章节
        if current_section:
            sections.append(current_section)
        
        return sections
    
    def _is_table_row(self, line: str) -> bool:
        """判断是否为表格行"""
        # 简单的表格行检测
        if not line.strip():
            return False
        
        # 检查是否包含多个制表符或连续空格
        tab_count = line.count('\t')
        space_groups = len(re.findall(r'\s{2,}', line))
        
        return tab_count >= 2 or space_groups >= 2
    
    def _estimate_columns(self, line: str) -> int:
        """估算表格列数"""
        # 基于制表符和空格分组估算列数
        tab_count = line.count('\t')
        space_groups = len(re.findall(r'\s{2,}', line))
        
        return max(tab_count + 1, space_groups + 1, 1)
    
    def _empty_result(self) -> Dict[str, Any]:
        """返回空结果"""
        return {
            'metadata': {},
            'text': '',
            'structure': {'toc': [], 'headings': [], 'sections': []},
            'pages': [],
            'images': [],
            'tables': []
        }
