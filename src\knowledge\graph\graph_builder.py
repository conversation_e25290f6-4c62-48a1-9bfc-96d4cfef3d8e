"""
知识图谱构建器
从知识库构建知识图谱
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .knowledge_graph import KnowledgeGraph
from ..base.knowledge_entity import (
    KnowledgeEntity, EntityRelationship, EntityType, RelationshipType
)
from ..base.knowledge_base import KnowledgeBase
from ..standards.iec61850_knowledge import IEC61850Knowledge
from ..standards.gb_standards import GBStandardsKnowledge
from ..standards.dl_standards import DLStandardsKnowledge


logger = logging.getLogger(__name__)


class GraphBuilder:
    """知识图谱构建器"""
    
    def __init__(self):
        """初始化图构建器"""
        self.graph = KnowledgeGraph()
        
        # 知识源
        self.knowledge_sources = {
            'iec61850': IEC61850Knowledge(),
            'gb_standards': GBStandardsKnowledge(),
            'dl_standards': DLStandardsKnowledge()
        }
        
        logger.info("知识图谱构建器初始化完成")
    
    def build_graph(self, knowledge_base: Optional[KnowledgeBase] = None) -> KnowledgeGraph:
        """
        构建知识图谱
        
        Args:
            knowledge_base: 可选的知识库实例
            
        Returns:
            KnowledgeGraph: 构建的知识图谱
        """
        try:
            logger.info("开始构建知识图谱...")
            
            # 从内置知识源构建
            self._build_from_knowledge_sources()
            
            # 从外部知识库构建
            if knowledge_base:
                self._build_from_knowledge_base(knowledge_base)
            
            # 建立自动关系
            self._build_automatic_relationships()
            
            # 计算图统计
            stats = self.graph.get_statistics()
            logger.info(f"知识图谱构建完成: {stats['nodes']}个节点, {stats['edges']}条边")
            
            return self.graph
            
        except Exception as e:
            logger.error(f"知识图谱构建失败: {e}")
            return self.graph
    
    def _build_from_knowledge_sources(self):
        """从内置知识源构建图谱"""
        
        # 添加IEC61850知识
        iec61850_kb = self.knowledge_sources['iec61850']
        
        # 添加标准
        for standard in iec61850_kb.get_all_standards():
            self.graph.add_entity(standard)
        
        # 添加规则
        for rule in iec61850_kb.get_all_rules():
            self.graph.add_entity(rule)
        
        # 添加要求
        for requirement in iec61850_kb.get_all_requirements():
            self.graph.add_entity(requirement)
        
        # 添加设备
        for device in iec61850_kb.get_all_devices():
            self.graph.add_entity(device)
        
        # 添加协议
        for protocol in iec61850_kb.get_all_protocols():
            self.graph.add_entity(protocol)
        
        # 添加GB标准知识
        gb_kb = self.knowledge_sources['gb_standards']
        
        for standard in gb_kb.get_all_standards():
            self.graph.add_entity(standard)
        
        for rule in gb_kb.get_all_rules():
            self.graph.add_entity(rule)
        
        for requirement in gb_kb.get_all_requirements():
            self.graph.add_entity(requirement)
        
        # 添加DL标准知识
        dl_kb = self.knowledge_sources['dl_standards']
        
        for standard in dl_kb.get_all_standards():
            self.graph.add_entity(standard)
        
        for rule in dl_kb.get_all_rules():
            self.graph.add_entity(rule)
        
        for requirement in dl_kb.get_all_requirements():
            self.graph.add_entity(requirement)
        
        logger.info("从内置知识源添加实体完成")
    
    def _build_from_knowledge_base(self, knowledge_base: KnowledgeBase):
        """从外部知识库构建图谱"""
        try:
            # 获取所有实体类型
            for entity_type in EntityType:
                entities = knowledge_base.search_entities(entity_type=entity_type)
                for entity in entities:
                    self.graph.add_entity(entity)
            
            logger.info("从外部知识库添加实体完成")
            
        except Exception as e:
            logger.warning(f"从外部知识库构建失败: {e}")
    
    def _build_automatic_relationships(self):
        """建立自动关系"""
        
        # 建立标准与规则的关系
        self._build_standard_rule_relationships()
        
        # 建立标准与要求的关系
        self._build_standard_requirement_relationships()
        
        # 建立规则与设备的关系
        self._build_rule_device_relationships()
        
        # 建立协议与标准的关系
        self._build_protocol_standard_relationships()
        
        # 建立标准间的关系
        self._build_standard_relationships()
        
        logger.info("自动关系建立完成")
    
    def _build_standard_rule_relationships(self):
        """建立标准与规则的关系"""
        standards = self.graph.get_entities_by_type(EntityType.STANDARD)
        rules = self.graph.get_entities_by_type(EntityType.RULE)
        
        for rule in rules:
            for standard in standards:
                # 检查规则是否引用了标准
                if (standard.standard_number in rule.applicable_standards or
                    standard.standard_number in rule.content or
                    standard.name in rule.content):
                    
                    relationship = EntityRelationship(
                        source_entity_id=standard.id,
                        target_entity_id=rule.id,
                        relationship_type=RelationshipType.DERIVED_FROM,
                        strength=0.8,
                        confidence=0.7,
                        created_by="auto_builder"
                    )
                    self.graph.add_relationship(relationship)
    
    def _build_standard_requirement_relationships(self):
        """建立标准与要求的关系"""
        standards = self.graph.get_entities_by_type(EntityType.STANDARD)
        requirements = self.graph.get_entities_by_type(EntityType.REQUIREMENT)
        
        for requirement in requirements:
            for standard in standards:
                # 检查要求是否来源于标准
                if (standard.standard_number == requirement.source_standard or
                    standard.standard_number in requirement.content):
                    
                    relationship = EntityRelationship(
                        source_entity_id=standard.id,
                        target_entity_id=requirement.id,
                        relationship_type=RelationshipType.REFERENCES,
                        strength=0.9,
                        confidence=0.8,
                        created_by="auto_builder"
                    )
                    self.graph.add_relationship(relationship)
    
    def _build_rule_device_relationships(self):
        """建立规则与设备的关系"""
        rules = self.graph.get_entities_by_type(EntityType.RULE)
        devices = self.graph.get_entities_by_type(EntityType.DEVICE)
        
        for rule in rules:
            for device in devices:
                # 检查规则是否适用于设备
                if (device.device_type in rule.applicable_devices or
                    device.name in rule.content or
                    any(func in rule.content for func in device.supported_functions)):
                    
                    relationship = EntityRelationship(
                        source_entity_id=rule.id,
                        target_entity_id=device.id,
                        relationship_type=RelationshipType.APPLIES_TO,
                        strength=0.7,
                        confidence=0.6,
                        created_by="auto_builder"
                    )
                    self.graph.add_relationship(relationship)
    
    def _build_protocol_standard_relationships(self):
        """建立协议与标准的关系"""
        protocols = self.graph.get_entities_by_type(EntityType.PROTOCOL)
        standards = self.graph.get_entities_by_type(EntityType.STANDARD)
        
        for protocol in protocols:
            for standard in standards:
                # 检查协议是否在标准中定义
                if (standard.standard_number == protocol.standard_reference or
                    protocol.name in standard.content or
                    standard.standard_number in protocol.content):
                    
                    relationship = EntityRelationship(
                        source_entity_id=standard.id,
                        target_entity_id=protocol.id,
                        relationship_type=RelationshipType.IMPLEMENTS,
                        strength=0.9,
                        confidence=0.8,
                        created_by="auto_builder"
                    )
                    self.graph.add_relationship(relationship)
    
    def _build_standard_relationships(self):
        """建立标准间的关系"""
        standards = self.graph.get_entities_by_type(EntityType.STANDARD)
        
        for standard1 in standards:
            for standard2 in standards:
                if standard1.id == standard2.id:
                    continue
                
                # 检查替代关系
                if (standard2.standard_number in standard1.supersedes or
                    standard1.standard_number == standard2.superseded_by):
                    
                    relationship = EntityRelationship(
                        source_entity_id=standard1.id,
                        target_entity_id=standard2.id,
                        relationship_type=RelationshipType.SUPERSEDES,
                        strength=1.0,
                        confidence=0.9,
                        created_by="auto_builder"
                    )
                    self.graph.add_relationship(relationship)
                
                # 检查引用关系
                elif (standard2.standard_number in standard1.content or
                      any(quoted in standard2.standard_number 
                          for quoted in standard1.attributes.get('quoted_standards', []))):
                    
                    relationship = EntityRelationship(
                        source_entity_id=standard1.id,
                        target_entity_id=standard2.id,
                        relationship_type=RelationshipType.REFERENCES,
                        strength=0.6,
                        confidence=0.5,
                        created_by="auto_builder"
                    )
                    self.graph.add_relationship(relationship)
    
    def add_custom_relationship(self, 
                              source_entity_id: str,
                              target_entity_id: str,
                              relationship_type: RelationshipType,
                              strength: float = 1.0,
                              confidence: float = 1.0) -> bool:
        """
        添加自定义关系
        
        Args:
            source_entity_id: 源实体ID
            target_entity_id: 目标实体ID
            relationship_type: 关系类型
            strength: 关系强度
            confidence: 置信度
            
        Returns:
            bool: 是否添加成功
        """
        try:
            relationship = EntityRelationship(
                source_entity_id=source_entity_id,
                target_entity_id=target_entity_id,
                relationship_type=relationship_type,
                strength=strength,
                confidence=confidence,
                created_by="custom"
            )
            
            return self.graph.add_relationship(relationship)
            
        except Exception as e:
            logger.error(f"添加自定义关系失败: {e}")
            return False
    
    def get_graph(self) -> KnowledgeGraph:
        """获取构建的知识图谱"""
        return self.graph
    
    def rebuild_graph(self) -> KnowledgeGraph:
        """重新构建知识图谱"""
        self.graph = KnowledgeGraph()
        return self.build_graph()
