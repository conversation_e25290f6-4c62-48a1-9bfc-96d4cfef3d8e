name: Version Management and Release

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每日检查依赖安全漏洞
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '16'

jobs:
  # 版本验证作业
  version-validation:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyyaml semver
    
    - name: Validate version configuration
      run: |
        python version_tools.py version show
        python -c "
        import yaml
        with open('version_config.yml', 'r') as f:
            config = yaml.safe_load(f)
        print('版本配置验证通过')
        "
    
    - name: Check version consistency
      run: |
        python version_tools.py deps check

  # 依赖安全检查作业
  dependency-security-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit pip-audit
    
    - name: Check for security vulnerabilities
      run: |
        # 检查已知安全漏洞
        safety check --json --output security-report.json || true
        
        # 使用pip-audit进行额外检查
        pip-audit --format=json --output=audit-report.json || true
        
        # 代码安全扫描
        bandit -r src/ -f json -o bandit-report.json || true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          security-report.json
          audit-report.json
          bandit-report.json
    
    - name: Create security issue if vulnerabilities found
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          let hasVulnerabilities = false;
          let issueBody = '## 安全漏洞检测报告\n\n';
          
          // 检查safety报告
          try {
            const safetyReport = JSON.parse(fs.readFileSync('security-report.json', 'utf8'));
            if (safetyReport.length > 0) {
              hasVulnerabilities = true;
              issueBody += '### Safety检测到的漏洞:\n';
              safetyReport.forEach(vuln => {
                issueBody += `- **${vuln.package}** ${vuln.installed_version}: ${vuln.advisory}\n`;
              });
            }
          } catch (e) {}
          
          if (hasVulnerabilities) {
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 发现安全漏洞',
              body: issueBody,
              labels: ['security', 'high-priority']
            });
          }

  # 模块测试作业
  module-testing:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.9', '3.10', '3.11']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Setup Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run unit tests
      run: |
        pytest tests/unit/ --cov=src/ --cov-report=xml --cov-report=html
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ --cov=src/ --cov-append --cov-report=xml
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 版本发布作业
  release:
    runs-on: ubuntu-latest
    needs: [version-validation, dependency-security-check, module-testing]
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine PyInstaller
        pip install -r requirements.txt
    
    - name: Extract version from tag
      id: version
      run: |
        VERSION=${GITHUB_REF#refs/tags/v}
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "发布版本: $VERSION"
    
    - name: Build source distribution
      run: |
        python -m build --sdist
    
    - name: Build Windows executable
      if: matrix.os == 'windows-latest'
      run: |
        pyinstaller --onefile --windowed main.py --name iec61850-checker-${{ steps.version.outputs.version }}-windows
    
    - name: Build Linux AppImage
      if: matrix.os == 'ubuntu-latest'
      run: |
        # 构建Linux AppImage的脚本
        ./scripts/build_appimage.sh ${{ steps.version.outputs.version }}
    
    - name: Generate release notes
      id: release_notes
      run: |
        python scripts/generate_release_notes.py ${{ steps.version.outputs.version }} > release_notes.md
    
    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ steps.version.outputs.version }}
        release_name: Release ${{ steps.version.outputs.version }}
        body_path: release_notes.md
        draft: false
        prerelease: ${{ contains(steps.version.outputs.version, '-') }}
    
    - name: Upload release assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./dist/
        asset_name: release-assets
        asset_content_type: application/zip

  # 依赖更新作业
  dependency-update:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install update tools
      run: |
        python -m pip install --upgrade pip
        pip install pip-tools safety
    
    - name: Check for dependency updates
      run: |
        python version_tools.py deps check
        python version_tools.py deps update --security-only
    
    - name: Create pull request for security updates
      if: success()
      uses: peter-evans/create-pull-request@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore: 安全依赖更新"
        title: "🔒 自动安全依赖更新"
        body: |
          ## 自动依赖安全更新
          
          此PR包含以下安全更新:
          - 修复已知安全漏洞
          - 更新到最新安全版本
          
          请审查更改并测试后合并。
        branch: auto-security-updates
        delete-branch: true

  # 版本兼容性检查作业
  compatibility-check:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyyaml semver
    
    - name: Check API compatibility
      run: |
        python scripts/check_api_compatibility.py
    
    - name: Check data format compatibility
      run: |
        python scripts/check_data_compatibility.py
    
    - name: Generate compatibility report
      run: |
        python scripts/generate_compatibility_report.py > compatibility_report.md
    
    - name: Comment compatibility report
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('compatibility_report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 兼容性检查报告\n\n${report}`
          });
