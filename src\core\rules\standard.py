"""
IEC61850标准规则
实现IEC61850标准相关的验证规则
"""

import re
from typing import List, Set, Dict, Any
from ..models import (
    SubStation, VoltageLevel, Bay, ConductingEquipment,
    IED, AccessPoint, LDevice, LogicalNode
)
from ..parsers.scd_parser import SCLDocument
from .base import (
    BaseRule, RuleContext, RuleResult, RuleCategory, RuleSeverity,
    rule, applicable_to
)


class StandardRules:
    """IEC61850标准规则集合"""
    pass


@rule(
    rule_id="STD001",
    name="SCL版本检查",
    description="检查SCL文档版本是否符合标准",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.WARNING
)
@applicable_to(SCLDocument)
def check_scl_version(context: RuleContext) -> RuleResult:
        """检查SCL版本"""
        result = RuleResult(rule_id="STD001", success=True)
        scl_doc = context.data
        
        # 支持的版本列表
        supported_versions = ["2003", "2007", "2007B", "2007B4"]
        
        if not scl_doc.version:
            result.add_error("SCL文档缺少版本信息", context.get_path_string())
        elif scl_doc.version not in supported_versions:
            result.add_warning(
                f"SCL版本 '{scl_doc.version}' 可能不被完全支持",
                context.get_path_string(),
                suggestion=f"建议使用支持的版本: {', '.join(supported_versions)}"
            )
        else:
            result.add_info(f"SCL版本 '{scl_doc.version}' 符合标准", context.get_path_string())
        
        return result


@rule(
    rule_id="STD002",
    name="Header信息完整性",
    description="检查SCL文档Header信息的完整性",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.ERROR
)
@applicable_to(SCLDocument)
def check_header_completeness(context: RuleContext) -> RuleResult:
        """检查Header完整性"""
        result = RuleResult(rule_id="STD002", success=True)
        scl_doc = context.data
        
        if not scl_doc.header:
            result.add_error("SCL文档缺少Header元素", context.get_path_string())
            return result
        
        header = scl_doc.header
        
        # 检查必需字段
        if not header.id:
            result.add_error("Header缺少id属性", f"{context.get_path_string()}.header")
        
        # 检查推荐字段
        if not header.version:
            result.add_warning("Header缺少version属性", f"{context.get_path_string()}.header")
        
        if not header.tool_id:
            result.add_warning("Header缺少toolID属性", f"{context.get_path_string()}.header")
        
        return result


@rule(
    rule_id="STD003",
    name="IED命名规范",
    description="检查IED名称是否符合IEC61850命名规范",
    category=RuleCategory.STANDARD,
    severity=RuleSeverity.ERROR
)
@applicable_to(IED)
def check_ied_naming(context: RuleContext) -> RuleResult:
        """检查IED命名规范"""
        result = RuleResult(rule_id="STD003", success=True)
        ied = context.data
        
        # IED名称规范：1-64个字符，字母数字和下划线
        name_pattern = r'^[A-Za-z][A-Za-z0-9_]{0,63}$'
        
        if not ied.name:
            result.add_error("IED名称不能为空", context.get_path_string())
        elif not re.match(name_pattern, ied.name):
            result.add_error(
                f"IED名称 '{ied.name}' 不符合命名规范",
                context.get_path_string(),
                details="IED名称必须以字母开头，只能包含字母、数字和下划线，长度不超过64个字符",
                suggestion="修改IED名称以符合命名规范"
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD004",
        name="逻辑设备命名规范",
        description="检查逻辑设备名称是否符合IEC61850命名规范",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(LDevice)
    def check_ldevice_naming(context: RuleContext) -> RuleResult:
        """检查逻辑设备命名规范"""
        result = RuleResult(rule_id="STD004", success=True)
        ldevice = context.data
        
        # LDevice名称规范
        name_pattern = r'^[A-Za-z][A-Za-z0-9_]{0,63}$'
        inst_pattern = r'^[A-Za-z0-9_]{1,64}$'
        
        if not ldevice.name:
            result.add_error("逻辑设备名称不能为空", context.get_path_string())
        elif not re.match(name_pattern, ldevice.name):
            result.add_error(
                f"逻辑设备名称 '{ldevice.name}' 不符合命名规范",
                context.get_path_string()
            )
        
        if not ldevice.inst:
            result.add_error("逻辑设备实例名不能为空", context.get_path_string())
        elif not re.match(inst_pattern, ldevice.inst):
            result.add_error(
                f"逻辑设备实例名 '{ldevice.inst}' 不符合命名规范",
                context.get_path_string()
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD005",
        name="变电站结构完整性",
        description="检查变电站结构的完整性",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(SubStation)
    def check_substation_structure(context: RuleContext) -> RuleResult:
        """检查变电站结构完整性"""
        result = RuleResult(rule_id="STD005", success=True)
        substation = context.data
        
        if not substation.voltage_levels:
            result.add_warning(
                "变电站没有电压等级定义",
                context.get_path_string(),
                suggestion="添加至少一个电压等级"
            )
        else:
            # 检查每个电压等级
            for i, vl in enumerate(substation.voltage_levels):
                vl_path = f"{context.get_path_string()}.voltage_levels[{i}]"
                
                if not vl.voltage:
                    result.add_warning(f"电压等级 '{vl.name}' 缺少电压定义", vl_path)
                
                if not vl.bays:
                    result.add_info(f"电压等级 '{vl.name}' 没有间隔定义", vl_path)
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD006",
        name="设备类型有效性",
        description="检查一次设备类型是否为标准类型",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(ConductingEquipment)
    def check_equipment_type(context: RuleContext) -> RuleResult:
        """检查设备类型有效性"""
        result = RuleResult(rule_id="STD006", success=True)
        equipment = context.data
        
        # IEC61850标准设备类型
        standard_types = {
            'CBR': '断路器',
            'DIS': '隔离开关',
            'CTR': '电流互感器',
            'VTR': '电压互感器',
            'PTR': '电力变压器',
            'CAP': '电容器',
            'REA': '电抗器',
            'GEN': '发电机',
            'MOT': '电动机',
            'BAT': '电池',
            'BSH': '母线',
            'LIN': '线路',
            'RES': '电阻器',
            'SAR': '避雷器',
            'TCF': '可控串补',
            'TCR': '可控电抗器',
            'IFL': '滤波器'
        }
        
        if not equipment.type:
            result.add_error("设备类型不能为空", context.get_path_string())
        elif equipment.type not in standard_types:
            result.add_warning(
                f"设备类型 '{equipment.type}' 不是标准类型",
                context.get_path_string(),
                details=f"标准设备类型包括: {', '.join(standard_types.keys())}",
                suggestion="使用标准设备类型或确认自定义类型的必要性"
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD007",
        name="IED制造商信息",
        description="检查IED制造商信息的完整性",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.INFO
    )
    @applicable_to(IED)
    def check_ied_manufacturer(context: RuleContext) -> RuleResult:
        """检查IED制造商信息"""
        result = RuleResult(rule_id="STD007", success=True)
        ied = context.data
        
        if not ied.manufacturer:
            result.add_warning(
                f"IED '{ied.name}' 缺少制造商信息",
                context.get_path_string(),
                suggestion="添加制造商信息以便设备识别"
            )
        
        if not ied.type:
            result.add_info(
                f"IED '{ied.name}' 缺少类型信息",
                context.get_path_string()
            )
        
        if not ied.config_version:
            result.add_info(
                f"IED '{ied.name}' 缺少配置版本信息",
                context.get_path_string()
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD008",
        name="访问点配置完整性",
        description="检查IED访问点配置的完整性",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(IED)
    def check_access_point_config(context: RuleContext) -> RuleResult:
        """检查访问点配置完整性"""
        result = RuleResult(rule_id="STD008", success=True)
        ied = context.data
        
        if not ied.access_points:
            result.add_error(
                f"IED '{ied.name}' 没有访问点定义",
                context.get_path_string(),
                suggestion="至少定义一个访问点"
            )
            return result
        
        # 检查访问点名称唯一性
        ap_names = [ap.name for ap in ied.access_points]
        if len(ap_names) != len(set(ap_names)):
            result.add_error(
                f"IED '{ied.name}' 存在重复的访问点名称",
                context.get_path_string()
            )
        
        # 检查每个访问点
        for i, ap in enumerate(ied.access_points):
            ap_path = f"{context.get_path_string()}.access_points[{i}]"
            
            if not ap.name:
                result.add_error("访问点名称不能为空", ap_path)
            
            # 检查是否有Server或Services
            if not ap.server and not ap.services:
                result.add_warning(
                    f"访问点 '{ap.name}' 既没有Server也没有Services定义",
                    ap_path,
                    suggestion="定义Server或Services以提供功能"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD009",
        name="名称唯一性检查",
        description="检查同级元素名称的唯一性",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(SCLDocument)
    def check_name_uniqueness(context: RuleContext) -> RuleResult:
        """检查名称唯一性"""
        result = RuleResult(rule_id="STD009", success=True)
        scl_doc = context.data
        
        # 检查IED名称唯一性
        if scl_doc.ieds:
            ied_names = [ied.name for ied in scl_doc.ieds if ied.name]
            duplicates = [name for name in set(ied_names) if ied_names.count(name) > 1]
            
            for duplicate in duplicates:
                result.add_error(
                    f"IED名称 '{duplicate}' 重复",
                    context.get_path_string(),
                    suggestion="确保所有IED名称唯一"
                )
        
        # 检查变电站内部名称唯一性
        if scl_doc.substation:
            substation = scl_doc.substation
            
            # 检查电压等级名称唯一性
            vl_names = [vl.name for vl in substation.voltage_levels if vl.name]
            duplicates = [name for name in set(vl_names) if vl_names.count(name) > 1]
            
            for duplicate in duplicates:
                result.add_error(
                    f"电压等级名称 '{duplicate}' 重复",
                    f"{context.get_path_string()}.substation"
                )
            
            # 检查每个电压等级内的间隔名称唯一性
            for i, vl in enumerate(substation.voltage_levels):
                bay_names = [bay.name for bay in vl.bays if bay.name]
                duplicates = [name for name in set(bay_names) if bay_names.count(name) > 1]
                
                for duplicate in duplicates:
                    result.add_error(
                        f"间隔名称 '{duplicate}' 在电压等级 '{vl.name}' 中重复",
                        f"{context.get_path_string()}.substation.voltage_levels[{i}]"
                    )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="STD010",
        name="描述信息建议",
        description="建议为重要元素添加描述信息",
        category=RuleCategory.STANDARD,
        severity=RuleSeverity.INFO
    )
    @applicable_to(SCLDocument)
    def check_description_completeness(context: RuleContext) -> RuleResult:
        """检查描述信息完整性"""
        result = RuleResult(rule_id="STD010", success=True)
        scl_doc = context.data
        
        # 检查变电站描述
        if scl_doc.substation and not scl_doc.substation.desc:
            result.add_info(
                f"变电站 '{scl_doc.substation.name}' 缺少描述信息",
                f"{context.get_path_string()}.substation",
                suggestion="添加描述信息以提高可读性"
            )
        
        # 检查IED描述
        for i, ied in enumerate(scl_doc.ieds):
            if not ied.desc:
                result.add_info(
                    f"IED '{ied.name}' 缺少描述信息",
                    f"{context.get_path_string()}.ieds[{i}]",
                    suggestion="添加描述信息说明IED功能"
                )
        
        return result


# 自动注册所有规则
def register_standard_rules():
    """注册所有标准规则"""
    from .registry import rule_registry
    import inspect

    # 获取当前模块中的所有函数
    current_module = inspect.getmodule(register_standard_rules)

    for name, obj in inspect.getmembers(current_module, predicate=inspect.isfunction):
        if hasattr(obj, 'rule_id'):  # 检查是否是规则函数
            try:
                rule_registry.register(obj)
            except Exception as e:
                print(f"注册规则 {name} 失败: {e}")


# 在模块加载时自动注册规则
register_standard_rules()
