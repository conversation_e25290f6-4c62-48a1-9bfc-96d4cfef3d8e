"""
差异分析器
深度分析文件对比结果，提供智能化的差异分类和影响评估
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import defaultdict, Counter
import re

from .comparison_models import (
    ComparisonResult, DiffItem, DiffType, DiffCategory, DiffSeverity
)


logger = logging.getLogger(__name__)


class DiffAnalyzer:
    """差异分析器"""
    
    def __init__(self):
        """初始化差异分析器"""
        self.impact_rules = self._load_impact_rules()
        self.severity_rules = self._load_severity_rules()
        self.dependency_rules = self._load_dependency_rules()
        
        logger.info("差异分析器初始化完成")
    
    def analyze_comparison_result(self, result: ComparisonResult) -> Dict[str, Any]:
        """
        分析对比结果
        
        Args:
            result: 对比结果
            
        Returns:
            Dict[str, Any]: 分析报告
        """
        try:
            logger.info(f"开始分析对比结果: {result.comparison_id}")
            
            analysis = {
                'summary': self._generate_summary(result),
                'impact_analysis': self._analyze_impacts(result),
                'risk_assessment': self._assess_risks(result),
                'dependency_analysis': self._analyze_dependencies(result),
                'migration_plan': self._generate_migration_plan(result),
                'recommendations': self._generate_recommendations(result),
                'statistics': self._generate_detailed_statistics(result)
            }
            
            logger.info("对比结果分析完成")
            return analysis
            
        except Exception as e:
            logger.error(f"对比结果分析失败: {e}")
            return {}
    
    def categorize_diffs(self, diffs: List[DiffItem]) -> Dict[str, List[DiffItem]]:
        """
        对差异进行分类
        
        Args:
            diffs: 差异列表
            
        Returns:
            Dict[str, List[DiffItem]]: 分类后的差异
        """
        categories = {
            'structural_changes': [],      # 结构性变更
            'configuration_changes': [],   # 配置变更
            'data_changes': [],           # 数据变更
            'communication_changes': [],   # 通信变更
            'breaking_changes': [],       # 破坏性变更
            'cosmetic_changes': []        # 外观变更
        }
        
        for diff in diffs:
            # 结构性变更
            if diff.is_structural_change():
                categories['structural_changes'].append(diff)
            
            # 配置变更
            if diff.is_configuration_change():
                categories['configuration_changes'].append(diff)
            
            # 数据变更
            if diff.category in [DiffCategory.DATA_OBJECT, DiffCategory.DATA_ATTRIBUTE]:
                categories['data_changes'].append(diff)
            
            # 通信变更
            if diff.category in [DiffCategory.COMMUNICATION, DiffCategory.GOOSE, DiffCategory.SMV]:
                categories['communication_changes'].append(diff)
            
            # 破坏性变更
            if self._is_breaking_change(diff):
                categories['breaking_changes'].append(diff)
            
            # 外观变更
            if diff.severity == DiffSeverity.INFO and diff.category == DiffCategory.HEADER:
                categories['cosmetic_changes'].append(diff)
        
        return categories
    
    def find_related_diffs(self, target_diff: DiffItem, all_diffs: List[DiffItem]) -> List[DiffItem]:
        """
        查找相关差异
        
        Args:
            target_diff: 目标差异
            all_diffs: 所有差异列表
            
        Returns:
            List[DiffItem]: 相关差异列表
        """
        related_diffs = []
        
        for diff in all_diffs:
            if diff.diff_id == target_diff.diff_id:
                continue
            
            # 检查路径相关性
            if self._are_paths_related(target_diff.path, diff.path):
                related_diffs.append(diff)
            
            # 检查功能相关性
            if self._are_functionally_related(target_diff, diff):
                related_diffs.append(diff)
        
        return related_diffs
    
    def calculate_change_complexity(self, diffs: List[DiffItem]) -> Dict[str, Any]:
        """
        计算变更复杂度
        
        Args:
            diffs: 差异列表
            
        Returns:
            Dict[str, Any]: 复杂度分析
        """
        complexity_score = 0
        factors = {
            'structural_complexity': 0,
            'dependency_complexity': 0,
            'scope_complexity': 0,
            'risk_complexity': 0
        }
        
        # 结构复杂度
        structural_changes = [d for d in diffs if d.is_structural_change()]
        factors['structural_complexity'] = len(structural_changes) * 2
        
        # 依赖复杂度
        dependency_count = 0
        for diff in diffs:
            related = self.find_related_diffs(diff, diffs)
            dependency_count += len(related)
        factors['dependency_complexity'] = dependency_count * 0.5
        
        # 范围复杂度
        affected_devices = set()
        for diff in diffs:
            device_match = re.search(r'IED/([^/]+)', diff.path)
            if device_match:
                affected_devices.add(device_match.group(1))
        factors['scope_complexity'] = len(affected_devices) * 1.5
        
        # 风险复杂度
        critical_diffs = [d for d in diffs if d.severity == DiffSeverity.CRITICAL]
        factors['risk_complexity'] = len(critical_diffs) * 3
        
        complexity_score = sum(factors.values())
        
        # 确定复杂度等级
        if complexity_score < 10:
            complexity_level = "低"
        elif complexity_score < 30:
            complexity_level = "中"
        elif complexity_score < 60:
            complexity_level = "高"
        else:
            complexity_level = "极高"
        
        return {
            'complexity_score': complexity_score,
            'complexity_level': complexity_level,
            'factors': factors,
            'affected_devices': list(affected_devices),
            'recommendations': self._get_complexity_recommendations(complexity_level)
        }
    
    def _generate_summary(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成摘要"""
        stats = result.get_statistics()
        categories = self.categorize_diffs(result.diff_items)
        
        return {
            'total_differences': len(result.diff_items),
            'breaking_changes': len(categories['breaking_changes']),
            'structural_changes': len(categories['structural_changes']),
            'configuration_changes': len(categories['configuration_changes']),
            'compatibility_score': result.get_compatibility_score(),
            'risk_level': self._assess_overall_risk_level(result),
            'change_categories': {k: len(v) for k, v in categories.items()}
        }
    
    def _analyze_impacts(self, result: ComparisonResult) -> Dict[str, Any]:
        """分析影响"""
        impacts = {
            'functional_impact': [],
            'operational_impact': [],
            'security_impact': [],
            'performance_impact': [],
            'maintenance_impact': []
        }
        
        for diff in result.diff_items:
            # 功能影响
            if diff.category in [DiffCategory.LOGICAL_NODE, DiffCategory.DATA_OBJECT]:
                if diff.diff_type in [DiffType.REMOVED, DiffType.MODIFIED]:
                    impacts['functional_impact'].append({
                        'diff_id': diff.diff_id,
                        'description': f"可能影响 {diff.path} 的功能",
                        'severity': diff.severity.value
                    })
            
            # 运行影响
            if diff.category in [DiffCategory.COMMUNICATION, DiffCategory.GOOSE, DiffCategory.SMV]:
                impacts['operational_impact'].append({
                    'diff_id': diff.diff_id,
                    'description': f"可能影响通信运行: {diff.path}",
                    'severity': diff.severity.value
                })
            
            # 安全影响
            if 'security' in diff.path.lower() or 'auth' in diff.path.lower():
                impacts['security_impact'].append({
                    'diff_id': diff.diff_id,
                    'description': f"可能影响安全性: {diff.path}",
                    'severity': diff.severity.value
                })
            
            # 性能影响
            if diff.category == DiffCategory.COMMUNICATION:
                impacts['performance_impact'].append({
                    'diff_id': diff.diff_id,
                    'description': f"可能影响通信性能: {diff.path}",
                    'severity': diff.severity.value
                })
        
        return impacts
    
    def _assess_risks(self, result: ComparisonResult) -> Dict[str, Any]:
        """评估风险"""
        risks = {
            'high_risk_changes': [],
            'medium_risk_changes': [],
            'low_risk_changes': [],
            'overall_risk_score': 0
        }
        
        risk_score = 0
        
        for diff in result.diff_items:
            risk_level = self._calculate_diff_risk(diff)
            
            risk_item = {
                'diff_id': diff.diff_id,
                'path': diff.path,
                'description': diff.description,
                'risk_factors': self._identify_risk_factors(diff)
            }
            
            if risk_level >= 7:
                risks['high_risk_changes'].append(risk_item)
                risk_score += 3
            elif risk_level >= 4:
                risks['medium_risk_changes'].append(risk_item)
                risk_score += 2
            else:
                risks['low_risk_changes'].append(risk_item)
                risk_score += 1
        
        risks['overall_risk_score'] = risk_score
        risks['risk_level'] = self._get_risk_level_description(risk_score)
        
        return risks
    
    def _analyze_dependencies(self, result: ComparisonResult) -> Dict[str, Any]:
        """分析依赖关系"""
        dependencies = {
            'dependency_graph': {},
            'circular_dependencies': [],
            'orphaned_changes': [],
            'dependency_chains': []
        }
        
        # 构建依赖图
        for diff in result.diff_items:
            related_diffs = self.find_related_diffs(diff, result.diff_items)
            dependencies['dependency_graph'][diff.diff_id] = [
                related.diff_id for related in related_diffs
            ]
        
        # 查找循环依赖
        dependencies['circular_dependencies'] = self._find_circular_dependencies(
            dependencies['dependency_graph']
        )
        
        # 查找孤立变更
        dependencies['orphaned_changes'] = [
            diff_id for diff_id, deps in dependencies['dependency_graph'].items()
            if not deps
        ]
        
        # 生成依赖链
        dependencies['dependency_chains'] = self._generate_dependency_chains(
            dependencies['dependency_graph']
        )
        
        return dependencies
    
    def _generate_migration_plan(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成迁移计划"""
        plan = {
            'phases': [],
            'prerequisites': [],
            'rollback_plan': [],
            'validation_steps': []
        }
        
        # 按优先级和依赖关系对差异进行分组
        categorized_diffs = self.categorize_diffs(result.diff_items)
        
        # 第一阶段：准备工作
        if categorized_diffs['breaking_changes']:
            plan['phases'].append({
                'phase': 1,
                'name': "准备阶段",
                'description': "处理破坏性变更的准备工作",
                'changes': [diff.diff_id for diff in categorized_diffs['breaking_changes']],
                'estimated_time': "2-4小时",
                'risk_level': "高"
            })
        
        # 第二阶段：结构变更
        if categorized_diffs['structural_changes']:
            plan['phases'].append({
                'phase': 2,
                'name': "结构变更阶段",
                'description': "执行结构性变更",
                'changes': [diff.diff_id for diff in categorized_diffs['structural_changes']],
                'estimated_time': "1-2小时",
                'risk_level': "中"
            })
        
        # 第三阶段：配置变更
        if categorized_diffs['configuration_changes']:
            plan['phases'].append({
                'phase': 3,
                'name': "配置变更阶段",
                'description': "更新配置参数",
                'changes': [diff.diff_id for diff in categorized_diffs['configuration_changes']],
                'estimated_time': "30分钟-1小时",
                'risk_level': "低"
            })
        
        # 添加前置条件
        plan['prerequisites'] = [
            "备份当前配置文件",
            "确认系统处于维护模式",
            "准备回滚方案",
            "通知相关人员"
        ]
        
        # 添加验证步骤
        plan['validation_steps'] = [
            "验证文件语法正确性",
            "检查设备通信状态",
            "测试关键功能",
            "确认性能指标"
        ]
        
        return plan
    
    def _generate_recommendations(self, result: ComparisonResult) -> List[Dict[str, Any]]:
        """生成推荐建议"""
        recommendations = []
        
        # 基于兼容性分数的建议
        compatibility_score = result.get_compatibility_score()
        if compatibility_score < 0.7:
            recommendations.append({
                'type': 'compatibility',
                'priority': 'high',
                'title': '兼容性风险警告',
                'description': f'兼容性分数较低 ({compatibility_score:.2f})，建议谨慎处理变更',
                'actions': [
                    '详细测试所有变更',
                    '准备完整的回滚计划',
                    '分阶段实施变更'
                ]
            })
        
        # 基于破坏性变更的建议
        breaking_changes = [d for d in result.diff_items if self._is_breaking_change(d)]
        if breaking_changes:
            recommendations.append({
                'type': 'breaking_changes',
                'priority': 'critical',
                'title': '破坏性变更处理',
                'description': f'发现 {len(breaking_changes)} 个破坏性变更',
                'actions': [
                    '制定详细的迁移计划',
                    '进行充分的测试',
                    '考虑分步骤实施'
                ]
            })
        
        # 基于复杂度的建议
        complexity = self.calculate_change_complexity(result.diff_items)
        if complexity['complexity_level'] in ['高', '极高']:
            recommendations.append({
                'type': 'complexity',
                'priority': 'high',
                'title': '变更复杂度管理',
                'description': f'变更复杂度为 {complexity["complexity_level"]}',
                'actions': complexity['recommendations']
            })
        
        return recommendations
    
    def _generate_detailed_statistics(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成详细统计"""
        stats = result.get_statistics()
        
        # 添加更多统计信息
        path_analysis = self._analyze_paths(result.diff_items)
        temporal_analysis = self._analyze_temporal_patterns(result.diff_items)
        
        stats.update({
            'path_analysis': path_analysis,
            'temporal_analysis': temporal_analysis,
            'complexity_analysis': self.calculate_change_complexity(result.diff_items)
        })
        
        return stats
    
    def _is_breaking_change(self, diff: DiffItem) -> bool:
        """判断是否为破坏性变更"""
        # 删除关键组件
        if diff.diff_type == DiffType.REMOVED:
            if diff.category in [DiffCategory.IED, DiffCategory.LOGICAL_DEVICE]:
                return True
        
        # 修改关键属性
        if diff.diff_type == DiffType.MODIFIED:
            if 'name' in diff.path or 'type' in diff.path:
                return True
        
        # 关键通信配置变更
        if diff.category in [DiffCategory.COMMUNICATION, DiffCategory.GOOSE, DiffCategory.SMV]:
            if diff.severity == DiffSeverity.CRITICAL:
                return True
        
        return False
    
    def _are_paths_related(self, path1: str, path2: str) -> bool:
        """判断路径是否相关"""
        # 检查是否在同一设备下
        device1_match = re.search(r'IED/([^/]+)', path1)
        device2_match = re.search(r'IED/([^/]+)', path2)
        
        if device1_match and device2_match:
            return device1_match.group(1) == device2_match.group(1)
        
        # 检查是否在同一逻辑设备下
        ld1_match = re.search(r'LD/([^/]+)', path1)
        ld2_match = re.search(r'LD/([^/]+)', path2)
        
        if ld1_match and ld2_match:
            return ld1_match.group(1) == ld2_match.group(1)
        
        return False
    
    def _are_functionally_related(self, diff1: DiffItem, diff2: DiffItem) -> bool:
        """判断功能是否相关"""
        # 同类型的变更
        if diff1.category == diff2.category:
            return True
        
        # 通信相关的变更
        comm_categories = [DiffCategory.COMMUNICATION, DiffCategory.GOOSE, DiffCategory.SMV]
        if diff1.category in comm_categories and diff2.category in comm_categories:
            return True
        
        return False
    
    def _calculate_diff_risk(self, diff: DiffItem) -> int:
        """计算差异风险等级 (1-10)"""
        risk_score = 0
        
        # 基于严重程度
        severity_scores = {
            DiffSeverity.CRITICAL: 4,
            DiffSeverity.MAJOR: 3,
            DiffSeverity.MINOR: 2,
            DiffSeverity.INFO: 1
        }
        risk_score += severity_scores.get(diff.severity, 1)
        
        # 基于变更类型
        type_scores = {
            DiffType.REMOVED: 3,
            DiffType.MODIFIED: 2,
            DiffType.ADDED: 1,
            DiffType.MOVED: 2,
            DiffType.RENAMED: 1
        }
        risk_score += type_scores.get(diff.diff_type, 1)
        
        # 基于分类
        category_scores = {
            DiffCategory.IED: 3,
            DiffCategory.COMMUNICATION: 3,
            DiffCategory.LOGICAL_DEVICE: 2,
            DiffCategory.GOOSE: 2,
            DiffCategory.SMV: 2,
            DiffCategory.LOGICAL_NODE: 1,
            DiffCategory.DATA_OBJECT: 1
        }
        risk_score += category_scores.get(diff.category, 1)
        
        return min(risk_score, 10)
    
    def _identify_risk_factors(self, diff: DiffItem) -> List[str]:
        """识别风险因素"""
        factors = []
        
        if diff.severity == DiffSeverity.CRITICAL:
            factors.append("关键严重程度")
        
        if diff.diff_type == DiffType.REMOVED:
            factors.append("删除操作")
        
        if diff.is_structural_change():
            factors.append("结构性变更")
        
        if diff.category in [DiffCategory.COMMUNICATION, DiffCategory.GOOSE, DiffCategory.SMV]:
            factors.append("通信配置变更")
        
        return factors
    
    def _assess_overall_risk_level(self, result: ComparisonResult) -> str:
        """评估整体风险等级"""
        if result.has_breaking_changes():
            return "高风险"
        
        critical_count = len(result.get_critical_diffs())
        if critical_count > 5:
            return "高风险"
        elif critical_count > 0:
            return "中风险"
        else:
            return "低风险"
    
    def _get_risk_level_description(self, risk_score: int) -> str:
        """获取风险等级描述"""
        if risk_score > 50:
            return "极高风险"
        elif risk_score > 30:
            return "高风险"
        elif risk_score > 15:
            return "中风险"
        else:
            return "低风险"
    
    def _find_circular_dependencies(self, dependency_graph: Dict[str, List[str]]) -> List[List[str]]:
        """查找循环依赖"""
        # 简化的循环依赖检测
        cycles = []
        visited = set()
        
        def dfs(node, path):
            if node in path:
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:] + [node])
                return
            
            if node in visited:
                return
            
            visited.add(node)
            path.append(node)
            
            for neighbor in dependency_graph.get(node, []):
                dfs(neighbor, path.copy())
        
        for node in dependency_graph:
            if node not in visited:
                dfs(node, [])
        
        return cycles
    
    def _generate_dependency_chains(self, dependency_graph: Dict[str, List[str]]) -> List[List[str]]:
        """生成依赖链"""
        chains = []
        
        # 找到所有的依赖链
        def build_chain(node, current_chain):
            current_chain.append(node)
            dependencies = dependency_graph.get(node, [])
            
            if not dependencies:
                chains.append(current_chain.copy())
            else:
                for dep in dependencies:
                    if dep not in current_chain:  # 避免循环
                        build_chain(dep, current_chain.copy())
        
        # 从没有依赖的节点开始构建链
        root_nodes = [
            node for node in dependency_graph
            if not any(node in deps for deps in dependency_graph.values())
        ]
        
        for root in root_nodes:
            build_chain(root, [])
        
        return chains
    
    def _get_complexity_recommendations(self, complexity_level: str) -> List[str]:
        """获取复杂度建议"""
        recommendations = {
            "低": [
                "可以直接实施变更",
                "进行基本测试验证"
            ],
            "中": [
                "建议分阶段实施",
                "加强测试覆盖",
                "准备回滚方案"
            ],
            "高": [
                "必须分阶段实施",
                "进行全面测试",
                "制定详细的回滚计划",
                "考虑影响评估"
            ],
            "极高": [
                "强烈建议重新评估变更必要性",
                "如必须实施，需要详细的项目计划",
                "进行风险评估和缓解措施",
                "考虑专家咨询"
            ]
        }
        
        return recommendations.get(complexity_level, [])
    
    def _analyze_paths(self, diffs: List[DiffItem]) -> Dict[str, Any]:
        """分析路径模式"""
        path_stats = {
            'most_affected_devices': Counter(),
            'most_affected_categories': Counter(),
            'path_depth_distribution': Counter()
        }
        
        for diff in diffs:
            # 统计受影响最多的设备
            device_match = re.search(r'IED/([^/]+)', diff.path)
            if device_match:
                path_stats['most_affected_devices'][device_match.group(1)] += 1
            
            # 统计分类
            path_stats['most_affected_categories'][diff.category.value] += 1
            
            # 统计路径深度
            depth = len(diff.path.split('/'))
            path_stats['path_depth_distribution'][depth] += 1
        
        return {
            'most_affected_devices': dict(path_stats['most_affected_devices'].most_common(5)),
            'most_affected_categories': dict(path_stats['most_affected_categories']),
            'path_depth_distribution': dict(path_stats['path_depth_distribution'])
        }
    
    def _analyze_temporal_patterns(self, diffs: List[DiffItem]) -> Dict[str, Any]:
        """分析时间模式"""
        # 这里可以分析差异的时间模式，如果有时间戳信息
        return {
            'total_changes': len(diffs),
            'change_rate': len(diffs) / max(1, len(set(d.category for d in diffs)))
        }
    
    def _load_impact_rules(self) -> Dict[str, Any]:
        """加载影响规则"""
        return {
            'critical_paths': [
                r'IED/.*/LD/.*/LN/.*PTOC.*',  # 保护相关
                r'IED/.*/LD/.*/LN/.*XCBR.*',  # 断路器相关
                r'Communication/.*'           # 通信相关
            ],
            'functional_dependencies': {
                'GOOSE': ['LOGICAL_NODE', 'DATA_OBJECT'],
                'SMV': ['LOGICAL_NODE', 'DATA_OBJECT'],
                'REPORT': ['LOGICAL_NODE', 'DATA_OBJECT']
            }
        }
    
    def _load_severity_rules(self) -> Dict[str, Any]:
        """加载严重程度规则"""
        return {
            'critical_operations': ['REMOVED', 'RENAMED'],
            'critical_categories': ['IED', 'COMMUNICATION'],
            'severity_escalation': {
                'multiple_devices': 1,  # 影响多个设备时提升严重程度
                'communication_impact': 2  # 影响通信时提升严重程度
            }
        }
    
    def _load_dependency_rules(self) -> Dict[str, Any]:
        """加载依赖规则"""
        return {
            'parent_child_relations': {
                'IED': ['LOGICAL_DEVICE'],
                'LOGICAL_DEVICE': ['LOGICAL_NODE'],
                'LOGICAL_NODE': ['DATA_OBJECT'],
                'DATA_OBJECT': ['DATA_ATTRIBUTE']
            },
            'functional_relations': {
                'GOOSE': ['DATASET', 'INPUTS'],
                'SMV': ['DATASET', 'INPUTS'],
                'REPORT': ['DATASET', 'RCB']
            }
        }
