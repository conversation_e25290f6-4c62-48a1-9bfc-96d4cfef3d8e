"""
二次回路逻辑分析器
专门用于分析二次系统各个回路的逻辑关系
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum

from ..graph.power_system_knowledge_graph import PowerSystemKnowledgeGraph, LogicalCircuit
from ..standards.comprehensive_circuit_knowledge import ComprehensiveCircuitKnowledge
from ..standards.iec61850_knowledge import IEC61850Knowledge

logger = logging.getLogger(__name__)


class CircuitType(Enum):
    """回路类型枚举"""
    CONTROL = "control"
    PROTECTION = "protection"
    MEASUREMENT = "measurement"
    SIGNAL = "signal"
    REGULATION = "regulation"
    SYNCHRONIZATION = "synchronization"
    DC_POWER = "dc_power"
    AUTOMATIC = "automatic"
    COMMUNICATION = "communication"


@dataclass
class CircuitLogicAnalysis:
    """回路逻辑分析结果"""
    circuit_id: str
    circuit_type: str
    name: str
    entities: List[str]
    logical_flow: List[str]  # 逻辑流向
    expected_behavior: str
    actual_behavior: str
    consistency: bool
    issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    confidence: float = 1.0


@dataclass
class CircuitRelationshipAnalysis:
    """回路关系分析结果"""
    circuit1_id: str
    circuit2_id: str
    relationship_type: str
    shared_entities: List[str]
    interaction_points: List[str]
    potential_conflicts: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    confidence: float = 1.0


class SecondaryCircuitAnalyzer:
    """二次回路逻辑分析器"""
    
    def __init__(self):
        """初始化二次回路逻辑分析器"""
        # 初始化知识库
        self.knowledge_graph = PowerSystemKnowledgeGraph()
        self.circuit_knowledge = ComprehensiveCircuitKnowledge()
        self.iec61850_knowledge = IEC61850Knowledge()
        
        # 分析配置
        self.config = {
            'enable_deep_analysis': True,
            'enable_relationship_analysis': True,
            'enable_standard_compliance': True,
            'confidence_threshold': 0.6
        }
        
        logger.info("二次回路逻辑分析器初始化完成")
    
    def analyze_circuit_logic(self, 
                            circuits: Dict[str, LogicalCircuit],
                            context: Dict[str, Any] = None) -> List[CircuitLogicAnalysis]:
        """
        分析回路逻辑
        
        Args:
            circuits: 回路字典
            context: 分析上下文
            
        Returns:
            List[CircuitLogicAnalysis]: 回路逻辑分析结果列表
        """
        analysis_results = []
        
        try:
            for circuit_id, circuit in circuits.items():
                # 执行回路逻辑分析
                logic_analysis = self._analyze_single_circuit_logic(circuit, context)
                if logic_analysis:
                    analysis_results.append(logic_analysis)
            
            logger.info(f"完成 {len(analysis_results)} 个回路的逻辑分析")
            return analysis_results
            
        except Exception as e:
            logger.error(f"回路逻辑分析失败: {e}")
            return []
    
    def _analyze_single_circuit_logic(self, 
                                    circuit: LogicalCircuit,
                                    context: Dict[str, Any] = None) -> Optional[CircuitLogicAnalysis]:
        """
        分析单个回路逻辑
        
        Args:
            circuit: 逻辑回路
            context: 分析上下文
            
        Returns:
            CircuitLogicAnalysis: 回路逻辑分析结果
        """
        try:
            # 获取回路实体信息
            entities_info = self._get_circuit_entities_info(circuit)
            
            # 分析逻辑流向
            logical_flow = self._analyze_logical_flow(circuit, entities_info)
            
            # 获取预期行为
            expected_behavior = self._get_expected_circuit_behavior(circuit)
            
            # 分析实际行为（基于知识图谱）
            actual_behavior = self._analyze_actual_circuit_behavior(circuit, entities_info)
            
            # 检查一致性
            consistency, issues = self._check_circuit_consistency(
                circuit, logical_flow, expected_behavior, actual_behavior
            )
            
            # 生成建议
            recommendations = self._generate_circuit_recommendations(
                circuit, consistency, issues
            )
            
            # 计算置信度
            confidence = self._calculate_analysis_confidence(circuit, issues)
            
            analysis = CircuitLogicAnalysis(
                circuit_id=circuit.id,
                circuit_type=circuit.type,
                name=circuit.name,
                entities=circuit.entities,
                logical_flow=logical_flow,
                expected_behavior=expected_behavior,
                actual_behavior=actual_behavior,
                consistency=consistency,
                issues=issues,
                recommendations=recommendations,
                confidence=confidence
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"单个回路逻辑分析失败: {e}")
            return None
    
    def _get_circuit_entities_info(self, circuit: LogicalCircuit) -> Dict[str, Any]:
        """获取回路实体信息"""
        entities_info = {}
        
        for entity_id in circuit.entities:
            if entity_id in self.knowledge_graph.entities:
                entity = self.knowledge_graph.entities[entity_id]
                entities_info[entity_id] = {
                    'type': entity.type.value,
                    'name': entity.name,
                    'properties': entity.properties
                }
        
        return entities_info
    
    def _analyze_logical_flow(self, 
                            circuit: LogicalCircuit,
                            entities_info: Dict[str, Any]) -> List[str]:
        """分析逻辑流向"""
        logical_flow = []
        
        try:
            # 根据回路类型确定逻辑流向
            if circuit.type == "protection_trip":
                logical_flow = self._analyze_protection_trip_flow(circuit, entities_info)
            elif circuit.type == "alarm":
                logical_flow = self._analyze_alarm_flow(circuit, entities_info)
            elif circuit.type == "interlock":
                logical_flow = self._analyze_interlock_flow(circuit, entities_info)
            elif circuit.type == "control":
                logical_flow = self._analyze_control_flow(circuit, entities_info)
            else:
                # 通用流向分析
                logical_flow = self._analyze_generic_flow(circuit, entities_info)
                
        except Exception as e:
            logger.warning(f"逻辑流向分析失败: {e}")
            logical_flow = ["无法确定逻辑流向"]
        
        return logical_flow
    
    def _analyze_protection_trip_flow(self, 
                                    circuit: LogicalCircuit,
                                    entities_info: Dict[str, Any]) -> List[str]:
        """分析保护跳闸逻辑流向"""
        flow = []
        
        # 识别保护逻辑节点
        protection_lns = [eid for eid in circuit.entities 
                         if eid in entities_info and 
                         entities_info[eid]['properties'].get('lnClass', '').startswith(('PTOC', 'PDIF', 'PDIS'))]
        
        # 识别跳闸逻辑节点
        trip_lns = [eid for eid in circuit.entities 
                   if eid in entities_info and 
                   entities_info[eid]['properties'].get('lnClass', '') == 'PTRC']
        
        # 识别断路器逻辑节点
        breaker_lns = [eid for eid in circuit.entities 
                      if eid in entities_info and 
                      entities_info[eid]['properties'].get('lnClass', '') == 'XCBR']
        
        # 构建逻辑流向
        if protection_lns:
            flow.append(f"保护动作: {', '.join([entities_info[eid]['name'] for eid in protection_lns])}")
        
        if trip_lns:
            flow.append(f"跳闸逻辑: {', '.join([entities_info[eid]['name'] for eid in trip_lns])}")
        
        if breaker_lns:
            flow.append(f"断路器跳闸: {', '.join([entities_info[eid]['name'] for eid in breaker_lns])}")
        
        return flow
    
    def _analyze_alarm_flow(self, 
                          circuit: LogicalCircuit,
                          entities_info: Dict[str, Any]) -> List[str]:
        """分析告警逻辑流向"""
        flow = []
        
        # 识别告警逻辑节点
        alarm_lns = [eid for eid in circuit.entities 
                    if eid in entities_info and 
                    entities_info[eid]['properties'].get('lnClass', '').startswith(('ALM', 'ANN'))]
        
        # 构建逻辑流向
        if alarm_lns:
            flow.append(f"告警产生: {', '.join([entities_info[eid]['name'] for eid in alarm_lns])}")
        
        # 添加GOOSE传输（如果有）
        goose_entities = [eid for eid in circuit.entities 
                         if eid in entities_info and 
                         entities_info[eid]['type'] == 'GOOSEMessage']
        
        if goose_entities:
            flow.append(f"告警传输: {', '.join([entities_info[eid]['name'] for eid in goose_entities])}")
        
        return flow
    
    def _analyze_interlock_flow(self, 
                              circuit: LogicalCircuit,
                              entities_info: Dict[str, Any]) -> List[str]:
        """分析联锁逻辑流向"""
        flow = []
        
        # 识别联锁逻辑节点
        interlock_lns = [eid for eid in circuit.entities 
                        if eid in entities_info and 
                        entities_info[eid]['properties'].get('lnClass', '') == 'CILO']
        
        # 识别控制逻辑节点
        control_lns = [eid for eid in circuit.entities 
                      if eid in entities_info and 
                      entities_info[eid]['properties'].get('lnClass', '') == 'CSWI']
        
        # 识别断路器逻辑节点
        breaker_lns = [eid for eid in circuit.entities 
                      if eid in entities_info and 
                      entities_info[eid]['properties'].get('lnClass', '') == 'XCBR']
        
        # 构建逻辑流向
        if interlock_lns:
            flow.append(f"联锁条件: {', '.join([entities_info[eid]['name'] for eid in interlock_lns])}")
        
        if control_lns:
            flow.append(f"控制操作: {', '.join([entities_info[eid]['name'] for eid in control_lns])}")
        
        if breaker_lns:
            flow.append(f"断路器操作: {', '.join([entities_info[eid]['name'] for eid in breaker_lns])}")
        
        return flow
    
    def _analyze_control_flow(self, 
                            circuit: LogicalCircuit,
                            entities_info: Dict[str, Any]) -> List[str]:
        """分析控制逻辑流向"""
        flow = []
        
        # 识别控制开关逻辑节点
        cswi_lns = [eid for eid in circuit.entities 
                   if eid in entities_info and 
                   entities_info[eid]['properties'].get('lnClass', '') == 'CSWI']
        
        # 识别断路器逻辑节点
        breaker_lns = [eid for eid in circuit.entities 
                      if eid in entities_info and 
                      entities_info[eid]['properties'].get('lnClass', '') == 'XCBR']
        
        # 构建逻辑流向
        if cswi_lns:
            flow.append(f"控制命令: {', '.join([entities_info[eid]['name'] for eid in cswi_lns])}")
        
        if breaker_lns:
            flow.append(f"断路器操作: {', '.join([entities_info[eid]['name'] for eid in breaker_lns])}")
        
        return flow
    
    def _analyze_generic_flow(self, 
                            circuit: LogicalCircuit,
                            entities_info: Dict[str, Any]) -> List[str]:
        """分析通用逻辑流向"""
        flow = []
        
        # 按实体类型分组
        entity_groups = {}
        for entity_id, entity_info in entities_info.items():
            entity_type = entity_info['type']
            if entity_type not in entity_groups:
                entity_groups[entity_type] = []
            entity_groups[entity_type].append(entity_info['name'])
        
        # 构建流向
        for entity_type, names in entity_groups.items():
            flow.append(f"{entity_type}: {', '.join(names)}")
        
        return flow
    
    def _get_expected_circuit_behavior(self, circuit: LogicalCircuit) -> str:
        """获取预期回路行为"""
        try:
            # 根据回路类型获取预期行为
            if circuit.type == "protection_trip":
                return "保护动作时应可靠跳闸断路器"
            elif circuit.type == "alarm":
                return "设备异常时应正确发出告警信号"
            elif circuit.type == "interlock":
                return "联锁条件满足时应允许操作，不满足时应闭锁操作"
            elif circuit.type == "control":
                return "控制命令应能正确操作断路器"
            else:
                return "回路应按设计要求正常工作"
                
        except Exception as e:
            logger.warning(f"获取预期行为失败: {e}")
            return "未知预期行为"
    
    def _analyze_actual_circuit_behavior(self, 
                                       circuit: LogicalCircuit,
                                       entities_info: Dict[str, Any]) -> str:
        """分析实际回路行为"""
        try:
            # 检查回路完整性
            if circuit.is_complete:
                return "回路完整，逻辑节点连接正确"
            else:
                return f"回路不完整: {', '.join(circuit.issues)}"
                
        except Exception as e:
            logger.warning(f"分析实际行为失败: {e}")
            return "无法确定实际行为"
    
    def _check_circuit_consistency(self, 
                                 circuit: LogicalCircuit,
                                 logical_flow: List[str],
                                 expected_behavior: str,
                                 actual_behavior: str) -> Tuple[bool, List[str]]:
        """检查回路一致性"""
        issues = []
        consistency = True
        
        try:
            # 检查回路完整性
            if not circuit.is_complete:
                consistency = False
                issues.extend(circuit.issues)
            
            # 检查逻辑流向是否合理
            flow_issues = self._check_logical_flow_consistency(circuit, logical_flow)
            if flow_issues:
                consistency = False
                issues.extend(flow_issues)
            
            # 检查行为一致性
            if "不完整" in actual_behavior or "无法确定" in actual_behavior:
                consistency = False
                issues.append("回路行为异常")
                
        except Exception as e:
            logger.warning(f"一致性检查失败: {e}")
            consistency = False
            issues.append(f"一致性检查失败: {str(e)}")
        
        return consistency, issues
    
    def _check_logical_flow_consistency(self, 
                                      circuit: LogicalCircuit,
                                      logical_flow: List[str]) -> List[str]:
        """检查逻辑流向一致性"""
        issues = []
        
        # 根据回路类型检查特定的流向要求
        if circuit.type == "protection_trip":
            # 保护跳闸回路应该有保护->跳闸->断路器的流向
            flow_str = " -> ".join(logical_flow)
            if "保护动作" not in flow_str or "跳闸逻辑" not in flow_str or "断路器跳闸" not in flow_str:
                issues.append("保护跳闸回路逻辑流向不完整")
        
        elif circuit.type == "control":
            # 控制回路应该有控制命令->断路器操作的流向
            flow_str = " -> ".join(logical_flow)
            if "控制命令" not in flow_str or "断路器操作" not in flow_str:
                issues.append("控制回路逻辑流向不完整")
        
        return issues
    
    def _generate_circuit_recommendations(self, 
                                        circuit: LogicalCircuit,
                                        consistency: bool,
                                        issues: List[str]) -> List[str]:
        """生成回路改进建议"""
        recommendations = []
        
        try:
            if not consistency:
                if "回路不完整" in issues or "不完整" in ", ".join(issues):
                    recommendations.append("检查并完善回路配置，确保所有必需的逻辑节点都已正确连接")
                
                if "逻辑流向不完整" in ", ".join(issues):
                    recommendations.append("检查逻辑节点间的连接关系，确保符合标准配置要求")
            
            # 根据回路类型提供特定建议
            if circuit.type == "protection_trip":
                recommendations.append("确保保护装置与断路器之间的GOOSE通信配置正确")
                recommendations.append("验证保护定值设置是否满足选择性要求")
            
            elif circuit.type == "control":
                recommendations.append("检查防跳回路配置是否正确")
                recommendations.append("验证控制电源的可靠性")
            
            elif circuit.type == "interlock":
                recommendations.append("验证联锁逻辑的正确性和完整性")
                recommendations.append("检查联锁条件的合理性")
                
        except Exception as e:
            logger.warning(f"生成建议失败: {e}")
        
        return recommendations
    
    def _calculate_analysis_confidence(self, 
                                     circuit: LogicalCircuit,
                                     issues: List[str]) -> float:
        """计算分析置信度"""
        try:
            # 基础置信度
            confidence = 0.9
            
            # 根据问题数量调整置信度
            if issues:
                confidence -= min(len(issues) * 0.1, 0.5)
            
            # 根据回路完整性调整
            if not circuit.is_complete:
                confidence -= 0.2
            
            # 确保置信度在合理范围内
            confidence = max(0.1, min(confidence, 1.0))
            
            return confidence
            
        except Exception as e:
            logger.warning(f"计算置信度失败: {e}")
            return 0.5
    
    def analyze_circuit_relationships(self, 
                                    circuits: Dict[str, LogicalCircuit],
                                    context: Dict[str, Any] = None) -> List[CircuitRelationshipAnalysis]:
        """
        分析回路间关系
        
        Args:
            circuits: 回路字典
            context: 分析上下文
            
        Returns:
            List[CircuitRelationshipAnalysis]: 回路关系分析结果列表
        """
        relationship_analyses = []
        
        try:
            # 获取回路关系分析
            relationship_data = self.knowledge_graph.analyze_circuit_relationships()
            
            # 转换为分析结果
            for relationship in relationship_data.get('inter_circuit_relationships', []):
                analysis = CircuitRelationshipAnalysis(
                    circuit1_id=relationship['circuit1'],
                    circuit2_id=relationship['circuit2'],
                    relationship_type=relationship['relationship_type'],
                    shared_entities=relationship['common_entities'],
                    interaction_points=[],  # 需要进一步分析
                    potential_conflicts=self._identify_potential_conflicts(
                        relationship['circuit1'], relationship['circuit2'], relationship['relationship_type']
                    ),
                    recommendations=self._generate_relationship_recommendations(
                        relationship['relationship_type']
                    ),
                    confidence=0.8  # 简化置信度计算
                )
                relationship_analyses.append(analysis)
            
            logger.info(f"完成 {len(relationship_analyses)} 个回路关系分析")
            return relationship_analyses
            
        except Exception as e:
            logger.error(f"回路关系分析失败: {e}")
            return []
    
    def _identify_potential_conflicts(self, 
                                    circuit1_id: str, 
                                    circuit2_id: str, 
                                    relationship_type: str) -> List[str]:
        """识别潜在冲突"""
        conflicts = []
        
        try:
            if relationship_type == "conflicting":
                conflicts.append("回路间存在操作冲突")
            elif relationship_type == "dependency":
                conflicts.append("回路间存在依赖关系，可能影响可靠性")
            elif relationship_type == "shared":
                conflicts.append("回路间共享资源，可能存在竞争")
                
        except Exception as e:
            logger.warning(f"识别潜在冲突失败: {e}")
        
        return conflicts
    
    def _generate_relationship_recommendations(self, relationship_type: str) -> List[str]:
        """生成关系建议"""
        recommendations = []
        
        if relationship_type == "conflicting":
            recommendations.append("重新设计回路逻辑，消除操作冲突")
        elif relationship_type == "dependency":
            recommendations.append("考虑增加冗余配置以提高可靠性")
        elif relationship_type == "shared":
            recommendations.append("优化资源共享策略，避免竞争")
        elif relationship_type == "parallel":
            recommendations.append("验证并行回路的协调性")
        elif relationship_type == "serial":
            recommendations.append("检查串行回路的时序要求")
        
        return recommendations
    
    def generate_comprehensive_analysis_report(self, 
                                             circuits: Dict[str, LogicalCircuit],
                                             context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成综合分析报告
        
        Args:
            circuits: 回路字典
            context: 分析上下文
            
        Returns:
            Dict[str, Any]: 综合分析报告
        """
        report = {
            'analysis_time': None,
            'summary': {},
            'circuit_logic_analysis': [],
            'circuit_relationship_analysis': [],
            'recommendations': [],
            'risk_assessment': {}
        }
        
        try:
            import datetime
            report['analysis_time'] = datetime.datetime.now().isoformat()
            
            // 1. 回路逻辑分析
            logic_analyses = self.analyze_circuit_logic(circuits, context)
            report['circuit_logic_analysis'] = [
                {
                    'circuit_id': analysis.circuit_id,
                    'circuit_type': analysis.circuit_type,
                    'name': analysis.name,
                    'consistency': analysis.consistency,
                    'issues': analysis.issues,
                    'recommendations': analysis.recommendations,
                    'confidence': analysis.confidence
                }
                for analysis in logic_analyses
            ]
            
            // 2. 回路关系分析
            relationship_analyses = self.analyze_circuit_relationships(circuits, context)
            report['circuit_relationship_analysis'] = [
                {
                    'circuit1_id': analysis.circuit1_id,
                    'circuit2_id': analysis.circuit2_id,
                    'relationship_type': analysis.relationship_type,
                    'shared_entities': analysis.shared_entities,
                    'potential_conflicts': analysis.potential_conflicts,
                    'recommendations': analysis.recommendations,
                    'confidence': analysis.confidence
                }
                for analysis in relationship_analyses
            ]
            
            // 3. 生成摘要
            consistent_circuits = [a for a in logic_analyses if a.consistency]
            inconsistent_circuits = [a for a in logic_analyses if not a.consistency]
            
            report['summary'] = {
                'total_circuits': len(circuits),
                'consistent_circuits': len(consistent_circuits),
                'inconsistent_circuits': len(inconsistent_circuits),
                'relationship_analyses': len(relationship_analyses),
                'total_issues': sum(len(analysis.issues) for analysis in logic_analyses),
                'high_confidence_analyses': len([a for a in logic_analyses if a.confidence >= 0.8])
            }
            
            // 4. 汇总建议
            all_recommendations = []
            for analysis in logic_analyses:
                all_recommendations.extend(analysis.recommendations)
            for analysis in relationship_analyses:
                all_recommendations.extend(analysis.recommendations)
            
            report['recommendations'] = list(set(all_recommendations))  // 去重
            
            // 5. 风险评估
            report['risk_assessment'] = self._perform_risk_assessment(logic_analyses, relationship_analyses)
            
        except Exception as e:
            logger.error(f"生成综合分析报告失败: {e}")
            report['error'] = str(e)
        
        return report
    
    def _perform_risk_assessment(self, 
                               logic_analyses: List[CircuitLogicAnalysis],
                               relationship_analyses: List[CircuitRelationshipAnalysis]) -> Dict[str, Any]:
        """执行风险评估"""
        risk_assessment = {
            'overall_risk_level': 'low',
            'risk_factors': [],
            'mitigation_measures': []
        }
        
        try:
            // 评估不一致回路的风险
            inconsistent_count = len([a for a in logic_analyses if not a.consistency])
            if inconsistent_count > 0:
                risk_assessment['risk_factors'].append(f'{inconsistent_count}个回路逻辑不一致')
                if inconsistent_count > len(logic_analyses) * 0.3:
                    risk_assessment['overall_risk_level'] = 'high'
                elif inconsistent_count > len(logic_analyses) * 0.1:
                    risk_assessment['overall_risk_level'] = 'medium'
            
            // 评估冲突关系的风险
            conflict_count = len([a for a in relationship_analyses if a.potential_conflicts])
            if conflict_count > 0:
                risk_assessment['risk_factors'].append(f'{conflict_count}个回路关系存在潜在冲突')
                if conflict_count > len(relationship_analyses) * 0.5:
                    risk_assessment['overall_risk_level'] = 'high'
                elif conflict_count > len(relationship_analyses) * 0.2:
                    risk_assessment['overall_risk_level'] = 'medium'
            
            // 生成缓解措施
            if risk_assessment['overall_risk_level'] == 'high':
                risk_assessment['mitigation_measures'].append('立即检查并修复不一致的回路配置')
                risk_assessment['mitigation_measures'].append('重新设计存在冲突的回路关系')
            elif risk_assessment['overall_risk_level'] == 'medium':
                risk_assessment['mitigation_measures'].append('计划性检查回路配置的一致性')
                risk_assessment['mitigation_measures'].append('优化回路间的关系设计')
            else:
                risk_assessment['mitigation_measures'].append('定期检查回路配置，保持一致性')
                
        except Exception as e:
            logger.warning(f"风险评估失败: {e}")
        
        return risk_assessment