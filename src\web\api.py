"""
API接口模块
提供文件上传、验证、分析等核心功能的REST API
"""

import os
import uuid
import json
from datetime import datetime
from pathlib import Path
from flask import Blueprint, request, jsonify, current_app, send_file
from werkzeug.utils import secure_filename
import traceback

# 导入核心功能模块
from ..core.parsers import ParserFactory
from ..core.unified_review_engine import UnifiedReviewEngine
from ..comparison.file_comparator import FileComparator
from ..comparison.diff_analyzer import DiffAnalyzer
from ..comparison.merge_tool import MergeTool
from ..virtual_terminal.vt_generator import VirtualTerminalGenerator
from ..drawing_review.visualization import DrawingVisualizer
from ..core.rules.circuit_logic import register_circuit_logic_rules
from ..core.rules.engine import RuleEngine, ExecutionConfig
from ..core.rules.registry import rule_registry

api_bp = Blueprint('api', __name__)

# 初始化核心组件（延迟初始化避免循环导入）
_unified_engine = None

def get_unified_engine():
    """获取统一审查引擎实例"""
    global _unified_engine
    if _unified_engine is None:
        _unified_engine = UnifiedReviewEngine()
    return _unified_engine


def allowed_file(filename):
    """检查文件类型是否允许"""
    allowed_extensions = {'.scd', '.icd', '.cid', '.xml', '.dwg', '.dxf', '.pdf'}
    return Path(filename).suffix.lower() in allowed_extensions


def get_file_info(filepath):
    """获取文件基本信息"""
    stat = os.stat(filepath)
    return {
        'size': stat.st_size,
        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
        'extension': Path(filepath).suffix.lower()
    }


@api_bp.route('/upload', methods=['POST'])
def upload_file():
    """
    文件上传接口
    
    解决智能变电站配置文件分析的第一步：
    安全地接收和预处理IEC61850配置文件
    """
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件',
                'code': 'NO_FILE'
            }), 400
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '文件名为空',
                'code': 'EMPTY_FILENAME'
            }), 400
        
        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': f'不支持的文件类型。支持的格式: .scd, .icd, .cid, .xml',
                'code': 'INVALID_FILE_TYPE'
            }), 400
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        original_filename = secure_filename(file.filename)
        file_extension = Path(original_filename).suffix
        stored_filename = f"{file_id}{file_extension}"
        
        # 保存文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        filepath = os.path.join(upload_folder, stored_filename)
        file.save(filepath)
        
        # 获取文件信息
        file_info = get_file_info(filepath)
        
        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'data': {
                'file_id': file_id,
                'original_name': original_filename,
                'stored_name': stored_filename,
                'file_info': file_info
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"文件上传失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '文件上传失败',
            'details': str(e),
            'code': 'UPLOAD_ERROR'
        }), 500


@api_bp.route('/validate/<file_id>', methods=['POST'])
def validate_file(file_id):
    """
    文件验证接口
    
    核心功能：智能分析IEC61850配置文件
    - 自动检测文件类型
    - 执行专业验证规则
    - 生成详细诊断报告
    - 提供修复建议
    """
    try:
        # 获取验证配置
        config_data = request.get_json() or {}
        
        # 查找文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        file_pattern = f"{file_id}.*"
        
        uploaded_files = list(Path(upload_folder).glob(file_pattern))
        if not uploaded_files:
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'code': 'FILE_NOT_FOUND'
            }), 404
        
        filepath = uploaded_files[0]
        
        # 创建解析器工厂
        parser_factory = ParserFactory()
        
        # 解析文件
        current_app.logger.info(f"开始解析文件: {filepath}")
        parse_result = parser_factory.parse_file(filepath, validate_schema=False)
        
        if not parse_result.success:
            return jsonify({
                'success': False,
                'error': '文件解析失败',
                'details': [str(error) for error in parse_result.errors],
                'code': 'PARSE_ERROR'
            }), 400
        
        # 创建规则引擎
        rule_engine = RuleEngine()
        
        # 配置执行参数
        execution_config = ExecutionConfig(
            max_workers=config_data.get('max_workers', 2),
            rule_timeout=config_data.get('rule_timeout', 30.0),
            stop_on_error=config_data.get('stop_on_error', False)
        )
        
        # 执行验证 - 针对智能变电站的实际需求
        current_app.logger.info("开始执行验证规则")
        validation_result = rule_engine.validate(parse_result.data, execution_config)
        
        # 对嵌套对象也进行验证（解决虚端子连接检查问题）
        if hasattr(parse_result.data, 'ieds'):
            for ied in parse_result.data.ieds:
                ied_result = rule_engine.validate(ied, execution_config)
                # 合并结果
                validation_result.executed_rules += ied_result.executed_rules
                validation_result.skipped_rules += ied_result.skipped_rules
                validation_result.failed_rules += ied_result.failed_rules
                validation_result.total_time += ied_result.total_time
                validation_result.rule_results.update(ied_result.rule_results)
        
        # 生成验证报告
        report_generator = ReportGenerator()
        report = report_generator.generate_report(
            validation_result, 
            f"智能变电站配置验证报告 - {filepath.name}"
        )
        
        # 保存报告
        reports_folder = current_app.config['REPORTS_FOLDER']
        report_id = str(uuid.uuid4())
        report_path = os.path.join(reports_folder, f"{report_id}.json")
        report_generator.save_report(report, report_path, 'json')
        
        # 准备响应数据
        response_data = {
            'success': True,
            'message': '验证完成',
            'data': {
                'file_id': file_id,
                'report_id': report_id,
                'validation_summary': {
                    'executed_rules': validation_result.executed_rules,
                    'skipped_rules': validation_result.skipped_rules,
                    'failed_rules': validation_result.failed_rules,
                    'total_time': validation_result.total_time,
                    'total_issues': len(validation_result.get_all_issues()),
                    'errors': len(validation_result.get_errors()),
                    'warnings': len(validation_result.get_warnings()),
                    'infos': len(validation_result.get_infos())
                },
                'issues': [
                    {
                        'rule_id': issue.rule_id,
                        'severity': issue.severity.value,
                        'message': issue.message,
                        'path': issue.path,
                        'details': issue.details,
                        'suggestion': issue.suggestion,
                        'timestamp': issue.timestamp.isoformat()
                    }
                    for issue in validation_result.get_all_issues()
                ],
                'parse_info': {
                    'detected_type': parse_result.metadata.get('detected_type'),
                    'parser_class': parse_result.metadata.get('parser_class'),
                    'parse_duration': parse_result.metadata.get('parse_duration', 0),
                    'file_size': parse_result.metadata.get('file_size', 0),
                    'element_count': parse_result.metadata.get('element_count', 0)
                }
            }
        }
        
        current_app.logger.info(f"验证完成: {validation_result.get_summary()}")
        return jsonify(response_data)
        
    except Exception as e:
        current_app.logger.error(f"验证失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': '验证过程中发生错误',
            'details': str(e),
            'code': 'VALIDATION_ERROR'
        }), 500


@api_bp.route('/report/<report_id>')
def get_report(report_id):
    """获取验证报告"""
    try:
        reports_folder = current_app.config['REPORTS_FOLDER']
        report_path = os.path.join(reports_folder, f"{report_id}.json")
        
        if not os.path.exists(report_path):
            return jsonify({
                'success': False,
                'error': '报告不存在',
                'code': 'REPORT_NOT_FOUND'
            }), 404
        
        with open(report_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return jsonify({
            'success': True,
            'data': report_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '获取报告失败',
            'details': str(e),
            'code': 'REPORT_ERROR'
        }), 500


@api_bp.route('/report/<report_id>/download')
def download_report(report_id):
    """下载验证报告"""
    try:
        format_type = request.args.get('format', 'json').lower()
        
        reports_folder = current_app.config['REPORTS_FOLDER']
        
        if format_type == 'json':
            report_path = os.path.join(reports_folder, f"{report_id}.json")
            if os.path.exists(report_path):
                return send_file(report_path, as_attachment=True, 
                               download_name=f"validation_report_{report_id}.json")
        
        return jsonify({
            'success': False,
            'error': '报告文件不存在',
            'code': 'REPORT_NOT_FOUND'
        }), 404
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '下载报告失败',
            'details': str(e),
            'code': 'DOWNLOAD_ERROR'
        }), 500


@api_bp.route('/status')
def get_status():
    """获取系统状态"""
    try:
        from ..core.rules import rule_registry
        
        # 统计规则信息
        all_rules = rule_registry.get_all_rules()
        enabled_rules = rule_registry.get_enabled_rules()
        
        # 统计文件信息
        upload_folder = current_app.config['UPLOAD_FOLDER']
        reports_folder = current_app.config['REPORTS_FOLDER']
        
        upload_files = len(list(Path(upload_folder).glob('*'))) if os.path.exists(upload_folder) else 0
        report_files = len(list(Path(reports_folder).glob('*.json'))) if os.path.exists(reports_folder) else 0
        
        return jsonify({
            'success': True,
            'data': {
                'system': {
                    'status': 'running',
                    'version': '1.0.0',
                    'uptime': 'N/A'  # 可以添加实际的运行时间统计
                },
                'rules': {
                    'total': len(all_rules),
                    'enabled': len(enabled_rules),
                    'disabled': len(all_rules) - len(enabled_rules)
                },
                'files': {
                    'uploaded': upload_files,
                    'reports': report_files
                },
                'capabilities': {
                    'supported_formats': ['.scd', '.icd', '.cid', '.xml'],
                    'max_file_size': '50MB',
                    'parallel_validation': True,
                    'report_formats': ['json', 'html', 'markdown']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '获取状态失败',
            'details': str(e),
            'code': 'STATUS_ERROR'
        }), 500


@api_bp.route('/compare', methods=['POST'])
def compare_files():
    """
    配置文件对比接口

    解决智能变电站配置文件版本对比的实际需求
    """
    try:
        data = request.get_json() or {}
        source_file_id = data.get('source_file_id')
        target_file_id = data.get('target_file_id')

        if not source_file_id or not target_file_id:
            return jsonify({
                'success': False,
                'error': '缺少源文件或目标文件ID',
                'code': 'MISSING_FILE_IDS'
            }), 400

        # 查找文件
        upload_folder = current_app.config['UPLOAD_FOLDER']

        source_files = list(Path(upload_folder).glob(f"{source_file_id}.*"))
        target_files = list(Path(upload_folder).glob(f"{target_file_id}.*"))

        if not source_files or not target_files:
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'code': 'FILE_NOT_FOUND'
            }), 404

        # 执行对比
        from ..core.comparison import ConfigComparator, ComparisonReporter

        comparator = ConfigComparator()
        comparison_result = comparator.compare_files(str(source_files[0]), str(target_files[0]))

        # 生成报告
        reporter = ComparisonReporter()
        report = reporter.generate_report(comparison_result, "配置文件对比报告")

        # 保存报告
        reports_folder = current_app.config['REPORTS_FOLDER']
        report_id = str(uuid.uuid4())
        report_path = os.path.join(reports_folder, f"comparison_{report_id}.json")
        reporter.save_report(report, report_path, 'json')

        return jsonify({
            'success': True,
            'message': '对比完成',
            'data': {
                'comparison_id': comparison_result.id,
                'report_id': report_id,
                'summary': comparison_result.summary,
                'statistics': comparison_result.statistics,
                'has_critical_changes': comparison_result.has_critical_changes(),
                'total_changes': len(comparison_result.changes)
            }
        })

    except Exception as e:
        current_app.logger.error(f"文件对比失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '对比过程中发生错误',
            'details': str(e),
            'code': 'COMPARISON_ERROR'
        }), 500


@api_bp.route('/generate/virtual-terminals/<file_id>', methods=['POST'])
def generate_virtual_terminals(file_id):
    """
    生成虚端子表接口

    解决智能变电站虚端子表制作的实际痛点
    """
    try:
        data = request.get_json() or {}

        # 查找文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        uploaded_files = list(Path(upload_folder).glob(f"{file_id}.*"))

        if not uploaded_files:
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'code': 'FILE_NOT_FOUND'
            }), 404

        filepath = uploaded_files[0]

        # 解析文件
        from ..core.parsers import ParserFactory
        parser_factory = ParserFactory()
        parse_result = parser_factory.parse_file(filepath)

        if not parse_result.success:
            return jsonify({
                'success': False,
                'error': '文件解析失败',
                'details': [str(error) for error in parse_result.errors],
                'code': 'PARSE_ERROR'
            }), 400

        # 生成虚端子表
        from ..core.generators import VirtualTerminalGenerator

        generator = VirtualTerminalGenerator()
        project_name = data.get('project_name', '智能变电站项目')
        options = data.get('options', {})

        vt_table = generator.generate_from_scd(parse_result.data, project_name, options)

        # 保存结果
        reports_folder = current_app.config['REPORTS_FOLDER']
        table_id = str(uuid.uuid4())

        # 保存JSON格式
        json_path = os.path.join(reports_folder, f"vt_table_{table_id}.json")
        generator.export_to_json(vt_table, json_path)

        # 保存CSV格式
        csv_path = os.path.join(reports_folder, f"vt_table_{table_id}.csv")
        generator.export_to_csv(vt_table, csv_path)

        return jsonify({
            'success': True,
            'message': '虚端子表生成成功',
            'data': {
                'table_id': table_id,
                'project_name': vt_table.project_name,
                'substation_name': vt_table.substation_name,
                'statistics': vt_table.statistics,
                'download_urls': {
                    'json': f'/api/download/virtual-terminals/{table_id}?format=json',
                    'csv': f'/api/download/virtual-terminals/{table_id}?format=csv',
                    'excel': f'/api/download/virtual-terminals/{table_id}?format=excel'
                }
            }
        })

    except Exception as e:
        current_app.logger.error(f"虚端子表生成失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '生成过程中发生错误',
            'details': str(e),
            'code': 'GENERATION_ERROR'
        }), 500


@api_bp.route('/download/virtual-terminals/<table_id>')
def download_virtual_terminals(table_id):
    """下载虚端子表"""
    try:
        format_type = request.args.get('format', 'json').lower()
        reports_folder = current_app.config['REPORTS_FOLDER']

        if format_type == 'json':
            file_path = os.path.join(reports_folder, f"vt_table_{table_id}.json")
            download_name = f"virtual_terminals_{table_id}.json"
        elif format_type == 'csv':
            file_path = os.path.join(reports_folder, f"vt_table_{table_id}.csv")
            download_name = f"virtual_terminals_{table_id}.csv"
        elif format_type == 'excel':
            # 如果Excel文件不存在，从JSON重新生成
            excel_path = os.path.join(reports_folder, f"vt_table_{table_id}.xlsx")
            if not os.path.exists(excel_path):
                json_path = os.path.join(reports_folder, f"vt_table_{table_id}.json")
                if os.path.exists(json_path):
                    # 重新生成Excel文件
                    from ..core.generators import VirtualTerminalGenerator
                    import json

                    with open(json_path, 'r', encoding='utf-8') as f:
                        table_data = json.load(f)

                    # 这里需要重构VirtualTerminalTable以支持从字典创建
                    # 简化实现，返回CSV格式
                    file_path = os.path.join(reports_folder, f"vt_table_{table_id}.csv")
                    download_name = f"virtual_terminals_{table_id}.csv"
                else:
                    return jsonify({
                        'success': False,
                        'error': '表格文件不存在',
                        'code': 'TABLE_NOT_FOUND'
                    }), 404
            else:
                file_path = excel_path
                download_name = f"virtual_terminals_{table_id}.xlsx"
        else:
            return jsonify({
                'success': False,
                'error': '不支持的格式类型',
                'code': 'INVALID_FORMAT'
            }), 400

        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True, download_name=download_name)
        else:
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'code': 'FILE_NOT_FOUND'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'error': '下载失败',
            'details': str(e),
            'code': 'DOWNLOAD_ERROR'
        }), 500


@api_bp.route('/docs')
def api_docs():
    """API文档"""
    return jsonify({
        'name': 'IEC61850设计检查器 API',
        'version': '1.0.0',
        'description': '智能变电站二次设计验证与分析API',
        'endpoints': {
            'POST /api/upload': '上传配置文件',
            'POST /api/validate/<file_id>': '验证配置文件',
            'POST /api/validate/async/<file_id>': '异步验证配置文件',
            'POST /api/compare': '对比配置文件',
            'POST /api/compare/analyze': '深度分析对比结果',
            'POST /api/merge': '合并配置文件',
            'POST /api/generate/virtual-terminals/<file_id>': '生成虚端子表',
            'GET /api/download/virtual-terminals/<table_id>': '下载虚端子表',
            'GET /api/report/<report_id>': '获取验证报告',
            'GET /api/report/<report_id>/download': '下载验证报告',
            'GET /api/task/<task_id>/status': '获取任务状态',
            'GET /api/status': '获取系统状态',
            'GET /api/docs': '查看API文档'
        },
        'mission': '解决智能变电站虚端子连接检查和配置验证的实际工程问题'
    })


@api_bp.route('/validate/async/<file_id>', methods=['POST'])
def validate_file_async(file_id):
    """
    异步验证配置文件
    支持实时进度推送
    """
    try:
        # 获取文件路径
        upload_dir = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        file_path = None

        for ext in ['.scd', '.icd', '.cid', '.xml']:
            potential_path = os.path.join(upload_dir, f"{file_id}{ext}")
            if os.path.exists(potential_path):
                file_path = potential_path
                break

        if not file_path:
            return jsonify({
                'success': False,
                'error': '文件不存在'
            }), 404

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 启动异步验证任务
        def async_validation():
            try:
                # 这里应该集成WebSocket进度推送
                # 简化实现，实际应该使用WebSocket

                # 解析文件
                parser_factory = ParserFactory()
                parser = parser_factory.create_parser(file_path)
                parsed_data = parser.parse()

                # 执行验证
                rule_engine = RuleEngine()
                config = ExecutionConfig()

                # 这里可以添加进度回调
                result = rule_engine.execute_validation(parsed_data, config)

                # 生成报告
                report_generator = ReportGenerator()
                report = report_generator.generate_report(result)

                # 保存结果
                report_id = str(uuid.uuid4())
                report_path = os.path.join(upload_dir, f"report_{report_id}.json")

                with open(report_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)

                return {
                    'task_id': task_id,
                    'status': 'completed',
                    'report_id': report_id,
                    'summary': {
                        'total_rules': len(result.rule_results),
                        'passed': len([r for r in result.rule_results if r.passed]),
                        'failed': len([r for r in result.rule_results if not r.passed])
                    }
                }

            except Exception as e:
                return {
                    'task_id': task_id,
                    'status': 'error',
                    'error': str(e)
                }

        # 在后台线程中执行
        import threading
        thread = threading.Thread(target=async_validation)
        thread.start()

        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '异步验证任务已启动',
            'status_url': f'/api/task/{task_id}/status'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@api_bp.route('/compare/analyze', methods=['POST'])
def analyze_comparison():
    """
    深度分析对比结果
    """
    try:
        data = request.get_json()

        if not data or 'source_file_id' not in data or 'target_file_id' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        source_file_id = data['source_file_id']
        target_file_id = data['target_file_id']

        # 获取文件路径
        upload_dir = current_app.config.get('UPLOAD_FOLDER', 'uploads')

        source_path = None
        target_path = None

        for ext in ['.scd', '.icd', '.cid', '.xml']:
            if not source_path:
                potential_path = os.path.join(upload_dir, f"{source_file_id}{ext}")
                if os.path.exists(potential_path):
                    source_path = potential_path

            if not target_path:
                potential_path = os.path.join(upload_dir, f"{target_file_id}{ext}")
                if os.path.exists(potential_path):
                    target_path = potential_path

        if not source_path or not target_path:
            return jsonify({
                'success': False,
                'error': '文件不存在'
            }), 404

        # 执行对比
        comparator = FileComparator()
        comparison_result = comparator.compare_files(source_path, target_path)

        # 深度分析
        analyzer = DiffAnalyzer()
        analysis = analyzer.analyze_comparison_result(comparison_result)

        # 保存分析结果
        analysis_id = str(uuid.uuid4())
        analysis_path = os.path.join(upload_dir, f"analysis_{analysis_id}.json")

        analysis_data = {
            'comparison_result': comparison_result.to_dict(),
            'analysis': analysis,
            'created_at': datetime.now().isoformat()
        }

        with open(analysis_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)

        return jsonify({
            'success': True,
            'analysis_id': analysis_id,
            'summary': analysis.get('summary', {}),
            'risk_assessment': analysis.get('risk_assessment', {}),
            'recommendations': analysis.get('recommendations', [])
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@api_bp.route('/task/<task_id>/status', methods=['GET'])
def get_task_status(task_id):
    """
    获取任务状态
    """
    try:
        # 这里应该从任务存储中获取状态
        # 简化实现，实际应该使用数据库或缓存

        # 模拟任务状态
        return jsonify({
            'task_id': task_id,
            'status': 'running',  # running, completed, error
            'progress': 75,
            'message': '正在执行验证规则...',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# ==================== 图纸审查API端点 ====================

@api_bp.route('/drawing-review/upload', methods=['POST'])
def upload_drawing():
    """上传图纸文件进行审查"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 检查文件格式
        drawing_extensions = {'.dwg', '.dxf', '.pdf'}
        if Path(file.filename).suffix.lower() not in drawing_extensions:
            return jsonify({
                'success': False,
                'error': f'不支持的图纸格式，支持的格式: {", ".join(drawing_extensions)}'
            }), 400

        # 保存文件
        filename = secure_filename(file.filename)
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        return jsonify({
            'success': True,
            'data': {
                'filename': filename,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path),
                'file_type': Path(filename).suffix.lower()
            },
            'message': '图纸文件上传成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/drawing-review/review', methods=['POST'])
def review_drawing():
    """审查图纸"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        check_categories = data.get('check_categories', [])

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400

        # 初始化审查引擎
        review_engine = DrawingReviewEngine()

        # 执行审查
        review_result = review_engine.review_drawing(
            file_path=file_path,
            check_categories=check_categories if check_categories else None
        )

        if not review_result:
            return jsonify({'success': False, 'error': '图纸审查失败'}), 500

        # 生成响应数据
        response_data = {
            'review_id': review_result.review_id,
            'summary': review_result.get_summary(),
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'severity': issue.severity.value,
                    'category': issue.category,
                    'title': issue.title,
                    'description': issue.description,
                    'suggestion': issue.suggestion,
                    'auto_fixable': issue.auto_fixable,
                    'standard_reference': issue.standard_reference,
                    'location': {
                        'x': issue.location.x,
                        'y': issue.location.y
                    } if issue.location else None
                }
                for issue in review_result.issues
            ]
        }

        return jsonify({
            'success': True,
            'data': response_data,
            'message': f'图纸审查完成，发现 {len(review_result.issues)} 个问题'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/drawing-review/visualization', methods=['POST'])
def generate_drawing_visualization():
    """生成图纸审查可视化"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        visualization_type = data.get('type', 'interactive')

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400

        # 执行审查
        review_engine = DrawingReviewEngine()
        review_result = review_engine.review_drawing(file_path)

        if not review_result:
            return jsonify({'success': False, 'error': '图纸审查失败'}), 500

        # 生成可视化
        visualizer = DrawingVisualizer()
        if visualization_type == 'svg':
            content = visualizer.generate_review_visualization(review_result)
            content_type = 'image/svg+xml'
        else:
            content = visualizer.generate_interactive_viewer(review_result)
            content_type = 'text/html'

        return jsonify({
            'success': True,
            'data': {
                'content': content,
                'content_type': content_type,
                'review_summary': review_result.get_summary()
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/drawing-review/categories', methods=['GET'])
def get_drawing_check_categories():
    """获取图纸检查分类"""
    try:
        review_engine = DrawingReviewEngine()
        categories = review_engine.get_available_check_categories()

        return jsonify({
            'success': True,
            'data': {
                'categories': categories,
                'descriptions': {
                    '线型': '检查线型是否符合标准',
                    '线宽': '检查线宽是否符合规范',
                    '文字标注': '检查文字标注格式',
                    '图层': '检查图层命名和使用',
                    '尺寸标注': '检查尺寸标注规范',
                    '设备符号': '检查电气设备符号',
                    '连接': '检查线路连接规范'
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/drawing-review/formats', methods=['GET'])
def get_supported_drawing_formats():
    """获取支持的图纸格式"""
    try:
        review_engine = DrawingReviewEngine()
        formats = review_engine.get_supported_formats()

        return jsonify({
            'success': True,
            'data': {
                'formats': formats,
                'descriptions': {
                    '.dwg': 'AutoCAD图纸文件',
                    '.dxf': 'AutoCAD交换格式文件',
                    '.pdf': 'PDF图纸文件'
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ==================== 统一审查API端点 ====================

@api_bp.route('/unified-review/upload', methods=['POST'])
def upload_unified_file():
    """上传文件进行统一审查（自动识别类型）"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 检查文件格式
        all_extensions = {'.scd', '.icd', '.cid', '.xml', '.dwg', '.dxf', '.pdf'}
        if Path(file.filename).suffix.lower() not in all_extensions:
            return jsonify({
                'success': False,
                'error': f'不支持的文件格式，支持的格式: {", ".join(all_extensions)}'
            }), 400

        # 保存文件
        filename = secure_filename(file.filename)
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, filename)
        file.save(file_path)

        # 检测文件类型
        unified_engine = get_unified_engine()
        file_type = unified_engine._detect_file_type(file_path)

        return jsonify({
            'success': True,
            'data': {
                'filename': filename,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path),
                'file_extension': Path(filename).suffix.lower(),
                'detected_type': file_type
            },
            'message': '文件上传成功'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/unified-review/review', methods=['POST'])
def unified_review():
    """统一审查文件"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        check_categories = data.get('check_categories', [])
        severity_filter = data.get('severity_filter')

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400

        # 初始化统一审查引擎
        unified_engine = get_unified_engine()

        # 执行统一审查
        review_result = unified_engine.review_file(
            file_path=file_path,
            check_categories=check_categories if check_categories else None,
            severity_filter=severity_filter
        )

        if not review_result:
            return jsonify({'success': False, 'error': '文件审查失败'}), 500

        # 生成响应数据
        response_data = {
            'review_id': review_result.review_id,
            'document_type': review_result.document.get_document_type(),
            'summary': review_result.get_summary(),
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'severity': issue.severity,
                    'category': issue.category,
                    'title': issue.title,
                    'description': issue.description,
                    'source_type': issue.source_type,
                    'rule_id': issue.rule_id,
                    'location': issue.location,
                    'suggestion': issue.suggestion,
                    'auto_fixable': issue.auto_fixable,
                    'standard_reference': issue.standard_reference,
                    'affected_elements': issue.affected_elements
                }
                for issue in review_result.issues
            ]
        }

        return jsonify({
            'success': True,
            'data': response_data,
            'message': f'文件审查完成，发现 {len(review_result.issues)} 个问题'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/unified-review/batch', methods=['POST'])
def batch_unified_review():
    """批量统一审查"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])

        if not file_paths:
            return jsonify({'success': False, 'error': '没有提供文件路径'}), 400

        # 验证文件路径
        valid_paths = []
        for path in file_paths:
            if os.path.exists(path):
                valid_paths.append(path)

        if not valid_paths:
            return jsonify({'success': False, 'error': '没有有效的文件路径'}), 400

        # 批量审查
        unified_engine = get_unified_engine()
        review_results = unified_engine.review_multiple_files(valid_paths)

        # 生成响应数据
        results_data = []
        total_issues = 0

        for result in review_results:
            summary = result.get_summary()
            results_data.append({
                'file_path': result.document.file_path,
                'document_type': result.document.get_document_type(),
                'review_id': result.review_id,
                'summary': summary
            })
            total_issues += summary['total_issues']

        return jsonify({
            'success': True,
            'data': {
                'results': results_data,
                'total_files': len(valid_paths),
                'successful_reviews': len(review_results),
                'total_issues': total_issues
            },
            'message': f'批量审查完成，成功审查 {len(review_results)}/{len(valid_paths)} 个文件'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/unified-review/report', methods=['POST'])
def generate_unified_report():
    """生成统一审查报告"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        report_format = data.get('format', 'html')

        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400

        # 执行审查
        unified_engine = get_unified_engine()
        review_result = unified_engine.review_file(file_path)

        if not review_result:
            return jsonify({'success': False, 'error': '文件审查失败'}), 500

        # 生成报告
        report_content = unified_engine.generate_unified_report(review_result, report_format)

        if not report_content:
            return jsonify({'success': False, 'error': '报告生成失败'}), 500

        # 确定内容类型
        content_types = {
            'html': 'text/html',
            'json': 'application/json'
        }

        return jsonify({
            'success': True,
            'data': {
                'content': report_content,
                'content_type': content_types.get(report_format, 'text/plain'),
                'format': report_format,
                'summary': review_result.get_summary()
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/unified-review/formats', methods=['GET'])
def get_unified_supported_formats():
    """获取统一审查支持的格式"""
    try:
        unified_engine = get_unified_engine()
        formats = unified_engine.get_supported_formats()

        return jsonify({
            'success': True,
            'data': {
                'formats': formats,
                'descriptions': {
                    '.scd': 'IEC61850系统配置描述文件',
                    '.icd': 'IEC61850设备能力描述文件',
                    '.cid': 'IEC61850配置实例描述文件',
                    '.xml': 'XML格式配置文件',
                    '.dwg': 'AutoCAD图纸文件',
                    '.dxf': 'AutoCAD交换格式文件',
                    '.pdf': 'PDF图纸文件'
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/unified-review/categories', methods=['GET'])
def get_unified_categories():
    """获取统一审查的检查分类"""
    try:
        unified_engine = get_unified_engine()
        categories = unified_engine.get_available_categories()

        return jsonify({
            'success': True,
            'data': {
                'categories': categories,
                'descriptions': {
                    # 配置检查分类
                    '标准符合性': '检查IEC61850标准符合性',
                    '设备配置': '检查IED设备配置',
                    '通信网络': '检查通信网络配置',
                    '数据类型': '检查数据类型定义',
                    # 图纸检查分类
                    '线型': '检查线型是否符合标准',
                    '线宽': '检查线宽是否符合规范',
                    '文字标注': '检查文字标注格式',
                    '图层': '检查图层命名和使用',
                    '尺寸标注': '检查尺寸标注规范',
                    '设备符号': '检查电气设备符号'
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@api_bp.route('/validate_circuit_logic', methods=['POST'])
def validate_circuit_logic():
    """
    校验二次回路逻辑
    
    请求体:
    {
        "nodes": [
            {"id": "IED_001", "name": "保护IED", "type": "ied", "category": "protection"},
            ...
        ],
        "links": [
            {"source": "IED_001", "target": "IED_002", "type": "trip"},
            ...
        ]
    }
    
    返回:
    {
        "success": true,
        "data": {
            "is_valid": false,
            "invalid_links": [
                {
                    "source": "IED_001",
                    "target": "BRK_001", 
                    "type": "trip",
                    "invalid": true,
                    "reason": "跳闸应由保护/联跳发往BCU等控制IED"
                }
            ],
            "issues": [
                {
                    "rule_id": "CIRCUIT_TRIP_001",
                    "severity": "error",
                    "message": "跳闸连接不符合规程要求",
                    "path": "links.IED_001->BRK_001",
                    "details": "源节点类型: ied(protection), 目标节点类型: device(switchgear)",
                    "suggestion": "跳闸信号应由保护/联跳装置发往BCU等控制IED，禁止直达一次设备"
                }
            ]
        }
    }
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400
        
        # 确保回路逻辑校验规则已注册
        register_circuit_logic_rules()
        
        # 创建规则引擎
        rule_engine = RuleEngine()
        
        # 创建执行配置
        config = ExecutionConfig(
            max_workers=4,
            rule_timeout=30.0,
            stop_on_error=False,
            skip_inapplicable=True
        )
        
        # 执行回路逻辑校验
        execution_result = rule_engine.validate_with_rules(
            data=data,
            rule_ids=[
                'CIRCUIT_SMV_001',
                'CIRCUIT_GOOSE_001', 
                'CIRCUIT_TRIP_001',
                'CIRCUIT_CONTROL_001',
                'CIRCUIT_INTERLOCK_001',
                'CIRCUIT_PRIMARY_001'
            ],
            config=config
        )
        
        # 收集所有违规连接信息
        invalid_links = []
        for rule_result in execution_result.rule_results.values():
            if 'invalid_links' in rule_result.metadata:
                invalid_links.extend(rule_result.metadata['invalid_links'])
        
        # 收集所有问题
        issues = []
        for rule_result in execution_result.rule_results.values():
            for issue in rule_result.issues:
                issues.append(issue.to_dict())
        
        # 构建响应
        response_data = {
            'is_valid': len(invalid_links) == 0,
            'invalid_links': invalid_links,
            'issues': issues,
            'summary': {
                'total_links': len(data.get('links', [])),
                'invalid_links_count': len(invalid_links),
                'error_count': len([i for i in issues if i['severity'] == 'error']),
                'warning_count': len([i for i in issues if i['severity'] == 'warning']),
                'execution_time': execution_result.total_time
            }
        }
        
        return jsonify({
            'success': True,
            'data': response_data
        })
        
    except Exception as e:
        current_app.logger.error(f"回路逻辑校验失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'回路逻辑校验失败: {str(e)}'
        }), 500
