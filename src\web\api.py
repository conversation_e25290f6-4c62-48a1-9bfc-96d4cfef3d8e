"""
API接口模块
提供文件上传、验证、分析等核心功能的REST API
"""

import os
import uuid
import json
from datetime import datetime
from pathlib import Path
from flask import Blueprint, request, jsonify, current_app, send_file
from werkzeug.utils import secure_filename
import traceback

# 导入核心功能模块
from ..core.parsers import ParserFactory
from ..core.rules import RuleEngine, ExecutionConfig, ReportGenerator

api_bp = Blueprint('api', __name__)


def allowed_file(filename):
    """检查文件类型是否允许"""
    allowed_extensions = {'.scd', '.icd', '.cid', '.xml'}
    return Path(filename).suffix.lower() in allowed_extensions


def get_file_info(filepath):
    """获取文件基本信息"""
    stat = os.stat(filepath)
    return {
        'size': stat.st_size,
        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
        'extension': Path(filepath).suffix.lower()
    }


@api_bp.route('/upload', methods=['POST'])
def upload_file():
    """
    文件上传接口
    
    解决智能变电站配置文件分析的第一步：
    安全地接收和预处理IEC61850配置文件
    """
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有选择文件',
                'code': 'NO_FILE'
            }), 400
        
        file = request.files['file']
        
        # 检查文件名
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '文件名为空',
                'code': 'EMPTY_FILENAME'
            }), 400
        
        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'error': f'不支持的文件类型。支持的格式: .scd, .icd, .cid, .xml',
                'code': 'INVALID_FILE_TYPE'
            }), 400
        
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        original_filename = secure_filename(file.filename)
        file_extension = Path(original_filename).suffix
        stored_filename = f"{file_id}{file_extension}"
        
        # 保存文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        filepath = os.path.join(upload_folder, stored_filename)
        file.save(filepath)
        
        # 获取文件信息
        file_info = get_file_info(filepath)
        
        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'data': {
                'file_id': file_id,
                'original_name': original_filename,
                'stored_name': stored_filename,
                'file_info': file_info
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"文件上传失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '文件上传失败',
            'details': str(e),
            'code': 'UPLOAD_ERROR'
        }), 500


@api_bp.route('/validate/<file_id>', methods=['POST'])
def validate_file(file_id):
    """
    文件验证接口
    
    核心功能：智能分析IEC61850配置文件
    - 自动检测文件类型
    - 执行专业验证规则
    - 生成详细诊断报告
    - 提供修复建议
    """
    try:
        # 获取验证配置
        config_data = request.get_json() or {}
        
        # 查找文件
        upload_folder = current_app.config['UPLOAD_FOLDER']
        file_pattern = f"{file_id}.*"
        
        uploaded_files = list(Path(upload_folder).glob(file_pattern))
        if not uploaded_files:
            return jsonify({
                'success': False,
                'error': '文件不存在',
                'code': 'FILE_NOT_FOUND'
            }), 404
        
        filepath = uploaded_files[0]
        
        # 创建解析器工厂
        parser_factory = ParserFactory()
        
        # 解析文件
        current_app.logger.info(f"开始解析文件: {filepath}")
        parse_result = parser_factory.parse_file(filepath, validate_schema=False)
        
        if not parse_result.success:
            return jsonify({
                'success': False,
                'error': '文件解析失败',
                'details': [str(error) for error in parse_result.errors],
                'code': 'PARSE_ERROR'
            }), 400
        
        # 创建规则引擎
        rule_engine = RuleEngine()
        
        # 配置执行参数
        execution_config = ExecutionConfig(
            max_workers=config_data.get('max_workers', 2),
            rule_timeout=config_data.get('rule_timeout', 30.0),
            stop_on_error=config_data.get('stop_on_error', False)
        )
        
        # 执行验证 - 针对智能变电站的实际需求
        current_app.logger.info("开始执行验证规则")
        validation_result = rule_engine.validate(parse_result.data, execution_config)
        
        # 对嵌套对象也进行验证（解决虚端子连接检查问题）
        if hasattr(parse_result.data, 'ieds'):
            for ied in parse_result.data.ieds:
                ied_result = rule_engine.validate(ied, execution_config)
                # 合并结果
                validation_result.executed_rules += ied_result.executed_rules
                validation_result.skipped_rules += ied_result.skipped_rules
                validation_result.failed_rules += ied_result.failed_rules
                validation_result.total_time += ied_result.total_time
                validation_result.rule_results.update(ied_result.rule_results)
        
        # 生成验证报告
        report_generator = ReportGenerator()
        report = report_generator.generate_report(
            validation_result, 
            f"智能变电站配置验证报告 - {filepath.name}"
        )
        
        # 保存报告
        reports_folder = current_app.config['REPORTS_FOLDER']
        report_id = str(uuid.uuid4())
        report_path = os.path.join(reports_folder, f"{report_id}.json")
        report_generator.save_report(report, report_path, 'json')
        
        # 准备响应数据
        response_data = {
            'success': True,
            'message': '验证完成',
            'data': {
                'file_id': file_id,
                'report_id': report_id,
                'validation_summary': {
                    'executed_rules': validation_result.executed_rules,
                    'skipped_rules': validation_result.skipped_rules,
                    'failed_rules': validation_result.failed_rules,
                    'total_time': validation_result.total_time,
                    'total_issues': len(validation_result.get_all_issues()),
                    'errors': len(validation_result.get_errors()),
                    'warnings': len(validation_result.get_warnings()),
                    'infos': len(validation_result.get_infos())
                },
                'issues': [
                    {
                        'rule_id': issue.rule_id,
                        'severity': issue.severity.value,
                        'message': issue.message,
                        'path': issue.path,
                        'details': issue.details,
                        'suggestion': issue.suggestion,
                        'timestamp': issue.timestamp.isoformat()
                    }
                    for issue in validation_result.get_all_issues()
                ],
                'parse_info': {
                    'detected_type': parse_result.metadata.get('detected_type'),
                    'parser_class': parse_result.metadata.get('parser_class'),
                    'parse_duration': parse_result.metadata.get('parse_duration', 0),
                    'file_size': parse_result.metadata.get('file_size', 0),
                    'element_count': parse_result.metadata.get('element_count', 0)
                }
            }
        }
        
        current_app.logger.info(f"验证完成: {validation_result.get_summary()}")
        return jsonify(response_data)
        
    except Exception as e:
        current_app.logger.error(f"验证失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': '验证过程中发生错误',
            'details': str(e),
            'code': 'VALIDATION_ERROR'
        }), 500


@api_bp.route('/report/<report_id>')
def get_report(report_id):
    """获取验证报告"""
    try:
        reports_folder = current_app.config['REPORTS_FOLDER']
        report_path = os.path.join(reports_folder, f"{report_id}.json")
        
        if not os.path.exists(report_path):
            return jsonify({
                'success': False,
                'error': '报告不存在',
                'code': 'REPORT_NOT_FOUND'
            }), 404
        
        with open(report_path, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return jsonify({
            'success': True,
            'data': report_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '获取报告失败',
            'details': str(e),
            'code': 'REPORT_ERROR'
        }), 500


@api_bp.route('/report/<report_id>/download')
def download_report(report_id):
    """下载验证报告"""
    try:
        format_type = request.args.get('format', 'json').lower()
        
        reports_folder = current_app.config['REPORTS_FOLDER']
        
        if format_type == 'json':
            report_path = os.path.join(reports_folder, f"{report_id}.json")
            if os.path.exists(report_path):
                return send_file(report_path, as_attachment=True, 
                               download_name=f"validation_report_{report_id}.json")
        
        return jsonify({
            'success': False,
            'error': '报告文件不存在',
            'code': 'REPORT_NOT_FOUND'
        }), 404
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '下载报告失败',
            'details': str(e),
            'code': 'DOWNLOAD_ERROR'
        }), 500


@api_bp.route('/status')
def get_status():
    """获取系统状态"""
    try:
        from ..core.rules import rule_registry
        
        # 统计规则信息
        all_rules = rule_registry.get_all_rules()
        enabled_rules = rule_registry.get_enabled_rules()
        
        # 统计文件信息
        upload_folder = current_app.config['UPLOAD_FOLDER']
        reports_folder = current_app.config['REPORTS_FOLDER']
        
        upload_files = len(list(Path(upload_folder).glob('*'))) if os.path.exists(upload_folder) else 0
        report_files = len(list(Path(reports_folder).glob('*.json'))) if os.path.exists(reports_folder) else 0
        
        return jsonify({
            'success': True,
            'data': {
                'system': {
                    'status': 'running',
                    'version': '1.0.0',
                    'uptime': 'N/A'  # 可以添加实际的运行时间统计
                },
                'rules': {
                    'total': len(all_rules),
                    'enabled': len(enabled_rules),
                    'disabled': len(all_rules) - len(enabled_rules)
                },
                'files': {
                    'uploaded': upload_files,
                    'reports': report_files
                },
                'capabilities': {
                    'supported_formats': ['.scd', '.icd', '.cid', '.xml'],
                    'max_file_size': '50MB',
                    'parallel_validation': True,
                    'report_formats': ['json', 'html', 'markdown']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': '获取状态失败',
            'details': str(e),
            'code': 'STATUS_ERROR'
        }), 500


@api_bp.route('/docs')
def api_docs():
    """API文档"""
    return jsonify({
        'name': 'IEC61850设计检查器 API',
        'version': '1.0.0',
        'description': '智能变电站二次设计验证与分析API',
        'endpoints': {
            'POST /api/upload': '上传配置文件',
            'POST /api/validate/<file_id>': '验证配置文件',
            'GET /api/report/<report_id>': '获取验证报告',
            'GET /api/report/<report_id>/download': '下载验证报告',
            'GET /api/status': '获取系统状态',
            'GET /api/docs': '查看API文档'
        },
        'mission': '解决智能变电站虚端子连接检查和配置验证的实际工程问题'
    })
