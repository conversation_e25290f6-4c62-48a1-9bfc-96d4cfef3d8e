"""
GUI工具类
提供GUI相关的通用工具函数
"""

import logging
from typing import Optional, Tuple, List
from pathlib import Path

from PySide6.QtWidgets import QWidget, QMessageBox, QApplication
from PySide6.QtCore import QRect, QPoint, QSize
from PySide6.QtGui import QScreen, QPixmap, QIcon

logger = logging.getLogger(__name__)


class GuiUtils:
    """GUI工具类"""
    
    @staticmethod
    def center_window(window: QWidget, parent: Optional[QWidget] = None):
        """将窗口居中显示"""
        try:
            if parent:
                # 相对于父窗口居中
                parent_geometry = parent.geometry()
                window_size = window.size()
                
                x = parent_geometry.x() + (parent_geometry.width() - window_size.width()) // 2
                y = parent_geometry.y() + (parent_geometry.height() - window_size.height()) // 2
                
                window.move(x, y)
            else:
                # 相对于屏幕居中
                screen = QApplication.primaryScreen()
                if screen:
                    screen_geometry = screen.availableGeometry()
                    window_size = window.size()
                    
                    x = (screen_geometry.width() - window_size.width()) // 2
                    y = (screen_geometry.height() - window_size.height()) // 2
                    
                    window.move(x, y)
        except Exception as e:
            logger.warning(f"窗口居中失败: {e}")
    
    @staticmethod
    def get_screen_geometry() -> QRect:
        """获取屏幕几何信息"""
        try:
            screen = QApplication.primaryScreen()
            if screen:
                return screen.availableGeometry()
            else:
                return QRect(0, 0, 1920, 1080)  # 默认值
        except Exception as e:
            logger.warning(f"获取屏幕几何信息失败: {e}")
            return QRect(0, 0, 1920, 1080)
    
    @staticmethod
    def scale_size(size: QSize, scale_factor: float) -> QSize:
        """按比例缩放尺寸"""
        return QSize(
            int(size.width() * scale_factor),
            int(size.height() * scale_factor)
        )
    
    @staticmethod
    def load_icon(icon_path: str, size: Optional[Tuple[int, int]] = None) -> QIcon:
        """加载图标"""
        try:
            icon_path = Path(icon_path)
            
            if icon_path.exists():
                pixmap = QPixmap(str(icon_path))
                
                if size:
                    pixmap = pixmap.scaled(
                        size[0], size[1],
                        aspectRatioMode=1,  # Qt.KeepAspectRatio
                        transformMode=1     # Qt.SmoothTransformation
                    )
                
                return QIcon(pixmap)
            else:
                logger.warning(f"图标文件不存在: {icon_path}")
                return QIcon()
                
        except Exception as e:
            logger.error(f"加载图标失败: {e}")
            return QIcon()
    
    @staticmethod
    def show_info_message(parent: QWidget, title: str, message: str):
        """显示信息消息框"""
        QMessageBox.information(parent, title, message)
    
    @staticmethod
    def show_warning_message(parent: QWidget, title: str, message: str):
        """显示警告消息框"""
        QMessageBox.warning(parent, title, message)
    
    @staticmethod
    def show_error_message(parent: QWidget, title: str, message: str):
        """显示错误消息框"""
        QMessageBox.critical(parent, title, message)
    
    @staticmethod
    def show_question_message(parent: QWidget, title: str, message: str) -> bool:
        """显示询问消息框"""
        reply = QMessageBox.question(
            parent, title, message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes
    
    @staticmethod
    def get_file_size_string(size_bytes: int) -> str:
        """将字节数转换为可读的文件大小字符串"""
        try:
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
        except Exception as e:
            logger.error(f"文件大小转换失败: {e}")
            return f"{size_bytes} B"
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """截断文本"""
        if len(text) <= max_length:
            return text
        else:
            return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def validate_window_geometry(geometry: QRect) -> QRect:
        """验证窗口几何信息是否有效"""
        screen_geometry = GuiUtils.get_screen_geometry()
        
        # 确保窗口在屏幕范围内
        if geometry.x() < 0:
            geometry.setX(0)
        if geometry.y() < 0:
            geometry.setY(0)
        
        if geometry.right() > screen_geometry.right():
            geometry.setX(screen_geometry.right() - geometry.width())
        if geometry.bottom() > screen_geometry.bottom():
            geometry.setY(screen_geometry.bottom() - geometry.height())
        
        # 确保窗口有最小尺寸
        min_width = 400
        min_height = 300
        
        if geometry.width() < min_width:
            geometry.setWidth(min_width)
        if geometry.height() < min_height:
            geometry.setHeight(min_height)
        
        return geometry
    
    @staticmethod
    def get_color_for_severity(severity: str) -> str:
        """根据严重程度获取颜色"""
        color_map = {
            'error': '#dc3545',    # 红色
            'warning': '#ffc107',  # 黄色
            'info': '#17a2b8',     # 蓝色
            'success': '#28a745'   # 绿色
        }
        return color_map.get(severity.lower(), '#6c757d')  # 默认灰色
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化时间间隔"""
        try:
            if seconds < 1:
                return f"{seconds * 1000:.0f} ms"
            elif seconds < 60:
                return f"{seconds:.1f} s"
            elif seconds < 3600:
                minutes = int(seconds // 60)
                remaining_seconds = seconds % 60
                return f"{minutes}m {remaining_seconds:.0f}s"
            else:
                hours = int(seconds // 3600)
                remaining_minutes = int((seconds % 3600) // 60)
                return f"{hours}h {remaining_minutes}m"
        except Exception as e:
            logger.error(f"时间格式化失败: {e}")
            return f"{seconds:.1f} s"
    
    @staticmethod
    def create_separator_line() -> QWidget:
        """创建分隔线"""
        from PySide6.QtWidgets import QFrame
        
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        return line
    
    @staticmethod
    def set_widget_background_color(widget: QWidget, color: str):
        """设置控件背景颜色"""
        try:
            widget.setStyleSheet(f"background-color: {color};")
        except Exception as e:
            logger.error(f"设置背景颜色失败: {e}")
    
    @staticmethod
    def set_widget_text_color(widget: QWidget, color: str):
        """设置控件文本颜色"""
        try:
            current_style = widget.styleSheet()
            new_style = f"{current_style} color: {color};"
            widget.setStyleSheet(new_style)
        except Exception as e:
            logger.error(f"设置文本颜色失败: {e}")
    
    @staticmethod
    def get_available_fonts() -> List[str]:
        """获取可用字体列表"""
        try:
            from PySide6.QtGui import QFontDatabase
            font_db = QFontDatabase()
            return font_db.families()
        except Exception as e:
            logger.error(f"获取字体列表失败: {e}")
            return ["Arial", "Microsoft YaHei", "SimSun"]
    
    @staticmethod
    def is_dark_theme() -> bool:
        """检测是否为深色主题"""
        try:
            app = QApplication.instance()
            if app:
                palette = app.palette()
                window_color = palette.color(palette.Window)
                # 如果窗口背景色较暗，则认为是深色主题
                return window_color.lightness() < 128
            return False
        except Exception as e:
            logger.error(f"检测主题失败: {e}")
            return False
    
    @staticmethod
    def apply_widget_style(widget: QWidget, style_dict: dict):
        """应用控件样式"""
        try:
            style_parts = []
            for property_name, value in style_dict.items():
                # 将下划线转换为连字符
                css_property = property_name.replace('_', '-')
                style_parts.append(f"{css_property}: {value}")
            
            style_string = "; ".join(style_parts) + ";"
            widget.setStyleSheet(style_string)
            
        except Exception as e:
            logger.error(f"应用控件样式失败: {e}")
    
    @staticmethod
    def save_widget_geometry(widget: QWidget, settings_key: str):
        """保存控件几何信息"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings()
            settings.setValue(f"{settings_key}/geometry", widget.saveGeometry())
            if hasattr(widget, 'saveState'):
                settings.setValue(f"{settings_key}/state", widget.saveState())
        except Exception as e:
            logger.error(f"保存控件几何信息失败: {e}")
    
    @staticmethod
    def restore_widget_geometry(widget: QWidget, settings_key: str):
        """恢复控件几何信息"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings()
            
            geometry = settings.value(f"{settings_key}/geometry")
            if geometry:
                widget.restoreGeometry(geometry)
            
            if hasattr(widget, 'restoreState'):
                state = settings.value(f"{settings_key}/state")
                if state:
                    widget.restoreState(state)
        except Exception as e:
            logger.error(f"恢复控件几何信息失败: {e}")
