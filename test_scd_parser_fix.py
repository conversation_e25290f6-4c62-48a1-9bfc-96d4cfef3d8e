#!/usr/bin/env python3
"""
测试修复后的SCD解析器
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

try:
    from core.scd_parser import SCDParser
    
    print("测试修复后的SCD解析器")
    print("=" * 50)
    
    # 创建解析器实例
    parser = SCDParser()
    
    # 测试正确格式的SCD文件
    print("\n1. 测试正确格式的SCD文件...")
    result = parser.parse_scd_file('test_data/test_correct.scd')
    
    print(f"解析结果: {result.success}")
    if result.success:
        print(f"数据键: {list(result.data.keys())}")
        print(f"IED数量: {len(result.data.get('ieds', []))}")
        print("✅ SCD解析修复成功！")
    else:
        print(f"错误: {result.errors}")
        print("❌ SCD解析仍有问题")
    
    print("\n2. 测试IEC61850验证器...")
    try:
        from core.iec61850_validator import IEC61850Validator
        
        validator = IEC61850Validator()
        if result.success and result.data:
            validation_result = validator.validate_ld_ln_do_da_structure(result.data)
            print(f"验证结果: {validation_result.success}")
            print(f"验证得分: {validation_result.score:.1f}/100")
            print("✅ IEC61850验证器工作正常！")
        else:
            print("⚠️ 无法测试验证器，因为解析失败")
            
    except Exception as e:
        print(f"❌ IEC61850验证器测试失败: {e}")
    
    print("\n3. 测试知识推理引擎...")
    try:
        from knowledge.reasoning.iec61850_logic_verification_engine import IEC61850LogicVerificationEngine
        
        engine = IEC61850LogicVerificationEngine()
        print("✅ IEC61850逻辑验证引擎导入成功！")
        
    except Exception as e:
        print(f"❌ 知识推理引擎测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("修复测试完成！")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()