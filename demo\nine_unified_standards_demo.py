#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
继电保护九统一标准演示脚本
展示如何使用九统一标准知识库进行智能变电站二次回路标准化分析
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 直接从文件路径导入，避免复杂的依赖问题
from src.knowledge.standards.relay_protection_nine_unified import RelayProtectionNineUnified
from src.knowledge.standards.comprehensive_circuit_knowledge import ComprehensiveCircuitKnowledge


def demo_nine_unified_standards():
    print("继电保护九统一标准演示")
    print("=" * 50)
    
    # 1. 初始化九统一标准知识库
    print("\n1. 初始化继电保护九统一标准知识库...")
    try:
        nine_unified_knowledge = RelayProtectionNineUnified()
        print("  ✓ 知识库初始化成功")
    except Exception as e:
        print(f"  ✗ 知识库初始化失败: {e}")
        return
    
    # 2. 展示九统一标准
    print("\n2. 继电保护九统一标准:")
    try:
        standards = nine_unified_knowledge.get_all_standards()
        for standard in standards:
            print(f"  - {standard.name}")
            print(f"    标准编号: {standard.standard_number}")
            print(f"    描述: {standard.description}")
            print(f"    内容概要: {standard.content[:100]}...")
            print()
        print(f"  ✓ 成功获取 {len(standards)} 个标准")
    except Exception as e:
        print(f"  ✗ 获取标准失败: {e}")
    
    # 3. 展示九统一规则
    print("\n3. 九统一规则:")
    try:
        rules = nine_unified_knowledge.get_all_rules()
        for rule in rules:
            print(f"  - {rule.name}")
            print(f"    描述: {rule.description}")
            print(f"    严重程度: {rule.severity}")
            print(f"    类别: {rule.category}")
            print(f"    适用设备: {', '.join(rule.applicable_devices) if rule.applicable_devices else '无'}")
            print(f"    关键词: {', '.join(rule.keywords) if rule.keywords else '无'}")
            print()
        print(f"  ✓ 成功获取 {len(rules)} 条规则")
    except Exception as e:
        print(f"  ✗ 获取规则失败: {e}")
    
    # 4. 展示九统一技术要求
    print("\n4. 九统一技术要求:")
    try:
        requirements = nine_unified_knowledge.get_all_requirements()
        for req in requirements:
            print(f"  - {req.name}")
            print(f"    描述: {req.description}")
            print(f"    优先级: {req.priority}")
            print(f"    强制性: {'是' if req.mandatory else '否'}")
            print(f"    内容概要: {req.content[:100]}...")
            print()
        print(f"  ✓ 成功获取 {len(requirements)} 项技术要求")
    except Exception as e:
        print(f"  ✗ 获取技术要求失败: {e}")
    
    # 5. 初始化综合回路知识库
    print("\n5. 初始化综合回路知识库...")
    try:
        circuit_knowledge = ComprehensiveCircuitKnowledge()
        print("  ✓ 综合回路知识库初始化成功")
    except Exception as e:
        print(f"  ✗ 综合回路知识库初始化失败: {e}")
        return
    
    # 6. 展示保护回路的九统一标准
    print("\n6. 保护回路九统一标准:")
    try:
        protection_nine_unified = circuit_knowledge.get_nine_unified_standards('protection_circuit')
        if protection_nine_unified:
            print(f"  名称: {protection_nine_unified.get('name', 'N/A')}")
            principles = protection_nine_unified.get('principles', {})
            for principle_category, principle_details in principles.items():
                print(f"  {principle_category}:")
                for key, value in principle_details.items():
                    print(f"    {key}: {value}")
            
            technical_requirements = protection_nine_unified.get('technical_requirements', [])
            print("  技术要求:")
            for req in technical_requirements:
                print(f"    - {req}")
            
            common_violations = protection_nine_unified.get('common_violations', [])
            print("  常见违规项及预防措施:")
            for violation in common_violations:
                print(f"    违规类型: {violation.get('type', 'N/A')}")
                print(f"    违规原因: {', '.join(violation.get('causes', []))}")
                print(f"    预防措施: {', '.join(violation.get('prevention', []))}")
                print()
            print("  ✓ 成功获取保护回路九统一标准")
        else:
            print("  未找到保护回路九统一标准")
    except Exception as e:
        print(f"  ✗ 获取保护回路九统一标准失败: {e}")
    
    print("\n演示完成！")


if __name__ == "__main__":
    demo_nine_unified_standards()