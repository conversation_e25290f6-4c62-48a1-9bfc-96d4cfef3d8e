<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .goose-line { stroke: purple; stroke-width: 2; fill: none; stroke-dasharray: 10,5; }
            .sv-line { stroke: orange; stroke-width: 2; fill: none; stroke-dasharray: 15,5; }
            .hardwire-line { stroke: gray; stroke-width: 1; fill: none; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="1000" height="800" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="500" y="30" text-anchor="middle" class="title-text">保护回路图</text>
    <text x="500" y="50" text-anchor="middle" font-size="12" fill="#666">基于GOOSE和SV虚端子连接</text>
    
    <!-- 图例 -->
    <g transform="translate(20, 80)">
        <text x="0" y="0" font-size="12" font-weight="bold">图例:</text>
        <line x1="0" y1="15" x2="40" y2="15" class="goose-line"/>
        <text x="45" y="19" font-size="10">GOOSE连接</text>
        <line x1="0" y1="30" x2="40" y2="30" class="sv-line"/>
        <text x="45" y="34" font-size="10">SV采样值</text>
        <line x1="0" y1="45" x2="40" y2="45" class="hardwire-line"/>
        <text x="45" y="49" font-size="10">硬接线</text>
    </g>
    
    <!-- 保护功能框图 -->
    <g transform="translate(200, 150)">
        <!-- 采样值接收 -->
        <rect x="0" y="0" width="120" height="60" fill="lightblue" stroke="black"/>
        <text x="60" y="35" text-anchor="middle" font-size="10" font-weight="bold">SV采样值接收</text>
        
        <!-- 保护算法 -->
        <rect x="200" y="0" width="120" height="60" fill="lightgreen" stroke="black"/>
        <text x="260" y="25" text-anchor="middle" font-size="10" font-weight="bold">保护算法</text>
        <text x="260" y="40" text-anchor="middle" font-size="8">距离保护</text>
        <text x="260" y="50" text-anchor="middle" font-size="8">差动保护</text>
        
        <!-- GOOSE发送 -->
        <rect x="400" y="0" width="120" height="60" fill="lightyellow" stroke="black"/>
        <text x="460" y="35" text-anchor="middle" font-size="10" font-weight="bold">GOOSE发送</text>
        
        <!-- 连接线 -->
        <line x1="120" y1="30" x2="200" y2="30" stroke="black" stroke-width="2"/>
        <line x1="320" y1="30" x2="400" y2="30" stroke="black" stroke-width="2"/>
        
        <!-- 标注 -->
        <text x="160" y="25" text-anchor="middle" font-size="8">电流/电压</text>
        <text x="360" y="25" text-anchor="middle" font-size="8">跳闸命令</text>
    </g>
    
    <!-- 虚端子连接示意 -->
    <g transform="translate(100, 300)">
        <text x="0" y="0" font-size="14" font-weight="bold">虚端子连接关系:</text>
        
        <!-- 发送端 -->
        <rect x="0" y="20" width="150" height="80" fill="lightcoral" stroke="black"/>
        <text x="75" y="40" text-anchor="middle" font-size="10" font-weight="bold">发送端IED</text>
        <text x="75" y="55" text-anchor="middle" font-size="8">ProtectionIED1</text>
        <text x="75" y="70" text-anchor="middle" font-size="8">GOOSE: Trip_Out</text>
        <text x="75" y="85" text-anchor="middle" font-size="8">SV: Current_A</text>
        
        <!-- 接收端 -->
        <rect x="300" y="20" width="150" height="80" fill="lightsteelblue" stroke="black"/>
        <text x="375" y="40" text-anchor="middle" font-size="10" font-weight="bold">接收端IED</text>
        <text x="375" y="55" text-anchor="middle" font-size="8">ProtectionIED2</text>
        <text x="375" y="70" text-anchor="middle" font-size="8">GOOSE: Trip_In</text>
        <text x="375" y="85" text-anchor="middle" font-size="8">SV: Current_B</text>
        
        <!-- 虚端子连接 -->
        <line x1="150" y1="50" x2="300" y2="50" class="goose-line"/>
        <text x="225" y="45" text-anchor="middle" font-size="8">GOOSE虚端子</text>
        
        <line x1="150" y1="70" x2="300" y2="70" class="sv-line"/>
        <text x="225" y="85" text-anchor="middle" font-size="8">SV虚端子</text>
    </g>
    
    <!-- 说明文字 -->
    <g transform="translate(20, 500)">
        <text x="0" y="0" font-size="14" font-weight="bold">虚端子技术优势:</text>
        <text x="0" y="25" font-size="11">1. 减少硬接线，提高系统可靠性</text>
        <text x="0" y="45" font-size="11">2. 实现设备间的数字化通信</text>
        <text x="0" y="65" font-size="11">3. 支持复杂的保护逻辑配置</text>
        <text x="0" y="85" font-size="11">4. 便于系统维护和扩展</text>
        
        <text x="400" y="0" font-size="14" font-weight="bold">传统回路对比:</text>
        <text x="400" y="25" font-size="11">1. 传统硬接线复杂，故障率高</text>
        <text x="400" y="45" font-size="11">2. 虚端子实现点对点数字连接</text>
        <text x="400" y="65" font-size="11">3. 配置灵活，易于修改</text>
        <text x="400" y="85" font-size="11">4. 支持远程监控和诊断</text>
    </g>
    
</svg>