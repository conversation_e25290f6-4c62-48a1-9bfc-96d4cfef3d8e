"""
规则配置管理
提供规则的配置、启用/禁用等管理功能
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict
import logging

from .base import RuleCategory, RuleSeverity
from .registry import RuleRegistry, rule_registry


@dataclass
class RuleConfig:
    """单个规则的配置"""
    # 规则ID
    rule_id: str
    
    # 是否启用
    enabled: bool = True
    
    # 严重程度覆盖
    severity_override: Optional[RuleSeverity] = None
    
    # 规则参数
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # 描述信息
    description: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        if self.severity_override:
            result['severity_override'] = self.severity_override.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RuleConfig':
        """从字典创建"""
        if 'severity_override' in data and data['severity_override']:
            data['severity_override'] = RuleSeverity(data['severity_override'])
        return cls(**data)


@dataclass
class RuleSetConfig:
    """规则集配置"""
    # 配置名称
    name: str
    
    # 配置描述
    description: str = ""
    
    # 版本
    version: str = "1.0"
    
    # 全局配置
    global_config: Dict[str, Any] = field(default_factory=dict)
    
    # 规则配置
    rules: Dict[str, RuleConfig] = field(default_factory=dict)
    
    # 类别配置
    category_config: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'global_config': self.global_config,
            'rules': {rule_id: config.to_dict() for rule_id, config in self.rules.items()},
            'category_config': self.category_config
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RuleSetConfig':
        """从字典创建"""
        rules = {}
        if 'rules' in data:
            for rule_id, rule_data in data['rules'].items():
                rule_data['rule_id'] = rule_id
                rules[rule_id] = RuleConfig.from_dict(rule_data)
        
        return cls(
            name=data.get('name', ''),
            description=data.get('description', ''),
            version=data.get('version', '1.0'),
            global_config=data.get('global_config', {}),
            rules=rules,
            category_config=data.get('category_config', {})
        )


class RuleConfigManager:
    """规则配置管理器"""
    
    def __init__(self, registry: Optional[RuleRegistry] = None):
        """
        初始化配置管理器
        
        Args:
            registry: 规则注册表
        """
        self.registry = registry or rule_registry
        self.logger = logging.getLogger(self.__class__.__name__)
        self._current_config: Optional[RuleSetConfig] = None
    
    def create_default_config(self, name: str = "default") -> RuleSetConfig:
        """
        创建默认配置
        
        Args:
            name: 配置名称
            
        Returns:
            默认配置
        """
        config = RuleSetConfig(
            name=name,
            description="默认规则配置",
            version="1.0"
        )
        
        # 为所有注册的规则创建默认配置
        for rule in self.registry.get_all_rules():
            rule_config = RuleConfig(
                rule_id=rule.rule_id,
                enabled=rule.enabled,
                description=rule.description
            )
            config.rules[rule.rule_id] = rule_config
        
        return config
    
    def load_config(self, file_path: Union[str, Path]) -> RuleSetConfig:
        """
        从文件加载配置
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            规则配置
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 配置格式错误
        """
        path = Path(file_path)
        
        if not path.exists():
            raise FileNotFoundError(f"配置文件不存在: {path}")
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() in ['.yaml', '.yml']:
                    data = yaml.safe_load(f)
                elif path.suffix.lower() == '.json':
                    data = json.load(f)
                else:
                    raise ValueError(f"不支持的配置文件格式: {path.suffix}")
            
            config = RuleSetConfig.from_dict(data)
            self.logger.info(f"加载配置文件: {path}")
            return config
            
        except Exception as e:
            raise ValueError(f"解析配置文件失败: {e}")
    
    def save_config(self, config: RuleSetConfig, file_path: Union[str, Path],
                   format: str = 'yaml') -> None:
        """
        保存配置到文件
        
        Args:
            config: 规则配置
            file_path: 保存路径
            format: 文件格式 ('yaml' 或 'json')
        """
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        
        data = config.to_dict()
        
        try:
            with open(path, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    yaml.dump(data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif format.lower() == 'json':
                    json.dump(data, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的格式: {format}")
            
            self.logger.info(f"保存配置文件: {path}")
            
        except Exception as e:
            raise ValueError(f"保存配置文件失败: {e}")
    
    def apply_config(self, config: RuleSetConfig) -> None:
        """
        应用配置到规则注册表
        
        Args:
            config: 要应用的配置
        """
        self._current_config = config
        
        # 应用规则配置
        for rule_id, rule_config in config.rules.items():
            rule = self.registry.get_rule(rule_id)
            if rule:
                # 应用启用状态
                rule.enabled = rule_config.enabled
                
                # 应用严重程度覆盖
                if rule_config.severity_override:
                    rule.severity = rule_config.severity_override
                
                self.logger.debug(f"应用规则配置: {rule_id}")
            else:
                self.logger.warning(f"规则不存在，跳过配置: {rule_id}")
        
        self.logger.info(f"应用配置: {config.name}")
    
    def get_current_config(self) -> Optional[RuleSetConfig]:
        """
        获取当前配置
        
        Returns:
            当前配置
        """
        return self._current_config
    
    def enable_rule(self, rule_id: str) -> bool:
        """
        启用规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            是否成功
        """
        rule = self.registry.get_rule(rule_id)
        if rule:
            rule.enabled = True
            
            # 更新当前配置
            if self._current_config and rule_id in self._current_config.rules:
                self._current_config.rules[rule_id].enabled = True
            
            self.logger.info(f"启用规则: {rule_id}")
            return True
        
        return False
    
    def disable_rule(self, rule_id: str) -> bool:
        """
        禁用规则
        
        Args:
            rule_id: 规则ID
            
        Returns:
            是否成功
        """
        rule = self.registry.get_rule(rule_id)
        if rule:
            rule.enabled = False
            
            # 更新当前配置
            if self._current_config and rule_id in self._current_config.rules:
                self._current_config.rules[rule_id].enabled = False
            
            self.logger.info(f"禁用规则: {rule_id}")
            return True
        
        return False
    
    def enable_category(self, category: RuleCategory) -> int:
        """
        启用类别下的所有规则
        
        Args:
            category: 规则类别
            
        Returns:
            启用的规则数量
        """
        rules = self.registry.get_rules_by_category(category)
        count = 0
        
        for rule in rules:
            if self.enable_rule(rule.rule_id):
                count += 1
        
        self.logger.info(f"启用类别 {category.value} 下的 {count} 个规则")
        return count
    
    def disable_category(self, category: RuleCategory) -> int:
        """
        禁用类别下的所有规则
        
        Args:
            category: 规则类别
            
        Returns:
            禁用的规则数量
        """
        rules = self.registry.get_rules_by_category(category)
        count = 0
        
        for rule in rules:
            if self.disable_rule(rule.rule_id):
                count += 1
        
        self.logger.info(f"禁用类别 {category.value} 下的 {count} 个规则")
        return count
    
    def set_rule_severity(self, rule_id: str, severity: RuleSeverity) -> bool:
        """
        设置规则严重程度
        
        Args:
            rule_id: 规则ID
            severity: 严重程度
            
        Returns:
            是否成功
        """
        rule = self.registry.get_rule(rule_id)
        if rule:
            rule.severity = severity
            
            # 更新当前配置
            if self._current_config:
                if rule_id not in self._current_config.rules:
                    self._current_config.rules[rule_id] = RuleConfig(rule_id=rule_id)
                self._current_config.rules[rule_id].severity_override = severity
            
            self.logger.info(f"设置规则 {rule_id} 严重程度为 {severity.value}")
            return True
        
        return False
    
    def get_rule_config(self, rule_id: str) -> Optional[RuleConfig]:
        """
        获取规则配置
        
        Args:
            rule_id: 规则ID
            
        Returns:
            规则配置
        """
        if self._current_config and rule_id in self._current_config.rules:
            return self._current_config.rules[rule_id]
        
        # 如果没有配置，从注册表创建默认配置
        rule = self.registry.get_rule(rule_id)
        if rule:
            return RuleConfig(
                rule_id=rule_id,
                enabled=rule.enabled,
                description=rule.description
            )
        
        return None
    
    def validate_config(self, config: RuleSetConfig) -> List[str]:
        """
        验证配置
        
        Args:
            config: 要验证的配置
            
        Returns:
            验证问题列表
        """
        issues = []
        
        # 检查规则是否存在
        for rule_id in config.rules:
            if not self.registry.get_rule(rule_id):
                issues.append(f"规则不存在: {rule_id}")
        
        # 检查类别配置
        for category_name in config.category_config:
            try:
                RuleCategory(category_name)
            except ValueError:
                issues.append(f"无效的规则类别: {category_name}")
        
        return issues
    
    def get_config_summary(self, config: Optional[RuleSetConfig] = None) -> Dict[str, Any]:
        """
        获取配置摘要
        
        Args:
            config: 配置对象，如果为None则使用当前配置
            
        Returns:
            配置摘要
        """
        if config is None:
            config = self._current_config
        
        if not config:
            return {'error': '没有配置'}
        
        enabled_count = sum(1 for rule_config in config.rules.values() if rule_config.enabled)
        disabled_count = len(config.rules) - enabled_count
        
        severity_overrides = sum(1 for rule_config in config.rules.values() 
                               if rule_config.severity_override)
        
        return {
            'name': config.name,
            'description': config.description,
            'version': config.version,
            'total_rules': len(config.rules),
            'enabled_rules': enabled_count,
            'disabled_rules': disabled_count,
            'severity_overrides': severity_overrides,
            'has_global_config': bool(config.global_config),
            'category_configs': len(config.category_config)
        }
