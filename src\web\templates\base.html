<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-bolt me-2"></i>
                {{ app_name }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% for nav_item in navigation %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ nav_item.url }}">
                            <i class="fas fa-{{ nav_item.icon }} me-1"></i>
                            {{ nav_item.name }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>
                            系统
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.admin_page') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>管理界面
                            </a></li>
                            <li><a class="dropdown-item" href="/api/docs">
                                <i class="fas fa-code me-2"></i>API文档
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.about_page') }}">
                                <i class="fas fa-info-circle me-2"></i>关于
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">{{ app_name }}</h6>
                    <p class="text-muted small">{{ app_description }}</p>
                    <p class="text-muted small mb-0">
                        <strong>项目使命：</strong>解决智能变电站二次设计的实际痛点
                    </p>
                </div>
                <div class="col-md-3">
                    <h6>核心功能</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-check text-success me-1"></i>虚端子连接可视化</li>
                        <li><i class="fas fa-check text-success me-1"></i>配置错误智能诊断</li>
                        <li><i class="fas fa-check text-success me-1"></i>传统回路检查数字化</li>
                        <li><i class="fas fa-check text-success me-1"></i>设计质量自动保证</li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>技术支持</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-file-code me-1"></i>IEC61850标准</li>
                        <li><i class="fas fa-cogs me-1"></i>智能规则引擎</li>
                        <li><i class="fas fa-chart-line me-1"></i>可视化分析</li>
                        <li><i class="fas fa-shield-alt me-1"></i>专业验证</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 text-center">
                    <p class="text-muted small mb-0">
                        &copy; 2024 {{ app_name }} v{{ app_version }} | 
                        专为智能变电站工程师设计
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block extra_js %}{% endblock %}

    <!-- 全局提示框 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="globalToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">系统提示</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body"></div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3 text-primary">正在处理，请稍候...</p>
        </div>
    </div>
</body>
</html>
