#!/usr/bin/env python3
"""
测试IEC61850设计检查器API接口
"""

import requests
import json
import time

def test_api():
    """测试API功能"""
    base_url = "http://127.0.0.1:5000/api"
    
    print("🔧 测试IEC61850设计检查器API")
    print("=" * 50)
    
    # 1. 测试系统状态
    print("1. 测试系统状态...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 系统状态: {data['data']['system']['status']}")
            print(f"   ✓ 规则数量: {data['data']['rules']['total']}")
            print(f"   ✓ 支持格式: {', '.join(data['data']['capabilities']['supported_formats'])}")
        else:
            print(f"   ❌ 状态检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 状态检查异常: {e}")
        return False
    
    # 2. 测试文件上传
    print("\n2. 测试文件上传...")
    try:
        with open("tests/fixtures/sample.scd", "rb") as f:
            files = {"file": ("sample.scd", f, "application/xml")}
            response = requests.post(f"{base_url}/upload", files=files)
        
        if response.status_code == 200:
            upload_data = response.json()
            if upload_data["success"]:
                file_id = upload_data["data"]["file_id"]
                print(f"   ✓ 文件上传成功: {file_id}")
                print(f"   ✓ 文件大小: {upload_data['data']['file_info']['size']} 字节")
            else:
                print(f"   ❌ 上传失败: {upload_data['error']}")
                return False
        else:
            print(f"   ❌ 上传请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 文件上传异常: {e}")
        return False
    
    # 3. 测试文件验证
    print("\n3. 测试文件验证...")
    try:
        validation_config = {
            "max_workers": 2,
            "rule_timeout": 30.0,
            "stop_on_error": False
        }
        
        response = requests.post(
            f"{base_url}/validate/{file_id}",
            json=validation_config,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            validation_data = response.json()
            if validation_data["success"]:
                summary = validation_data["data"]["validation_summary"]
                print(f"   ✓ 验证完成")
                print(f"   ✓ 执行规则: {summary['executed_rules']}")
                print(f"   ✓ 发现错误: {summary['errors']}")
                print(f"   ✓ 发现警告: {summary['warnings']}")
                print(f"   ✓ 信息条目: {summary['infos']}")
                print(f"   ✓ 执行时间: {summary['total_time']:.3f}s")
                
                report_id = validation_data["data"]["report_id"]
                print(f"   ✓ 报告ID: {report_id}")
                
                # 4. 测试报告获取
                print("\n4. 测试报告获取...")
                report_response = requests.get(f"{base_url}/report/{report_id}")
                if report_response.status_code == 200:
                    report_data = report_response.json()
                    if report_data["success"]:
                        print(f"   ✓ 报告获取成功")
                        print(f"   ✓ 报告标题: {report_data['data']['title']}")
                        print(f"   ✓ 生成时间: {report_data['data']['generated_at']}")
                    else:
                        print(f"   ❌ 报告获取失败: {report_data['error']}")
                else:
                    print(f"   ❌ 报告请求失败: {report_response.status_code}")
                
            else:
                print(f"   ❌ 验证失败: {validation_data['error']}")
                return False
        else:
            print(f"   ❌ 验证请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 文件验证异常: {e}")
        return False
    
    print("\n🎉 API测试完成！所有功能正常工作")
    return True

if __name__ == "__main__":
    success = test_api()
    if not success:
        exit(1)
