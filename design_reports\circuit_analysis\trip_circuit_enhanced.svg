<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <style>
            .title-text { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #333; }
            .trip-line { stroke: red; stroke-width: 3; fill: none; }
            .close-line { stroke: green; stroke-width: 2; fill: none; }
            .aux-line { stroke: blue; stroke-width: 1; fill: none; stroke-dasharray: 3,3; }
        </style>
    </defs>
    
    <!-- 背景 -->
    <rect width="900" height="600" fill="#f8f9fa" stroke="#ddd"/>
    
    <!-- 标题 -->
    <text x="450" y="30" text-anchor="middle" class="title-text">跳闸回路图</text>
    <text x="450" y="50" text-anchor="middle" font-size="12" fill="#666">断路器控制回路</text>
    
    <!-- 跳闸回路 -->
    <g transform="translate(100, 100)">
        <text x="0" y="0" font-size="14" font-weight="bold">跳闸回路:</text>
        
        <!-- 保护装置输出 -->
        <rect x="0" y="20" width="80" height="40" fill="lightgreen" stroke="black"/>
        <text x="40" y="45" text-anchor="middle" font-size="9">保护装置</text>
        
        <!-- 跳闸继电器 -->
        <rect x="150" y="20" width="60" height="40" fill="lightblue" stroke="black"/>
        <text x="180" y="45" text-anchor="middle" font-size="9">跳闸继电器</text>
        
        <!-- 断路器跳闸线圈 -->
        <rect x="280" y="20" width="80" height="40" fill="lightcoral" stroke="black"/>
        <text x="320" y="45" text-anchor="middle" font-size="9">跳闸线圈</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="150" y2="40" class="trip-line"/>
        <line x1="210" y1="40" x2="280" y2="40" class="trip-line"/>
        
        <!-- 标注 -->
        <text x="115" y="35" text-anchor="middle" font-size="8">GOOSE</text>
        <text x="245" y="35" text-anchor="middle" font-size="8">触点</text>
    </g>
    
    <!-- 合闸回路 -->
    <g transform="translate(100, 200)">
        <text x="0" y="0" font-size="14" font-weight="bold">合闸回路:</text>
        
        <!-- 控制开关 -->
        <rect x="0" y="20" width="80" height="40" fill="lightyellow" stroke="black"/>
        <text x="40" y="45" text-anchor="middle" font-size="9">控制开关</text>
        
        <!-- 合闸继电器 -->
        <rect x="150" y="20" width="60" height="40" fill="lightblue" stroke="black"/>
        <text x="180" y="45" text-anchor="middle" font-size="9">合闸继电器</text>
        
        <!-- 断路器合闸线圈 -->
        <rect x="280" y="20" width="80" height="40" fill="lightgreen" stroke="black"/>
        <text x="320" y="45" text-anchor="middle" font-size="9">合闸线圈</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="150" y2="40" class="close-line"/>
        <line x1="210" y1="40" x2="280" y2="40" class="close-line"/>
        
        <!-- 标注 -->
        <text x="115" y="35" text-anchor="middle" font-size="8">手动</text>
        <text x="245" y="35" text-anchor="middle" font-size="8">触点</text>
    </g>
    
    <!-- 辅助回路 -->
    <g transform="translate(100, 300)">
        <text x="0" y="0" font-size="14" font-weight="bold">辅助回路:</text>
        
        <!-- 位置信号 -->
        <rect x="0" y="20" width="80" height="40" fill="lightsteelblue" stroke="black"/>
        <text x="40" y="45" text-anchor="middle" font-size="9">位置信号</text>
        
        <!-- 信号继电器 -->
        <rect x="150" y="20" width="60" height="40" fill="lightblue" stroke="black"/>
        <text x="180" y="45" text-anchor="middle" font-size="9">信号继电器</text>
        
        <!-- 指示灯 -->
        <circle cx="320" cy="40" r="20" fill="yellow" stroke="black"/>
        <text x="320" y="45" text-anchor="middle" font-size="8">指示灯</text>
        
        <!-- 连接线 -->
        <line x1="80" y1="40" x2="150" y2="40" class="aux-line"/>
        <line x1="210" y1="40" x2="300" y2="40" class="aux-line"/>
    </g>
    
    <!-- 说明 -->
    <g transform="translate(500, 150)">
        <text x="0" y="0" font-size="14" font-weight="bold">虚端子实现:</text>
        <text x="0" y="25" font-size="11">1. 保护装置通过GOOSE发送跳闸命令</text>
        <text x="0" y="45" font-size="11">2. 断路器IED接收GOOSE信号</text>
        <text x="0" y="65" font-size="11">3. 直接驱动跳闸线圈动作</text>
        <text x="0" y="85" font-size="11">4. 减少中间继电器环节</text>
        
        <text x="0" y="120" font-size="14" font-weight="bold">传统回路特点:</text>
        <text x="0" y="145" font-size="11">• 硬接线连接，可靠性高</text>
        <text x="0" y="165" font-size="11">• 中间继电器多，故障点多</text>
        <text x="0" y="185" font-size="11">• 接线复杂，维护困难</text>
        
        <text x="0" y="220" font-size="14" font-weight="bold">虚端子优势:</text>
        <text x="0" y="245" font-size="11">• 数字化连接，速度快</text>
        <text x="0" y="265" font-size="11">• 减少硬接线，提高可靠性</text>
        <text x="0" y="285" font-size="11">• 配置灵活，易于扩展</text>
    </g>
    
</svg>