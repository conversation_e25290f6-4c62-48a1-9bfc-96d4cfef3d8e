"""
知识规则引擎
基于知识库的高级规则处理引擎
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from ..base.knowledge_entity import RuleEntity, KnowledgeEntity, EntityType
from ..graph.knowledge_graph import KnowledgeGraph
from ...core.rules.base_rule import BaseRule, ValidationResult


logger = logging.getLogger(__name__)


class KnowledgeRuleEngine:
    """知识规则引擎"""
    
    def __init__(self, knowledge_graph: KnowledgeGraph):
        """
        初始化知识规则引擎
        
        Args:
            knowledge_graph: 知识图谱实例
        """
        self.graph = knowledge_graph
        self.dynamic_rules = {}  # 动态生成的规则
        self.rule_cache = {}     # 规则缓存
        
        logger.info("知识规则引擎初始化完成")
    
    def generate_dynamic_rule(self, rule_entity: RuleEntity) -> Optional[BaseRule]:
        """
        从知识实体生成动态规则
        
        Args:
            rule_entity: 规则实体
            
        Returns:
            Optional[BaseRule]: 生成的动态规则
        """
        try:
            # 检查缓存
            if rule_entity.id in self.rule_cache:
                return self.rule_cache[rule_entity.id]
            
            # 生成动态规则类
            dynamic_rule = self._create_dynamic_rule_class(rule_entity)
            
            if dynamic_rule:
                # 缓存规则
                self.rule_cache[rule_entity.id] = dynamic_rule
                logger.debug(f"生成动态规则: {rule_entity.name}")
            
            return dynamic_rule
            
        except Exception as e:
            logger.error(f"生成动态规则失败: {e}")
            return None
    
    def _create_dynamic_rule_class(self, rule_entity: RuleEntity) -> Optional[BaseRule]:
        """创建动态规则类"""
        try:
            class DynamicRule(BaseRule):
                def __init__(self):
                    super().__init__(
                        rule_id=rule_entity.id,
                        name=rule_entity.name,
                        description=rule_entity.description,
                        severity=rule_entity.severity,
                        category=rule_entity.category
                    )
                    self.rule_entity = rule_entity
                
                def validate(self, data: Any) -> ValidationResult:
                    """执行验证"""
                    try:
                        # 根据规则类型执行不同的验证逻辑
                        if rule_entity.rule_type == "syntax":
                            return self._validate_syntax(data)
                        elif rule_entity.rule_type == "semantic":
                            return self._validate_semantic(data)
                        elif rule_entity.rule_type == "business":
                            return self._validate_business(data)
                        else:
                            return self._validate_generic(data)
                    
                    except Exception as e:
                        return ValidationResult(
                            rule_id=self.rule_id,
                            passed=False,
                            message=f"规则执行失败: {e}",
                            severity=self.severity,
                            details={'error': str(e)}
                        )
                
                def _validate_syntax(self, data: Any) -> ValidationResult:
                    """语法验证"""
                    # 基于规则条件进行语法检查
                    condition = getattr(rule_entity, 'condition', '')
                    
                    if 'XML格式' in condition or 'format' in condition.lower():
                        # XML格式检查
                        if hasattr(data, 'tag'):  # 是XML元素
                            return ValidationResult(
                                rule_id=self.rule_id,
                                passed=True,
                                message="XML格式正确",
                                severity=self.severity
                            )
                        else:
                            return ValidationResult(
                                rule_id=self.rule_id,
                                passed=False,
                                message="数据不是有效的XML格式",
                                severity=self.severity
                            )
                    
                    # 默认语法验证
                    return ValidationResult(
                        rule_id=self.rule_id,
                        passed=True,
                        message="语法验证通过",
                        severity=self.severity
                    )
                
                def _validate_semantic(self, data: Any) -> ValidationResult:
                    """语义验证"""
                    # 基于规则内容进行语义检查
                    content = rule_entity.content
                    
                    # 检查必要元素
                    if '必须包含' in content:
                        required_elements = self._extract_required_elements(content)
                        missing_elements = []
                        
                        for element in required_elements:
                            if not self._check_element_exists(data, element):
                                missing_elements.append(element)
                        
                        if missing_elements:
                            return ValidationResult(
                                rule_id=self.rule_id,
                                passed=False,
                                message=f"缺少必要元素: {', '.join(missing_elements)}",
                                severity=self.severity,
                                details={'missing_elements': missing_elements}
                            )
                    
                    return ValidationResult(
                        rule_id=self.rule_id,
                        passed=True,
                        message="语义验证通过",
                        severity=self.severity
                    )
                
                def _validate_business(self, data: Any) -> ValidationResult:
                    """业务规则验证"""
                    # 基于业务逻辑进行验证
                    content = rule_entity.content
                    
                    # 检查配置要求
                    if '配置' in content and '要求' in content:
                        # 配置验证逻辑
                        if hasattr(data, 'get'):  # 字典类型数据
                            config_valid = self._validate_configuration(data, content)
                            if not config_valid:
                                return ValidationResult(
                                    rule_id=self.rule_id,
                                    passed=False,
                                    message="配置不符合业务要求",
                                    severity=self.severity
                                )
                    
                    return ValidationResult(
                        rule_id=self.rule_id,
                        passed=True,
                        message="业务规则验证通过",
                        severity=self.severity
                    )
                
                def _validate_generic(self, data: Any) -> ValidationResult:
                    """通用验证"""
                    # 基于规则描述进行通用验证
                    return ValidationResult(
                        rule_id=self.rule_id,
                        passed=True,
                        message=f"通用验证通过: {rule_entity.description}",
                        severity=self.severity
                    )
                
                def _extract_required_elements(self, content: str) -> List[str]:
                    """从规则内容中提取必要元素"""
                    import re
                    # 简化的元素提取
                    pattern = r'必须包含[：:]?\s*([^。，,]*)'
                    matches = re.findall(pattern, content)
                    elements = []
                    for match in matches:
                        elements.extend([elem.strip() for elem in match.split('、')])
                    return elements
                
                def _check_element_exists(self, data: Any, element: str) -> bool:
                    """检查元素是否存在"""
                    if hasattr(data, 'find'):  # XML元素
                        return data.find(f".//{element}") is not None
                    elif isinstance(data, dict):
                        return element in data
                    elif hasattr(data, element):
                        return True
                    return False
                
                def _validate_configuration(self, data: Dict[str, Any], content: str) -> bool:
                    """验证配置"""
                    # 简化的配置验证
                    if 'GOOSE' in content:
                        return 'goose' in data or 'GOOSE' in data
                    if 'SMV' in content:
                        return 'smv' in data or 'SMV' in data
                    return True
            
            return DynamicRule()
            
        except Exception as e:
            logger.error(f"创建动态规则类失败: {e}")
            return None
    
    def execute_knowledge_rules(self, 
                               data: Any,
                               context: Dict[str, Any] = None) -> List[ValidationResult]:
        """
        执行知识规则
        
        Args:
            data: 验证数据
            context: 验证上下文
            
        Returns:
            List[ValidationResult]: 验证结果列表
        """
        results = []
        
        try:
            # 获取适用的规则实体
            applicable_rule_entities = self._get_applicable_rule_entities(context or {})
            
            for rule_entity in applicable_rule_entities:
                # 生成动态规则
                dynamic_rule = self.generate_dynamic_rule(rule_entity)
                
                if dynamic_rule:
                    # 执行规则
                    result = dynamic_rule.validate(data)
                    results.append(result)
            
            logger.info(f"执行了 {len(results)} 条知识规则")
            return results
            
        except Exception as e:
            logger.error(f"执行知识规则失败: {e}")
            return []
    
    def _get_applicable_rule_entities(self, context: Dict[str, Any]) -> List[RuleEntity]:
        """获取适用的规则实体"""
        applicable_rules = []
        
        try:
            # 获取所有规则实体
            rule_entities = self.graph.get_entities_by_type(EntityType.RULE)
            
            for entity in rule_entities:
                if isinstance(entity, RuleEntity):
                    # 检查规则是否适用于当前上下文
                    if self._is_rule_applicable(entity, context):
                        applicable_rules.append(entity)
            
            return applicable_rules
            
        except Exception as e:
            logger.error(f"获取适用规则实体失败: {e}")
            return []
    
    def _is_rule_applicable(self, rule_entity: RuleEntity, context: Dict[str, Any]) -> bool:
        """检查规则是否适用"""
        try:
            # 检查设备类型
            device_type = context.get('device_type')
            if device_type and hasattr(rule_entity, 'applicable_devices'):
                if rule_entity.applicable_devices and device_type not in rule_entity.applicable_devices:
                    return False
            
            # 检查协议
            protocol = context.get('protocol')
            if protocol and hasattr(rule_entity, 'applicable_protocols'):
                if rule_entity.applicable_protocols and protocol not in rule_entity.applicable_protocols:
                    return False
            
            # 检查标准
            standard = context.get('standard')
            if standard and hasattr(rule_entity, 'applicable_standards'):
                if rule_entity.applicable_standards and standard not in rule_entity.applicable_standards:
                    return False
            
            # 检查类别
            category = context.get('category')
            if category and hasattr(rule_entity, 'category'):
                if rule_entity.category and rule_entity.category != category:
                    return False
            
            return True
            
        except Exception as e:
            logger.warning(f"规则适用性检查失败: {e}")
            return False
    
    def update_rule_knowledge(self, rule_entity: RuleEntity) -> bool:
        """
        更新规则知识
        
        Args:
            rule_entity: 更新的规则实体
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新图中的实体
            success = self.graph.add_entity(rule_entity)
            
            if success:
                # 清除缓存
                if rule_entity.id in self.rule_cache:
                    del self.rule_cache[rule_entity.id]
                
                logger.info(f"更新规则知识: {rule_entity.name}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新规则知识失败: {e}")
            return False
    
    def get_rule_statistics(self) -> Dict[str, Any]:
        """获取规则统计信息"""
        stats = {
            'total_rule_entities': 0,
            'dynamic_rules_generated': len(self.rule_cache),
            'rule_types': {},
            'rule_categories': {},
            'rule_severities': {}
        }
        
        try:
            rule_entities = self.graph.get_entities_by_type(EntityType.RULE)
            stats['total_rule_entities'] = len(rule_entities)
            
            for entity in rule_entities:
                if isinstance(entity, RuleEntity):
                    # 统计规则类型
                    rule_type = getattr(entity, 'rule_type', 'unknown')
                    stats['rule_types'][rule_type] = stats['rule_types'].get(rule_type, 0) + 1
                    
                    # 统计规则类别
                    category = getattr(entity, 'category', 'unknown')
                    stats['rule_categories'][category] = stats['rule_categories'].get(category, 0) + 1
                    
                    # 统计严重程度
                    severity = getattr(entity, 'severity', 'unknown')
                    stats['rule_severities'][severity] = stats['rule_severities'].get(severity, 0) + 1
            
        except Exception as e:
            logger.error(f"获取规则统计失败: {e}")
        
        return stats
    
    def clear_cache(self):
        """清除规则缓存"""
        self.rule_cache.clear()
        logger.info("规则缓存已清除")
