"""
改进后模块的综合测试
验证每个模块的独立性、接口设计和功能完整性
"""

import sys
import os
import importlib.util
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional


class ImprovedModuleTester:
    """改进后模块测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        
    def run_comprehensive_tests(self):
        """运行综合测试"""
        
        print("=" * 80)
        print("改进后模块综合测试")
        print("=" * 80)
        
        print("\n【测试目标】")
        print("• 验证模块独立性改进效果")
        print("• 检查面向对象设计实现")
        print("• 测试类型注解和错误处理")
        print("• 验证模块接口设计质量")
        
        # 定义改进后的模块
        improved_modules = [
            {
                'name': 'SCD解析模块',
                'path': 'src/core/scd_parser.py',
                'main_class': 'SCDParser',
                'key_methods': ['parse_scd_file', 'validate_scd_schema']
            },
            {
                'name': 'IEC61850验证模块',
                'path': 'src/core/iec61850_validator.py', 
                'main_class': 'IEC61850Validator',
                'key_methods': ['validate_ld_ln_do_da_structure', 'validate_goose_configuration']
            }
        ]
        
        # 测试每个模块
        for module_info in improved_modules:
            self.test_improved_module(module_info)
        
        # 生成对比报告
        self.generate_improvement_report()
    
    def test_improved_module(self, module_info: Dict[str, Any]):
        """测试改进后的模块"""
        
        module_name = module_info['name']
        module_path = module_info['path']
        
        print(f"\n{'='*60}")
        print(f"测试改进模块: {module_name}")
        print(f"路径: {module_path}")
        print('='*60)
        
        test_result = {
            'module_name': module_name,
            'module_path': module_path,
            'independence_score': 0,
            'oop_design_score': 0,
            'type_annotation_score': 0,
            'error_handling_score': 0,
            'interface_design_score': 0,
            'functionality_score': 0,
            'issues': [],
            'improvements': []
        }
        
        try:
            # 1. 检查文件存在性
            print("\n1. 文件存在性检查")
            if not os.path.exists(module_path):
                print(f"✗ 文件不存在: {module_path}")
                test_result['issues'].append("文件不存在")
                self.test_results[module_name] = test_result
                return
            
            print(f"✓ 文件存在: {module_path}")
            
            # 2. 读取模块内容
            with open(module_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 3. 测试模块独立性
            print("\n2. 模块独立性测试")
            independence_result = self._test_independence(content, module_path)
            test_result['independence_score'] = independence_result['score']
            test_result['issues'].extend(independence_result['issues'])
            test_result['improvements'].extend(independence_result['improvements'])
            
            # 4. 测试面向对象设计
            print("\n3. 面向对象设计测试")
            oop_result = self._test_oop_design(content, module_info)
            test_result['oop_design_score'] = oop_result['score']
            test_result['issues'].extend(oop_result['issues'])
            test_result['improvements'].extend(oop_result['improvements'])
            
            # 5. 测试类型注解
            print("\n4. 类型注解测试")
            typing_result = self._test_type_annotations(content)
            test_result['type_annotation_score'] = typing_result['score']
            test_result['issues'].extend(typing_result['issues'])
            test_result['improvements'].extend(typing_result['improvements'])
            
            # 6. 测试错误处理
            print("\n5. 错误处理测试")
            error_handling_result = self._test_error_handling(content)
            test_result['error_handling_score'] = error_handling_result['score']
            test_result['issues'].extend(error_handling_result['issues'])
            test_result['improvements'].extend(error_handling_result['improvements'])
            
            # 7. 测试接口设计
            print("\n6. 接口设计测试")
            interface_result = self._test_interface_design(content, module_info)
            test_result['interface_design_score'] = interface_result['score']
            test_result['issues'].extend(interface_result['issues'])
            test_result['improvements'].extend(interface_result['improvements'])
            
            # 8. 测试功能完整性
            print("\n7. 功能完整性测试")
            functionality_result = self._test_functionality(module_path, module_info)
            test_result['functionality_score'] = functionality_result['score']
            test_result['issues'].extend(functionality_result['issues'])
            test_result['improvements'].extend(functionality_result['improvements'])
            
            # 计算总分
            scores = [
                test_result['independence_score'],
                test_result['oop_design_score'],
                test_result['type_annotation_score'],
                test_result['error_handling_score'],
                test_result['interface_design_score'],
                test_result['functionality_score']
            ]
            test_result['total_score'] = sum(scores) / len(scores)
            
            print(f"\n模块总分: {test_result['total_score']:.1f}/100")
            
        except Exception as e:
            print(f"✗ 测试过程中发生错误: {e}")
            test_result['issues'].append(f"测试错误: {e}")
            test_result['total_score'] = 0
        
        self.test_results[module_name] = test_result
    
    def _test_independence(self, content: str, module_path: str) -> Dict[str, Any]:
        """测试模块独立性"""
        result = {'score': 0, 'issues': [], 'improvements': []}
        
        # 检查导入依赖
        import_lines = [line.strip() for line in content.split('\n') if line.strip().startswith(('import ', 'from '))]
        external_imports = []
        
        for line in import_lines:
            if 'src.' not in line and not any(std_lib in line for std_lib in ['sys', 'os', 'datetime', 'typing', 'logging', 'pathlib', 'dataclasses', 'enum']):
                external_imports.append(line)
        
        if len(external_imports) <= 2:
            print("✓ 外部依赖数量合理")
            result['score'] += 30
            result['improvements'].append("外部依赖控制良好")
        else:
            print(f"⚠ 外部依赖较多: {len(external_imports)}")
            result['issues'].append(f"外部依赖过多: {len(external_imports)}")
        
        # 检查是否可独立运行
        if 'if __name__ == "__main__"' in content:
            print("✓ 包含独立运行入口")
            result['score'] += 35
            result['improvements'].append("支持独立运行")
        else:
            print("⚠ 缺少独立运行入口")
            result['issues'].append("缺少main函数")
        
        # 检查配置参数化
        if 'config' in content.lower():
            print("✓ 支持配置参数化")
            result['score'] += 35
            result['improvements'].append("支持配置参数化")
        else:
            print("⚠ 缺少配置参数化")
            result['issues'].append("建议添加配置参数")
        
        print(f"独立性得分: {result['score']}/100")
        return result
    
    def _test_oop_design(self, content: str, module_info: Dict[str, Any]) -> Dict[str, Any]:
        """测试面向对象设计"""
        result = {'score': 0, 'issues': [], 'improvements': []}
        
        # 检查主类定义
        main_class = module_info.get('main_class', '')
        if main_class and f'class {main_class}' in content:
            print(f"✓ 找到主类: {main_class}")
            result['score'] += 30
            result['improvements'].append(f"实现了主类 {main_class}")
        else:
            print(f"✗ 缺少主类: {main_class}")
            result['issues'].append(f"缺少主类 {main_class}")
        
        # 检查数据类定义
        if '@dataclass' in content:
            print("✓ 使用数据类设计")
            result['score'] += 25
            result['improvements'].append("使用了dataclass设计")
        else:
            print("⚠ 未使用数据类")
            result['issues'].append("建议使用dataclass")
        
        # 检查枚举类定义
        if 'class ' in content and 'Enum' in content:
            print("✓ 使用枚举类设计")
            result['score'] += 20
            result['improvements'].append("使用了枚举类设计")
        
        # 检查方法定义
        key_methods = module_info.get('key_methods', [])
        found_methods = 0
        for method in key_methods:
            if f'def {method}(' in content:
                found_methods += 1
        
        if found_methods == len(key_methods):
            print(f"✓ 所有关键方法已实现: {found_methods}/{len(key_methods)}")
            result['score'] += 25
            result['improvements'].append("关键方法实现完整")
        else:
            print(f"⚠ 部分关键方法缺失: {found_methods}/{len(key_methods)}")
            result['issues'].append("部分关键方法缺失")
        
        print(f"面向对象设计得分: {result['score']}/100")
        return result
    
    def _test_type_annotations(self, content: str) -> Dict[str, Any]:
        """测试类型注解"""
        result = {'score': 0, 'issues': [], 'improvements': []}
        
        # 检查typing导入
        if 'from typing import' in content:
            print("✓ 导入了typing模块")
            result['score'] += 20
            result['improvements'].append("导入了typing模块")
        else:
            print("⚠ 未导入typing模块")
            result['issues'].append("建议导入typing模块")
        
        # 检查函数返回类型注解
        return_annotations = content.count(' -> ')
        if return_annotations >= 5:
            print(f"✓ 函数返回类型注解充分: {return_annotations}个")
            result['score'] += 30
            result['improvements'].append("函数返回类型注解充分")
        elif return_annotations > 0:
            print(f"⚠ 函数返回类型注解不足: {return_annotations}个")
            result['issues'].append("建议增加返回类型注解")
        else:
            print("✗ 缺少函数返回类型注解")
            result['issues'].append("缺少函数返回类型注解")
        
        # 检查参数类型注解
        param_annotations = content.count(': ')
        if param_annotations >= 10:
            print(f"✓ 参数类型注解充分: {param_annotations}个")
            result['score'] += 30
            result['improvements'].append("参数类型注解充分")
        elif param_annotations > 0:
            print(f"⚠ 参数类型注解不足: {param_annotations}个")
            result['issues'].append("建议增加参数类型注解")
        
        # 检查泛型使用
        if any(generic in content for generic in ['Dict[', 'List[', 'Optional[', 'Union[']):
            print("✓ 使用了泛型类型")
            result['score'] += 20
            result['improvements'].append("使用了泛型类型")
        else:
            print("⚠ 未使用泛型类型")
            result['issues'].append("建议使用泛型类型")
        
        print(f"类型注解得分: {result['score']}/100")
        return result
    
    def _test_error_handling(self, content: str) -> Dict[str, Any]:
        """测试错误处理"""
        result = {'score': 0, 'issues': [], 'improvements': []}
        
        # 检查try-except块
        try_count = content.count('try:')
        except_count = content.count('except')
        
        if try_count >= 3 and except_count >= 3:
            print(f"✓ 错误处理充分: {try_count}个try块")
            result['score'] += 40
            result['improvements'].append("错误处理机制完善")
        elif try_count > 0:
            print(f"⚠ 错误处理不足: {try_count}个try块")
            result['issues'].append("建议增加错误处理")
            result['score'] += 20
        else:
            print("✗ 缺少错误处理")
            result['issues'].append("缺少错误处理机制")
        
        # 检查日志记录
        if 'logging' in content and 'logger' in content:
            print("✓ 使用了日志记录")
            result['score'] += 30
            result['improvements'].append("使用了日志记录")
        else:
            print("⚠ 未使用日志记录")
            result['issues'].append("建议添加日志记录")
        
        # 检查异常类型
        if 'Exception' in content:
            print("✓ 处理了异常类型")
            result['score'] += 30
            result['improvements'].append("处理了异常类型")
        
        print(f"错误处理得分: {result['score']}/100")
        return result
    
    def _test_interface_design(self, content: str, module_info: Dict[str, Any]) -> Dict[str, Any]:
        """测试接口设计"""
        result = {'score': 0, 'issues': [], 'improvements': []}
        
        # 检查构造函数设计
        if '__init__' in content and 'config' in content:
            print("✓ 构造函数支持配置参数")
            result['score'] += 25
            result['improvements'].append("构造函数设计良好")
        else:
            print("⚠ 构造函数缺少配置参数")
            result['issues'].append("建议构造函数支持配置")
        
        # 检查返回值设计
        if 'return ' in content:
            print("✓ 方法有返回值")
            result['score'] += 25
            result['improvements'].append("方法返回值设计合理")
        
        # 检查文档字符串
        docstring_count = content.count('"""')
        if docstring_count >= 6:  # 类和主要方法都有文档
            print(f"✓ 文档字符串充分: {docstring_count//2}个")
            result['score'] += 25
            result['improvements'].append("文档字符串完善")
        elif docstring_count > 0:
            print(f"⚠ 文档字符串不足: {docstring_count//2}个")
            result['issues'].append("建议增加文档字符串")
        
        # 检查接口一致性
        main_class = module_info.get('main_class', '')
        if main_class and f'class {main_class}:' in content:
            print("✓ 主类接口设计清晰")
            result['score'] += 25
            result['improvements'].append("主类接口设计清晰")
        
        print(f"接口设计得分: {result['score']}/100")
        return result
    
    def _test_functionality(self, module_path: str, module_info: Dict[str, Any]) -> Dict[str, Any]:
        """测试功能完整性"""
        result = {'score': 0, 'issues': [], 'improvements': []}
        
        try:
            # 尝试导入模块
            spec = importlib.util.spec_from_file_location("test_module", module_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            print("✓ 模块可以成功导入")
            result['score'] += 30
            result['improvements'].append("模块导入成功")
            
            # 检查主类是否存在
            main_class_name = module_info.get('main_class', '')
            if main_class_name and hasattr(module, main_class_name):
                print(f"✓ 主类 {main_class_name} 存在")
                result['score'] += 30
                result['improvements'].append(f"主类 {main_class_name} 可访问")
                
                # 尝试实例化主类
                try:
                    main_class = getattr(module, main_class_name)
                    instance = main_class()
                    print(f"✓ 主类 {main_class_name} 可以实例化")
                    result['score'] += 20
                    result['improvements'].append("主类可以实例化")
                except Exception as e:
                    print(f"⚠ 主类实例化失败: {e}")
                    result['issues'].append("主类实例化失败")
            else:
                print(f"✗ 主类 {main_class_name} 不存在")
                result['issues'].append(f"主类 {main_class_name} 不存在")
            
            # 检查main函数
            if hasattr(module, 'main'):
                print("✓ main函数存在")
                result['score'] += 20
                result['improvements'].append("main函数存在")
            else:
                print("⚠ main函数不存在")
                result['issues'].append("缺少main函数")
        
        except Exception as e:
            print(f"✗ 模块导入失败: {e}")
            result['issues'].append(f"模块导入失败: {e}")
        
        print(f"功能完整性得分: {result['score']}/100")
        return result
    
    def generate_improvement_report(self):
        """生成改进报告"""
        
        print(f"\n{'='*80}")
        print("模块改进效果报告")
        print('='*80)
        
        print(f"\n【测试概览】")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试模块数量: {len(self.test_results)}")
        
        # 统计改进效果
        total_score = 0
        excellent_modules = 0
        
        print(f"\n【各模块改进效果】")
        for module_name, result in self.test_results.items():
            score = result.get('total_score', 0)
            total_score += score
            
            if score >= 85:
                status = "✓ 优秀"
                excellent_modules += 1
            elif score >= 70:
                status = "✓ 良好"
            elif score >= 60:
                status = "⚠ 合格"
            else:
                status = "✗ 需要改进"
            
            print(f"\n{module_name}: {score:.1f}/100 {status}")
            print(f"  - 独立性: {result.get('independence_score', 0):.1f}/100")
            print(f"  - 面向对象: {result.get('oop_design_score', 0):.1f}/100")
            print(f"  - 类型注解: {result.get('type_annotation_score', 0):.1f}/100")
            print(f"  - 错误处理: {result.get('error_handling_score', 0):.1f}/100")
            print(f"  - 接口设计: {result.get('interface_design_score', 0):.1f}/100")
            print(f"  - 功能完整: {result.get('functionality_score', 0):.1f}/100")
            
            # 显示主要改进
            improvements = result.get('improvements', [])
            if improvements:
                print(f"  主要改进:")
                for improvement in improvements[:3]:
                    print(f"    ✓ {improvement}")
        
        # 总体评估
        avg_score = total_score / len(self.test_results) if self.test_results else 0
        excellence_rate = (excellent_modules / len(self.test_results)) * 100 if self.test_results else 0
        
        print(f"\n【改进效果总结】")
        print(f"平均得分: {avg_score:.1f}/100")
        print(f"优秀率: {excellence_rate:.1f}%")
        print(f"优秀模块: {excellent_modules}/{len(self.test_results)}")
        
        # 改进等级评估
        if avg_score >= 85:
            grade = "显著改进"
            color = "🟢"
        elif avg_score >= 70:
            grade = "明显改进"
            color = "🟡"
        elif avg_score >= 60:
            grade = "有所改进"
            color = "🟠"
        else:
            grade = "改进不足"
            color = "🔴"
        
        print(f"改进等级: {color} {grade}")
        
        # 改进建议
        print(f"\n【下一步改进建议】")
        if avg_score >= 80:
            print("✓ 模块设计质量已达到良好水平")
            print("✓ 可以进入集成测试和系统测试阶段")
            print("✓ 建议建立持续的代码质量监控")
        else:
            print("⚠ 继续完善模块设计质量")
            print("⚠ 重点关注得分较低的方面")
            print("⚠ 建议制定详细的改进计划")
        
        print("✓ 定期进行模块质量评估")
        print("✓ 建立代码审查机制")


def main():
    """主测试函数"""
    tester = ImprovedModuleTester()
    tester.run_comprehensive_tests()


if __name__ == "__main__":
    main()