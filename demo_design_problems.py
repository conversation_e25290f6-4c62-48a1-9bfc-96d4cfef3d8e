#!/usr/bin/env python3
"""
设计问题检查演示
展示对实际设计文件中具体问题的检测能力
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_review_engine import UnifiedReviewEngine


def main():
    """主函数"""
    
    print("=" * 80)
    print("🔍 设计问题检查演示")
    print("=" * 80)
    print("📋 演示内容:")
    print("   • 检查IEC61850配置文件中的具体设计问题")
    print("   • 检查图纸文件中的具体设计问题")
    print("   • 详细描述每个问题的位置、原因和解决建议")
    print("=" * 80)
    
    # 初始化统一审查引擎
    print("\n==================== 🚀 初始化审查引擎 ====================")
    try:
        engine = UnifiedReviewEngine()
        print("✅ 统一审查引擎初始化成功")
        print("✅ 详细设计检查器已加载")
    except Exception as e:
        print(f"❌ 引擎初始化失败: {e}")
        return
    
    # 检查测试文件
    print("\n==================== 📁 准备测试文件 ====================")
    config_file = "test_project/problematic_substation.scd"
    drawing_file = "test_project/problematic_drawing.dxf"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return
    
    if not os.path.exists(drawing_file):
        print(f"❌ 图纸文件不存在: {drawing_file}")
        return
    
    config_size = os.path.getsize(config_file) / 1024
    drawing_size = os.path.getsize(drawing_file) / 1024
    
    print(f"✅ {config_file} ({config_size:.1f} KB)")
    print(f"✅ {drawing_file} ({drawing_size:.1f} KB)")
    
    # 审查配置文件
    print("\n==================== 🔍 配置文件设计问题检查 ====================")
    print(f"\n--- 审查文件: {config_file} ---")
    
    try:
        config_result = engine.review_file(config_file)
        
        if config_result and config_result.issues:
            print(f"📊 发现设计问题: {len(config_result.issues)} 个")
            print(f"📈 合规性评分: {config_result.compliance_score}/100")
            
            # 按严重程度分类显示问题
            critical_issues = [issue for issue in config_result.issues if issue.severity == 'critical']
            error_issues = [issue for issue in config_result.issues if issue.severity == 'error']
            warning_issues = [issue for issue in config_result.issues if issue.severity == 'warning']
            info_issues = [issue for issue in config_result.issues if issue.severity == 'info']
            
            print(f"📋 问题分布:")
            print(f"   • 严重问题: {len(critical_issues)} 个")
            print(f"   • 错误: {len(error_issues)} 个")
            print(f"   • 警告: {len(warning_issues)} 个")
            print(f"   • 信息: {len(info_issues)} 个")
            
            print(f"\n🔍 详细问题分析:")
            
            # 显示所有问题的详细信息
            for i, issue in enumerate(config_result.issues, 1):
                severity_icon = {
                    'critical': '🔴',
                    'error': '🟠', 
                    'warning': '🟡',
                    'info': '🔵'
                }.get(issue.severity, '⚪')
                
                print(f"\n  {i}. {severity_icon} [{issue.severity.upper()}] {issue.title}")
                print(f"     📝 描述: {issue.description}")
                if issue.location:
                    print(f"     📍 位置: {issue.location}")
                if issue.suggestion:
                    print(f"     💡 建议: {issue.suggestion}")
                if issue.standard_reference:
                    print(f"     📚 标准: {issue.standard_reference}")
                if issue.affected_elements:
                    print(f"     🎯 影响元素: {', '.join(issue.affected_elements)}")
        else:
            print("✅ 未发现设计问题")
    
    except Exception as e:
        print(f"❌ 配置文件审查失败: {e}")
    
    # 审查图纸文件
    print("\n==================== 🔍 图纸文件设计问题检查 ====================")
    print(f"\n--- 审查文件: {drawing_file} ---")
    
    try:
        drawing_result = engine.review_file(drawing_file)
        
        if drawing_result and drawing_result.issues:
            print(f"📊 发现设计问题: {len(drawing_result.issues)} 个")
            print(f"📈 合规性评分: {drawing_result.compliance_score}/100")
            
            # 按类别分类显示问题
            categories = {}
            for issue in drawing_result.issues:
                category = issue.category
                if category not in categories:
                    categories[category] = []
                categories[category].append(issue)
            
            print(f"📋 问题分类:")
            for category, issues in categories.items():
                print(f"   • {category}: {len(issues)} 个")
            
            print(f"\n🔍 详细问题分析:")
            
            # 显示所有问题的详细信息
            for i, issue in enumerate(drawing_result.issues, 1):
                severity_icon = {
                    'critical': '🔴',
                    'error': '🟠', 
                    'warning': '🟡',
                    'info': '🔵'
                }.get(issue.severity, '⚪')
                
                print(f"\n  {i}. {severity_icon} [{issue.severity.upper()}] {issue.title}")
                print(f"     📝 描述: {issue.description}")
                if issue.location:
                    print(f"     📍 位置: {issue.location}")
                if issue.suggestion:
                    print(f"     💡 建议: {issue.suggestion}")
                if issue.standard_reference:
                    print(f"     📚 标准: {issue.standard_reference}")
                if issue.affected_elements:
                    print(f"     🎯 影响元素: {', '.join(issue.affected_elements)}")
        else:
            print("✅ 未发现设计问题")
    
    except Exception as e:
        print(f"❌ 图纸文件审查失败: {e}")
    
    # 综合分析
    print("\n==================== 📊 综合设计问题分析 ====================")
    
    total_config_issues = len(config_result.issues) if config_result else 0
    total_drawing_issues = len(drawing_result.issues) if drawing_result else 0
    total_issues = total_config_issues + total_drawing_issues
    
    print(f"📁 配置文件问题: {total_config_issues} 个")
    print(f"📐 图纸文件问题: {total_drawing_issues} 个")
    print(f"📊 总计问题: {total_issues} 个")
    
    if total_issues > 0:
        print(f"\n🎯 主要问题类型:")
        
        # 统计问题类型
        all_issues = []
        if config_result:
            all_issues.extend(config_result.issues)
        if drawing_result:
            all_issues.extend(drawing_result.issues)
        
        # 按类别统计
        category_stats = {}
        severity_stats = {}
        
        for issue in all_issues:
            # 类别统计
            category = issue.category
            if category not in category_stats:
                category_stats[category] = 0
            category_stats[category] += 1
            
            # 严重程度统计
            severity = issue.severity
            if severity not in severity_stats:
                severity_stats[severity] = 0
            severity_stats[severity] += 1
        
        print(f"\n📋 按类别统计:")
        for category, count in sorted(category_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   • {category}: {count} 个")
        
        print(f"\n⚠️ 按严重程度统计:")
        for severity, count in sorted(severity_stats.items(), key=lambda x: x[1], reverse=True):
            severity_icon = {
                'critical': '🔴',
                'error': '🟠', 
                'warning': '🟡',
                'info': '🔵'
            }.get(severity, '⚪')
            print(f"   • {severity_icon} {severity}: {count} 个")
        
        print(f"\n💡 改进建议:")
        print(f"   1. 优先解决严重问题和错误级别的问题")
        print(f"   2. 检查回路连接的完整性，确保电气回路通畅")
        print(f"   3. 完善设备配置，添加缺少的互感器和虚端子连接")
        print(f"   4. 规范通信网络配置，避免IP地址冲突")
        print(f"   5. 完善数据类型定义，确保引用的完整性")
        print(f"   6. 规范图纸绘制，遵循国家标准要求")
    
    print("\n==================== 🎯 演示总结 ====================")
    print("✅ 设计问题检查演示完成！")
    
    print(f"\n🌟 检查能力验证:")
    print(f"   ✅ 回路连通性检查 - 发现断路器缺少Terminal连接")
    print(f"   ✅ 设备配置完整性 - 发现缺少电流互感器")
    print(f"   ✅ 虚端子连接检查 - 发现电流互感器二次侧连接问题")
    print(f"   ✅ 通信网络检查 - 发现IP地址冲突")
    print(f"   ✅ 数据集引用检查 - 发现引用不存在的逻辑节点")
    print(f"   ✅ 图纸规范检查 - 发现线型、线宽、文字标注问题")
    print(f"   ✅ 设备符号检查 - 发现符号与标注不匹配")
    
    print(f"\n💡 实际应用价值:")
    print(f"   • 自动发现人工容易遗漏的设计问题")
    print(f"   • 提供具体的问题位置和修复建议")
    print(f"   • 确保设计符合IEC61850标准要求")
    print(f"   • 提高设计质量和工程可靠性")
    
    print(f"\n🚀 系统特点:")
    print(f"   • 智能识别文件类型，自动选择检查方法")
    print(f"   • 详细的问题描述和标准引用")
    print(f"   • 分类统计和优先级排序")
    print(f"   • 统一的问题格式和报告生成")


if __name__ == "__main__":
    main()
