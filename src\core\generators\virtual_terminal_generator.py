"""
虚端子表生成器
解决智能变电站虚端子连接表制作的实际痛点
"""

import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import json
import csv

from ..models import SCLDocument, IED, LogicalNode, DataObject


@dataclass
class VirtualTerminal:
    """虚端子定义"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    ied_name: str = ""
    logical_device: str = ""
    logical_node: str = ""
    data_object: str = ""
    data_attribute: str = ""
    fc: str = ""  # Functional Constraint
    cdc: str = ""  # Common Data Class
    data_type: str = ""
    description: str = ""
    
    # 连接信息
    source_ref: str = ""
    destination_refs: List[str] = field(default_factory=list)
    
    # 网络信息
    subnet_name: str = ""
    multicast_address: str = ""
    vlan_id: Optional[int] = None
    app_id: Optional[int] = None
    
    # 工程信息
    signal_type: str = ""  # GOOSE, SMV, MMS
    direction: str = ""    # Input, Output, Bidirectional
    priority: int = 4      # GOOSE优先级
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'ied_name': self.ied_name,
            'logical_device': self.logical_device,
            'logical_node': self.logical_node,
            'data_object': self.data_object,
            'data_attribute': self.data_attribute,
            'fc': self.fc,
            'cdc': self.cdc,
            'data_type': self.data_type,
            'description': self.description,
            'source_ref': self.source_ref,
            'destination_refs': self.destination_refs,
            'subnet_name': self.subnet_name,
            'multicast_address': self.multicast_address,
            'vlan_id': self.vlan_id,
            'app_id': self.app_id,
            'signal_type': self.signal_type,
            'direction': self.direction,
            'priority': self.priority
        }


@dataclass
class VirtualTerminalTable:
    """虚端子表"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = "虚端子连接表"
    project_name: str = ""
    substation_name: str = ""
    generated_at: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    
    terminals: List[VirtualTerminal] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    
    def add_terminal(self, terminal: VirtualTerminal):
        """添加虚端子"""
        self.terminals.append(terminal)
        self._update_statistics()
    
    def get_terminals_by_ied(self, ied_name: str) -> List[VirtualTerminal]:
        """按IED获取虚端子"""
        return [t for t in self.terminals if t.ied_name == ied_name]
    
    def get_terminals_by_signal_type(self, signal_type: str) -> List[VirtualTerminal]:
        """按信号类型获取虚端子"""
        return [t for t in self.terminals if t.signal_type == signal_type]
    
    def _update_statistics(self):
        """更新统计信息"""
        self.statistics = {
            'total_terminals': len(self.terminals),
            'by_signal_type': {},
            'by_ied': {},
            'by_direction': {}
        }
        
        for terminal in self.terminals:
            # 按信号类型统计
            signal_type = terminal.signal_type
            self.statistics['by_signal_type'][signal_type] = \
                self.statistics['by_signal_type'].get(signal_type, 0) + 1
            
            # 按IED统计
            ied_name = terminal.ied_name
            self.statistics['by_ied'][ied_name] = \
                self.statistics['by_ied'].get(ied_name, 0) + 1
            
            # 按方向统计
            direction = terminal.direction
            self.statistics['by_direction'][direction] = \
                self.statistics['by_direction'].get(direction, 0) + 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'project_name': self.project_name,
            'substation_name': self.substation_name,
            'generated_at': self.generated_at.isoformat(),
            'version': self.version,
            'terminals': [t.to_dict() for t in self.terminals],
            'statistics': self.statistics
        }


class VirtualTerminalGenerator:
    """
    虚端子表生成器
    
    解决智能变电站工程师的实际痛点：
    1. 手工制作虚端子表耗时且容易出错
    2. 缺乏标准化的虚端子表格式
    3. 虚端子连接关系复杂，难以理清
    4. 工程交付文档不规范
    
    提供的价值：
    1. 自动从SCD文件提取虚端子信息
    2. 生成标准化的虚端子连接表
    3. 支持多种输出格式（Excel、CSV、JSON）
    4. 提供详细的统计分析
    """
    
    def __init__(self):
        self.signal_type_mapping = {
            'GOOSE': 'GOOSE',
            'SMV': 'SMV', 
            'MMS': 'MMS',
            'Report': 'Report'
        }
        
        self.fc_descriptions = {
            'ST': '状态信息',
            'MX': '测量值',
            'CO': '控制',
            'SP': '设定点',
            'SG': '设定组',
            'SE': '设定编辑',
            'SV': '替代值',
            'CF': '配置',
            'DC': '描述',
            'EX': '扩展定义'
        }
    
    def generate_from_scd(self, scd_document: SCLDocument, 
                         project_name: str = "", 
                         options: Dict[str, Any] = None) -> VirtualTerminalTable:
        """
        从SCD文档生成虚端子表
        
        Args:
            scd_document: SCD文档对象
            project_name: 项目名称
            options: 生成选项
            
        Returns:
            VirtualTerminalTable: 虚端子表
        """
        options = options or {}
        
        # 创建虚端子表
        table = VirtualTerminalTable(
            project_name=project_name,
            substation_name=scd_document.substation.name if scd_document.substation else "",
            title=f"{project_name} 虚端子连接表" if project_name else "虚端子连接表"
        )
        
        # 处理每个IED
        for ied in scd_document.ieds or []:
            self._process_ied(ied, table, options)
        
        # 分析连接关系
        self._analyze_connections(scd_document, table)
        
        return table
    
    def _process_ied(self, ied: IED, table: VirtualTerminalTable, options: Dict[str, Any]):
        """处理单个IED"""
        if not ied.logical_devices:
            return
        
        for ld in ied.logical_devices:
            if not ld.logical_nodes:
                continue
                
            for ln in ld.logical_nodes:
                self._process_logical_node(ied, ld, ln, table, options)
    
    def _process_logical_node(self, ied: IED, ld, ln: LogicalNode, 
                            table: VirtualTerminalTable, options: Dict[str, Any]):
        """处理逻辑节点"""
        if not ln.data_objects:
            return
        
        for do in ln.data_objects:
            self._process_data_object(ied, ld, ln, do, table, options)
    
    def _process_data_object(self, ied: IED, ld, ln: LogicalNode, do: DataObject,
                           table: VirtualTerminalTable, options: Dict[str, Any]):
        """处理数据对象"""
        # 判断是否需要包含此数据对象
        if not self._should_include_data_object(do, options):
            return
        
        # 创建虚端子
        terminal = VirtualTerminal(
            ied_name=ied.name,
            logical_device=ld.inst if hasattr(ld, 'inst') else ld.name,
            logical_node=ln.ln_class,
            data_object=do.name,
            fc=do.fc if hasattr(do, 'fc') else '',
            cdc=do.cdc if hasattr(do, 'cdc') else '',
            data_type=do.type if hasattr(do, 'type') else '',
            description=self._generate_description(ln, do)
        )
        
        # 确定信号类型
        terminal.signal_type = self._determine_signal_type(ln, do)
        
        # 确定方向
        terminal.direction = self._determine_direction(ln, do, terminal.fc)
        
        # 获取网络信息
        self._set_network_info(ied, terminal)
        
        table.add_terminal(terminal)
    
    def _should_include_data_object(self, do: DataObject, options: Dict[str, Any]) -> bool:
        """判断是否应该包含数据对象"""
        # 根据选项过滤
        include_all = options.get('include_all', False)
        if include_all:
            return True
        
        # 只包含重要的数据对象
        important_fcs = {'ST', 'MX', 'CO', 'SP'}
        fc = getattr(do, 'fc', '')
        
        return fc in important_fcs
    
    def _determine_signal_type(self, ln: LogicalNode, do: DataObject) -> str:
        """确定信号类型"""
        # 根据逻辑节点类型和数据对象特征判断
        ln_class = ln.ln_class.upper()
        
        # GOOSE相关的逻辑节点
        if ln_class in ['GGIO', 'CSWI', 'CILO', 'PTOC', 'PDIF']:
            return 'GOOSE'
        
        # SMV相关的逻辑节点  
        elif ln_class in ['TCTR', 'TVTR', 'MSQI']:
            return 'SMV'
        
        # MMS相关
        elif ln_class in ['LPHD', 'LLN0']:
            return 'MMS'
        
        # 默认根据FC判断
        fc = getattr(do, 'fc', '')
        if fc in ['ST', 'CO']:
            return 'GOOSE'
        elif fc == 'MX':
            return 'SMV'
        else:
            return 'MMS'
    
    def _determine_direction(self, ln: LogicalNode, do: DataObject, fc: str) -> str:
        """确定数据方向"""
        if fc == 'CO':  # 控制
            return 'Output'
        elif fc in ['ST', 'MX']:  # 状态和测量
            return 'Input'
        else:
            return 'Bidirectional'
    
    def _generate_description(self, ln: LogicalNode, do: DataObject) -> str:
        """生成描述信息"""
        ln_desc = getattr(ln, 'desc', '') or ''
        do_desc = getattr(do, 'desc', '') or ''
        fc = getattr(do, 'fc', '')
        
        parts = []
        if ln_desc:
            parts.append(ln_desc)
        if do_desc:
            parts.append(do_desc)
        
        # 添加FC描述
        if fc and fc in self.fc_descriptions:
            parts.append(f"({self.fc_descriptions[fc]})")
        
        return ' - '.join(parts) if parts else f"{ln.ln_class}.{do.name}"
    
    def _set_network_info(self, ied: IED, terminal: VirtualTerminal):
        """设置网络信息"""
        # 从IED的访问点获取网络信息
        if not ied.access_points:
            return
        
        for ap in ied.access_points:
            if hasattr(ap, 'server') and ap.server:
                # 获取网络配置
                if hasattr(ap.server, 'authentication'):
                    # 这里应该从通信配置中获取实际的网络信息
                    # 简化实现
                    terminal.subnet_name = "Station_Bus"
                    
                    # 根据信号类型设置默认值
                    if terminal.signal_type == 'GOOSE':
                        terminal.multicast_address = "01-0C-CD-01-00-01"
                        terminal.vlan_id = 1
                        terminal.app_id = 1
                    elif terminal.signal_type == 'SMV':
                        terminal.multicast_address = "01-0C-CD-04-00-01"
                        terminal.vlan_id = 4
                        terminal.app_id = 4000
                break
    
    def _analyze_connections(self, scd_document: SCLDocument, table: VirtualTerminalTable):
        """分析连接关系"""
        # 这里应该分析SCD中的连接配置
        # 简化实现，实际需要解析Inputs/ExtRef等元素
        
        for terminal in table.terminals:
            # 设置源引用
            terminal.source_ref = f"{terminal.ied_name}/{terminal.logical_device}.{terminal.logical_node}.{terminal.data_object}"
            
            # 查找目标引用（简化实现）
            if terminal.signal_type == 'GOOSE' and terminal.direction == 'Output':
                # 查找可能的接收方
                for other_terminal in table.terminals:
                    if (other_terminal.signal_type == 'GOOSE' and 
                        other_terminal.direction == 'Input' and
                        other_terminal.ied_name != terminal.ied_name):
                        terminal.destination_refs.append(other_terminal.source_ref)
    
    def export_to_excel(self, table: VirtualTerminalTable, output_path: str):
        """导出到Excel文件"""
        try:
            import pandas as pd
            
            # 准备数据
            data = []
            for terminal in table.terminals:
                row = {
                    '序号': len(data) + 1,
                    'IED名称': terminal.ied_name,
                    '逻辑设备': terminal.logical_device,
                    '逻辑节点': terminal.logical_node,
                    '数据对象': terminal.data_object,
                    '数据属性': terminal.data_attribute,
                    'FC': terminal.fc,
                    'CDC': terminal.cdc,
                    '数据类型': terminal.data_type,
                    '描述': terminal.description,
                    '信号类型': terminal.signal_type,
                    '方向': terminal.direction,
                    '子网': terminal.subnet_name,
                    '组播地址': terminal.multicast_address,
                    'VLAN ID': terminal.vlan_id,
                    'APP ID': terminal.app_id,
                    '源引用': terminal.source_ref,
                    '目标引用': '; '.join(terminal.destination_refs)
                }
                data.append(row)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 写入Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 主表
                df.to_excel(writer, sheet_name='虚端子表', index=False)
                
                # 统计表
                stats_data = []
                for key, value in table.statistics.items():
                    if isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            stats_data.append({
                                '分类': key,
                                '项目': sub_key,
                                '数量': sub_value
                            })
                    else:
                        stats_data.append({
                            '分类': '总计',
                            '项目': key,
                            '数量': value
                        })
                
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
                
        except ImportError:
            # 如果没有pandas，使用CSV格式
            self.export_to_csv(table, output_path.replace('.xlsx', '.csv'))
    
    def export_to_csv(self, table: VirtualTerminalTable, output_path: str):
        """导出到CSV文件"""
        with open(output_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = [
                '序号', 'IED名称', '逻辑设备', '逻辑节点', '数据对象', '数据属性',
                'FC', 'CDC', '数据类型', '描述', '信号类型', '方向',
                '子网', '组播地址', 'VLAN ID', 'APP ID', '源引用', '目标引用'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for i, terminal in enumerate(table.terminals, 1):
                writer.writerow({
                    '序号': i,
                    'IED名称': terminal.ied_name,
                    '逻辑设备': terminal.logical_device,
                    '逻辑节点': terminal.logical_node,
                    '数据对象': terminal.data_object,
                    '数据属性': terminal.data_attribute,
                    'FC': terminal.fc,
                    'CDC': terminal.cdc,
                    '数据类型': terminal.data_type,
                    '描述': terminal.description,
                    '信号类型': terminal.signal_type,
                    '方向': terminal.direction,
                    '子网': terminal.subnet_name,
                    '组播地址': terminal.multicast_address,
                    'VLAN ID': terminal.vlan_id or '',
                    'APP ID': terminal.app_id or '',
                    '源引用': terminal.source_ref,
                    '目标引用': '; '.join(terminal.destination_refs)
                })
    
    def export_to_json(self, table: VirtualTerminalTable, output_path: str):
        """导出到JSON文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(table.to_dict(), f, ensure_ascii=False, indent=2)
