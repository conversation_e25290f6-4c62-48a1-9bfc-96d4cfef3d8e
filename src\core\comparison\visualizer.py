"""
差异可视化器
为配置文件对比结果提供可视化展示
"""

from typing import Dict, List, Any, Optional
import json

from .comparator import ComparisonResult, Change, ChangeType, ChangeLevel


class DifferenceVisualizer:
    """
    差异可视化器
    
    为配置文件对比结果生成可视化数据：
    1. 生成图表数据
    2. 创建差异热力图
    3. 生成变更时间线
    4. 提供交互式可视化数据
    """
    
    def __init__(self):
        self.color_schemes = {
            'change_types': {
                'added': '#28a745',
                'removed': '#dc3545', 
                'modified': '#ffc107',
                'moved': '#17a2b8',
                'renamed': '#6f42c1'
            },
            'change_levels': {
                'critical': '#dc3545',
                'major': '#fd7e14',
                'minor': '#ffc107',
                'cosmetic': '#6c757d'
            }
        }
    
    def generate_visualization_data(self, comparison_result: ComparisonResult) -> Dict[str, Any]:
        """
        生成完整的可视化数据
        
        Args:
            comparison_result: 对比结果
            
        Returns:
            Dict: 可视化数据
        """
        return {
            'summary_charts': self._generate_summary_charts(comparison_result),
            'change_timeline': self._generate_change_timeline(comparison_result),
            'heatmap_data': self._generate_heatmap_data(comparison_result),
            'tree_view': self._generate_tree_view(comparison_result),
            'statistics': self._generate_statistics_charts(comparison_result)
        }
    
    def _generate_summary_charts(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成摘要图表数据"""
        stats = result.statistics
        
        # 按类型分布的饼图
        type_chart = {
            'type': 'pie',
            'title': '变更类型分布',
            'data': []
        }
        
        for change_type, count in stats.get('by_type', {}).items():
            if count > 0:
                type_chart['data'].append({
                    'label': self._translate_change_type(change_type),
                    'value': count,
                    'color': self.color_schemes['change_types'].get(change_type, '#6c757d')
                })
        
        # 按级别分布的饼图
        level_chart = {
            'type': 'pie',
            'title': '严重程度分布',
            'data': []
        }
        
        for level, count in stats.get('by_level', {}).items():
            if count > 0:
                level_chart['data'].append({
                    'label': self._translate_change_level(level),
                    'value': count,
                    'color': self.color_schemes['change_levels'].get(level, '#6c757d')
                })
        
        return {
            'type_distribution': type_chart,
            'level_distribution': level_chart
        }
    
    def _generate_change_timeline(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成变更时间线"""
        timeline_data = []
        
        # 按时间排序变更
        sorted_changes = sorted(result.changes, key=lambda x: x.timestamp)
        
        for i, change in enumerate(sorted_changes):
            timeline_data.append({
                'id': change.id,
                'timestamp': change.timestamp.isoformat(),
                'title': change.description,
                'type': change.change_type.value,
                'level': change.change_level.value,
                'path': change.path,
                'color': self.color_schemes['change_levels'].get(change.change_level.value, '#6c757d'),
                'index': i
            })
        
        return {
            'type': 'timeline',
            'title': '变更时间线',
            'data': timeline_data
        }
    
    def _generate_heatmap_data(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成热力图数据"""
        # 按路径分组变更
        path_groups = {}
        
        for change in result.changes:
            # 提取路径的顶层部分
            path_parts = change.path.split('.')
            top_level = path_parts[0] if path_parts else 'root'
            
            if top_level not in path_groups:
                path_groups[top_level] = {
                    'total': 0,
                    'critical': 0,
                    'major': 0,
                    'minor': 0,
                    'cosmetic': 0
                }
            
            path_groups[top_level]['total'] += 1
            path_groups[top_level][change.change_level.value] += 1
        
        # 转换为热力图格式
        heatmap_data = []
        for path, counts in path_groups.items():
            heatmap_data.append({
                'path': path,
                'total_changes': counts['total'],
                'critical_changes': counts['critical'],
                'major_changes': counts['major'],
                'minor_changes': counts['minor'],
                'cosmetic_changes': counts['cosmetic'],
                'intensity': self._calculate_intensity(counts)
            })
        
        return {
            'type': 'heatmap',
            'title': '变更热力图',
            'data': heatmap_data
        }
    
    def _generate_tree_view(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成树形视图数据"""
        tree_root = {
            'name': 'Configuration',
            'type': 'root',
            'children': [],
            'changes': 0
        }
        
        # 按路径构建树形结构
        for change in result.changes:
            self._add_change_to_tree(tree_root, change)
        
        return {
            'type': 'tree',
            'title': '配置结构树',
            'data': tree_root
        }
    
    def _generate_statistics_charts(self, result: ComparisonResult) -> Dict[str, Any]:
        """生成统计图表"""
        stats = result.statistics
        
        # 变更数量柱状图
        bar_chart = {
            'type': 'bar',
            'title': '变更统计',
            'data': {
                'labels': [],
                'datasets': [{
                    'label': '变更数量',
                    'data': [],
                    'backgroundColor': []
                }]
            }
        }
        
        # 添加类型统计
        for change_type, count in stats.get('by_type', {}).items():
            if count > 0:
                bar_chart['data']['labels'].append(self._translate_change_type(change_type))
                bar_chart['data']['datasets'][0]['data'].append(count)
                bar_chart['data']['datasets'][0]['backgroundColor'].append(
                    self.color_schemes['change_types'].get(change_type, '#6c757d')
                )
        
        return {
            'change_counts': bar_chart
        }
    
    def _add_change_to_tree(self, parent_node: Dict[str, Any], change: Change):
        """将变更添加到树形结构"""
        path_parts = change.path.split('.')
        current_node = parent_node
        
        for part in path_parts:
            if not part:
                continue
                
            # 查找或创建子节点
            child_node = None
            for child in current_node['children']:
                if child['name'] == part:
                    child_node = child
                    break
            
            if not child_node:
                child_node = {
                    'name': part,
                    'type': 'node',
                    'children': [],
                    'changes': 0,
                    'change_details': []
                }
                current_node['children'].append(child_node)
            
            current_node = child_node
        
        # 添加变更信息
        current_node['changes'] += 1
        current_node['change_details'].append({
            'id': change.id,
            'type': change.change_type.value,
            'level': change.change_level.value,
            'description': change.description
        })
    
    def _calculate_intensity(self, counts: Dict[str, int]) -> float:
        """计算热力图强度"""
        # 根据变更级别计算权重
        weights = {
            'critical': 4,
            'major': 3,
            'minor': 2,
            'cosmetic': 1
        }
        
        weighted_sum = sum(counts[level] * weight for level, weight in weights.items())
        max_possible = counts['total'] * weights['critical']
        
        return weighted_sum / max_possible if max_possible > 0 else 0
    
    def _translate_change_type(self, change_type: str) -> str:
        """翻译变更类型"""
        translations = {
            'added': '新增',
            'removed': '删除',
            'modified': '修改',
            'moved': '移动',
            'renamed': '重命名'
        }
        return translations.get(change_type, change_type)
    
    def _translate_change_level(self, level: str) -> str:
        """翻译变更级别"""
        translations = {
            'critical': '关键',
            'major': '重要',
            'minor': '次要',
            'cosmetic': '外观'
        }
        return translations.get(level, level)
    
    def export_visualization_config(self, comparison_result: ComparisonResult, 
                                  output_path: str):
        """
        导出可视化配置
        
        Args:
            comparison_result: 对比结果
            output_path: 输出路径
        """
        viz_data = self.generate_visualization_data(comparison_result)
        
        # 添加配置信息
        config = {
            'version': '1.0',
            'generated_at': comparison_result.comparison_time.isoformat(),
            'source_file': comparison_result.source_file,
            'target_file': comparison_result.target_file,
            'visualization_data': viz_data,
            'color_schemes': self.color_schemes
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def generate_d3_config(self, comparison_result: ComparisonResult) -> Dict[str, Any]:
        """
        生成D3.js可视化配置
        
        Args:
            comparison_result: 对比结果
            
        Returns:
            Dict: D3.js配置
        """
        viz_data = self.generate_visualization_data(comparison_result)
        
        return {
            'charts': {
                'summary_pie': {
                    'type': 'pie',
                    'data': viz_data['summary_charts']['type_distribution']['data'],
                    'config': {
                        'width': 400,
                        'height': 300,
                        'margin': {'top': 20, 'right': 20, 'bottom': 20, 'left': 20}
                    }
                },
                'timeline': {
                    'type': 'timeline',
                    'data': viz_data['change_timeline']['data'],
                    'config': {
                        'width': 800,
                        'height': 200,
                        'margin': {'top': 20, 'right': 20, 'bottom': 40, 'left': 60}
                    }
                },
                'heatmap': {
                    'type': 'heatmap',
                    'data': viz_data['heatmap_data']['data'],
                    'config': {
                        'width': 600,
                        'height': 400,
                        'margin': {'top': 20, 'right': 20, 'bottom': 40, 'left': 100}
                    }
                }
            },
            'interactions': {
                'tooltip': True,
                'zoom': True,
                'filter': True
            }
        }
