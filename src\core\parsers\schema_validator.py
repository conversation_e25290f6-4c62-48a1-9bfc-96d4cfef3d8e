"""
IEC61850 XML Schema验证器
提供XML Schema验证功能
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
from lxml import etree
import logging

from .base_parser import ParseError


class SchemaValidator:
    """XML Schema验证器"""
    
    def __init__(self, schema_dir: Optional[Path] = None):
        """
        初始化Schema验证器
        
        Args:
            schema_dir: Schema文件目录路径
        """
        self.schema_dir = schema_dir or Path(__file__).parent / "schemas"
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 缓存已加载的Schema
        self._schema_cache: Dict[str, etree.XMLSchema] = {}
        
        # IEC61850标准Schema文件映射
        self.schema_files = {
            'SCL': 'SCL.xsd',
            'SCD': 'SCL.xsd',  # SCD使用SCL Schema
            'ICD': 'SCL.xsd',  # ICD使用SCL Schema
            'CID': 'SCL.xsd',  # CID使用SCL Schema
            'SSD': 'SCL.xsd'   # SSD使用SCL Schema
        }
    
    def load_schema(self, schema_name: str) -> Optional[etree.XMLSchema]:
        """
        加载XML Schema
        
        Args:
            schema_name: Schema名称
            
        Returns:
            XMLSchema对象或None
        """
        if schema_name in self._schema_cache:
            return self._schema_cache[schema_name]
        
        schema_file = self.schema_files.get(schema_name)
        if not schema_file:
            self.logger.warning(f"未知的Schema名称: {schema_name}")
            return None
        
        schema_path = self.schema_dir / schema_file
        if not schema_path.exists():
            self.logger.warning(f"Schema文件不存在: {schema_path}")
            return None
        
        try:
            with open(schema_path, 'r', encoding='utf-8') as f:
                schema_doc = etree.parse(f)
            
            schema = etree.XMLSchema(schema_doc)
            self._schema_cache[schema_name] = schema
            
            self.logger.info(f"成功加载Schema: {schema_name}")
            return schema
            
        except Exception as e:
            self.logger.error(f"加载Schema失败 {schema_name}: {str(e)}")
            return None
    
    def validate_document(self, document: etree.ElementTree, 
                         schema_name: str) -> List[ParseError]:
        """
        验证XML文档
        
        Args:
            document: XML文档树
            schema_name: Schema名称
            
        Returns:
            验证错误列表
        """
        errors = []
        
        # 加载Schema
        schema = self.load_schema(schema_name)
        if not schema:
            errors.append(ParseError(f"无法加载Schema: {schema_name}"))
            return errors
        
        # 执行验证
        try:
            if not schema.validate(document):
                # 收集验证错误
                for error in schema.error_log:
                    parse_error = ParseError(
                        message=error.message,
                        line_number=error.line,
                        column_number=error.column,
                        element_name=error.path
                    )
                    errors.append(parse_error)
        
        except Exception as e:
            errors.append(ParseError(f"Schema验证过程出错: {str(e)}"))
        
        return errors
    
    def validate_element(self, element: etree.Element, 
                        schema_name: str) -> List[ParseError]:
        """
        验证XML元素
        
        Args:
            element: XML元素
            schema_name: Schema名称
            
        Returns:
            验证错误列表
        """
        # 创建临时文档进行验证
        temp_doc = etree.ElementTree(element)
        return self.validate_document(temp_doc, schema_name)
    
    def get_schema_info(self, schema_name: str) -> Optional[Dict[str, Any]]:
        """
        获取Schema信息
        
        Args:
            schema_name: Schema名称
            
        Returns:
            Schema信息字典
        """
        schema = self.load_schema(schema_name)
        if not schema:
            return None
        
        # 提取Schema基本信息
        schema_doc = schema.schema_doc
        root = schema_doc.getroot()
        
        info = {
            'target_namespace': root.get('targetNamespace'),
            'element_form_default': root.get('elementFormDefault'),
            'attribute_form_default': root.get('attributeFormDefault'),
            'version': root.get('version'),
            'elements': [],
            'types': []
        }
        
        # 提取元素定义
        for element in root.xpath('.//xs:element[@name]', 
                                namespaces={'xs': 'http://www.w3.org/2001/XMLSchema'}):
            info['elements'].append({
                'name': element.get('name'),
                'type': element.get('type'),
                'min_occurs': element.get('minOccurs', '1'),
                'max_occurs': element.get('maxOccurs', '1')
            })
        
        # 提取类型定义
        for type_def in root.xpath('.//xs:complexType[@name] | .//xs:simpleType[@name]',
                                 namespaces={'xs': 'http://www.w3.org/2001/XMLSchema'}):
            info['types'].append({
                'name': type_def.get('name'),
                'type': type_def.tag.split('}')[-1] if '}' in type_def.tag else type_def.tag
            })
        
        return info
    
    def create_default_schema(self, schema_name: str) -> str:
        """
        创建默认的Schema文件内容
        
        Args:
            schema_name: Schema名称
            
        Returns:
            Schema XML内容
        """
        if schema_name == 'SCL':
            return self._create_scl_schema()
        else:
            return self._create_basic_schema(schema_name)
    
    def _create_scl_schema(self) -> str:
        """创建基本的SCL Schema"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.iec.ch/61850/2003/SCL"
           xmlns:scl="http://www.iec.ch/61850/2003/SCL"
           elementFormDefault="qualified">
    
    <!-- 根元素定义 -->
    <xs:element name="SCL" type="scl:tSCL"/>
    
    <!-- SCL根类型 -->
    <xs:complexType name="tSCL">
        <xs:sequence>
            <xs:element name="Header" type="scl:tHeader"/>
            <xs:element name="Substation" type="scl:tSubstation" minOccurs="0"/>
            <xs:element name="Communication" type="scl:tCommunication" minOccurs="0"/>
            <xs:element name="IED" type="scl:tIED" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="DataTypeTemplates" type="scl:tDataTypeTemplates" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="version" type="xs:string" use="required"/>
        <xs:attribute name="revision" type="xs:string"/>
    </xs:complexType>
    
    <!-- Header类型 -->
    <xs:complexType name="tHeader">
        <xs:attribute name="id" type="xs:string" use="required"/>
        <xs:attribute name="version" type="xs:string"/>
        <xs:attribute name="revision" type="xs:string"/>
        <xs:attribute name="toolID" type="xs:string"/>
        <xs:attribute name="nameStructure" type="xs:string"/>
    </xs:complexType>
    
    <!-- 基础命名类型 -->
    <xs:complexType name="tNaming">
        <xs:attribute name="name" type="xs:string" use="required"/>
        <xs:attribute name="desc" type="xs:string"/>
    </xs:complexType>
    
    <!-- Substation类型 -->
    <xs:complexType name="tSubstation">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="VoltageLevel" type="scl:tVoltageLevel" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- VoltageLevel类型 -->
    <xs:complexType name="tVoltageLevel">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="Voltage" type="scl:tVoltage" minOccurs="0"/>
                    <xs:element name="Bay" type="scl:tBay" minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- Voltage类型 -->
    <xs:complexType name="tVoltage">
        <xs:attribute name="unit" type="xs:string" use="required"/>
        <xs:attribute name="multiplier" type="xs:string"/>
        <xs:attribute name="value" type="xs:decimal" use="required"/>
    </xs:complexType>
    
    <!-- Bay类型 -->
    <xs:complexType name="tBay">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="ConductingEquipment" type="scl:tConductingEquipment" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- ConductingEquipment类型 -->
    <xs:complexType name="tConductingEquipment">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="type" type="xs:string" use="required"/>
                <xs:attribute name="virtual" type="xs:boolean" default="false"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- Communication类型 -->
    <xs:complexType name="tCommunication">
        <xs:sequence>
            <xs:element name="SubNetwork" type="scl:tSubNetwork" 
                       minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- SubNetwork类型 -->
    <xs:complexType name="tSubNetwork">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="ConnectedAP" type="scl:tConnectedAP" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
                <xs:attribute name="type" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- ConnectedAP类型 -->
    <xs:complexType name="tConnectedAP">
        <xs:attribute name="iedName" type="xs:string" use="required"/>
        <xs:attribute name="apName" type="xs:string" use="required"/>
    </xs:complexType>
    
    <!-- IED类型 -->
    <xs:complexType name="tIED">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="AccessPoint" type="scl:tAccessPoint" 
                               minOccurs="0" maxOccurs="unbounded"/>
                </xs:sequence>
                <xs:attribute name="type" type="xs:string"/>
                <xs:attribute name="manufacturer" type="xs:string"/>
                <xs:attribute name="configVersion" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- AccessPoint类型 -->
    <xs:complexType name="tAccessPoint">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:sequence>
                    <xs:element name="Server" type="scl:tServer" minOccurs="0"/>
                </xs:sequence>
                <xs:attribute name="router" type="xs:boolean" default="false"/>
                <xs:attribute name="clock" type="xs:boolean" default="false"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- Server类型 -->
    <xs:complexType name="tServer">
        <xs:sequence>
            <xs:element name="LDevice" type="scl:tLDevice" 
                       minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="timeout" type="xs:int" default="30"/>
    </xs:complexType>
    
    <!-- LDevice类型 -->
    <xs:complexType name="tLDevice">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="inst" type="xs:string" use="required"/>
                <xs:attribute name="ldName" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- DataTypeTemplates类型 -->
    <xs:complexType name="tDataTypeTemplates">
        <xs:sequence>
            <xs:element name="LNodeType" type="scl:tLNodeType" 
                       minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="DOType" type="scl:tDOType" 
                       minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="DAType" type="scl:tDAType" 
                       minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="EnumType" type="scl:tEnumType" 
                       minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- LNodeType类型 -->
    <xs:complexType name="tLNodeType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="lnClass" type="xs:string" use="required"/>
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- DOType类型 -->
    <xs:complexType name="tDOType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="cdc" type="xs:string" use="required"/>
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- DAType类型 -->
    <xs:complexType name="tDAType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
    <!-- EnumType类型 -->
    <xs:complexType name="tEnumType">
        <xs:complexContent>
            <xs:extension base="scl:tNaming">
                <xs:attribute name="iedType" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    
</xs:schema>'''
    
    def _create_basic_schema(self, schema_name: str) -> str:
        """创建基本Schema模板"""
        return f'''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://www.iec.ch/61850/2003/SCL"
           elementFormDefault="qualified">
    
    <xs:element name="{schema_name}" type="xs:anyType"/>
    
</xs:schema>'''
    
    def ensure_schema_files(self) -> None:
        """确保Schema文件存在"""
        if not self.schema_dir.exists():
            self.schema_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建默认的SCL Schema文件
        scl_schema_path = self.schema_dir / 'SCL.xsd'
        if not scl_schema_path.exists():
            with open(scl_schema_path, 'w', encoding='utf-8') as f:
                f.write(self.create_default_schema('SCL'))
            self.logger.info(f"创建默认Schema文件: {scl_schema_path}")
    
    def clear_cache(self) -> None:
        """清空Schema缓存"""
        self._schema_cache.clear()
        self.logger.info("Schema缓存已清空")
