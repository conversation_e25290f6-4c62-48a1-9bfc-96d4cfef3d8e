{"report_info": {"report_id": "design_check_20250818_001500", "file_path": "test_project/problematic_substation.scd", "file_type": "IEC61850配置文件", "check_date": "2025-08-18T00:15:00.000000", "compliance_score": 30, "checker_version": "1.0.0"}, "summary": {"total_issues": 9, "critical_issues": 2, "error_issues": 6, "warning_issues": 1, "info_issues": 0, "auto_fixable_issues": 3, "compliance_score": 30, "severity_distribution": {"critical": 2, "error": 6, "warning": 1}, "category_distribution": {"回路连接": 1, "设备配置": 1, "虚端子连接": 1, "配置完整性": 1, "通信配置": 2, "数据集配置": 1, "IED配置": 1, "数据类型定义": 1}}, "issues": [{"id": "CBR_QF1_NO_TERMINAL", "title": "断路器QF1缺少Terminal连接", "description": "断路器QF1没有定义任何Terminal连接，导致回路不通", "severity": "critical", "category": "回路连接", "location": "断路器: QF1", "suggestion": "为断路器QF1添加至少两个Terminal连接，分别连接到母线和线路侧", "standard_reference": "IEC61850-6: 配置描述语言", "affected_elements": ["QF1"], "auto_fixable": false}, {"id": "IP_CONFLICT_************", "title": "IP地址冲突: ************", "description": "IP地址************被多个IED使用: ProtectionIED1 和 ProtectionIED2", "severity": "critical", "category": "通信配置", "location": "IED: ProtectionIED2", "suggestion": "为IED ProtectionIED2分配唯一的IP地址", "standard_reference": "IEC61850-8-1: 特定通信服务映射", "affected_elements": ["ProtectionIED1", "ProtectionIED2"], "auto_fixable": true}, {"id": "BAY_220kV_Line1_NO_CTR", "title": "间隔220kV_Line1缺少电流互感器", "description": "间隔220kV_Line1有断路器但缺少电流互感器，无法进行电流测量和保护", "severity": "error", "category": "设备配置", "location": "间隔: 220kV_Line1", "suggestion": "在间隔220kV_Line1中添加电流互感器(CTR)用于电流测量", "standard_reference": "IEC61850-7-4: 兼容的逻辑节点类和数据类", "affected_elements": ["220kV_Line1"], "auto_fixable": false}, {"id": "CTR_TA2_INCOMPLETE_TERMINAL", "title": "电流互感器TA2缺少二次侧连接", "description": "电流互感器TA2缺少二次侧Terminal连接，无法向保护和测量装置提供电流信号", "severity": "error", "category": "虚端子连接", "location": "电流互感器: TA2", "suggestion": "为电流互感器TA2添加二次侧Terminal连接，连接到保护和测量装置", "standard_reference": "IEC61850-9-2: 采样值传输的特殊通信服务映射", "affected_elements": ["TA2"], "auto_fixable": false}, {"id": "VL_110kV_NO_BAY", "title": "电压等级110kV没有配置任何间隔", "description": "电压等级110kV下没有定义任何间隔，这是不完整的配置", "severity": "error", "category": "配置完整性", "location": "电压等级: 110kV", "suggestion": "在电压等级110kV下添加相应的间隔配置，如出线间隔、母联间隔等", "standard_reference": "IEC61850-6: 配置描述语言", "affected_elements": ["110kV"], "auto_fixable": false}, {"id": "DATASET_Events_INVALID_REF_PTRC2", "title": "数据集Events引用不存在的逻辑节点", "description": "数据集Events中引用了不存在的逻辑节点: PTRC2", "severity": "error", "category": "数据集配置", "location": "数据集: Events", "suggestion": "检查逻辑节点PTRC2是否正确定义，或修正数据集中的引用", "standard_reference": "IEC61850-7-2: 抽象通信服务接口", "affected_elements": ["Events", "PTRC2"], "auto_fixable": false}, {"id": "IED_ProtectionIED2_NO_PROTECTION_LN", "title": "保护IED ProtectionIED2缺少保护逻辑节点", "description": "保护类型的IED ProtectionIED2没有定义任何保护逻辑节点(PTRC)", "severity": "error", "category": "IED配置", "location": "IED: ProtectionIED2", "suggestion": "为保护IED ProtectionIED2添加相应的保护逻辑节点(PTRC)", "standard_reference": "IEC61850-7-4: 兼容的逻辑节点类和数据类", "affected_elements": ["ProtectionIED2"], "auto_fixable": false}, {"id": "LNTYPE_LLN0_Type_NO_NAMEPLT", "title": "LLN0类型LLN0_Type缺少NamPlt数据对象", "description": "LLN0类型LLN0_Type缺少必要的NamPlt(名牌)数据对象", "severity": "error", "category": "数据类型定义", "location": "LNodeType: LLN0_Type", "suggestion": "为LLN0类型LLN0_Type添加NamPlt数据对象", "standard_reference": "IEC61850-7-4: 兼容的逻辑节点类和数据类", "affected_elements": ["LLN0_Type"], "auto_fixable": true}, {"id": "IED_ProtectionIED1_NO_GATEWAY", "title": "IED ProtectionIED1缺少网关配置", "description": "IED ProtectionIED1的网络配置中缺少IP-GATEWAY设置", "severity": "warning", "category": "通信配置", "location": "IED: ProtectionIED1", "suggestion": "为IED ProtectionIED1添加IP-GATEWAY配置", "standard_reference": "IEC61850-8-1: 特定通信服务映射", "affected_elements": ["ProtectionIED1"], "auto_fixable": true}], "recommendations": ["优先解决严重问题和错误级别的问题", "检查回路连接的完整性，确保电气回路通畅", "完善设备配置，添加缺少的互感器和虚端子连接", "规范通信网络配置，避免IP地址冲突", "完善数据类型定义，确保引用的完整性"]}