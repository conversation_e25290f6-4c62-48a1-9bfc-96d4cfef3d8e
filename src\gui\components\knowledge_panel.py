"""
知识面板组件
显示知识库信息、智能推荐和专家建议
"""

import logging
from typing import Dict, List, Any, Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QTextEdit, QLabel, QPushButton, QGroupBox, QTabWidget, QListWidget,
    QListWidgetItem, QSplitter, QProgressBar, QComboBox, QLineEdit,
    QMessageBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QIcon, QFont, QColor, QBrush

logger = logging.getLogger(__name__)


class KnowledgePanelWidget(QWidget):
    """知识面板组件"""
    
    # 信号
    knowledge_selected = Signal(dict)      # 知识选择信号
    recommendation_applied = Signal(dict)  # 推荐应用信号
    
    def __init__(self, parent=None):
        """初始化知识面板组件"""
        super().__init__(parent)
        
        self.knowledge_data = {}
        self.recommendations = []
        self.current_context = {}
        
        self._init_ui()
        self._setup_knowledge_categories()
        
        logger.debug("知识面板组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 搜索区域
        search_group = QGroupBox("知识搜索")
        search_layout = QVBoxLayout(search_group)
        
        search_input_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索知识库...")
        self.search_input.returnPressed.connect(self._search_knowledge)
        search_input_layout.addWidget(self.search_input)
        
        self.search_button = QPushButton("搜索")
        self.search_button.clicked.connect(self._search_knowledge)
        search_input_layout.addWidget(self.search_button)
        
        search_layout.addLayout(search_input_layout)
        
        # 搜索过滤器
        filter_layout = QHBoxLayout()
        filter_label = QLabel("类型:")
        filter_layout.addWidget(filter_label)
        
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "标准", "规则", "设备", "协议", "要求"])
        filter_layout.addWidget(self.filter_combo)
        
        search_layout.addLayout(filter_layout)
        layout.addWidget(search_group)
        
        # 主标签页
        self.main_tabs = QTabWidget()
        layout.addWidget(self.main_tabs)
        
        # 知识浏览标签页
        self.knowledge_tab = self._create_knowledge_tab()
        self.main_tabs.addTab(self.knowledge_tab, "知识浏览")
        
        # 智能推荐标签页
        self.recommendations_tab = self._create_recommendations_tab()
        self.main_tabs.addTab(self.recommendations_tab, "智能推荐")
        
        # 专家建议标签页
        self.expert_tab = self._create_expert_tab()
        self.main_tabs.addTab(self.expert_tab, "专家建议")
        
        # 统计信息标签页
        self.stats_tab = self._create_stats_tab()
        self.main_tabs.addTab(self.stats_tab, "统计信息")
    
    def _create_knowledge_tab(self) -> QWidget:
        """创建知识浏览标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 分割器
        splitter = QSplitter(Qt.Vertical)
        layout.addWidget(splitter)
        
        # 知识树
        knowledge_group = QGroupBox("知识分类")
        knowledge_layout = QVBoxLayout(knowledge_group)
        
        self.knowledge_tree = QTreeWidget()
        self.knowledge_tree.setHeaderLabel("知识库")
        self.knowledge_tree.itemClicked.connect(self._on_knowledge_item_clicked)
        knowledge_layout.addWidget(self.knowledge_tree)
        
        splitter.addWidget(knowledge_group)
        
        # 详细信息
        details_group = QGroupBox("详细信息")
        details_layout = QVBoxLayout(details_group)
        
        self.knowledge_details = QTextEdit()
        self.knowledge_details.setReadOnly(True)
        details_layout.addWidget(self.knowledge_details)
        
        splitter.addWidget(details_group)
        
        # 设置分割器比例
        splitter.setSizes([200, 150])
        
        return widget
    
    def _create_recommendations_tab(self) -> QWidget:
        """创建智能推荐标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 推荐列表
        recommendations_group = QGroupBox("智能推荐")
        recommendations_layout = QVBoxLayout(recommendations_group)
        
        self.recommendations_list = QListWidget()
        self.recommendations_list.itemClicked.connect(self._on_recommendation_clicked)
        recommendations_layout.addWidget(self.recommendations_list)
        
        # 推荐操作按钮
        button_layout = QHBoxLayout()
        
        self.apply_recommendation_button = QPushButton("应用推荐")
        self.apply_recommendation_button.clicked.connect(self._apply_recommendation)
        self.apply_recommendation_button.setEnabled(False)
        button_layout.addWidget(self.apply_recommendation_button)
        
        self.refresh_recommendations_button = QPushButton("刷新推荐")
        self.refresh_recommendations_button.clicked.connect(self._refresh_recommendations)
        button_layout.addWidget(self.refresh_recommendations_button)
        
        button_layout.addStretch()
        recommendations_layout.addLayout(button_layout)
        
        layout.addWidget(recommendations_group)
        
        # 推荐详情
        details_group = QGroupBox("推荐详情")
        details_layout = QVBoxLayout(details_group)
        
        self.recommendation_details = QTextEdit()
        self.recommendation_details.setReadOnly(True)
        details_layout.addWidget(self.recommendation_details)
        
        layout.addWidget(details_group)
        
        return widget
    
    def _create_expert_tab(self) -> QWidget:
        """创建专家建议标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 专家建议列表
        expert_group = QGroupBox("专家建议")
        expert_layout = QVBoxLayout(expert_group)
        
        self.expert_list = QListWidget()
        expert_layout.addWidget(self.expert_list)
        
        layout.addWidget(expert_group)
        
        # 建议详情
        expert_details_group = QGroupBox("建议详情")
        expert_details_layout = QVBoxLayout(expert_details_group)
        
        self.expert_details = QTextEdit()
        self.expert_details.setReadOnly(True)
        expert_details_layout.addWidget(self.expert_details)
        
        layout.addWidget(expert_details_group)
        
        return widget
    
    def _create_stats_tab(self) -> QWidget:
        """创建统计信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 知识库统计
        kb_stats_group = QGroupBox("知识库统计")
        kb_stats_layout = QVBoxLayout(kb_stats_group)
        
        self.kb_stats_tree = QTreeWidget()
        self.kb_stats_tree.setHeaderLabel("统计项目")
        kb_stats_layout.addWidget(self.kb_stats_tree)
        
        layout.addWidget(kb_stats_group)
        
        # 使用统计
        usage_stats_group = QGroupBox("使用统计")
        usage_stats_layout = QVBoxLayout(usage_stats_group)
        
        self.usage_stats_tree = QTreeWidget()
        self.usage_stats_tree.setHeaderLabel("使用情况")
        usage_stats_layout.addWidget(self.usage_stats_tree)
        
        layout.addWidget(usage_stats_group)
        
        return widget
    
    def _setup_knowledge_categories(self):
        """设置知识分类"""
        # 创建知识分类树结构
        categories = {
            "IEC61850标准": {
                "IEC 61850-1": "概述和一般要求",
                "IEC 61850-6": "SCL配置语言",
                "IEC 61850-7-1": "基本通信结构",
                "IEC 61850-8-1": "MMS映射",
                "IEC 61850-9-2": "采样值传输"
            },
            "国家标准": {
                "GB/T 51072-2014": "智能变电站设计规范",
                "GB/T 14285-2023": "继电保护和安全自动装置技术规程"
            },
            "行业标准": {
                "DL/T 5136-2012": "二次接线设计技术规程",
                "DL/T 5218": "变电站设计技术规程"
            },
            "验证规则": {
                "语法规则": "SCL文件语法验证规则",
                "语义规则": "逻辑一致性验证规则",
                "业务规则": "业务逻辑验证规则"
            },
            "设备知识": {
                "保护设备": "继电保护装置相关知识",
                "测控设备": "测量控制装置相关知识",
                "通信设备": "通信网关等设备知识"
            }
        }
        
        self._populate_knowledge_tree(categories)
    
    def _populate_knowledge_tree(self, categories: Dict[str, Any]):
        """填充知识树"""
        self.knowledge_tree.clear()
        
        for category, items in categories.items():
            category_item = QTreeWidgetItem(self.knowledge_tree)
            category_item.setText(0, category)
            category_item.setIcon(0, QIcon("resources/icons/folder.png"))
            
            if isinstance(items, dict):
                for item_name, item_desc in items.items():
                    item_widget = QTreeWidgetItem(category_item)
                    item_widget.setText(0, item_name)
                    item_widget.setIcon(0, QIcon("resources/icons/document.png"))
                    item_widget.setData(0, Qt.UserRole, {
                        'name': item_name,
                        'description': item_desc,
                        'category': category
                    })
            
            category_item.setExpanded(True)
    
    def show_recommendations(self, recommendations: List[Dict[str, Any]]):
        """显示智能推荐"""
        try:
            self.recommendations = recommendations
            self._update_recommendations_list()
            
            # 切换到推荐标签页
            self.main_tabs.setCurrentIndex(1)
            
            logger.info(f"显示 {len(recommendations)} 个智能推荐")
            
        except Exception as e:
            logger.error(f"显示智能推荐失败: {e}")
    
    def _update_recommendations_list(self):
        """更新推荐列表"""
        self.recommendations_list.clear()
        
        for i, recommendation in enumerate(self.recommendations):
            item = QListWidgetItem()
            
            # 设置推荐文本
            title = recommendation.get('title', f'推荐 {i+1}')
            confidence = recommendation.get('confidence', 0.0)
            item.setText(f"{title} (置信度: {confidence:.2f})")
            
            # 根据置信度设置颜色
            if confidence >= 0.8:
                item.setBackground(QBrush(QColor(144, 238, 144)))  # 浅绿色
            elif confidence >= 0.6:
                item.setBackground(QBrush(QColor(255, 255, 224)))  # 浅黄色
            else:
                item.setBackground(QBrush(QColor(255, 182, 193)))  # 浅红色
            
            # 存储推荐数据
            item.setData(Qt.UserRole, recommendation)
            
            self.recommendations_list.addItem(item)
    
    def _search_knowledge(self):
        """搜索知识库"""
        query = self.search_input.text().strip()
        if not query:
            return
        
        try:
            # TODO: 实现知识库搜索
            # 这里应该调用知识库的搜索功能
            QMessageBox.information(self, "搜索", f"搜索功能正在开发中\n查询: {query}")
            
        except Exception as e:
            logger.error(f"知识搜索失败: {e}")
            QMessageBox.critical(self, "错误", f"知识搜索失败:\n{e}")
    
    def _on_knowledge_item_clicked(self, item: QTreeWidgetItem, column: int):
        """知识项点击事件"""
        data = item.data(0, Qt.UserRole)
        if data:
            # 显示知识详情
            self._show_knowledge_details(data)
            
            # 发送选择信号
            self.knowledge_selected.emit(data)
    
    def _show_knowledge_details(self, knowledge_data: Dict[str, Any]):
        """显示知识详情"""
        details = []
        details.append(f"名称: {knowledge_data.get('name', '')}")
        details.append(f"类别: {knowledge_data.get('category', '')}")
        details.append(f"描述: {knowledge_data.get('description', '')}")
        details.append("")
        
        # 添加更多详细信息
        if 'content' in knowledge_data:
            details.append("内容:")
            details.append(knowledge_data['content'])
        
        if 'source' in knowledge_data:
            details.append(f"来源: {knowledge_data['source']}")
        
        if 'confidence' in knowledge_data:
            details.append(f"置信度: {knowledge_data['confidence']}")
        
        self.knowledge_details.setPlainText("\n".join(details))
    
    def _on_recommendation_clicked(self, item: QListWidgetItem):
        """推荐项点击事件"""
        recommendation = item.data(Qt.UserRole)
        if recommendation:
            self._show_recommendation_details(recommendation)
            self.apply_recommendation_button.setEnabled(True)
    
    def _show_recommendation_details(self, recommendation: Dict[str, Any]):
        """显示推荐详情"""
        details = []
        details.append(f"标题: {recommendation.get('title', '')}")
        details.append(f"类型: {recommendation.get('type', '')}")
        details.append(f"置信度: {recommendation.get('confidence', 0.0):.2f}")
        details.append(f"来源: {recommendation.get('source', '')}")
        details.append("")
        details.append("描述:")
        details.append(recommendation.get('description', ''))
        
        if 'details' in recommendation:
            details.append("")
            details.append("详细信息:")
            details.append(str(recommendation['details']))
        
        self.recommendation_details.setPlainText("\n".join(details))
    
    def _apply_recommendation(self):
        """应用推荐"""
        current_item = self.recommendations_list.currentItem()
        if current_item:
            recommendation = current_item.data(Qt.UserRole)
            if recommendation:
                # 发送应用推荐信号
                self.recommendation_applied.emit(recommendation)
                
                # 显示确认消息
                QMessageBox.information(self, "推荐应用", 
                                      f"推荐 '{recommendation.get('title', '')}' 已应用")
    
    def _refresh_recommendations(self):
        """刷新推荐"""
        try:
            # TODO: 实现推荐刷新逻辑
            # 这里应该重新获取推荐
            QMessageBox.information(self, "刷新", "推荐刷新功能正在开发中")
            
        except Exception as e:
            logger.error(f"刷新推荐失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新推荐失败:\n{e}")
    
    def update_context(self, context: Dict[str, Any]):
        """更新上下文信息"""
        self.current_context = context
        
        # 根据新的上下文更新显示
        self._update_expert_suggestions()
        self._update_statistics()
    
    def _update_expert_suggestions(self):
        """更新专家建议"""
        self.expert_list.clear()
        
        # 基于当前上下文生成专家建议
        suggestions = self._generate_expert_suggestions()
        
        for suggestion in suggestions:
            item = QListWidgetItem(suggestion['title'])
            item.setData(Qt.UserRole, suggestion)
            self.expert_list.addItem(item)
    
    def _generate_expert_suggestions(self) -> List[Dict[str, Any]]:
        """生成专家建议"""
        suggestions = []
        
        # 基于上下文生成建议
        if 'device_type' in self.current_context:
            device_type = self.current_context['device_type']
            if device_type == 'protection':
                suggestions.append({
                    'title': '保护设备配置建议',
                    'description': '建议检查保护设备的逻辑节点配置是否完整',
                    'priority': 'high'
                })
        
        if 'protocol' in self.current_context:
            protocol = self.current_context['protocol']
            if protocol == 'GOOSE':
                suggestions.append({
                    'title': 'GOOSE配置优化建议',
                    'description': '建议优化GOOSE消息的发布间隔和数据集配置',
                    'priority': 'medium'
                })
        
        return suggestions
    
    def _update_statistics(self):
        """更新统计信息"""
        # 更新知识库统计
        self.kb_stats_tree.clear()
        
        kb_stats = QTreeWidgetItem(self.kb_stats_tree)
        kb_stats.setText(0, "知识库统计")
        
        # 添加统计项目
        stats_items = [
            ("标准数量", "11"),
            ("规则数量", "21"),
            ("设备类型", "5"),
            ("协议类型", "3")
        ]
        
        for name, value in stats_items:
            item = QTreeWidgetItem(kb_stats)
            item.setText(0, f"{name}: {value}")
        
        kb_stats.setExpanded(True)
        
        # 更新使用统计
        self.usage_stats_tree.clear()
        
        usage_stats = QTreeWidgetItem(self.usage_stats_tree)
        usage_stats.setText(0, "使用统计")
        
        usage_items = [
            ("验证次数", "0"),
            ("推荐应用次数", "0"),
            ("知识查询次数", "0")
        ]
        
        for name, value in usage_items:
            item = QTreeWidgetItem(usage_stats)
            item.setText(0, f"{name}: {value}")
        
        usage_stats.setExpanded(True)
