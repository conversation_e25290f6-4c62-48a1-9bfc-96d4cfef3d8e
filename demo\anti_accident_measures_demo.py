#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
国家电网公司十八项电网重大反事故措施演示脚本
展示如何使用反事故措施知识库进行智能变电站二次回路安全分析
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 直接导入需要的模块，避免依赖问题
from src.knowledge.standards.state_grid_anti_accident_measures import StateGridAntiAccidentMeasures
from src.knowledge.standards.comprehensive_circuit_knowledge import ComprehensiveCircuitKnowledge


def main():
    print("国家电网公司十八项电网重大反事故措施演示")
    print("=" * 50)
    
    # 1. 初始化反事故措施知识库
    print("\n1. 初始化国家电网公司十八项电网重大反事故措施知识库...")
    anti_accident_knowledge = StateGridAntiAccidentMeasures()
    
    # 2. 展示反事故措施标准
    print("\n2. 国家电网公司十八项电网重大反事故措施标准:")
    standards = anti_accident_knowledge.get_all_standards()
    for standard in standards:
        print(f"  - {standard.name}")
        print(f"    标准编号: {standard.standard_number}")
        print(f"    描述: {standard.description}")
        print(f"    内容概要: {standard.content[:100]}...")
        print()
    
    # 3. 展示反事故措施规则
    print("\n3. 反事故措施规则:")
    rules = anti_accident_knowledge.get_all_rules()
    for rule in rules:
        print(f"  - {rule.name}")
        print(f"    描述: {rule.description}")
        print(f"    严重程度: {rule.severity}")
        print(f"    类别: {rule.category}")
        print(f"    适用设备: {', '.join(rule.applicable_devices) if rule.applicable_devices else '无'}")
        print(f"    关键词: {', '.join(rule.keywords) if rule.keywords else '无'}")
        print()
    
    # 4. 展示反事故措施技术要求
    print("\n4. 反事故措施技术要求:")
    requirements = anti_accident_knowledge.get_all_requirements()
    for req in requirements:
        print(f"  - {req.name}")
        print(f"    描述: {req.description}")
        print(f"    优先级: {req.priority}")
        print(f"    强制性: {'是' if req.mandatory else '否'}")
        print(f"    内容概要: {req.content[:100]}...")
        print()
    
    # 5. 初始化综合回路知识库
    print("\n5. 初始化综合回路知识库...")
    circuit_knowledge = ComprehensiveCircuitKnowledge()
    
    # 6. 展示保护回路的反事故措施
    print("\n6. 保护回路反事故措施:")
    protection_anti_accident = circuit_knowledge.get_anti_accident_measures('protection_circuit')
    if protection_anti_accident:
        print(f"  名称: {protection_anti_accident.get('name', 'N/A')}")
        measures = protection_anti_accident.get('measures', {})
        for measure_category, measure_details in measures.items():
            print(f"  {measure_category}:")
            for key, value in measure_details.items():
                print(f"    {key}: {value}")
        
        common_accidents = protection_anti_accident.get('common_accidents', [])
        print("  常见事故及预防措施:")
        for accident in common_accidents:
            print(f"    事故类型: {accident.get('type', 'N/A')}")
            print(f"    事故原因: {', '.join(accident.get('causes', []))}")
            print(f"    预防措施: {', '.join(accident.get('prevention', []))}")
            print()
    
    print("\n演示完成！")


if __name__ == "__main__":
    main()