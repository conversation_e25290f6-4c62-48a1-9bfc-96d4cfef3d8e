"""
文本分析器
提供自然语言处理功能
"""

import re
import logging
from typing import List, Dict, Tuple, Set
from collections import Counter
import jieba
import jieba.posseg as pseg


logger = logging.getLogger(__name__)


class TextAnalyzer:
    """文本分析器"""
    
    def __init__(self):
        """初始化文本分析器"""
        # 初始化jieba分词
        jieba.initialize()
        
        # 添加电力专业词汇
        self._add_power_vocabulary()
        
        # 停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
            'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those'
        }
        
        logger.info("文本分析器初始化完成")
    
    def _add_power_vocabulary(self):
        """添加电力专业词汇"""
        power_terms = [
            # IEC61850相关
            'IEC61850', 'SCD', 'ICD', 'CID', 'SCL', 'GOOSE', 'SMV', 'MMS',
            'IED', 'LN', 'DO', 'DA', 'FC', 'CDC',
            
            # 设备类型
            '变电站', '智能变电站', '数字化变电站', '开关站',
            '变压器', '主变', '断路器', '隔离开关', '接地开关',
            '电流互感器', '电压互感器', '避雷器', '电抗器',
            '母线', '线路', '馈线', '出线', '进线',
            
            # 保护装置
            '继电保护', '保护装置', '差动保护', '距离保护', '过流保护',
            '零序保护', '接地保护', '失灵保护', '重合闸',
            '备自投', '低频减载', '低压减载',
            
            # 通信和网络
            '通信网络', '以太网', '光纤', '串口', '网络交换机',
            '通信协议', '报文', '数据传输', '网络拓扑',
            
            # 测量和监控
            '测量', '监控', '遥测', '遥信', '遥控', '遥调',
            '状态监测', '故障录波', '电能质量',
            
            # 系统和功能
            '自动化系统', '监控系统', '调度系统', 'SCADA',
            '人机界面', 'HMI', '操作员站', '工程师站',
            
            # 标准和规范
            '国家标准', '行业标准', '企业标准', '技术规程',
            '设计规范', '验收规范', '运行规程', '检修规程'
        ]
        
        for term in power_terms:
            jieba.add_word(term)
    
    def extract_keywords(self, text: str, top_k: int = 20) -> List[Tuple[str, float]]:
        """
        提取关键词
        
        Args:
            text: 输入文本
            top_k: 返回前k个关键词
            
        Returns:
            List[Tuple[str, float]]: 关键词及其权重
        """
        try:
            # 分词和词性标注
            words = pseg.cut(text)
            
            # 过滤词汇
            filtered_words = []
            for word, flag in words:
                # 保留名词、动词、形容词、专有名词
                if (flag.startswith('n') or flag.startswith('v') or 
                    flag.startswith('a') or flag.startswith('nr') or
                    flag.startswith('nt')):
                    if (len(word) >= 2 and 
                        word not in self.stop_words and
                        not word.isdigit()):
                        filtered_words.append(word)
            
            # 计算词频
            word_freq = Counter(filtered_words)
            
            # 计算TF-IDF权重（简化版）
            total_words = len(filtered_words)
            keywords = []
            
            for word, freq in word_freq.most_common(top_k):
                tf = freq / total_words
                # 简化的权重计算
                weight = tf * (1 + len(word) * 0.1)  # 长词汇权重稍高
                keywords.append((word, weight))
            
            return keywords
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        提取命名实体
        
        Args:
            text: 输入文本
            
        Returns:
            Dict[str, List[str]]: 实体类型和实体列表
        """
        entities = {
            'standards': [],
            'devices': [],
            'protocols': [],
            'organizations': [],
            'locations': []
        }
        
        try:
            # 标准编号
            standard_patterns = [
                r'GB/T?\s*\d+(?:\.\d+)*-\d{4}',
                r'DL/T?\s*\d+(?:\.\d+)*-\d{4}',
                r'IEC\s*\d+(?:-\d+)*:?\d{4}?',
                r'IEEE\s*\d+(?:\.\d+)*-\d{4}',
                r'Q/GDW\s*\d+-\d{4}'
            ]
            
            for pattern in standard_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                entities['standards'].extend(matches)
            
            # 设备名称
            device_patterns = [
                r'[^，。]*(?:变压器|断路器|隔离开关|互感器|避雷器)[^，。]*',
                r'[^，。]*(?:保护装置|测控装置|监控装置)[^，。]*'
            ]
            
            for pattern in device_patterns:
                matches = re.findall(pattern, text)
                entities['devices'].extend([m.strip() for m in matches if len(m.strip()) > 2])
            
            # 通信协议
            protocol_keywords = ['IEC61850', 'GOOSE', 'SMV', 'MMS', '以太网', 'TCP/IP']
            for keyword in protocol_keywords:
                if keyword in text:
                    entities['protocols'].append(keyword)
            
            # 去重
            for key in entities:
                entities[key] = list(set(entities[key]))
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
        
        return entities
    
    def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """
        情感分析（简化版）
        
        Args:
            text: 输入文本
            
        Returns:
            Dict[str, float]: 情感分析结果
        """
        # 简化的情感词典
        positive_words = {'好', '优秀', '先进', '可靠', '安全', '高效', '稳定'}
        negative_words = {'差', '错误', '故障', '失效', '危险', '不安全', '不稳定'}
        
        words = jieba.lcut(text)
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        total_words = len(words)
        
        if total_words == 0:
            return {'positive': 0.0, 'negative': 0.0, 'neutral': 1.0}
        
        positive_score = positive_count / total_words
        negative_score = negative_count / total_words
        neutral_score = 1.0 - positive_score - negative_score
        
        return {
            'positive': positive_score,
            'negative': negative_score,
            'neutral': neutral_score
        }
    
    def extract_sentences_with_keywords(self, text: str, keywords: List[str]) -> List[str]:
        """
        提取包含关键词的句子
        
        Args:
            text: 输入文本
            keywords: 关键词列表
            
        Returns:
            List[str]: 包含关键词的句子
        """
        sentences = re.split(r'[。！？；\n]', text)
        matching_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            for keyword in keywords:
                if keyword in sentence:
                    matching_sentences.append(sentence)
                    break
        
        return matching_sentences
    
    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        计算文本相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        try:
            # 分词
            words1 = set(jieba.lcut(text1))
            words2 = set(jieba.lcut(text2))
            
            # 移除停用词
            words1 = words1 - self.stop_words
            words2 = words2 - self.stop_words
            
            # 计算Jaccard相似度
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            
            if union == 0:
                return 0.0
            
            return intersection / union
            
        except Exception as e:
            logger.error(f"相似度计算失败: {e}")
            return 0.0
    
    def extract_technical_terms(self, text: str) -> List[str]:
        """
        提取技术术语
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 技术术语列表
        """
        # 技术术语模式
        patterns = [
            r'[A-Z]{2,}(?:\d+)?',  # 大写缩写
            r'\w*(?:器|装置|设备|系统|网络|协议|标准|规范)\w*',  # 技术设备
            r'IEC\s*\d+(?:-\d+)*',  # IEC标准
            r'GB/T?\s*\d+',  # 国标
            r'DL/T?\s*\d+'  # 行标
        ]
        
        technical_terms = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            technical_terms.extend(matches)
        
        # 去重并过滤
        technical_terms = list(set(technical_terms))
        technical_terms = [term for term in technical_terms if len(term) >= 2]
        
        return technical_terms
