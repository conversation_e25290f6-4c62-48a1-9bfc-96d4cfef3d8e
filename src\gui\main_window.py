"""
主窗口类
IEC61850设计检查器的主界面
"""

import sys
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QToolBar, QStatusBar, QTabWidget, QTextEdit,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem,
    QLabel, QPushButton, QProgressBar, QMessageBox, QFileDialog,
    QDockWidget, QApplication
)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QSettings
from PySide6.QtGui import QAction, QIcon, QFont, QPixmap

from .components.file_manager import FileManagerWidget
from .components.validation_results import ValidationResultsWidget
from .components.network_topology import NetworkTopologyWidget
from .components.device_config import DeviceConfigWidget
from .components.knowledge_panel import KnowledgePanelWidget
from .dialogs.preferences_dialog import PreferencesDialog
from .dialogs.about_dialog import AboutDialog
from .utils.gui_utils import GuiUtils
from .utils.theme_manager import ThemeManager

from ..core.validation.validator import Validator
from ..knowledge.integration.smart_rule_engine import SmartRuleEngine


logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 应用设置
        self.settings = QSettings("IEC61850Checker", "DesignChecker")
        
        # 核心组件
        self.validator = None
        self.smart_engine = None
        self.current_file = None
        self.validation_results = []
        
        # GUI组件
        self.central_widget = None
        self.file_manager = None
        self.validation_results_widget = None
        self.network_topology = None
        self.device_config = None
        self.knowledge_panel = None
        
        # 工具和状态
        self.progress_bar = None
        self.status_label = None
        self.theme_manager = ThemeManager()
        
        # 初始化界面
        self._init_ui()
        self._init_core_components()
        self._load_settings()
        
        logger.info("主窗口初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("IEC61850智能设计检查器 v1.0")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1000)
        
        # 设置窗口图标
        self.setWindowIcon(QIcon("resources/icons/app_icon.png"))
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 创建中央部件
        self._create_central_widget()
        
        # 创建停靠窗口
        self._create_dock_widgets()
        
        # 应用主题
        self.theme_manager.apply_theme(self, "default")
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件
        open_action = QAction("打开文件(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.setStatusTip("打开SCD/ICD/CID文件")
        open_action.triggered.connect(self._open_file)
        file_menu.addAction(open_action)
        
        # 最近文件
        recent_menu = file_menu.addMenu("最近文件(&R)")
        self._update_recent_files_menu(recent_menu)
        
        file_menu.addSeparator()
        
        # 导出报告
        export_action = QAction("导出报告(&E)", self)
        export_action.setShortcut("Ctrl+E")
        export_action.setStatusTip("导出验证报告")
        export_action.triggered.connect(self._export_report)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 首选项
        preferences_action = QAction("首选项(&P)", self)
        preferences_action.setStatusTip("打开首选项设置")
        preferences_action.triggered.connect(self._show_preferences)
        edit_menu.addAction(preferences_action)
        
        # 验证菜单
        validate_menu = menubar.addMenu("验证(&V)")
        
        # 开始验证
        validate_action = QAction("开始验证(&S)", self)
        validate_action.setShortcut("F5")
        validate_action.setStatusTip("开始验证当前文件")
        validate_action.triggered.connect(self._start_validation)
        validate_menu.addAction(validate_action)
        
        # 智能验证
        smart_validate_action = QAction("智能验证(&I)", self)
        smart_validate_action.setShortcut("Ctrl+F5")
        smart_validate_action.setStatusTip("使用知识库进行智能验证")
        smart_validate_action.triggered.connect(self._start_smart_validation)
        validate_menu.addAction(smart_validate_action)
        
        validate_menu.addSeparator()
        
        # 验证配置
        config_action = QAction("验证配置(&C)", self)
        config_action.setStatusTip("配置验证规则")
        config_action.triggered.connect(self._configure_validation)
        validate_menu.addAction(config_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 工具栏
        toolbar_action = QAction("工具栏(&T)", self)
        toolbar_action.setCheckable(True)
        toolbar_action.setChecked(True)
        toolbar_action.triggered.connect(self._toggle_toolbar)
        view_menu.addAction(toolbar_action)
        
        # 状态栏
        statusbar_action = QAction("状态栏(&S)", self)
        statusbar_action.setCheckable(True)
        statusbar_action.setChecked(True)
        statusbar_action.triggered.connect(self._toggle_statusbar)
        view_menu.addAction(statusbar_action)
        
        view_menu.addSeparator()
        
        # 主题
        theme_menu = view_menu.addMenu("主题(&H)")
        self._create_theme_menu(theme_menu)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 用户手册
        manual_action = QAction("用户手册(&M)", self)
        manual_action.setShortcut("F1")
        manual_action.setStatusTip("打开用户手册")
        manual_action.triggered.connect(self._show_manual)
        help_menu.addAction(manual_action)
        
        # IEC61850标准
        standard_action = QAction("IEC61850标准(&S)", self)
        standard_action.setStatusTip("查看IEC61850标准说明")
        standard_action.triggered.connect(self._show_standard_help)
        help_menu.addAction(standard_action)
        
        help_menu.addSeparator()
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于IEC61850设计检查器")
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setObjectName("MainToolBar")
        
        # 打开文件
        open_action = QAction(QIcon("resources/icons/open.png"), "打开", self)
        open_action.setStatusTip("打开文件")
        open_action.triggered.connect(self._open_file)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # 验证
        validate_action = QAction(QIcon("resources/icons/validate.png"), "验证", self)
        validate_action.setStatusTip("开始验证")
        validate_action.triggered.connect(self._start_validation)
        toolbar.addAction(validate_action)
        
        # 智能验证
        smart_action = QAction(QIcon("resources/icons/smart.png"), "智能验证", self)
        smart_action.setStatusTip("智能验证")
        smart_action.triggered.connect(self._start_smart_validation)
        toolbar.addAction(smart_action)
        
        toolbar.addSeparator()
        
        # 导出报告
        export_action = QAction(QIcon("resources/icons/export.png"), "导出", self)
        export_action.setStatusTip("导出报告")
        export_action.triggered.connect(self._export_report)
        toolbar.addAction(export_action)
        
        toolbar.addSeparator()
        
        # 设置
        settings_action = QAction(QIcon("resources/icons/settings.png"), "设置", self)
        settings_action.setStatusTip("打开设置")
        settings_action.triggered.connect(self._show_preferences)
        toolbar.addAction(settings_action)
    
    def _create_status_bar(self):
        """创建状态栏"""
        statusbar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        statusbar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        statusbar.addPermanentWidget(self.progress_bar)
        
        # 文件信息
        self.file_info_label = QLabel("")
        statusbar.addPermanentWidget(self.file_info_label)
    
    def _create_central_widget(self):
        """创建中央部件"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(self.central_widget)
        
        # 创建分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧面板 - 文件管理
        self.file_manager = FileManagerWidget()
        self.file_manager.file_selected.connect(self._on_file_selected)
        main_splitter.addWidget(self.file_manager)
        
        # 右侧面板 - 标签页
        self.tab_widget = QTabWidget()
        main_splitter.addWidget(self.tab_widget)
        
        # 验证结果标签页
        self.validation_results_widget = ValidationResultsWidget()
        self.tab_widget.addTab(self.validation_results_widget, "验证结果")
        
        # 网络拓扑标签页
        self.network_topology = NetworkTopologyWidget()
        self.tab_widget.addTab(self.network_topology, "网络拓扑")
        
        # 设备配置标签页
        self.device_config = DeviceConfigWidget()
        self.tab_widget.addTab(self.device_config, "设备配置")
        
        # 设置分割器比例
        main_splitter.setSizes([300, 900])
    
    def _create_dock_widgets(self):
        """创建停靠窗口"""
        # 知识面板
        knowledge_dock = QDockWidget("知识库", self)
        knowledge_dock.setObjectName("KnowledgeDock")
        self.knowledge_panel = KnowledgePanelWidget()
        knowledge_dock.setWidget(self.knowledge_panel)
        self.addDockWidget(Qt.RightDockWidgetArea, knowledge_dock)
        
        # 日志面板
        log_dock = QDockWidget("日志", self)
        log_dock.setObjectName("LogDock")
        self.log_widget = QTextEdit()
        self.log_widget.setMaximumHeight(150)
        self.log_widget.setReadOnly(True)
        log_dock.setWidget(self.log_widget)
        self.addDockWidget(Qt.BottomDockWidgetArea, log_dock)
    
    def _init_core_components(self):
        """初始化核心组件"""
        try:
            # 初始化验证器
            self.validator = Validator()
            
            # 初始化智能规则引擎
            self.smart_engine = SmartRuleEngine()
            
            logger.info("核心组件初始化完成")
            
        except Exception as e:
            logger.error(f"核心组件初始化失败: {e}")
            QMessageBox.critical(self, "错误", f"核心组件初始化失败:\n{e}")
    
    def _load_settings(self):
        """加载设置"""
        try:
            # 恢复窗口状态
            geometry = self.settings.value("geometry")
            if geometry:
                self.restoreGeometry(geometry)
            
            state = self.settings.value("windowState")
            if state:
                self.restoreState(state)
            
            # 恢复主题
            theme = self.settings.value("theme", "default")
            self.theme_manager.apply_theme(self, theme)
            
        except Exception as e:
            logger.warning(f"加载设置失败: {e}")
    
    def _save_settings(self):
        """保存设置"""
        try:
            self.settings.setValue("geometry", self.saveGeometry())
            self.settings.setValue("windowState", self.saveState())
            self.settings.setValue("theme", self.theme_manager.current_theme)
            
        except Exception as e:
            logger.warning(f"保存设置失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self._save_settings()
        event.accept()
    
    # 槽函数实现
    def _open_file(self):
        """打开文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "打开IEC61850配置文件",
            "",
            "IEC61850文件 (*.scd *.icd *.cid);;所有文件 (*.*)"
        )

        if file_path:
            self._load_file(file_path)

    def _load_file(self, file_path: str):
        """加载文件"""
        try:
            self.current_file = Path(file_path)
            self.file_manager.load_file(file_path)
            self.file_info_label.setText(f"文件: {self.current_file.name}")
            self.status_label.setText(f"已加载: {self.current_file.name}")

            # 添加到最近文件
            self._add_recent_file(file_path)

            logger.info(f"文件加载成功: {file_path}")

        except Exception as e:
            logger.error(f"文件加载失败: {e}")
            QMessageBox.critical(self, "错误", f"文件加载失败:\n{e}")

    def _start_validation(self):
        """开始验证"""
        if not self.current_file:
            QMessageBox.warning(self, "警告", "请先打开一个文件")
            return

        try:
            self.status_label.setText("正在验证...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            # 执行验证
            results = self.validator.validate_file(str(self.current_file))
            self.validation_results = results

            # 显示结果
            self.validation_results_widget.show_results(results)
            self.tab_widget.setCurrentIndex(0)  # 切换到验证结果标签页

            self.progress_bar.setVisible(False)
            self.status_label.setText(f"验证完成 - 发现 {len(results)} 个问题")

            logger.info(f"验证完成，发现 {len(results)} 个问题")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.status_label.setText("验证失败")
            logger.error(f"验证失败: {e}")
            QMessageBox.critical(self, "错误", f"验证失败:\n{e}")

    def _start_smart_validation(self):
        """开始智能验证"""
        if not self.current_file:
            QMessageBox.warning(self, "警告", "请先打开一个文件")
            return

        try:
            self.status_label.setText("正在进行智能验证...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)

            # 执行智能验证
            data = self.file_manager.get_parsed_data()
            context = self._build_validation_context()

            results = self.smart_engine.validate(data, context)
            self.validation_results = results

            # 显示结果
            self.validation_results_widget.show_results(results)
            self.tab_widget.setCurrentIndex(0)

            # 显示智能推荐
            recommendations = self.smart_engine.get_smart_recommendations(data, context)
            self.knowledge_panel.show_recommendations(recommendations)

            self.progress_bar.setVisible(False)
            self.status_label.setText(f"智能验证完成 - 发现 {len(results)} 个问题")

            logger.info(f"智能验证完成，发现 {len(results)} 个问题")

        except Exception as e:
            self.progress_bar.setVisible(False)
            self.status_label.setText("智能验证失败")
            logger.error(f"智能验证失败: {e}")
            QMessageBox.critical(self, "错误", f"智能验证失败:\n{e}")

    def _build_validation_context(self) -> Dict[str, Any]:
        """构建验证上下文"""
        context = {}

        if self.current_file:
            context['file_path'] = str(self.current_file)
            context['file_type'] = self.current_file.suffix.upper()[1:]  # 去掉点号

        # 从文件管理器获取更多上下文信息
        if self.file_manager:
            file_info = self.file_manager.get_file_info()
            context.update(file_info)

        return context

    def _export_report(self):
        """导出报告"""
        if not self.validation_results:
            QMessageBox.warning(self, "警告", "没有验证结果可导出")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出验证报告",
            f"validation_report_{self.current_file.stem}.html",
            "HTML文件 (*.html);;PDF文件 (*.pdf);;所有文件 (*.*)"
        )

        if file_path:
            try:
                self.validation_results_widget.export_report(file_path, self.validation_results)
                QMessageBox.information(self, "成功", "报告导出成功")

            except Exception as e:
                logger.error(f"报告导出失败: {e}")
                QMessageBox.critical(self, "错误", f"报告导出失败:\n{e}")

    def _on_file_selected(self, file_info: Dict[str, Any]):
        """文件选择事件"""
        # 更新设备配置视图
        self.device_config.show_file_info(file_info)

        # 更新网络拓扑视图
        self.network_topology.show_network(file_info)

    def _show_preferences(self):
        """显示首选项对话框"""
        dialog = PreferencesDialog(self)
        if dialog.exec() == dialog.Accepted:
            # 应用新设置
            self._apply_preferences(dialog.get_preferences())

    def _show_about(self):
        """显示关于对话框"""
        dialog = AboutDialog(self)
        dialog.exec()

    def _add_recent_file(self, file_path: str):
        """添加到最近文件"""
        recent_files = self.settings.value("recentFiles", [])
        if isinstance(recent_files, str):
            recent_files = [recent_files]
        elif not isinstance(recent_files, list):
            recent_files = []

        if file_path in recent_files:
            recent_files.remove(file_path)

        recent_files.insert(0, file_path)
        recent_files = recent_files[:10]  # 保留最近10个文件

        self.settings.setValue("recentFiles", recent_files)

    def _update_recent_files_menu(self, menu):
        """更新最近文件菜单"""
        menu.clear()

        recent_files = self.settings.value("recentFiles", [])
        if isinstance(recent_files, str):
            recent_files = [recent_files]
        elif not isinstance(recent_files, list):
            recent_files = []

        for file_path in recent_files:
            if Path(file_path).exists():
                action = QAction(Path(file_path).name, self)
                action.setStatusTip(file_path)
                action.triggered.connect(lambda checked, path=file_path: self._load_file(path))
                menu.addAction(action)

    def _create_theme_menu(self, menu):
        """创建主题菜单"""
        themes = ["default", "dark", "light"]

        for theme in themes:
            action = QAction(theme.title(), self)
            action.setCheckable(True)
            action.setChecked(theme == self.theme_manager.current_theme)
            action.triggered.connect(lambda checked, t=theme: self._apply_theme(t))
            menu.addAction(action)

    def _apply_theme(self, theme: str):
        """应用主题"""
        self.theme_manager.apply_theme(self, theme)
        self.settings.setValue("theme", theme)

    def _toggle_toolbar(self, checked: bool):
        """切换工具栏显示"""
        toolbar = self.findChild(QToolBar, "MainToolBar")
        if toolbar:
            toolbar.setVisible(checked)

    def _toggle_statusbar(self, checked: bool):
        """切换状态栏显示"""
        self.statusBar().setVisible(checked)

    def _configure_validation(self):
        """配置验证规则"""
        # TODO: 实现验证配置对话框
        QMessageBox.information(self, "提示", "验证配置功能正在开发中")

    def _show_manual(self):
        """显示用户手册"""
        # TODO: 实现用户手册
        QMessageBox.information(self, "提示", "用户手册功能正在开发中")

    def _show_standard_help(self):
        """显示标准帮助"""
        # TODO: 实现标准帮助
        QMessageBox.information(self, "提示", "标准帮助功能正在开发中")

    def _apply_preferences(self, preferences: Dict[str, Any]):
        """应用首选项设置"""
        # TODO: 实现首选项应用
        pass
