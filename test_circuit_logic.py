#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回路逻辑校验API
"""

import requests
import json

def test_circuit_logic_api():
    """测试回路逻辑校验API"""
    
    # 测试数据1: 违规的跳闸连接（保护IED直接跳闸断路器）
    test_data_1 = {
        "nodes": [
            {"id": "IED_001", "name": "保护IED", "type": "ied", "category": "protection"},
            {"id": "BRK_001", "name": "断路器", "type": "device", "category": "switchgear"}
        ],
        "links": [
            {"source": "IED_001", "target": "BRK_001", "type": "trip"}
        ]
    }
    
    # 测试数据2: 正确的跳闸连接（保护IED -> BCU -> 断路器）
    test_data_2 = {
        "nodes": [
            {"id": "IED_001", "name": "保护IED", "type": "ied", "category": "protection"},
            {"id": "BCU_001", "name": "BCU", "type": "ied", "category": "control"},
            {"id": "BRK_001", "name": "断路器", "type": "device", "category": "switchgear"}
        ],
        "links": [
            {"source": "IED_001", "target": "BCU_001", "type": "trip"},
            {"source": "BCU_001", "target": "BRK_001", "type": "control"}
        ]
    }
    
    # 测试数据3: 复杂的110kV线路二次回路
    test_data_3 = {
        "nodes": [
            # 网络层
            {"id": "NET_001", "name": "过程层网络", "type": "network", "category": "process"},
            {"id": "NET_002", "name": "站控层网络", "type": "network", "category": "station"},
            
            # 保护层
            {"id": "PROT_001", "name": "线路保护", "type": "ied", "category": "protection"},
            {"id": "PROT_002", "name": "母线保护", "type": "ied", "category": "protection"},
            {"id": "PROT_003", "name": "失灵保护", "type": "ied", "category": "protection"},
            
            # 控制层
            {"id": "BCU_001", "name": "BCU", "type": "ied", "category": "control"},
            {"id": "TPL_001", "name": "联跳装置", "type": "ied", "category": "control"},
            
            # 测量层
            {"id": "MU_001", "name": "合并单元", "type": "ied", "category": "measurement"},
            {"id": "MU_002", "name": "合并单元", "type": "ied", "category": "measurement"},
            
            # 一次设备
            {"id": "BRK_001", "name": "断路器", "type": "device", "category": "switchgear"},
            {"id": "DS_001", "name": "隔离开关", "type": "device", "category": "switchgear"},
            {"id": "CT_001", "name": "电流互感器", "type": "device", "category": "transformer"},
            {"id": "PT_001", "name": "电压互感器", "type": "device", "category": "transformer"},
            
            # 监控层
            {"id": "MMS_001", "name": "监控系统", "type": "ied", "category": "monitoring"}
        ],
        "links": [
            # SMV采样
            {"source": "MU_001", "target": "PROT_001", "type": "smv"},
            {"source": "MU_002", "target": "PROT_001", "type": "smv"},
            
            # 跳闸连接（违规：保护直接跳闸断路器）
            {"source": "PROT_001", "target": "BRK_001", "type": "trip"},
            
            # 控制连接
            {"source": "BCU_001", "target": "BRK_001", "type": "control"},
            {"source": "BCU_001", "target": "DS_001", "type": "control"},
            
            # 联锁连接
            {"source": "PROT_003", "target": "TPL_001", "type": "interlock"},
            {"source": "TPL_001", "target": "BCU_001", "type": "interlock"},
            
            # GOOSE连接
            {"source": "PROT_001", "target": "PROT_002", "type": "goose"},
            
            # MMS连接
            {"source": "MMS_001", "target": "PROT_001", "type": "mms"},
            {"source": "MMS_001", "target": "BCU_001", "type": "mms"},
            
            # 一次连接
            {"source": "BRK_001", "target": "DS_001", "type": "primary"}
        ]
    }
    
    test_cases = [
        ("违规跳闸连接", test_data_1),
        ("正确跳闸连接", test_data_2),
        ("复杂110kV回路", test_data_3)
    ]
    
    for test_name, test_data in test_cases:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print(f"{'='*50}")
        
        try:
            response = requests.post(
                'http://127.0.0.1:5000/api/validate_circuit_logic',
                json=test_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    print(f"✅ 校验成功")
                    print(f"   是否有效: {data['is_valid']}")
                    print(f"   违规连接数: {data['summary']['invalid_links_count']}")
                    print(f"   错误数: {data['summary']['error_count']}")
                    print(f"   警告数: {data['summary']['warning_count']}")
                    print(f"   执行时间: {data['summary']['execution_time']:.4f}秒")
                    
                    if data['invalid_links']:
                        print(f"   违规连接:")
                        for link in data['invalid_links']:
                            print(f"     - {link['source']} -> {link['target']} ({link['type']}): {link['reason']}")
                    
                    if data['issues']:
                        print(f"   问题详情:")
                        for issue in data['issues']:
                            print(f"     - [{issue['severity']}] {issue['message']}")
                            print(f"       路径: {issue['path']}")
                            print(f"       建议: {issue['suggestion']}")
                else:
                    print(f"❌ 校验失败: {result['error']}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")

if __name__ == "__main__":
    test_circuit_logic_api()
