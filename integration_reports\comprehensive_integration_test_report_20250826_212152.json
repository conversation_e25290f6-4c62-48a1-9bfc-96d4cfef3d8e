{"test_summary": {"test_time": "2025-08-26T21:21:52.793849", "total_scenarios": 7, "successful_scenarios": 7, "average_score": 80.0, "success_rate": 100.0, "functional_test_rate": 100.0}, "test_results": {"端到端SCD审查流程": {"scenario_name": "端到端SCD审查流程", "description": "完整的SCD文件审查流程，从解析到报告生成", "start_time": "2025-08-26T21:21:50.753296", "success": true, "integration_score": 80, "performance_metrics": {"parse_time": 0.0010631084442138672, "validation_time": 0.0002751350402832031, "validation_score": 100.0, "total_processing_time": 0.0013382434844970703}, "functional_tests": {"scd_file_creation": true, "scd_parsing": true, "iec61850_validation": true}, "issues": ["专业报告生成测试失败: 'ProfessionalReportGenerator' object has no attribute 'generate_comprehensive_report'"], "recommendations": ["端到端流程基本可用，建议优化性能"], "end_time": "2025-08-26T21:21:50.793017", "duration": 0.039721}, "智能审查系统集成": {"scenario_name": "智能审查系统集成", "description": "智能审查系统的完整功能验证", "start_time": "2025-08-26T21:21:50.793450", "success": true, "integration_score": 65, "performance_metrics": {}, "functional_tests": {"demo_availability": 100.0, "unified_engine": true, "intelligence_rate": 100.0}, "issues": ["demo_scd_intelligent_review.py 执行失败: Traceback (most recent call last):\n  File \"F:\\baohu1\\demo_scd_intelligent_review.py\", line 568, in <module>\n    main()\n    ~~~~^^\n  File \"F:\\baohu1\\demo_scd_intelligent_review.py\", line 555, in main\n    system.demonstrate_intelligent_review_process()\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"F:\\baohu1\\demo_scd_intelligent_review.py\", line 48, in demonstrate_intelligent_review_process\n    self._step_parse_scd_file()\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"F:\\baohu1\\demo_scd_intelligent_review.py\", line 159, in _step_parse_scd_file\n    print(\"\\u2713 SCD文件解析完成\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'gbk' codec can't encode character '\\u2713' in position 0: illegal multibyte sequence\n"], "recommendations": ["建议完善智能审查系统的功能模块"], "end_time": "2025-08-26T21:21:51.805073", "duration": 1.011623}, "知识推理引擎协作": {"scenario_name": "知识推理引擎协作", "description": "多个知识推理引擎的协同工作验证", "start_time": "2025-08-26T21:21:51.805422", "success": true, "integration_score": 88, "performance_metrics": {}, "functional_tests": {"engine_availability": 100.0, "import_success_rate": 66.66666666666666, "collaboration_rate": 100.0, "knowledge_sharing_score": 12}, "issues": ["引擎导入失败 src/knowledge/reasoning/iec61850_logic_verification_engine.py: unindent does not match any outer indentation level (src/knowledge/reasoning/iec61850_logic_verification_engine.py, line 1211)"], "recommendations": ["知识推理引擎协作优秀，智能化程度高"], "end_time": "2025-08-26T21:21:51.812894", "duration": 0.007472}, "专业报告生成集成": {"scenario_name": "专业报告生成集成", "description": "专业技术报告的完整生成流程", "start_time": "2025-08-26T21:21:51.813437", "success": true, "integration_score": 60, "performance_metrics": {}, "functional_tests": {"report_files_count": 5, "report_quality_score": 10}, "issues": ["报告生成演示失败: Traceback (most recent call last):\n  File \"F:\\baohu1\\demo_professional_report.py\", line 448, in <module>\n    sys.exit(main())\n             ~~~~^^\n  File \"F:\\baohu1\\demo_professional_report.py\", line 435, in main\n    success = demo_professional_report()\n  File \"F:\\baohu1\\demo_professional_report.py\", line 76, in demo_professional_report\n    print(detailed_issue)\n    ~~~~~^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'gbk' codec can't encode character '\\u2081' in position 329: illegal multibyte sequence\n"], "recommendations": ["建议完善报告生成功能和提升报告质量"], "end_time": "2025-08-26T21:21:51.947411", "duration": 0.133974}, "并发处理能力测试": {"scenario_name": "并发处理能力测试", "description": "系统在并发场景下的处理能力", "start_time": "2025-08-26T21:21:51.948039", "success": true, "integration_score": 100, "performance_metrics": {"single_thread_time": 0.5017228126525879, "multi_thread_time": 0.20186114311218262, "performance_improvement": 59.76640128341961, "memory_usage": 2.171875}, "functional_tests": {"concurrent_safety": true, "concurrent_error_handling": true}, "issues": [], "recommendations": ["并发处理能力优秀，支持高并发场景"], "end_time": "2025-08-26T21:21:52.668780", "duration": 0.720741}, "系统稳定性测试": {"scenario_name": "系统稳定性测试", "description": "长时间运行和压力测试", "start_time": "2025-08-26T21:21:52.669253", "success": true, "integration_score": 92, "performance_metrics": {"long_run_time": 0.10962677001953125, "success_rate": 100.0, "operations_per_second": 912.1859558772358, "memory_growth": 0.0, "recovery_rate": 75.0}, "functional_tests": {}, "issues": [], "recommendations": ["系统稳定性优秀，可以投入生产环境"], "end_time": "2025-08-26T21:21:52.782133", "duration": 0.11288}, "错误恢复能力测试": {"scenario_name": "错误恢复能力测试", "description": "系统在异常情况下的恢复能力", "start_time": "2025-08-26T21:21:52.782747", "success": true, "integration_score": 75, "performance_metrics": {"error_recovery_rate": 75.0}, "functional_tests": {"log_quality_score": 25, "notification_score": 20}, "issues": [], "recommendations": ["建议加强错误处理和恢复机制"], "end_time": "2025-08-26T21:21:52.784179", "duration": 0.001432}}}