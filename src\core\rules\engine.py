"""
规则执行引擎
负责规则的执行、调度和结果收集
"""

import time
import logging
from typing import List, Dict, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field

from .base import BaseRule, RuleContext, RuleResult, RuleSeverity
from .registry import RuleRegistry, rule_registry


@dataclass
class ExecutionConfig:
    """执行配置"""
    # 并行执行的最大线程数
    max_workers: int = 4
    
    # 单个规则的超时时间（秒）
    rule_timeout: float = 30.0
    
    # 是否在遇到错误时停止执行
    stop_on_error: bool = False
    
    # 是否跳过不适用的规则
    skip_inapplicable: bool = True
    
    # 是否执行禁用的规则
    execute_disabled: bool = False
    
    # 规则过滤器
    rule_filter: Optional[Callable[[BaseRule], bool]] = None
    
    # 严重程度过滤器
    severity_filter: Optional[List[RuleSeverity]] = None
    
    # 类别过滤器
    category_filter: Optional[List[str]] = None


@dataclass
class ExecutionResult:
    """执行结果"""
    # 执行的规则数量
    executed_rules: int = 0
    
    # 跳过的规则数量
    skipped_rules: int = 0
    
    # 失败的规则数量
    failed_rules: int = 0
    
    # 总执行时间
    total_time: float = 0.0
    
    # 各规则的执行结果
    rule_results: Dict[str, RuleResult] = field(default_factory=dict)
    
    # 执行错误
    execution_errors: List[str] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_all_issues(self):
        """获取所有问题"""
        issues = []
        for result in self.rule_results.values():
            issues.extend(result.issues)
        return issues
    
    def get_errors(self):
        """获取所有错误"""
        return [issue for issue in self.get_all_issues() 
                if issue.severity == RuleSeverity.ERROR]
    
    def get_warnings(self):
        """获取所有警告"""
        return [issue for issue in self.get_all_issues() 
                if issue.severity == RuleSeverity.WARNING]
    
    def get_infos(self):
        """获取所有信息"""
        return [issue for issue in self.get_all_issues() 
                if issue.severity == RuleSeverity.INFO]
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.get_errors()) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.get_warnings()) > 0
    
    def get_summary(self) -> str:
        """获取执行摘要"""
        error_count = len(self.get_errors())
        warning_count = len(self.get_warnings())
        info_count = len(self.get_infos())
        
        summary = f"执行了 {self.executed_rules} 个规则"
        if self.skipped_rules > 0:
            summary += f"，跳过 {self.skipped_rules} 个"
        if self.failed_rules > 0:
            summary += f"，失败 {self.failed_rules} 个"
        
        summary += f"。发现 {error_count} 个错误，{warning_count} 个警告，{info_count} 个信息"
        summary += f"。总耗时 {self.total_time:.3f} 秒"
        
        return summary


class RuleExecutor:
    """规则执行器"""
    
    def __init__(self, registry: Optional[RuleRegistry] = None):
        """
        初始化执行器
        
        Args:
            registry: 规则注册表，如果为None则使用全局注册表
        """
        self.registry = registry or rule_registry
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def execute_rule(self, rule: BaseRule, context: RuleContext, 
                    timeout: Optional[float] = None) -> RuleResult:
        """
        执行单个规则
        
        Args:
            rule: 要执行的规则
            context: 执行上下文
            timeout: 超时时间
            
        Returns:
            规则执行结果
        """
        start_time = time.time()
        
        try:
            # 检查规则是否适用
            if not rule.is_applicable(context):
                result = RuleResult(rule_id=rule.rule_id, success=True)
                result.metadata['skipped'] = True
                result.metadata['reason'] = 'not_applicable'
                return result
            
            # 执行规则
            self.logger.debug(f"执行规则: {rule.rule_id}")
            result = rule.validate(context)
            
            # 设置执行时间
            result.execution_time = time.time() - start_time
            
            self.logger.debug(f"规则 {rule.rule_id} 执行完成，耗时 {result.execution_time:.3f}s")
            return result
            
        except Exception as e:
            # 处理执行异常
            execution_time = time.time() - start_time
            self.logger.error(f"规则 {rule.rule_id} 执行异常: {e}")
            
            result = RuleResult(rule_id=rule.rule_id, success=False, execution_time=execution_time)
            result.add_error(f"规则执行异常: {str(e)}", context.get_path_string())
            result.metadata['exception'] = str(e)
            return result
    
    def execute_rules(self, rules: List[BaseRule], context: RuleContext,
                     config: Optional[ExecutionConfig] = None) -> ExecutionResult:
        """
        执行多个规则
        
        Args:
            rules: 要执行的规则列表
            context: 执行上下文
            config: 执行配置
            
        Returns:
            执行结果
        """
        if config is None:
            config = ExecutionConfig()
        
        start_time = time.time()
        result = ExecutionResult()
        
        # 过滤规则
        filtered_rules = self._filter_rules(rules, config)
        
        # 解析执行顺序
        try:
            rule_ids = [rule.rule_id for rule in filtered_rules]
            ordered_rule_ids = self.registry.resolve_execution_order(rule_ids)
            ordered_rules = [self.registry.get_rule(rule_id) for rule_id in ordered_rule_ids]
            ordered_rules = [rule for rule in ordered_rules if rule is not None]
        except ValueError as e:
            result.execution_errors.append(f"依赖解析失败: {e}")
            return result
        
        # 执行规则
        if config.max_workers == 1:
            # 串行执行
            result = self._execute_sequential(ordered_rules, context, config, result)
        else:
            # 并行执行（考虑依赖关系）
            result = self._execute_parallel(ordered_rules, context, config, result)
        
        result.total_time = time.time() - start_time
        result.metadata['config'] = config
        
        self.logger.info(f"规则执行完成: {result.get_summary()}")
        return result
    
    def _filter_rules(self, rules: List[BaseRule], config: ExecutionConfig) -> List[BaseRule]:
        """过滤规则"""
        filtered = []
        
        for rule in rules:
            # 检查是否启用
            if not rule.enabled and not config.execute_disabled:
                continue
            
            # 检查严重程度过滤器
            if config.severity_filter and rule.severity not in config.severity_filter:
                continue
            
            # 检查类别过滤器
            if config.category_filter and rule.category.value not in config.category_filter:
                continue
            
            # 检查自定义过滤器
            if config.rule_filter and not config.rule_filter(rule):
                continue
            
            filtered.append(rule)
        
        return filtered
    
    def _execute_sequential(self, rules: List[BaseRule], context: RuleContext,
                          config: ExecutionConfig, result: ExecutionResult) -> ExecutionResult:
        """串行执行规则"""
        for rule in rules:
            try:
                rule_result = self.execute_rule(rule, context, config.rule_timeout)
                result.rule_results[rule.rule_id] = rule_result
                
                if rule_result.metadata.get('skipped', False):
                    result.skipped_rules += 1
                else:
                    result.executed_rules += 1
                    
                    if not rule_result.success:
                        result.failed_rules += 1
                        
                        if config.stop_on_error:
                            self.logger.warning(f"规则 {rule.rule_id} 失败，停止执行")
                            break
                
            except Exception as e:
                result.execution_errors.append(f"执行规则 {rule.rule_id} 时发生异常: {e}")
                result.failed_rules += 1
                
                if config.stop_on_error:
                    break
        
        return result
    
    def _execute_parallel(self, rules: List[BaseRule], context: RuleContext,
                         config: ExecutionConfig, result: ExecutionResult) -> ExecutionResult:
        """
        并行执行规则（考虑依赖关系）
        
        注意：这是一个简化的并行执行实现，真正的并行执行需要考虑依赖关系
        目前采用分批执行的方式
        """
        # 构建依赖层级
        dependency_levels = self._build_dependency_levels(rules)
        
        # 按层级执行
        for level, level_rules in enumerate(dependency_levels):
            self.logger.debug(f"执行第 {level + 1} 层规则，共 {len(level_rules)} 个")
            
            with ThreadPoolExecutor(max_workers=min(config.max_workers, len(level_rules))) as executor:
                # 提交任务
                future_to_rule = {
                    executor.submit(self.execute_rule, rule, context, config.rule_timeout): rule
                    for rule in level_rules
                }
                
                # 收集结果
                for future in as_completed(future_to_rule):
                    rule = future_to_rule[future]
                    try:
                        rule_result = future.result()
                        result.rule_results[rule.rule_id] = rule_result
                        
                        if rule_result.metadata.get('skipped', False):
                            result.skipped_rules += 1
                        else:
                            result.executed_rules += 1
                            
                            if not rule_result.success:
                                result.failed_rules += 1
                    
                    except Exception as e:
                        result.execution_errors.append(f"执行规则 {rule.rule_id} 时发生异常: {e}")
                        result.failed_rules += 1
            
            # 检查是否需要停止
            if config.stop_on_error and result.failed_rules > 0:
                self.logger.warning("检测到错误，停止执行")
                break
        
        return result
    
    def _build_dependency_levels(self, rules: List[BaseRule]) -> List[List[BaseRule]]:
        """构建依赖层级"""
        rule_map = {rule.rule_id: rule for rule in rules}
        levels = []
        remaining_rules = set(rule.rule_id for rule in rules)
        
        while remaining_rules:
            current_level = []
            
            # 找到没有未满足依赖的规则
            for rule_id in list(remaining_rules):
                rule = rule_map[rule_id]
                dependencies = set(rule.get_dependencies())
                
                # 检查依赖是否都已处理
                unmet_deps = dependencies.intersection(remaining_rules)
                if not unmet_deps:
                    current_level.append(rule)
                    remaining_rules.remove(rule_id)
            
            if not current_level:
                # 存在循环依赖
                self.logger.error(f"检测到循环依赖，剩余规则: {remaining_rules}")
                # 强制添加剩余规则到当前层级
                for rule_id in remaining_rules:
                    current_level.append(rule_map[rule_id])
                remaining_rules.clear()
            
            levels.append(current_level)
        
        return levels


class RuleEngine:
    """规则引擎主类"""
    
    def __init__(self, registry: Optional[RuleRegistry] = None):
        """
        初始化规则引擎
        
        Args:
            registry: 规则注册表
        """
        self.registry = registry or rule_registry
        self.executor = RuleExecutor(self.registry)
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def validate(self, data: Any, config: Optional[ExecutionConfig] = None,
                global_context: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        验证数据
        
        Args:
            data: 要验证的数据
            config: 执行配置
            global_context: 全局上下文
            
        Returns:
            执行结果
        """
        # 创建执行上下文
        context = RuleContext(
            data=data,
            global_context=global_context or {},
            config=config.__dict__ if config else {}
        )
        
        # 获取所有启用的规则
        rules = self.registry.get_enabled_rules()
        
        # 执行规则
        return self.executor.execute_rules(rules, context, config)
    
    def validate_with_rules(self, data: Any, rule_ids: List[str],
                           config: Optional[ExecutionConfig] = None,
                           global_context: Optional[Dict[str, Any]] = None) -> ExecutionResult:
        """
        使用指定规则验证数据
        
        Args:
            data: 要验证的数据
            rule_ids: 要执行的规则ID列表
            config: 执行配置
            global_context: 全局上下文
            
        Returns:
            执行结果
        """
        # 获取指定规则
        rules = []
        for rule_id in rule_ids:
            rule = self.registry.get_rule(rule_id)
            if rule:
                rules.append(rule)
            else:
                self.logger.warning(f"规则 '{rule_id}' 不存在")
        
        # 创建执行上下文
        context = RuleContext(
            data=data,
            global_context=global_context or {},
            config=config.__dict__ if config else {}
        )
        
        # 执行规则
        return self.executor.execute_rules(rules, context, config)
    
    def get_applicable_rules(self, data: Any) -> List[BaseRule]:
        """
        获取适用于指定数据的规则
        
        Args:
            data: 数据对象
            
        Returns:
            适用的规则列表
        """
        context = RuleContext(data=data)
        applicable_rules = []
        
        for rule in self.registry.get_enabled_rules():
            if rule.is_applicable(context):
                applicable_rules.append(rule)
        
        return applicable_rules
