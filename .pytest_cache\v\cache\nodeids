["tests/unit/test_base_models.py::TestBaseModel::test_base_model_copy", "tests/unit/test_base_models.py::TestBaseModel::test_base_model_creation", "tests/unit/test_base_models.py::TestBaseModel::test_base_model_equality", "tests/unit/test_base_models.py::TestBaseModel::test_base_model_serialization", "tests/unit/test_base_models.py::TestIECDataType::test_iec_data_type_values", "tests/unit/test_base_models.py::TestNamedModel::test_named_model_iec_name_validation", "tests/unit/test_base_models.py::TestNamedModel::test_named_model_validation", "tests/unit/test_base_models.py::TestReferenceModel::test_reference_model_validation", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_enum", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_list_not_empty", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_range", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_required", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_unique_names", "tests/unit/test_parsers.py::TestCIDParser::test_cid_parser_creation", "tests/unit/test_parsers.py::TestCIDParser::test_parse_simple_cid", "tests/unit/test_parsers.py::TestICDParser::test_icd_parser_creation", "tests/unit/test_parsers.py::TestICDParser::test_parse_simple_icd", "tests/unit/test_parsers.py::TestParseError::test_parse_error_creation", "tests/unit/test_parsers.py::TestParseError::test_parse_error_with_details", "tests/unit/test_parsers.py::TestParseResult::test_add_error", "tests/unit/test_parsers.py::TestParseResult::test_add_warning", "tests/unit/test_parsers.py::TestParseResult::test_get_summary", "tests/unit/test_parsers.py::TestParseResult::test_parse_result_creation", "tests/unit/test_parsers.py::TestParserFactory::test_create_parser", "tests/unit/test_parsers.py::TestParserFactory::test_create_parser_invalid_type", "tests/unit/test_parsers.py::TestParserFactory::test_detect_file_type_by_extension", "tests/unit/test_parsers.py::TestParserFactory::test_detect_file_type_by_filename", "tests/unit/test_parsers.py::TestParserFactory::test_factory_creation", "tests/unit/test_parsers.py::TestParserFactory::test_get_parser_info", "tests/unit/test_parsers.py::TestSCDParser::test_parse_invalid_xml", "tests/unit/test_parsers.py::TestSCDParser::test_parse_missing_required_elements", "tests/unit/test_parsers.py::TestSCDParser::test_parse_simple_scl_string", "tests/unit/test_parsers.py::TestSCDParser::test_parse_with_communication", "tests/unit/test_parsers.py::TestSCDParser::test_scd_parser_creation", "tests/unit/test_rules.py::TestRuleEngine::test_data_validation", "tests/unit/test_substation_models.py::TestBay::test_bay_add_equipment", "tests/unit/test_substation_models.py::TestBay::test_bay_creation", "tests/unit/test_substation_models.py::TestBay::test_bay_equipment_name_uniqueness", "tests/unit/test_substation_models.py::TestBay::test_bay_get_equipment", "tests/unit/test_substation_models.py::TestBay::test_bay_get_equipments_by_type", "tests/unit/test_substation_models.py::TestBay::test_bay_remove_equipment", "tests/unit/test_substation_models.py::TestConductingEquipment::test_conducting_equipment_creation", "tests/unit/test_substation_models.py::TestConductingEquipment::test_conducting_equipment_validation", "tests/unit/test_substation_models.py::TestSubStation::test_substation_add_voltage_level", "tests/unit/test_substation_models.py::TestSubStation::test_substation_creation", "tests/unit/test_substation_models.py::TestSubStation::test_substation_get_equipment_by_path", "tests/unit/test_substation_models.py::TestSubStation::test_substation_statistics", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_conversion", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_creation", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_string_representation", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_validation", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_add_bay", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_bay_name_uniqueness", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_creation", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_get_all_equipments"]