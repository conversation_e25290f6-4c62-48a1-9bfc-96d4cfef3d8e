["tests/unit/test_base_models.py::TestBaseModel::test_base_model_copy", "tests/unit/test_base_models.py::TestBaseModel::test_base_model_creation", "tests/unit/test_base_models.py::TestBaseModel::test_base_model_equality", "tests/unit/test_base_models.py::TestBaseModel::test_base_model_serialization", "tests/unit/test_base_models.py::TestIECDataType::test_iec_data_type_values", "tests/unit/test_base_models.py::TestNamedModel::test_named_model_iec_name_validation", "tests/unit/test_base_models.py::TestNamedModel::test_named_model_validation", "tests/unit/test_base_models.py::TestReferenceModel::test_reference_model_validation", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_enum", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_list_not_empty", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_range", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_required", "tests/unit/test_base_models.py::TestValidationFunctions::test_validate_unique_names", "tests/unit/test_substation_models.py::TestBay::test_bay_add_equipment", "tests/unit/test_substation_models.py::TestBay::test_bay_creation", "tests/unit/test_substation_models.py::TestBay::test_bay_equipment_name_uniqueness", "tests/unit/test_substation_models.py::TestBay::test_bay_get_equipment", "tests/unit/test_substation_models.py::TestBay::test_bay_get_equipments_by_type", "tests/unit/test_substation_models.py::TestBay::test_bay_remove_equipment", "tests/unit/test_substation_models.py::TestConductingEquipment::test_conducting_equipment_creation", "tests/unit/test_substation_models.py::TestConductingEquipment::test_conducting_equipment_validation", "tests/unit/test_substation_models.py::TestSubStation::test_substation_add_voltage_level", "tests/unit/test_substation_models.py::TestSubStation::test_substation_creation", "tests/unit/test_substation_models.py::TestSubStation::test_substation_get_equipment_by_path", "tests/unit/test_substation_models.py::TestSubStation::test_substation_statistics", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_conversion", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_creation", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_string_representation", "tests/unit/test_substation_models.py::TestVoltage::test_voltage_validation", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_add_bay", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_bay_name_uniqueness", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_creation", "tests/unit/test_substation_models.py::TestVoltageLevel::test_voltage_level_get_all_equipments"]