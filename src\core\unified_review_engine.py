"""
统一审查引擎
整合IEC61850配置文件审查和图纸审查功能，避免重复代码
"""

import logging
import os
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pathlib import Path
from abc import ABC, abstractmethod

# 导入现有的核心组件
from .parsers import ParserFactory as ConfigParserFactory, ParseResult
from .rules import RuleEngine, ExecutionConfig, ReportGenerator, ValidationReport
from .rules.base import BaseRule, RuleContext, RuleResult, RuleSeverity

# 导入图纸审查组件
from ..drawing_review.drawing_parser import DrawingParserFactory
from ..drawing_review.drawing_models import DrawingDocument, ReviewIssue, ReviewSeverity
from ..drawing_review.standards_knowledge import StandardsKnowledgeBase
from ..drawing_review.drawing_analyzer import DrawingAnalyzer

# 导入简化设计检查器
from .simple_design_checker import SimpleDesignChecker

logger = logging.getLogger(__name__)


class ReviewDocument(ABC):
    """审查文档基类"""
    
    def __init__(self, file_path: str, file_type: str):
        self.file_path = file_path
        self.file_type = file_type
        self.metadata = {}
    
    @abstractmethod
    def get_elements(self) -> List[Any]:
        """获取文档元素"""
        pass
    
    @abstractmethod
    def get_document_type(self) -> str:
        """获取文档类型"""
        pass


class ConfigDocument(ReviewDocument):
    """配置文档包装器"""
    
    def __init__(self, file_path: str, parse_result: ParseResult):
        super().__init__(file_path, "config")
        self.parse_result = parse_result
        self.data = parse_result.data
    
    def get_elements(self) -> List[Any]:
        """获取配置元素"""
        elements = []
        if hasattr(self.data, 'ieds'):
            elements.extend(self.data.ieds)
        if hasattr(self.data, 'substations'):
            elements.extend(self.data.substations)
        return elements
    
    def get_document_type(self) -> str:
        return "IEC61850配置文件"


class DrawingDocumentWrapper(ReviewDocument):
    """图纸文档包装器"""
    
    def __init__(self, drawing_doc: DrawingDocument):
        super().__init__(drawing_doc.file_path, "drawing")
        self.drawing_doc = drawing_doc
    
    def get_elements(self) -> List[Any]:
        """获取图纸元素"""
        return self.drawing_doc.elements
    
    def get_document_type(self) -> str:
        return "二次图纸"


class UnifiedReviewIssue:
    """统一的审查问题"""
    
    def __init__(self, 
                 issue_id: str,
                 severity: str,
                 category: str,
                 title: str,
                 description: str,
                 source_type: str,  # "config" 或 "drawing"
                 rule_id: str = "",
                 location: Optional[Dict[str, Any]] = None,
                 suggestion: str = "",
                 auto_fixable: bool = False,
                 standard_reference: str = "",
                 affected_elements: List[str] = None):
        
        self.issue_id = issue_id
        self.severity = severity
        self.category = category
        self.title = title
        self.description = description
        self.source_type = source_type
        self.rule_id = rule_id
        self.location = location
        self.suggestion = suggestion
        self.auto_fixable = auto_fixable
        self.standard_reference = standard_reference
        self.affected_elements = affected_elements or []
        self.created_at = datetime.now()


class UnifiedReviewResult:
    """统一的审查结果"""
    
    def __init__(self, document: ReviewDocument):
        self.document = document
        self.review_id = f"review_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.review_date = datetime.now()
        self.issues: List[UnifiedReviewIssue] = []
        self.compliance_score = 0.0
        self.metadata = {}

        # 添加文件路径和类型属性
        self.file_path = document.file_path
        self.file_type = document.get_document_type()
    
    def add_issue(self, issue: UnifiedReviewIssue):
        """添加问题"""
        self.issues.append(issue)
    
    def get_issues_by_severity(self, severity: str) -> List[UnifiedReviewIssue]:
        """按严重程度获取问题"""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_issues_by_source(self, source_type: str) -> List[UnifiedReviewIssue]:
        """按来源类型获取问题"""
        return [issue for issue in self.issues if issue.source_type == source_type]
    
    def get_summary(self) -> Dict[str, Any]:
        """获取摘要"""
        return {
            'total_issues': len(self.issues),
            'critical_issues': len(self.get_issues_by_severity('critical')),
            'error_issues': len(self.get_issues_by_severity('error')),
            'warning_issues': len(self.get_issues_by_severity('warning')),
            'info_issues': len(self.get_issues_by_severity('info')),
            'config_issues': len(self.get_issues_by_source('config')),
            'drawing_issues': len(self.get_issues_by_source('drawing')),
            'compliance_score': self.compliance_score,
            'auto_fixable_issues': len([i for i in self.issues if i.auto_fixable])
        }
    
    def calculate_compliance_score(self):
        """计算合规性评分"""
        if not self.issues:
            self.compliance_score = 100.0
            return
        
        # 根据问题严重程度计算扣分
        penalty = 0
        for issue in self.issues:
            if issue.severity == 'critical':
                penalty += 20
            elif issue.severity == 'error':
                penalty += 10
            elif issue.severity == 'warning':
                penalty += 5
            elif issue.severity == 'info':
                penalty += 1
        
        self.compliance_score = max(0, 100 - penalty)


class UnifiedReviewEngine:
    """统一审查引擎"""
    
    def __init__(self):
        """初始化统一审查引擎"""
        # 初始化现有组件
        self.config_parser_factory = ConfigParserFactory()
        self.rule_engine = RuleEngine()
        self.report_generator = ReportGenerator()
        
        # 初始化图纸审查组件
        self.drawing_parser_factory = DrawingParserFactory()
        self.drawing_analyzer = DrawingAnalyzer()
        self.standards_knowledge = StandardsKnowledgeBase()

        # 初始化简化设计检查器
        self.design_checker = SimpleDesignChecker()

        logger.info("统一审查引擎初始化完成")
    
    def review_file(self, file_path: str, 
                   check_categories: Optional[List[str]] = None,
                   severity_filter: Optional[str] = None,
                   config: Optional[ExecutionConfig] = None) -> Optional[UnifiedReviewResult]:
        """审查文件（自动识别类型）"""
        try:
            logger.info(f"开始审查文件: {file_path}")
            
            # 检测文件类型
            file_type = self._detect_file_type(file_path)
            
            if file_type == "config":
                return self._review_config_file(file_path, check_categories, severity_filter, config)
            elif file_type == "drawing":
                return self._review_drawing_file(file_path, check_categories, severity_filter)
            else:
                logger.error(f"不支持的文件类型: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"文件审查失败: {e}")
            return None
    
    def review_multiple_files(self, file_paths: List[str], 
                             **kwargs) -> List[UnifiedReviewResult]:
        """批量审查文件"""
        results = []
        
        for file_path in file_paths:
            result = self.review_file(file_path, **kwargs)
            if result:
                results.append(result)
        
        return results
    
    def generate_unified_report(self, review_result: UnifiedReviewResult, 
                               output_format: str = 'html') -> str:
        """生成统一报告"""
        try:
            if output_format.lower() == 'html':
                return self._generate_html_report(review_result)
            elif output_format.lower() == 'json':
                return self._generate_json_report(review_result)
            else:
                raise ValueError(f"不支持的报告格式: {output_format}")
        except Exception as e:
            logger.error(f"生成统一报告失败: {e}")
            return ""
    
    def _detect_file_type(self, file_path: str) -> str:
        """检测文件类型"""
        path = Path(file_path)
        extension = path.suffix.lower()
        
        # 配置文件格式
        config_extensions = {'.scd', '.icd', '.cid', '.xml'}
        # 图纸文件格式
        drawing_extensions = {'.dwg', '.dxf', '.pdf'}
        
        if extension in config_extensions:
            return "config"
        elif extension in drawing_extensions:
            return "drawing"
        else:
            return "unknown"
    
    def _review_config_file(self, file_path: str,
                           check_categories: Optional[List[str]],
                           severity_filter: Optional[str],
                           config: Optional[ExecutionConfig]) -> Optional[UnifiedReviewResult]:
        """审查配置文件"""
        try:
            # 解析配置文件
            parser = self.config_parser_factory.create_parser(file_path)
            parse_result = parser.parse_file(file_path)

            if not parse_result.success:
                logger.error(f"配置文件解析失败: {file_path}")
                # 即使解析失败，也创建一个基本的结果对象
                from .parsers.base_parser import ParseResult
                parse_result = ParseResult()
                parse_result.success = False
                parse_result.data = None

            # 创建文档包装器
            config_doc = ConfigDocument(file_path, parse_result)

            # 创建统一审查结果
            review_result = UnifiedReviewResult(config_doc)

            # 如果解析成功，执行规则验证
            if parse_result.success and parse_result.data:
                execution_result = self.rule_engine.validate(parse_result.data, config)
                # 转换规则执行结果为统一格式
                self._convert_config_issues(execution_result, review_result)
            else:
                # 添加解析失败的问题
                parse_issue = UnifiedReviewIssue(
                    issue_id="parse_error",
                    severity="error",
                    category="文件解析",
                    title="配置文件解析失败",
                    description=f"无法解析配置文件: {file_path}",
                    source_type="config",
                    suggestion="检查文件格式是否正确，确保符合IEC61850标准"
                )
                review_result.add_issue(parse_issue)

            # 执行详细设计检查
            try:
                design_issues = self.design_checker.check_config_design(file_path)
                for issue_dict in design_issues:
                    # 转换字典为UnifiedReviewIssue对象
                    design_issue = UnifiedReviewIssue(
                        title=issue_dict.get('title', '设计问题'),
                        description=issue_dict.get('description', ''),
                        severity=issue_dict.get('severity', 'warning'),
                        category=issue_dict.get('category', '设计检查'),
                        source_type=issue_dict.get('source_type', 'config'),
                        location=issue_dict.get('location', ''),
                        suggestion=issue_dict.get('suggestion', ''),
                        standard_reference=issue_dict.get('standard_reference', ''),
                        affected_elements=issue_dict.get('affected_elements', [])
                    )
                    review_result.add_issue(design_issue)
                logger.info(f"详细设计检查完成: 发现 {len(design_issues)} 个设计问题")
            except Exception as e:
                logger.warning(f"详细设计检查失败: {e}")

            # 计算合规性评分
            review_result.calculate_compliance_score()

            logger.info(f"配置文件审查完成: {len(review_result.issues)} 个问题")
            return review_result

        except Exception as e:
            logger.error(f"配置文件审查失败: {e}")
            # 创建一个包含错误信息的结果
            try:
                from .parsers.base_parser import ParseResult
                parse_result = ParseResult()
                config_doc = ConfigDocument(file_path, parse_result)
                review_result = UnifiedReviewResult(config_doc)

                error_issue = UnifiedReviewIssue(
                    issue_id="review_error",
                    severity="critical",
                    category="系统错误",
                    title="审查过程发生错误",
                    description=str(e),
                    source_type="config"
                )
                review_result.add_issue(error_issue)
                review_result.calculate_compliance_score()

                return review_result
            except:
                return None
    
    def _review_drawing_file(self, file_path: str,
                            check_categories: Optional[List[str]],
                            severity_filter: Optional[str]) -> Optional[UnifiedReviewResult]:
        """审查图纸文件"""
        try:
            # 解析图纸文件
            drawing_doc = self.drawing_parser_factory.parse_file(file_path)
            
            if not drawing_doc:
                logger.error(f"图纸文件解析失败: {file_path}")
                return None
            
            # 创建文档包装器
            drawing_wrapper = DrawingDocumentWrapper(drawing_doc)
            
            # 分析图纸内容
            analysis = self.drawing_analyzer.analyze_drawing(drawing_doc)
            
            # 执行图纸规范检查
            drawing_issues = self._check_drawing_compliance(drawing_doc, analysis, check_categories)
            
            # 创建统一审查结果
            review_result = UnifiedReviewResult(drawing_wrapper)
            
            # 转换图纸问题为统一格式
            self._convert_drawing_issues(drawing_issues, review_result)

            # 执行详细设计检查
            try:
                design_issues = self.design_checker.check_drawing_design(file_path)
                for issue in design_issues:
                    review_result.add_issue(issue)
                logger.info(f"详细设计检查完成: 发现 {len(design_issues)} 个设计问题")
            except Exception as e:
                logger.warning(f"详细设计检查失败: {e}")

            # 计算合规性评分
            review_result.calculate_compliance_score()

            logger.info(f"图纸审查完成: {len(review_result.issues)} 个问题")
            return review_result
            
        except Exception as e:
            logger.error(f"图纸审查失败: {e}")
            return None
    
    def _convert_config_issues(self, execution_result, review_result: UnifiedReviewResult):
        """转换配置问题为统一格式"""
        for rule_result in execution_result.rule_results.values():
            for error in rule_result.errors:
                issue = UnifiedReviewIssue(
                    issue_id=f"config_{len(review_result.issues)}",
                    severity="error",
                    category="配置验证",
                    title=error.message,
                    description=error.details or error.message,
                    source_type="config",
                    rule_id=rule_result.rule_id,
                    suggestion=getattr(error, 'suggestion', ''),
                    standard_reference=getattr(error, 'standard', '')
                )
                review_result.add_issue(issue)
            
            for warning in rule_result.warnings:
                issue = UnifiedReviewIssue(
                    issue_id=f"config_{len(review_result.issues)}",
                    severity="warning",
                    category="配置验证",
                    title=warning.message,
                    description=warning.details or warning.message,
                    source_type="config",
                    rule_id=rule_result.rule_id,
                    suggestion=getattr(warning, 'suggestion', ''),
                    standard_reference=getattr(warning, 'standard', '')
                )
                review_result.add_issue(issue)
    
    def _convert_drawing_issues(self, drawing_issues: List[ReviewIssue], 
                               review_result: UnifiedReviewResult):
        """转换图纸问题为统一格式"""
        for drawing_issue in drawing_issues:
            issue = UnifiedReviewIssue(
                issue_id=drawing_issue.issue_id,
                severity=drawing_issue.severity.value,
                category=drawing_issue.category,
                title=drawing_issue.title,
                description=drawing_issue.description,
                source_type="drawing",
                rule_id=drawing_issue.rule_code,
                location={
                    'x': drawing_issue.location.x,
                    'y': drawing_issue.location.y
                } if drawing_issue.location else None,
                suggestion=drawing_issue.suggestion,
                auto_fixable=drawing_issue.auto_fixable,
                standard_reference=drawing_issue.standard_reference,
                affected_elements=drawing_issue.affected_elements
            )
            review_result.add_issue(issue)
    
    def _check_drawing_compliance(self, drawing_doc: DrawingDocument, 
                                 analysis: Dict[str, Any],
                                 check_categories: Optional[List[str]]) -> List[ReviewIssue]:
        """检查图纸规范（简化版本，复用现有逻辑）"""
        # 这里可以复用现有的图纸规范检查逻辑
        from ..drawing_review.compliance_checker import ComplianceChecker
        
        compliance_checker = ComplianceChecker()
        
        if check_categories:
            # 只检查指定分类
            rule_ids = []
            for category in check_categories:
                category_rules = compliance_checker.get_rules_by_category(category)
                rule_ids.extend(category_rules)
            return compliance_checker.check_specific_rules(drawing_doc, rule_ids)
        else:
            # 检查所有规则
            return compliance_checker.check_compliance(drawing_doc)
    
    def _generate_html_report(self, review_result: UnifiedReviewResult) -> str:
        """生成HTML报告"""
        summary = review_result.get_summary()
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>统一审查报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .summary {{ margin: 20px 0; }}
                .issue {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
                .critical {{ border-left-color: #d32f2f; }}
                .error {{ border-left-color: #f57c00; }}
                .warning {{ border-left-color: #fbc02d; }}
                .info {{ border-left-color: #1976d2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>统一审查报告</h1>
                <p><strong>文件:</strong> {review_result.document.file_path}</p>
                <p><strong>文件类型:</strong> {review_result.document.get_document_type()}</p>
                <p><strong>审查时间:</strong> {review_result.review_date.strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>合规性评分:</strong> {review_result.compliance_score:.1f}/100</p>
            </div>
            
            <div class="summary">
                <h2>问题摘要</h2>
                <ul>
                    <li>总问题数: {summary['total_issues']}</li>
                    <li>关键问题: {summary['critical_issues']}</li>
                    <li>错误: {summary['error_issues']}</li>
                    <li>警告: {summary['warning_issues']}</li>
                    <li>信息: {summary['info_issues']}</li>
                    <li>配置问题: {summary['config_issues']}</li>
                    <li>图纸问题: {summary['drawing_issues']}</li>
                </ul>
            </div>
            
            <div class="issues">
                <h2>问题详情</h2>
                {''.join([self._format_issue_html(issue) for issue in review_result.issues])}
            </div>
        </body>
        </html>
        """
        
        return html_template
    
    def _format_issue_html(self, issue: UnifiedReviewIssue) -> str:
        """格式化问题为HTML"""
        return f"""
        <div class="issue {issue.severity}">
            <h3>{issue.title}</h3>
            <p><strong>来源:</strong> {issue.source_type}</p>
            <p><strong>严重程度:</strong> {issue.severity.upper()}</p>
            <p><strong>描述:</strong> {issue.description}</p>
            <p><strong>建议:</strong> {issue.suggestion}</p>
            <p><strong>标准引用:</strong> {issue.standard_reference}</p>
        </div>
        """
    
    def _generate_json_report(self, review_result: UnifiedReviewResult) -> str:
        """生成JSON报告"""
        import json
        
        report_data = {
            'review_info': {
                'review_id': review_result.review_id,
                'file_path': review_result.document.file_path,
                'file_type': review_result.document.get_document_type(),
                'review_date': review_result.review_date.isoformat(),
                'compliance_score': review_result.compliance_score
            },
            'summary': review_result.get_summary(),
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'severity': issue.severity,
                    'category': issue.category,
                    'title': issue.title,
                    'description': issue.description,
                    'source_type': issue.source_type,
                    'rule_id': issue.rule_id,
                    'location': issue.location,
                    'suggestion': issue.suggestion,
                    'auto_fixable': issue.auto_fixable,
                    'standard_reference': issue.standard_reference,
                    'affected_elements': issue.affected_elements
                }
                for issue in review_result.issues
            ]
        }
        
        return json.dumps(report_data, ensure_ascii=False, indent=2)
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """获取支持的文件格式"""
        return {
            'config_formats': ['.scd', '.icd', '.cid', '.xml'],
            'drawing_formats': self.drawing_parser_factory.get_supported_formats()
        }
    
    def get_available_categories(self) -> Dict[str, List[str]]:
        """获取可用的检查分类"""
        # 获取配置检查分类
        config_categories = ["标准符合性", "设备配置", "通信网络", "数据类型"]
        
        # 获取图纸检查分类
        from ..drawing_review.review_engine import DrawingReviewEngine
        drawing_engine = DrawingReviewEngine()
        drawing_categories = drawing_engine.get_available_check_categories()
        
        return {
            'config_categories': config_categories,
            'drawing_categories': drawing_categories
        }
