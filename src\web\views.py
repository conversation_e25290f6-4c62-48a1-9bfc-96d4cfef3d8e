"""
Web界面视图
提供主要的页面路由和模板渲染
"""

from flask import Blueprint, render_template, request, jsonify, current_app
import os

main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def index():
    """
    主页 - IEC61850设计检查器入口
    
    为智能变电站工程师提供：
    - 清晰的功能导航
    - 快速的文件验证入口
    - 项目价值说明
    """
    return render_template('index.html')


@main_bp.route('/upload')
def upload_page():
    """
    文件上传页面
    
    支持的文件类型：
    - SCD (系统配置描述)
    - ICD (IED能力描述) 
    - CID (已配置IED描述)
    """
    return render_template('upload.html')


@main_bp.route('/validate')
def validate_page():
    """
    验证结果页面
    
    展示验证结果：
    - 错误诊断和修复建议
    - 警告信息和优化建议
    - 配置统计和分析报告
    """
    return render_template('validate.html')


@main_bp.route('/visualize')
def visualize_page():
    """
    可视化分析页面
    
    解决虚端子连接的直观性问题：
    - 网络拓扑图
    - 设备连接关系
    - 数据流向分析
    - 虚拟回路追踪
    """
    return render_template('visualize.html')


@main_bp.route('/compare')
def compare_page():
    """
    配置对比页面
    
    支持版本对比：
    - 配置文件差异分析
    - 变更影响评估
    - 升级建议生成
    """
    return render_template('compare.html')


@main_bp.route('/reports')
def reports_page():
    """
    报告管理页面
    
    报告功能：
    - 历史验证记录
    - 报告导出下载
    - 统计分析图表
    """
    return render_template('reports.html')


@main_bp.route('/help')
def help_page():
    """
    帮助文档页面
    
    降低使用门槛：
    - IEC61850标准解释
    - 工具使用指南
    - 常见问题解答
    - 最佳实践建议
    """
    return render_template('help.html')


@main_bp.route('/about')
def about_page():
    """
    关于页面
    
    项目背景和价值：
    - 智能变电站设计挑战
    - 虚端子问题解决方案
    - 工具技术特点
    - 团队和联系方式
    """
    return render_template('about.html')


@main_bp.route('/admin')
def admin_page():
    """
    管理界面
    
    系统管理功能：
    - 规则配置管理
    - 系统状态监控
    - 用户使用统计
    - 性能分析报告
    """
    return render_template('admin.html')


# 静态页面路由
@main_bp.route('/docs')
def docs_redirect():
    """重定向到帮助文档"""
    return render_template('help.html')


@main_bp.route('/features')
def features_page():
    """功能特性页面"""
    return render_template('features.html')


# 错误页面处理
@main_bp.app_errorhandler(404)
def page_not_found(error):
    """404错误页面"""
    return render_template('errors/404.html'), 404


@main_bp.app_errorhandler(500)
def internal_server_error(error):
    """500错误页面"""
    return render_template('errors/500.html'), 500


# 模板过滤器
@main_bp.app_template_filter('filesize')
def filesize_filter(size_bytes):
    """文件大小格式化过滤器"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


@main_bp.app_template_filter('duration')
def duration_filter(seconds):
    """时间长度格式化过滤器"""
    if seconds < 1:
        return f"{seconds*1000:.0f} ms"
    elif seconds < 60:
        return f"{seconds:.1f} s"
    else:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.0f}s"


# 上下文处理器
@main_bp.context_processor
def inject_navigation():
    """注入导航信息"""
    navigation = [
        {'name': '首页', 'url': '/', 'icon': 'home'},
        {'name': '文件验证', 'url': '/upload', 'icon': 'upload'},
        {'name': '可视化分析', 'url': '/visualize', 'icon': 'network'},
        {'name': '配置对比', 'url': '/compare', 'icon': 'compare'},
        {'name': '报告管理', 'url': '/reports', 'icon': 'reports'},
        {'name': '帮助文档', 'url': '/help', 'icon': 'help'},
    ]
    
    return {'navigation': navigation}


@main_bp.context_processor
def inject_project_info():
    """注入项目信息"""
    return {
        'project_mission': {
            'title': '解决智能变电站二次设计痛点',
            'challenges': [
                '虚端子连接缺乏直观性',
                '传统回路检查方法失效', 
                '配置错误难以发现',
                '专业知识门槛高'
            ],
            'solutions': [
                '智能配置文件分析',
                '可视化网络拓扑展示',
                '自动错误诊断和修复建议',
                '直观的用户界面设计'
            ]
        }
    }
