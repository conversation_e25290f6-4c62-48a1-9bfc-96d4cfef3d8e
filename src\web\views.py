"""
Web界面视图
提供主要的页面路由和模板渲染
"""

from flask import Blueprint, render_template, request, jsonify, current_app, send_file
import os
import json
import tempfile
from datetime import datetime
from pathlib import Path

main_bp = Blueprint('main', __name__)

# 初始化统一审查引擎
try:
    from ..core.unified_review_engine import UnifiedReviewEngine
    unified_engine = UnifiedReviewEngine()
except ImportError as e:
    print(f"统一审查引擎导入失败: {e}")
    unified_engine = None


@main_bp.route('/')
def index():
    """
    主页 - IEC61850设计检查器入口
    
    为智能变电站工程师提供：
    - 清晰的功能导航
    - 快速的文件验证入口
    - 项目价值说明
    """
    return render_template('index.html')


@main_bp.route('/upload')
def upload_page():
    """
    文件上传页面
    
    支持的文件类型：
    - SCD (系统配置描述)
    - ICD (IED能力描述) 
    - CID (已配置IED描述)
    """
    return render_template('upload.html')


@main_bp.route('/validate')
def validate_page():
    """
    验证结果页面
    
    展示验证结果：
    - 错误诊断和修复建议
    - 警告信息和优化建议
    - 配置统计和分析报告
    """
    return render_template('validate.html')


@main_bp.route('/visualize')
def visualize_page():
    """
    可视化分析页面
    
    解决虚端子连接的直观性问题：
    - 网络拓扑图
    - 设备连接关系
    - 数据流向分析
    - 虚拟回路追踪
    """
    return render_template('visualize.html')


@main_bp.route('/compare')
def compare_page():
    """
    配置对比页面

    支持版本对比：
    - 配置文件差异分析
    - 变更影响评估
    - 升级建议生成
    """
    return render_template('compare.html')


@main_bp.route('/drawing-review')
def drawing_review_page():
    """
    图纸审查页面

    功能特点：
    - 支持DWG、DXF、PDF格式
    - 智能规范检查
    - 可视化问题定位
    - 自动修改建议
    """
    return render_template('drawing_review.html')


@main_bp.route('/unified-review')
def unified_review_page():
    """
    统一审查页面

    功能特点：
    - 智能识别文件类型
    - 统一审查配置文件和图纸
    - 支持SCD、ICD、CID、DWG、DXF、PDF格式
    - 统一的问题报告和分析
    """
    return render_template('unified_review.html')


@main_bp.route('/reports')
def reports_page():
    """
    报告管理页面
    
    报告功能：
    - 历史验证记录
    - 报告导出下载
    - 统计分析图表
    """
    return render_template('reports.html')


@main_bp.route('/help')
def help_page():
    """
    帮助文档页面
    
    降低使用门槛：
    - IEC61850标准解释
    - 工具使用指南
    - 常见问题解答
    - 最佳实践建议
    """
    return render_template('help.html')


@main_bp.route('/about')
def about_page():
    """
    关于页面
    
    项目背景和价值：
    - 智能变电站设计挑战
    - 虚端子问题解决方案
    - 工具技术特点
    - 团队和联系方式
    """
    return render_template('about.html')


@main_bp.route('/admin')
def admin_page():
    """
    管理界面
    
    系统管理功能：
    - 规则配置管理
    - 系统状态监控
    - 用户使用统计
    - 性能分析报告
    """
    return render_template('admin.html')


# 静态页面路由
@main_bp.route('/docs')
def docs_redirect():
    """重定向到帮助文档"""
    return render_template('help.html')


@main_bp.route('/features')
def features_page():
    """功能特性页面"""
    return render_template('features.html')


# 错误页面处理
@main_bp.app_errorhandler(404)
def page_not_found(error):
    """404错误页面"""
    return render_template('errors/404.html'), 404


@main_bp.app_errorhandler(500)
def internal_server_error(error):
    """500错误页面"""
    return render_template('errors/500.html'), 500


# 模板过滤器
@main_bp.app_template_filter('filesize')
def filesize_filter(size_bytes):
    """文件大小格式化过滤器"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


@main_bp.app_template_filter('duration')
def duration_filter(seconds):
    """时间长度格式化过滤器"""
    if seconds < 1:
        return f"{seconds*1000:.0f} ms"
    elif seconds < 60:
        return f"{seconds:.1f} s"
    else:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.0f}s"


# 上下文处理器
@main_bp.context_processor
def inject_navigation():
    """注入导航信息"""
    navigation = [
        {'name': '首页', 'url': '/', 'icon': 'home'},
        {'name': '文件验证', 'url': '/upload', 'icon': 'upload'},
        {'name': '图纸审查', 'url': '/drawing-review', 'icon': 'drafting-compass'},
        {'name': '统一审查', 'url': '/unified-review', 'icon': 'search-plus', 'highlight': True},
        {'name': '可视化分析', 'url': '/visualize', 'icon': 'network'},
        {'name': '配置对比', 'url': '/compare', 'icon': 'compare'},
        {'name': '报告管理', 'url': '/reports', 'icon': 'reports'},
        {'name': '帮助文档', 'url': '/help', 'icon': 'help'},
    ]
    
    return {'navigation': navigation}


@main_bp.context_processor
def inject_project_info():
    """注入项目信息"""
    return {
        'project_mission': {
            'title': '解决智能变电站二次设计痛点',
            'challenges': [
                '虚端子连接缺乏直观性',
                '传统回路检查方法失效', 
                '配置错误难以发现',
                '专业知识门槛高'
            ],
            'solutions': [
                '智能配置文件分析',
                '可视化网络拓扑展示',
                '自动错误诊断和修复建议',
                '直观的用户界面设计'
            ]
        }
    }


# ==================== 统一审查API路由 ====================

@main_bp.route('/api/unified-review/formats', methods=['GET'])
def api_unified_review_formats():
    """获取支持的文件格式"""
    try:
        if not unified_engine:
            return jsonify({
                'success': False,
                'message': '统一审查引擎未初始化'
            }), 500

        formats = unified_engine.get_supported_formats()

        # 格式化返回数据
        format_descriptions = {
            '.scd': 'IEC61850系统配置描述文件',
            '.icd': 'IEC61850设备能力描述文件',
            '.cid': 'IEC61850配置实例描述文件',
            '.xml': 'XML格式配置文件',
            '.dxf': 'AutoCAD交换格式文件',
            '.dwg': 'AutoCAD图纸文件',
            '.pdf': 'PDF图纸文件'
        }

        return jsonify({
            'success': True,
            'data': {
                'formats': formats,
                'descriptions': format_descriptions
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取支持格式失败: {str(e)}'
        }), 500


@main_bp.route('/api/unified-review/categories', methods=['GET'])
def api_unified_review_categories():
    """获取可用的检查分类"""
    try:
        if not unified_engine:
            return jsonify({
                'success': False,
                'message': '统一审查引擎未初始化'
            }), 500

        categories = unified_engine.get_available_categories()

        # 添加分类描述
        category_descriptions = {
            '标准符合性': '检查IEC61850标准符合性',
            '设备配置': '检查IED设备配置',
            '通信网络': '检查通信网络配置',
            '数据类型': '检查数据类型定义',
            '文字标注': '检查文字标注格式',
            '线型': '检查线型是否符合标准',
            '线宽': '检查线宽是否符合规范',
            '设备符号': '检查电气设备符号',
            '图层': '检查图层命名和使用',
            '尺寸标注': '检查尺寸标注规范'
        }

        return jsonify({
            'success': True,
            'data': {
                'categories': categories,
                'descriptions': category_descriptions
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取检查分类失败: {str(e)}'
        }), 500


@main_bp.route('/api/unified-review/upload', methods=['POST'])
def api_unified_review_upload():
    """文件上传"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': '文件名为空'
            }), 400

        # 检查文件格式
        if not unified_engine:
            return jsonify({
                'success': False,
                'message': '统一审查引擎未初始化'
            }), 500

        file_ext = Path(file.filename).suffix.lower()
        supported_formats = unified_engine.get_supported_formats()
        all_formats = supported_formats.get('config_formats', []) + supported_formats.get('drawing_formats', [])

        if file_ext not in all_formats:
            return jsonify({
                'success': False,
                'message': f'不支持的文件格式: {file_ext}'
            }), 400

        # 保存文件
        upload_dir = Path(current_app.config.get('UPLOAD_FOLDER', 'uploads'))
        upload_dir.mkdir(exist_ok=True)

        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file_path = upload_dir / filename
        file.save(str(file_path))

        # 检测文件类型
        file_type = unified_engine._detect_file_type(str(file_path))

        return jsonify({
            'success': True,
            'data': {
                'file_path': str(file_path),
                'file_name': file.filename,
                'file_size': file_path.stat().st_size,
                'file_type': file_type,
                'upload_time': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'文件上传失败: {str(e)}'
        }), 500


@main_bp.route('/api/unified-review/review', methods=['POST'])
def api_unified_review_review():
    """执行统一审查"""
    try:
        if not unified_engine:
            return jsonify({
                'success': False,
                'message': '统一审查引擎未初始化'
            }), 500

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            }), 400

        file_path = data.get('file_path')
        if not file_path:
            return jsonify({
                'success': False,
                'message': '文件路径不能为空'
            }), 400

        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'message': '文件不存在'
            }), 404

        # 执行审查
        result = unified_engine.review_file(file_path)

        if not result:
            return jsonify({
                'success': False,
                'message': '审查失败'
            }), 500

        # 转换结果为JSON格式
        result_data = {
            'review_id': result.review_id,
            'file_path': result.file_path,
            'file_type': result.file_type,
            'review_date': result.review_date.isoformat() if result.review_date else None,
            'compliance_score': result.compliance_score,
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'severity': issue.severity,
                    'category': issue.category,
                    'title': issue.title,
                    'description': issue.description,
                    'source_type': issue.source_type,
                    'rule_id': issue.rule_id,
                    'location': issue.location,
                    'suggestion': issue.suggestion,
                    'auto_fixable': issue.auto_fixable,
                    'standard_reference': issue.standard_reference,
                    'affected_elements': issue.affected_elements
                }
                for issue in result.issues
            ]
        }

        return jsonify({
            'success': True,
            'data': result_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'审查失败: {str(e)}'
        }), 500
