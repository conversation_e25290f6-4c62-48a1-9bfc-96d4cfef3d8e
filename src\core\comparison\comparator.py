"""
配置文件对比器
智能对比IEC61850配置文件的核心引擎
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum

from ..models import SCLDocument, IED, SubStation
from ..parsers import ParserFactory


class ChangeType(Enum):
    """变更类型枚举"""
    ADDED = "added"           # 新增
    REMOVED = "removed"       # 删除
    MODIFIED = "modified"     # 修改
    MOVED = "moved"          # 移动
    RENAMED = "renamed"      # 重命名


class ChangeLevel(Enum):
    """变更级别枚举"""
    CRITICAL = "critical"     # 关键变更，可能影响系统运行
    MAJOR = "major"          # 重要变更，需要仔细评估
    MINOR = "minor"          # 次要变更，影响较小
    COSMETIC = "cosmetic"    # 外观变更，不影响功能


@dataclass
class Change:
    """单个变更记录"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    change_type: ChangeType = ChangeType.MODIFIED
    change_level: ChangeLevel = ChangeLevel.MINOR
    path: str = ""
    element_type: str = ""
    element_name: str = ""
    old_value: Any = None
    new_value: Any = None
    description: str = ""
    impact_analysis: str = ""
    recommendation: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'change_type': self.change_type.value,
            'change_level': self.change_level.value,
            'path': self.path,
            'element_type': self.element_type,
            'element_name': self.element_name,
            'old_value': self.old_value,
            'new_value': self.new_value,
            'description': self.description,
            'impact_analysis': self.impact_analysis,
            'recommendation': self.recommendation,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class ComparisonResult:
    """对比结果"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    source_file: str = ""
    target_file: str = ""
    source_version: str = ""
    target_version: str = ""
    comparison_time: datetime = field(default_factory=datetime.now)
    
    changes: List[Change] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    summary: str = ""
    
    def add_change(self, change: Change):
        """添加变更记录"""
        self.changes.append(change)
        self._update_statistics()
    
    def get_changes_by_type(self, change_type: ChangeType) -> List[Change]:
        """按类型获取变更"""
        return [c for c in self.changes if c.change_type == change_type]
    
    def get_changes_by_level(self, change_level: ChangeLevel) -> List[Change]:
        """按级别获取变更"""
        return [c for c in self.changes if c.change_level == change_level]
    
    def get_critical_changes(self) -> List[Change]:
        """获取关键变更"""
        return self.get_changes_by_level(ChangeLevel.CRITICAL)
    
    def has_critical_changes(self) -> bool:
        """是否有关键变更"""
        return len(self.get_critical_changes()) > 0
    
    def _update_statistics(self):
        """更新统计信息"""
        self.statistics = {
            'total_changes': len(self.changes),
            'by_type': {
                change_type.value: len(self.get_changes_by_type(change_type))
                for change_type in ChangeType
            },
            'by_level': {
                change_level.value: len(self.get_changes_by_level(change_level))
                for change_level in ChangeLevel
            }
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'source_file': self.source_file,
            'target_file': self.target_file,
            'source_version': self.source_version,
            'target_version': self.target_version,
            'comparison_time': self.comparison_time.isoformat(),
            'changes': [change.to_dict() for change in self.changes],
            'statistics': self.statistics,
            'summary': self.summary
        }


class ConfigComparator:
    """
    配置文件对比器
    
    解决智能变电站配置文件版本对比的核心问题：
    1. 结构化对比：不仅仅是文本对比，而是理解IEC61850结构的智能对比
    2. 语义分析：理解变更的实际含义和影响
    3. 风险评估：评估变更对系统运行的潜在影响
    4. 专业报告：生成符合电力行业需求的对比报告
    """
    
    def __init__(self):
        self.parser_factory = ParserFactory()
    
    def compare_files(self, source_path: str, target_path: str) -> ComparisonResult:
        """
        对比两个配置文件
        
        Args:
            source_path: 源文件路径
            target_path: 目标文件路径
            
        Returns:
            ComparisonResult: 对比结果
        """
        # 解析文件
        source_result = self.parser_factory.parse_file(source_path)
        target_result = self.parser_factory.parse_file(target_path)
        
        if not source_result.success:
            raise ValueError(f"源文件解析失败: {source_result.errors}")
        
        if not target_result.success:
            raise ValueError(f"目标文件解析失败: {target_result.errors}")
        
        # 创建对比结果
        result = ComparisonResult(
            source_file=source_path,
            target_file=target_path,
            source_version=getattr(source_result.data, 'version', 'unknown'),
            target_version=getattr(target_result.data, 'version', 'unknown')
        )
        
        # 执行对比
        self._compare_documents(source_result.data, target_result.data, result)
        
        # 生成摘要
        result.summary = self._generate_summary(result)
        
        return result
    
    def compare_documents(self, source_doc: SCLDocument, target_doc: SCLDocument) -> ComparisonResult:
        """
        对比两个已解析的文档
        
        Args:
            source_doc: 源文档
            target_doc: 目标文档
            
        Returns:
            ComparisonResult: 对比结果
        """
        result = ComparisonResult(
            source_version=getattr(source_doc, 'version', 'unknown'),
            target_version=getattr(target_doc, 'version', 'unknown')
        )
        
        self._compare_documents(source_doc, target_doc, result)
        result.summary = self._generate_summary(result)
        
        return result
    
    def _compare_documents(self, source: SCLDocument, target: SCLDocument, result: ComparisonResult):
        """对比文档内容"""
        # 对比基本信息
        self._compare_basic_info(source, target, result)
        
        # 对比IED设备
        self._compare_ieds(source.ieds or [], target.ieds or [], result)
        
        # 对比变电站配置
        if source.substation and target.substation:
            self._compare_substations(source.substation, target.substation, result)
        elif source.substation and not target.substation:
            result.add_change(Change(
                change_type=ChangeType.REMOVED,
                change_level=ChangeLevel.CRITICAL,
                path="substation",
                element_type="SubStation",
                element_name=source.substation.name or "unnamed",
                description="变电站配置被删除",
                impact_analysis="删除变电站配置将导致系统无法正常运行",
                recommendation="请确认是否需要删除变电站配置，这是一个关键变更"
            ))
        elif not source.substation and target.substation:
            result.add_change(Change(
                change_type=ChangeType.ADDED,
                change_level=ChangeLevel.MAJOR,
                path="substation",
                element_type="SubStation",
                element_name=target.substation.name or "unnamed",
                description="新增变电站配置",
                impact_analysis="新增变电站配置，需要验证配置的正确性",
                recommendation="请仔细检查新增的变电站配置是否符合设计要求"
            ))
        
        # 对比通信配置
        self._compare_communication(source, target, result)
    
    def _compare_basic_info(self, source: SCLDocument, target: SCLDocument, result: ComparisonResult):
        """对比基本信息"""
        # 对比版本
        if source.version != target.version:
            result.add_change(Change(
                change_type=ChangeType.MODIFIED,
                change_level=ChangeLevel.MINOR,
                path="version",
                element_type="SCL",
                element_name="version",
                old_value=source.version,
                new_value=target.version,
                description=f"SCL版本从 {source.version} 变更为 {target.version}",
                impact_analysis="版本变更可能影响兼容性",
                recommendation="请确认新版本的兼容性要求"
            ))
        
        # 对比Header信息
        if source.header and target.header:
            if source.header.id != target.header.id:
                result.add_change(Change(
                    change_type=ChangeType.MODIFIED,
                    change_level=ChangeLevel.MINOR,
                    path="header.id",
                    element_type="Header",
                    element_name="id",
                    old_value=source.header.id,
                    new_value=target.header.id,
                    description="Header ID发生变更",
                    impact_analysis="Header ID变更通常不影响功能",
                    recommendation="确认ID变更的原因"
                ))
    
    def _compare_ieds(self, source_ieds: List[IED], target_ieds: List[IED], result: ComparisonResult):
        """对比IED设备"""
        source_ied_map = {ied.name: ied for ied in source_ieds if ied.name}
        target_ied_map = {ied.name: ied for ied in target_ieds if ied.name}
        
        # 查找新增的IED
        for name, ied in target_ied_map.items():
            if name not in source_ied_map:
                result.add_change(Change(
                    change_type=ChangeType.ADDED,
                    change_level=ChangeLevel.MAJOR,
                    path=f"ieds.{name}",
                    element_type="IED",
                    element_name=name,
                    new_value=ied.to_dict() if hasattr(ied, 'to_dict') else str(ied),
                    description=f"新增IED设备: {name}",
                    impact_analysis="新增IED设备需要验证网络配置和通信参数",
                    recommendation="请确认新IED的配置正确性和网络连接"
                ))
        
        # 查找删除的IED
        for name, ied in source_ied_map.items():
            if name not in target_ied_map:
                result.add_change(Change(
                    change_type=ChangeType.REMOVED,
                    change_level=ChangeLevel.CRITICAL,
                    path=f"ieds.{name}",
                    element_type="IED",
                    element_name=name,
                    old_value=ied.to_dict() if hasattr(ied, 'to_dict') else str(ied),
                    description=f"删除IED设备: {name}",
                    impact_analysis="删除IED设备可能影响保护、测量或控制功能",
                    recommendation="请确认删除IED不会影响系统安全运行"
                ))
        
        # 对比修改的IED
        for name in set(source_ied_map.keys()) & set(target_ied_map.keys()):
            self._compare_single_ied(source_ied_map[name], target_ied_map[name], result, f"ieds.{name}")
    
    def _compare_single_ied(self, source_ied: IED, target_ied: IED, result: ComparisonResult, path: str):
        """对比单个IED"""
        # 对比基本属性
        if source_ied.manufacturer != target_ied.manufacturer:
            result.add_change(Change(
                change_type=ChangeType.MODIFIED,
                change_level=ChangeLevel.MINOR,
                path=f"{path}.manufacturer",
                element_type="IED",
                element_name=source_ied.name,
                old_value=source_ied.manufacturer,
                new_value=target_ied.manufacturer,
                description=f"IED {source_ied.name} 制造商信息变更",
                impact_analysis="制造商信息变更通常不影响功能",
                recommendation="确认制造商信息的准确性"
            ))
        
        if source_ied.type != target_ied.type:
            result.add_change(Change(
                change_type=ChangeType.MODIFIED,
                change_level=ChangeLevel.MAJOR,
                path=f"{path}.type",
                element_type="IED",
                element_name=source_ied.name,
                old_value=source_ied.type,
                new_value=target_ied.type,
                description=f"IED {source_ied.name} 类型变更",
                impact_analysis="IED类型变更可能影响功能和兼容性",
                recommendation="请验证新类型的功能兼容性"
            ))
    
    def _compare_substations(self, source: SubStation, target: SubStation, result: ComparisonResult):
        """对比变电站配置"""
        if source.name != target.name:
            result.add_change(Change(
                change_type=ChangeType.RENAMED,
                change_level=ChangeLevel.MINOR,
                path="substation.name",
                element_type="SubStation",
                element_name=source.name,
                old_value=source.name,
                new_value=target.name,
                description="变电站名称变更",
                impact_analysis="变电站名称变更通常不影响功能",
                recommendation="确认名称变更的必要性"
            ))
    
    def _compare_communication(self, source: SCLDocument, target: SCLDocument, result: ComparisonResult):
        """对比通信配置"""
        # 对比子网配置
        source_subnets = getattr(source, 'subnets', []) or []
        target_subnets = getattr(target, 'subnets', []) or []
        
        if len(source_subnets) != len(target_subnets):
            result.add_change(Change(
                change_type=ChangeType.MODIFIED,
                change_level=ChangeLevel.MAJOR,
                path="communication.subnets",
                element_type="SubNetwork",
                element_name="subnets",
                old_value=len(source_subnets),
                new_value=len(target_subnets),
                description=f"子网数量从 {len(source_subnets)} 变更为 {len(target_subnets)}",
                impact_analysis="子网配置变更可能影响设备通信",
                recommendation="请验证网络拓扑的正确性"
            ))
    
    def _generate_summary(self, result: ComparisonResult) -> str:
        """生成对比摘要"""
        stats = result.statistics
        total = stats.get('total_changes', 0)
        
        if total == 0:
            return "两个配置文件完全相同，未发现任何差异。"
        
        critical = stats.get('by_level', {}).get('critical', 0)
        major = stats.get('by_level', {}).get('major', 0)
        
        summary_parts = [f"共发现 {total} 处变更"]
        
        if critical > 0:
            summary_parts.append(f"其中 {critical} 处关键变更需要特别关注")
        
        if major > 0:
            summary_parts.append(f"{major} 处重要变更需要仔细评估")
        
        return "，".join(summary_parts) + "。"
