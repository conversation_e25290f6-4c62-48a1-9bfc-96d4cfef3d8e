"""
IEC61850逻辑验证模块 - 独立模块设计
负责IEC61850逻辑节点一致性验证和复杂回路逻辑关系检查
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum


class ValidationSeverity(Enum):
    """验证问题严重程度"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class ValidationIssue:
    """验证问题"""
    issue_type: str
    severity: ValidationSeverity
    description: str
    location: str
    standard_reference: str
    recommendation: str


@dataclass
class ValidationResult:
    """验证结果"""
    module_name: str
    success: bool
    score: float
    issues: List[ValidationIssue]
    summary: Dict[str, Any]
    
    def __post_init__(self):
        if self.issues is None:
            self.issues = []


class IEC61850Validator:
    """IEC61850逻辑验证器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化验证器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # 加载标准逻辑节点定义
        self.standard_logical_nodes = self._load_standard_logical_nodes()
        self.validation_rules = self._load_validation_rules()
    
    def _setup_logging(self) -> None:
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_standard_logical_nodes(self) -> Dict[str, Any]:
        """加载标准逻辑节点定义"""
        return {
            'XCBR': {
                'description': '断路器逻辑节点',
                'mandatory_data_objects': {
                    'Pos': {
                        'cdc': 'DPC',
                        'description': '断路器位置',
                        'mandatory_attributes': ['stVal', 'q', 't', 'ctlVal']
                    },
                    'BlkOpn': {
                        'cdc': 'SPS',
                        'description': '分闸闭锁',
                        'mandatory_attributes': ['stVal', 'q', 't']
                    },
                    'BlkCls': {
                        'cdc': 'SPS',
                        'description': '合闸闭锁',
                        'mandatory_attributes': ['stVal', 'q', 't']
                    }
                }
            },
            'PTRC': {
                'description': '保护跳闸条件逻辑节点',
                'mandatory_data_objects': {
                    'Tr': {
                        'cdc': 'ACD',
                        'description': '跳闸',
                        'mandatory_attributes': ['general', 'dirGeneral', 'q', 't']
                    },
                    'Str': {
                        'cdc': 'ACD',
                        'description': '启动',
                        'mandatory_attributes': ['general', 'dirGeneral', 'q', 't']
                    }
                }
            },
            'CSWI': {
                'description': '控制开关逻辑节点',
                'mandatory_data_objects': {
                    'Pos': {
                        'cdc': 'DPC',
                        'description': '开关位置控制',
                        'mandatory_attributes': ['stVal', 'q', 't', 'ctlVal']
                    }
                }
            },
            'CILO': {
                'description': '联锁逻辑节点',
                'mandatory_data_objects': {
                    'EnaOpn': {
                        'cdc': 'SPS',
                        'description': '允许分闸',
                        'mandatory_attributes': ['stVal', 'q', 't']
                    },
                    'EnaCls': {
                        'cdc': 'SPS',
                        'description': '允许合闸',
                        'mandatory_attributes': ['stVal', 'q', 't']
                    }
                }
            }
        }
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载验证规则"""
        return {
            'naming_rules': {
                'ied_name_pattern': r'^[A-Z][A-Za-z0-9_]*$',
                'ld_name_pattern': r'^[A-Z][A-Za-z0-9_]*$',
                'ln_class_pattern': r'^[A-Z]{4}$'
            },
            'goose_rules': {
                'min_time_limit': 4,  # ms
                'max_time_limit': 60000,  # ms
                'vlan_priority_range': [4, 7]
            },
            'smv_rules': {
                'standard_sample_rates': [4000, 4800, 12800],
                'max_asdu_count': 8
            }
        }
    
    def validate_ld_ln_do_da_structure(self, scd_data: Dict[str, Any]) -> ValidationResult:
        """
        验证LD/LN/DO/DA结构一致性
        
        Args:
            scd_data: SCD数据
            
        Returns:
            ValidationResult: 验证结果
        """
        try:
            self.logger.info("开始LD/LN/DO/DA结构验证")
            
            issues = []
            
            # 验证IED结构
            ieds = scd_data.get('ieds', [])
            for ied in ieds:
                ied_issues = self._validate_ied_structure(ied)
                issues.extend(ied_issues)
            
            # 计算得分
            score = self._calculate_score(issues, 'structure_validation')
            
            return ValidationResult(
                module_name='LD/LN/DO/DA结构验证',
                success=len([i for i in issues if i.severity in [ValidationSeverity.CRITICAL, ValidationSeverity.HIGH]]) == 0,
                score=score,
                issues=issues,
                summary={
                    'total_issues': len(issues),
                    'critical_issues': len([i for i in issues if i.severity == ValidationSeverity.CRITICAL]),
                    'high_issues': len([i for i in issues if i.severity == ValidationSeverity.HIGH]),
                    'validated_ieds': len(ieds)
                }
            )
            
        except Exception as e:
            self.logger.error(f"结构验证过程中发生错误: {e}")
            return ValidationResult(
                module_name='LD/LN/DO/DA结构验证',
                success=False,
                score=0.0,
                issues=[ValidationIssue(
                    issue_type='validation_error',
                    severity=ValidationSeverity.CRITICAL,
                    description=f"验证过程错误: {e}",
                    location='validator',
                    standard_reference='',
                    recommendation='检查输入数据格式'
                )],
                summary={'error': str(e)}
            )
    
    def _validate_ied_structure(self, ied: Dict[str, Any]) -> List[ValidationIssue]:
        """验证IED结构"""
        issues = []
        ied_name = ied.get('name', 'Unknown')
        
        # 验证IED命名
        if not self._validate_naming(ied_name, 'ied'):
            issues.append(ValidationIssue(
                issue_type='naming_violation',
                severity=ValidationSeverity.MEDIUM,
                description=f'IED名称不符合命名规范: {ied_name}',
                location=f'IED[{ied_name}]',
                standard_reference='IEC 61850-7-1 命名规则',
                recommendation='使用符合规范的IED命名'
            ))
        
        # 验证逻辑设备
        logical_devices = ied.get('logical_devices', [])
        for ld in logical_devices:
            ld_issues = self._validate_logical_device(ld, ied_name)
            issues.extend(ld_issues)
        
        return issues
    
    def _validate_logical_device(self, ld: Dict[str, Any], ied_name: str) -> List[ValidationIssue]:
        """验证逻辑设备"""
        issues = []
        ld_inst = ld.get('inst', 'Unknown')
        
        # 验证LD命名
        if not self._validate_naming(ld_inst, 'ld'):
            issues.append(ValidationIssue(
                issue_type='naming_violation',
                severity=ValidationSeverity.MEDIUM,
                description=f'LD名称不符合命名规范: {ld_inst}',
                location=f'IED[{ied_name}]/LD[{ld_inst}]',
                standard_reference='IEC 61850-7-1 命名规则',
                recommendation='使用符合规范的LD命名'
            ))
        
        # 验证逻辑节点
        logical_nodes = ld.get('logical_nodes', [])
        for ln in logical_nodes:
            ln_issues = self._validate_logical_node(ln, ied_name, ld_inst)
            issues.extend(ln_issues)
        
        return issues
    
    def _validate_logical_node(self, ln: Dict[str, Any], ied_name: str, ld_inst: str) -> List[ValidationIssue]:
        """验证逻辑节点"""
        issues = []
        ln_class = ln.get('lnClass', 'Unknown')
        ln_inst = ln.get('inst', '')
        ln_prefix = ln.get('prefix', '')
        
        location = f'IED[{ied_name}]/LD[{ld_inst}]/LN[{ln_prefix}{ln_class}{ln_inst}]'
        
        # 验证LN类别
        if ln_class not in self.standard_logical_nodes:
            issues.append(ValidationIssue(
                issue_type='unknown_ln_class',
                severity=ValidationSeverity.HIGH,
                description=f'未知的逻辑节点类别: {ln_class}',
                location=location,
                standard_reference='IEC 61850-7-4 逻辑节点类别',
                recommendation='使用标准定义的逻辑节点类别'
            ))
        else:
            # 验证强制数据对象（这里简化处理，实际需要从DataTypeTemplates中获取）
            standard_ln = self.standard_logical_nodes[ln_class]
            mandatory_dos = standard_ln.get('mandatory_data_objects', {})
            
            # 注意：这里需要实际的DO配置数据，当前SCD解析器还没有提取DO/DA信息
            # 在实际实现中，需要从DataTypeTemplates中获取完整的DO/DA结构
            
        return issues
    
    def validate_goose_configuration(self, scd_data: Dict[str, Any]) -> ValidationResult:
        """
        验证GOOSE配置
        
        Args:
            scd_data: SCD数据
            
        Returns:
            ValidationResult: 验证结果
        """
        try:
            self.logger.info("开始GOOSE配置验证")
            
            issues = []
            
            # 从通信配置中提取GOOSE信息
            communication = scd_data.get('communication', {})
            subnets = communication.get('subnets', [])
            
            goose_count = 0
            for subnet in subnets:
                connected_aps = subnet.get('connected_aps', [])
                for ap in connected_aps:
                    gse_list = ap.get('gse', [])
                    for gse in gse_list:
                        goose_count += 1
                        gse_issues = self._validate_gse_configuration(gse, ap.get('iedName', ''))
                        issues.extend(gse_issues)
            
            # 计算得分
            score = self._calculate_score(issues, 'goose_validation')
            
            return ValidationResult(
                module_name='GOOSE配置验证',
                success=len([i for i in issues if i.severity in [ValidationSeverity.CRITICAL, ValidationSeverity.HIGH]]) == 0,
                score=score,
                issues=issues,
                summary={
                    'total_issues': len(issues),
                    'goose_count': goose_count,
                    'validated_subnets': len(subnets)
                }
            )
            
        except Exception as e:
            self.logger.error(f"GOOSE验证过程中发生错误: {e}")
            return ValidationResult(
                module_name='GOOSE配置验证',
                success=False,
                score=0.0,
                issues=[ValidationIssue(
                    issue_type='validation_error',
                    severity=ValidationSeverity.CRITICAL,
                    description=f"验证过程错误: {e}",
                    location='validator',
                    standard_reference='',
                    recommendation='检查输入数据格式'
                )],
                summary={'error': str(e)}
            )
    
    def _validate_gse_configuration(self, gse: Dict[str, Any], ied_name: str) -> List[ValidationIssue]:
        """验证GSE配置"""
        issues = []
        cb_name = gse.get('cbName', 'Unknown')
        location = f'IED[{ied_name}]/GSE[{cb_name}]'
        
        # 验证地址配置
        address = gse.get('address', {})
        
        # 检查MAC地址
        mac_address = address.get('MAC-Address', '')
        if mac_address and not self._validate_goose_mac_address(mac_address):
            issues.append(ValidationIssue(
                issue_type='invalid_mac_address',
                severity=ValidationSeverity.HIGH,
                description=f'无效的GOOSE MAC地址: {mac_address}',
                location=location,
                standard_reference='IEC 61850-8-1 网络配置',
                recommendation='使用符合规范的组播MAC地址'
            ))
        
        # 检查VLAN优先级
        vlan_priority = address.get('VLAN-PRIORITY', '')
        if vlan_priority:
            try:
                priority = int(vlan_priority)
                if priority < 4 or priority > 7:
                    issues.append(ValidationIssue(
                        issue_type='invalid_vlan_priority',
                        severity=ValidationSeverity.MEDIUM,
                        description=f'GOOSE VLAN优先级不合理: {priority}',
                        location=location,
                        standard_reference='IEC 61850-90-4 网络工程指南',
                        recommendation='使用4-7范围内的VLAN优先级'
                    ))
            except ValueError:
                issues.append(ValidationIssue(
                    issue_type='invalid_vlan_priority_format',
                    severity=ValidationSeverity.HIGH,
                    description=f'VLAN优先级格式错误: {vlan_priority}',
                    location=location,
                    standard_reference='IEC 61850-8-1',
                    recommendation='使用数字格式的VLAN优先级'
                ))
        
        return issues
    
    def validate_cross_ied_logic(self, scd_data: Dict[str, Any]) -> ValidationResult:
        """
        验证跨IED逻辑关系
        
        Args:
            scd_data: SCD数据
            
        Returns:
            ValidationResult: 验证结果
        """
        try:
            self.logger.info("开始跨IED逻辑关系验证")
            
            issues = []
            
            # 分析IED间的逻辑关系
            logic_chains = self._analyze_logic_chains(scd_data)
            
            # 验证保护控制逻辑链
            for chain in logic_chains:
                chain_issues = self._validate_logic_chain(chain)
                issues.extend(chain_issues)
            
            # 计算得分
            score = self._calculate_score(issues, 'logic_validation')
            
            return ValidationResult(
                module_name='跨IED逻辑关系验证',
                success=len([i for i in issues if i.severity in [ValidationSeverity.CRITICAL, ValidationSeverity.HIGH]]) == 0,
                score=score,
                issues=issues,
                summary={
                    'total_issues': len(issues),
                    'logic_chains': len(logic_chains),
                    'validated_relationships': len(logic_chains)
                }
            )
            
        except Exception as e:
            self.logger.error(f"跨IED逻辑验证过程中发生错误: {e}")
            return ValidationResult(
                module_name='跨IED逻辑关系验证',
                success=False,
                score=0.0,
                issues=[ValidationIssue(
                    issue_type='validation_error',
                    severity=ValidationSeverity.CRITICAL,
                    description=f"验证过程错误: {e}",
                    location='validator',
                    standard_reference='',
                    recommendation='检查输入数据格式'
                )],
                summary={'error': str(e)}
            )
    
    def _analyze_logic_chains(self, scd_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析逻辑链"""
        logic_chains = []
        
        # 简化的逻辑链分析
        ieds = scd_data.get('ieds', [])
        protection_ieds = [ied for ied in ieds if 'PROT' in ied.get('name', '').upper()]
        control_ieds = [ied for ied in ieds if 'CTRL' in ied.get('name', '').upper()]
        
        # 分析保护到控制的逻辑链
        for prot_ied in protection_ieds:
            for ctrl_ied in control_ieds:
                chain = {
                    'type': 'protection_control',
                    'source_ied': prot_ied.get('name'),
                    'target_ied': ctrl_ied.get('name'),
                    'description': '保护跳闸到控制执行'
                }
                logic_chains.append(chain)
        
        return logic_chains
    
    def _validate_logic_chain(self, chain: Dict[str, Any]) -> List[ValidationIssue]:
        """验证逻辑链"""
        issues = []
        
        # 这里可以添加具体的逻辑链验证规则
        # 例如检查GOOSE订阅关系、信号路径完整性等
        
        return issues
    
    def _validate_naming(self, name: str, name_type: str) -> bool:
        """验证命名规范"""
        import re
        
        rules = self.validation_rules.get('naming_rules', {})
        pattern = rules.get(f'{name_type}_name_pattern', r'.*')
        
        return bool(re.match(pattern, name))
    
    def _validate_goose_mac_address(self, mac_address: str) -> bool:
        """验证GOOSE MAC地址"""
        import re
        
        # GOOSE组播MAC地址格式: 01-0C-CD-xx-xx-xx
        pattern = r'^01-0C-CD-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}$'
        return bool(re.match(pattern, mac_address))
    
    def _calculate_score(self, issues: List[ValidationIssue], validation_type: str) -> float:
        """计算验证得分"""
        if not issues:
            return 100.0
        
        # 根据问题严重程度计算扣分
        deduction = 0
        for issue in issues:
            if issue.severity == ValidationSeverity.CRITICAL:
                deduction += 25
            elif issue.severity == ValidationSeverity.HIGH:
                deduction += 15
            elif issue.severity == ValidationSeverity.MEDIUM:
                deduction += 10
            elif issue.severity == ValidationSeverity.LOW:
                deduction += 5
            else:  # INFO
                deduction += 2
        
        score = max(0, 100 - deduction)
        return score


def main() -> Dict[str, Any]:
    """主函数 - 模块测试"""
    # 创建测试用的验证器
    validator = IEC61850Validator()
    
    print("IEC61850逻辑验证模块独立测试")
    print("=" * 40)
    
    # 测试配置
    test_config = {
        'strict_validation': True,
        'include_warnings': True
    }
    
    print(f"配置参数: {test_config}")
    print("模块初始化成功")
    
    # 模拟SCD数据
    mock_scd_data = {
        'ieds': [
            {
                'name': 'PROT_IED_001',
                'logical_devices': [
                    {
                        'inst': 'LD0',
                        'logical_nodes': [
                            {'lnClass': 'PTRC', 'inst': '1', 'prefix': ''},
                            {'lnClass': 'XCBR', 'inst': '1', 'prefix': ''}
                        ]
                    }
                ]
            }
        ],
        'communication': {
            'subnets': [
                {
                    'name': 'ProcessBus',
                    'connected_aps': [
                        {
                            'iedName': 'PROT_IED_001',
                            'gse': [
                                {
                                    'cbName': 'GoCB_TRIP_001',
                                    'address': {
                                        'MAC-Address': '01-0C-CD-01-00-01',
                                        'VLAN-PRIORITY': '7'
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    }
    
    # 执行验证
    structure_result = validator.validate_ld_ln_do_da_structure(mock_scd_data)
    goose_result = validator.validate_goose_configuration(mock_scd_data)
    logic_result = validator.validate_cross_ied_logic(mock_scd_data)
    
    print(f"结构验证得分: {structure_result.score:.1f}")
    print(f"GOOSE验证得分: {goose_result.score:.1f}")
    print(f"逻辑验证得分: {logic_result.score:.1f}")
    
    return {
        'module_name': 'IEC61850逻辑验证模块',
        'status': 'success',
        'results': {
            'structure': structure_result.score,
            'goose': goose_result.score,
            'logic': logic_result.score
        }
    }


if __name__ == "__main__":
    result = main()
    print(f"模块测试完成: {result}")