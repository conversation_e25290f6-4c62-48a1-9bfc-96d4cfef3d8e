"""
变电站数据模型单元测试
"""

import pytest
from src.core.models.substation import (
    Voltage, ConductingEquipment, Bay, VoltageLevel, SubStation,
    VoltageLevel as VoltageLevelEnum, EquipmentType, ValidationError
)


class TestVoltage:
    """测试电压模型"""
    
    def test_voltage_creation(self):
        """测试电压创建"""
        voltage = Voltage(multiplier="k", unit="V", value=110.0)
        
        assert voltage.multiplier == "k"
        assert voltage.unit == "V"
        assert voltage.value == 110.0
    
    def test_voltage_validation(self):
        """测试电压验证"""
        # 有效电压
        voltage = Voltage(multiplier="k", unit="V", value=110.0)
        voltage.validate()
        
        # 无效电压值
        with pytest.raises(ValidationError):
            Voltage(multiplier="k", unit="V", value=-110.0)
        
        # 无效倍数
        with pytest.raises(ValidationError):
            Voltage(multiplier="invalid", unit="V", value=110.0)
    
    def test_voltage_conversion(self):
        """测试电压单位转换"""
        # 千伏
        voltage_kv = Voltage(multiplier="k", unit="V", value=110.0)
        assert voltage_kv.get_voltage_kv() == 110.0
        
        # 伏特
        voltage_v = Voltage(multiplier="", unit="V", value=110000.0)
        assert voltage_v.get_voltage_kv() == 110.0
        
        # 兆伏
        voltage_mv = Voltage(multiplier="M", unit="V", value=0.11)
        assert voltage_mv.get_voltage_kv() == 110.0
    
    def test_voltage_string_representation(self):
        """测试电压字符串表示"""
        voltage = Voltage(multiplier="k", unit="V", value=110.0)
        assert str(voltage) == "110.0kV"


class TestConductingEquipment:
    """测试导电设备模型"""
    
    def test_conducting_equipment_creation(self):
        """测试导电设备创建"""
        equipment = ConductingEquipment(
            name="CBR1",
            type=EquipmentType.CIRCUIT_BREAKER.value,
            manufacturer="TestManufacturer",
            model="TestModel"
        )
        
        assert equipment.name == "CBR1"
        assert equipment.type == EquipmentType.CIRCUIT_BREAKER.value
        assert equipment.manufacturer == "TestManufacturer"
        assert equipment.model == "TestModel"
        assert equipment.virtual is False
    
    def test_conducting_equipment_validation(self):
        """测试导电设备验证"""
        # 有效设备
        equipment = ConductingEquipment(
            name="CBR1",
            type=EquipmentType.CIRCUIT_BREAKER.value
        )
        equipment.validate()
        
        # 无效名称
        with pytest.raises(ValidationError):
            ConductingEquipment(name="", type=EquipmentType.CIRCUIT_BREAKER.value)
        
        # 无效类型
        with pytest.raises(ValidationError):
            ConductingEquipment(name="CBR1", type="")
        
        # 自定义类型（有效）
        equipment_custom = ConductingEquipment(name="CUSTOM1", type="CUSTOM_TYPE")
        equipment_custom.validate()
        
        # 自定义类型（无效命名 - 包含特殊字符）
        with pytest.raises(ValidationError):
            ConductingEquipment(name="CUSTOM1", type="Invalid@Type")


class TestBay:
    """测试间隔模型"""
    
    def test_bay_creation(self):
        """测试间隔创建"""
        bay = Bay(name="Bay1")
        
        assert bay.name == "Bay1"
        assert len(bay.conducting_equipments) == 0
    
    def test_bay_add_equipment(self):
        """测试添加设备"""
        bay = Bay(name="Bay1")
        equipment = ConductingEquipment(
            name="CBR1",
            type=EquipmentType.CIRCUIT_BREAKER.value
        )
        
        bay.add_equipment(equipment)
        
        assert len(bay.conducting_equipments) == 1
        assert bay.conducting_equipments[0].name == "CBR1"
    
    def test_bay_equipment_name_uniqueness(self):
        """测试设备名称唯一性"""
        bay = Bay(name="Bay1")
        equipment1 = ConductingEquipment(name="CBR1", type="CBR")
        equipment2 = ConductingEquipment(name="CBR1", type="CBR")  # 重复名称
        
        bay.add_equipment(equipment1)
        
        with pytest.raises(ValidationError):
            bay.add_equipment(equipment2)
    
    def test_bay_remove_equipment(self):
        """测试移除设备"""
        bay = Bay(name="Bay1")
        equipment = ConductingEquipment(name="CBR1", type="CBR")
        
        bay.add_equipment(equipment)
        assert len(bay.conducting_equipments) == 1
        
        # 移除存在的设备
        result = bay.remove_equipment("CBR1")
        assert result is True
        assert len(bay.conducting_equipments) == 0
        
        # 移除不存在的设备
        result = bay.remove_equipment("NonExistent")
        assert result is False
    
    def test_bay_get_equipment(self):
        """测试获取设备"""
        bay = Bay(name="Bay1")
        equipment = ConductingEquipment(name="CBR1", type="CBR")
        bay.add_equipment(equipment)
        
        # 获取存在的设备
        found_equipment = bay.get_equipment("CBR1")
        assert found_equipment is not None
        assert found_equipment.name == "CBR1"
        
        # 获取不存在的设备
        not_found = bay.get_equipment("NonExistent")
        assert not_found is None
    
    def test_bay_get_equipments_by_type(self):
        """测试按类型获取设备"""
        bay = Bay(name="Bay1")
        
        cbr1 = ConductingEquipment(name="CBR1", type="CBR")
        cbr2 = ConductingEquipment(name="CBR2", type="CBR")
        dis1 = ConductingEquipment(name="DIS1", type="DIS")
        
        bay.add_equipment(cbr1)
        bay.add_equipment(cbr2)
        bay.add_equipment(dis1)
        
        # 获取断路器
        cbrs = bay.get_equipments_by_type("CBR")
        assert len(cbrs) == 2
        assert all(eq.type == "CBR" for eq in cbrs)
        
        # 获取隔离开关
        diss = bay.get_equipments_by_type("DIS")
        assert len(diss) == 1
        assert diss[0].name == "DIS1"
        
        # 获取不存在的类型
        others = bay.get_equipments_by_type("OTHER")
        assert len(others) == 0


class TestVoltageLevel:
    """测试电压等级模型"""
    
    def test_voltage_level_creation(self):
        """测试电压等级创建"""
        voltage = Voltage(multiplier="k", unit="V", value=110.0)
        vl = VoltageLevel(name="110kV", voltage=voltage)
        
        assert vl.name == "110kV"
        assert vl.voltage.value == 110.0
        assert len(vl.bays) == 0
    
    def test_voltage_level_add_bay(self):
        """测试添加间隔"""
        vl = VoltageLevel(name="110kV")
        bay = Bay(name="Bay1")
        
        vl.add_bay(bay)
        
        assert len(vl.bays) == 1
        assert vl.bays[0].name == "Bay1"
    
    def test_voltage_level_bay_name_uniqueness(self):
        """测试间隔名称唯一性"""
        vl = VoltageLevel(name="110kV")
        bay1 = Bay(name="Bay1")
        bay2 = Bay(name="Bay1")  # 重复名称
        
        vl.add_bay(bay1)
        
        with pytest.raises(ValidationError):
            vl.add_bay(bay2)
    
    def test_voltage_level_get_all_equipments(self):
        """测试获取所有设备"""
        vl = VoltageLevel(name="110kV")
        
        bay1 = Bay(name="Bay1")
        bay2 = Bay(name="Bay2")
        
        equipment1 = ConductingEquipment(name="CBR1", type="CBR")
        equipment2 = ConductingEquipment(name="CBR2", type="CBR")
        equipment3 = ConductingEquipment(name="DIS1", type="DIS")
        
        bay1.add_equipment(equipment1)
        bay1.add_equipment(equipment2)
        bay2.add_equipment(equipment3)
        
        vl.add_bay(bay1)
        vl.add_bay(bay2)
        
        all_equipments = vl.get_all_equipments()
        assert len(all_equipments) == 3
        
        equipment_names = [eq.name for eq in all_equipments]
        assert "CBR1" in equipment_names
        assert "CBR2" in equipment_names
        assert "DIS1" in equipment_names


class TestSubStation:
    """测试变电站模型"""
    
    def test_substation_creation(self):
        """测试变电站创建"""
        substation = SubStation(
            name="TestSubstation",
            location="TestLocation",
            owner="TestOwner"
        )
        
        assert substation.name == "TestSubstation"
        assert substation.location == "TestLocation"
        assert substation.owner == "TestOwner"
        assert len(substation.voltage_levels) == 0
    
    def test_substation_add_voltage_level(self):
        """测试添加电压等级"""
        substation = SubStation(name="TestSubstation")
        vl = VoltageLevel(name="110kV")
        
        substation.add_voltage_level(vl)
        
        assert len(substation.voltage_levels) == 1
        assert substation.voltage_levels[0].name == "110kV"
    
    def test_substation_get_equipment_by_path(self):
        """测试根据路径获取设备"""
        substation = SubStation(name="TestSubstation")
        
        # 创建层次结构
        vl = VoltageLevel(name="110kV")
        bay = Bay(name="Bay1")
        equipment = ConductingEquipment(name="CBR1", type="CBR")
        
        bay.add_equipment(equipment)
        vl.add_bay(bay)
        substation.add_voltage_level(vl)
        
        # 测试路径查找
        found_equipment = substation.get_equipment_by_path("110kV/Bay1/CBR1")
        assert found_equipment is not None
        assert found_equipment.name == "CBR1"
        
        # 测试无效路径
        not_found = substation.get_equipment_by_path("invalid/path")
        assert not_found is None
        
        not_found = substation.get_equipment_by_path("110kV/Bay1/NonExistent")
        assert not_found is None
    
    def test_substation_statistics(self):
        """测试变电站统计信息"""
        substation = SubStation(name="TestSubstation")
        
        # 创建测试数据
        vl1 = VoltageLevel(name="110kV")
        vl2 = VoltageLevel(name="10kV")
        
        bay1 = Bay(name="Bay1")
        bay2 = Bay(name="Bay2")
        bay3 = Bay(name="Bay3")
        
        equipment1 = ConductingEquipment(name="CBR1", type="CBR")
        equipment2 = ConductingEquipment(name="CBR2", type="CBR")
        equipment3 = ConductingEquipment(name="DIS1", type="DIS")
        
        bay1.add_equipment(equipment1)
        bay2.add_equipment(equipment2)
        bay3.add_equipment(equipment3)
        
        vl1.add_bay(bay1)
        vl1.add_bay(bay2)
        vl2.add_bay(bay3)
        
        substation.add_voltage_level(vl1)
        substation.add_voltage_level(vl2)
        
        # 获取统计信息
        stats = substation.get_statistics()
        
        assert stats["voltage_levels_count"] == 2
        assert stats["bays_count"] == 3
        assert stats["equipments_count"] == 3
        assert stats["equipment_count_by_type"]["CBR"] == 2
        assert stats["equipment_count_by_type"]["DIS"] == 1
        
        # 检查电压等级统计
        vl_stats = stats["voltage_levels"]
        assert len(vl_stats) == 2
        
        vl1_stats = next(vl for vl in vl_stats if vl["name"] == "110kV")
        assert vl1_stats["bays_count"] == 2
        assert vl1_stats["equipments_count"] == 2


if __name__ == "__main__":
    pytest.main([__file__])
