# IEC61850设计检查器项目结构（含知识库模块）

```
iec61850-design-checker/
├── src/                        # 主要源代码目录
│   ├── knowledge/              # 知识库和学习引擎模块 ⭐新增
│   │   ├── base/              # 知识库基础框架
│   │   │   ├── knowledge_base.py    # 知识库基类
│   │   │   ├── knowledge_entity.py  # 知识实体定义
│   │   │   └── storage_engine.py    # 存储引擎
│   │   ├── extractors/        # 知识提取器
│   │   │   ├── pdf_extractor.py     # PDF文档解析
│   │   │   ├── word_extractor.py    # Word文档解析
│   │   │   ├── rule_extractor.py    # 规则提取器
│   │   │   └── semantic_analyzer.py # 语义分析器
│   │   ├── standards/         # 标准知识库
│   │   │   ├── iec61850/      # IEC61850标准库
│   │   │   │   ├── part1_overview.py
│   │   │   │   ├── part6_scl.py
│   │   │   │   ├── part7_communication.py
│   │   │   │   └── part8_mms.py
│   │   │   ├── national/      # 国家标准库
│   │   │   │   ├── gb_standards.py
│   │   │   │   ├── dl_standards.py
│   │   │   │   └── qgdw_standards.py
│   │   │   └── industry/      # 行业规范库
│   │   ├── reasoning/         # 推理引擎
│   │   │   ├── rule_engine.py       # 规则推理引擎
│   │   │   ├── inference_engine.py  # 推理引擎
│   │   │   ├── conflict_resolver.py # 冲突解决器
│   │   │   └── explanation_engine.py # 解释引擎
│   │   ├── graph/             # 知识图谱
│   │   │   ├── knowledge_graph.py   # 知识图谱构建
│   │   │   ├── graph_query.py       # 图查询引擎
│   │   │   └── graph_visualization.py # 图可视化
│   │   ├── learning/          # 机器学习模块
│   │   │   ├── pattern_recognition.py # 模式识别
│   │   │   ├── rule_learning.py      # 规则学习
│   │   │   └── expert_system.py     # 专家系统
│   │   └── maintenance/       # 知识维护
│   │       ├── version_control.py   # 版本控制
│   │       ├── quality_check.py     # 质量检查
│   │       └── expert_interface.py  # 专家接口
│   ├── core/                   # 核心业务逻辑
│   │   ├── parsers/           # IEC61850文件解析器
│   │   ├── validators/        # 标准符合性验证（增强版）
│   │   │   ├── knowledge_validator.py # 基于知识库的验证器 ⭐新增
│   │   │   ├── adaptive_validator.py  # 自适应验证器 ⭐新增
│   │   │   └── intelligent_checker.py # 智能检查器 ⭐新增
│   │   ├── comparators/       # 文件对比功能
│   │   ├── generators/        # 生成器模块
│   │   ├── models/            # 数据模型
│   │   └── utils/             # 工具函数
│   ├── gui/                    # PySide6 GUI界面
│   │   ├── knowledge_panels/  # 知识库相关界面 ⭐新增
│   │   │   ├── knowledge_browser.py  # 知识浏览器
│   │   │   ├── rule_editor.py        # 规则编辑器
│   │   │   ├── learning_monitor.py   # 学习监控面板
│   │   │   └── expert_panel.py       # 专家知识录入面板
│   │   └── ... (其他GUI模块)
│   ├── api/                    # API服务
│   ├── services/              # 业务服务层
│   │   ├── knowledge_service.py      # 知识库服务 ⭐新增
│   │   └── ... (其他服务)
│   └── config/                # 配置文件
├── knowledge_data/            # 知识库数据目录 ⭐新增
│   ├── standards/             # 标准文档
│   │   ├── iec61850/         # IEC61850系列标准
│   │   ├── national/         # 国家标准
│   │   └── industry/         # 行业规范
│   ├── rules/                # 提取的规则库
│   ├── graphs/               # 知识图谱数据
│   ├── models/               # 训练的模型
│   └── cache/                # 缓存数据
├── tests/                     # 测试文件
│   ├── knowledge_tests/      # 知识库测试 ⭐新增
│   └── ... (其他测试)
├── docs/                      # 项目文档
│   ├── knowledge_base_guide.md # 知识库使用指南 ⭐新增
│   └── ... (其他文档)
├── examples/                  # 示例文件
└── tools/                     # 开发工具 ⭐新增
    ├── knowledge_importer.py  # 知识导入工具
    ├── rule_validator.py      # 规则验证工具
    └── standard_updater.py    # 标准更新工具
```

## 核心特性

### 1. 智能知识提取
- 自动解析PDF/Word格式的标准文档
- 使用NLP技术提取验证规则和约束条件
- 建立标准条款与验证规则的映射关系

### 2. 知识图谱构建
- 构建电力标准的语义网络
- 建立设备类型、通信协议、验证规则的关联关系
- 支持复杂查询和推理

### 3. 自适应验证
- 根据设备配置自动选择适用的验证规则
- 智能推理设备间的兼容性要求
- 提供验证结果的详细解释和建议

### 4. 专家知识集成
- 支持专家经验和最佳实践的录入
- 建立案例库和问题解决方案库
- 持续学习和知识更新机制
