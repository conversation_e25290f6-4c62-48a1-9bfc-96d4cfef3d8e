<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚端子回路图查看器</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 20px 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .tabs { display: flex; border-bottom: 2px solid #ddd; margin-bottom: 20px; }
        .tab { padding: 10px 20px; cursor: pointer; border: none; background: none; font-size: 14px; }
        .tab.active { background-color: #667eea; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .circuit-container { text-align: center; padding: 20px; }
        .circuit-description { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: left; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 虚端子回路图查看器</h1>
            <p>基于IEC61850虚端子连接关系生成的传统二次回路图</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('current')">电流回路</button>
            <button class="tab" onclick="showTab('protection')">保护回路</button>
            <button class="tab" onclick="showTab('trip')">跳闸回路</button>
        </div>
        <div id="current" class="tab-content active">
            <div class="circuit-description">
                <h3>电流回路图</h3>
                <p>基于电流互感器的电流测量回路</p>
            </div>
            <div class="circuit-container">
                <object data="current_circuit_enhanced.svg" type="image/svg+xml" width="100%" height="600">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
        <div id="protection" class="tab-content ">
            <div class="circuit-description">
                <h3>保护回路图</h3>
                <p>基于GOOSE和SV的保护通信回路</p>
            </div>
            <div class="circuit-container">
                <object data="protection_circuit_enhanced.svg" type="image/svg+xml" width="100%" height="600">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
        <div id="trip" class="tab-content ">
            <div class="circuit-description">
                <h3>跳闸回路图</h3>
                <p>断路器控制和跳闸回路</p>
            </div>
            <div class="circuit-container">
                <object data="trip_circuit_enhanced.svg" type="image/svg+xml" width="100%" height="600">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
    </div>
    
    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            var contents = document.getElementsByClassName('tab-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].classList.remove('active');
            }
            
            // 移除所有标签的active类
            var tabs = document.getElementsByClassName('tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>