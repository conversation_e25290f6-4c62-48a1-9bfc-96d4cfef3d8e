"""
基于知识库的验证器
集成知识库功能到现有验证系统
"""

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..base.knowledge_base import KnowledgeBase
from ..base.storage_engine import SQLiteStorageEngine
from ..graph.graph_builder import GraphBuilder
from ..reasoning.inference_engine import InferenceEngine
from ..reasoning.rule_engine import KnowledgeRuleEngine
from ...core.rules.base_rule import ValidationResult
from ...core.models.base_model import BaseModel


logger = logging.getLogger(__name__)


class KnowledgeBasedValidator:
    """基于知识库的验证器"""
    
    def __init__(self, knowledge_db_path: str = "data/knowledge.db"):
        """
        初始化知识库验证器
        
        Args:
            knowledge_db_path: 知识库数据库路径
        """
        try:
            # 初始化存储引擎
            self.storage_engine = SQLiteStorageEngine(knowledge_db_path)
            
            # 初始化知识库
            self.knowledge_base = KnowledgeBase(self.storage_engine)
            
            # 构建知识图谱
            self.graph_builder = GraphBuilder()
            self.knowledge_graph = self.graph_builder.build_graph(self.knowledge_base)
            
            # 初始化推理引擎
            self.inference_engine = InferenceEngine(self.knowledge_graph)
            
            # 初始化知识规则引擎
            self.knowledge_rule_engine = KnowledgeRuleEngine(self.knowledge_graph)
            
            # 验证统计
            self.validation_stats = {
                'total_validations': 0,
                'knowledge_rules_applied': 0,
                'inference_suggestions': 0,
                'adaptive_rules_generated': 0
            }
            
            logger.info("知识库验证器初始化完成")
            
        except Exception as e:
            logger.error(f"知识库验证器初始化失败: {e}")
            raise
    
    def validate_with_knowledge(self, 
                               data: BaseModel,
                               context: Dict[str, Any] = None) -> List[ValidationResult]:
        """
        使用知识库进行验证
        
        Args:
            data: 验证数据
            context: 验证上下文
            
        Returns:
            List[ValidationResult]: 验证结果列表
        """
        results = []
        
        try:
            self.validation_stats['total_validations'] += 1
            
            # 准备验证上下文
            validation_context = self._prepare_validation_context(data, context or {})
            
            # 1. 执行知识规则验证
            knowledge_results = self._execute_knowledge_rules(data, validation_context)
            results.extend(knowledge_results)
            
            # 2. 执行推理验证
            inference_results = self._execute_inference_validation(data, validation_context)
            results.extend(inference_results)
            
            # 3. 执行回路逻辑验证
            circuit_logic_results = self._execute_circuit_logic_validation(data, validation_context)
            results.extend(circuit_logic_results)
            
            # 4. 执行回路关系验证
            circuit_relationship_results = self._execute_circuit_relationship_validation(data, validation_context)
            results.extend(circuit_relationship_results)
            
            # 5. 执行最佳实践验证
            best_practice_results = self._execute_best_practice_validation(data, validation_context)
            results.extend(best_practice_results)
            
            # 6. 执行自适应验证
            adaptive_results = self._execute_adaptive_validation(data, validation_context)
            results.extend(adaptive_results)
            
            logger.info(f"知识库验证完成，共 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"知识库验证失败: {e}")
            return []
    
    def _prepare_validation_context(self, data: BaseModel, context: Dict[str, Any]) -> Dict[str, Any]:
        """准备验证上下文"""
        validation_context = context.copy()
        
        try:
            # 从数据中提取上下文信息
            if hasattr(data, 'header') and data.header:
                validation_context['file_type'] = 'SCD'
                validation_context['version'] = getattr(data.header, 'version', '')
                validation_context['revision'] = getattr(data.header, 'revision', '')
            
            # 提取设备信息
            if hasattr(data, 'ieds') and data.ieds:
                device_types = set()
                manufacturers = set()
                for ied in data.ieds:
                    if hasattr(ied, 'type'):
                        device_types.add(ied.type)
                    if hasattr(ied, 'manufacturer'):
                        manufacturers.add(ied.manufacturer)
                
                validation_context['device_types'] = list(device_types)
                validation_context['manufacturers'] = list(manufacturers)
            
            # 提取通信信息
            if hasattr(data, 'communication') and data.communication:
                protocols = set()
                if hasattr(data.communication, 'subnets'):
                    for subnet in data.communication.subnets:
                        if hasattr(subnet, 'type'):
                            protocols.add(subnet.type)
                
                validation_context['protocols'] = list(protocols)
            
            # 添加标准信息
            validation_context['applicable_standards'] = [
                'IEC 61850-6', 'IEC 61850-7-1', 'IEC 61850-8-1'
            ]
            
        except Exception as e:
            logger.warning(f"准备验证上下文失败: {e}")
        
        return validation_context
    
    def _execute_knowledge_rules(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行知识规则验证"""
        try:
            results = self.knowledge_rule_engine.execute_knowledge_rules(data, context)
            self.validation_stats['knowledge_rules_applied'] += len(results)
            return results
        except Exception as e:
            logger.error(f"执行知识规则失败: {e}")
            return []
    
    def _execute_inference_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行推理验证"""
        results = []
        
        try:
            # 推理适用的规则
            applicable_rules = self.inference_engine.infer_applicable_rules(context)
            
            for rule_entity, confidence in applicable_rules:
                # 生成动态规则并执行
                dynamic_rule = self.knowledge_rule_engine.generate_dynamic_rule(rule_entity)
                if dynamic_rule:
                    result = dynamic_rule.validate(data)
                    
                    # 添加推理信息
                    result.details = result.details or {}
                    result.details['inference_confidence'] = confidence
                    result.details['inferred_rule'] = True
                    
                    results.append(result)
            
            self.validation_stats['inference_suggestions'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行推理验证失败: {e}")
            return []
    
    def _execute_circuit_logic_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行回路逻辑验证"""
        results = []
        
        try:
            # 获取回路信息
            circuits = context.get('circuits', [])
            
            for circuit_info in circuits:
                circuit_id = circuit_info.get('id', '')
                circuit_type = circuit_info.get('type', '')
                
                # 验证回路逻辑
                circuit_issues = self.knowledge_graph.validate_circuit_logic(circuit_id)
                
                for issue in circuit_issues:
                    result = ValidationResult(
                        rule_id=f"circuit_logic_{circuit_type}",
                        passed=False,
                        message=f"回路逻辑问题: {issue.get('description', '')}",
                        severity=issue.get('severity', 'warning'),
                        details={
                            'circuit_id': circuit_id,
                            'circuit_type': circuit_type,
                            'issue_details': issue
                        }
                    )
                    results.append(result)
            
            self.validation_stats['knowledge_rules_applied'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行回路逻辑验证失败: {e}")
            return []
    
    def _execute_circuit_relationship_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行回路关系验证"""
        results = []
        
        try:
            # 分析回路间关系
            relationship_analysis = self.knowledge_graph.analyze_circuit_relationships()
            
            # 检查冲突关系
            conflicting_relationships = relationship_analysis.get('conflicting_relationships', [])
            for conflict in conflicting_relationships:
                result = ValidationResult(
                    rule_id="circuit_relationship_conflict",
                    passed=False,
                    message=f"回路关系冲突: {conflict.get('description', '')}",
                    severity="error",
                    details={
                        'conflict_details': conflict
                    }
                )
                results.append(result)
            
            # 检查冗余关系
            redundant_relationships = relationship_analysis.get('redundant_relationships', [])
            for redundancy in redundant_relationships:
                result = ValidationResult(
                    rule_id="circuit_relationship_redundancy",
                    passed=True,  # 冗余不一定是错误
                    message=f"回路关系冗余: {redundancy.get('description', '')}",
                    severity="info",
                    details={
                        'redundancy_details': redundancy
                    }
                )
                results.append(result)
            
            self.validation_stats['inference_suggestions'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行回路关系验证失败: {e}")
            return []
    
    def _execute_best_practice_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行最佳实践验证"""
        results = []
        
        try:
            # 获取设备类型和协议信息
            device_types = context.get('device_types', [])
            protocols = context.get('protocols', [])
            
            # 基于设备类型检查最佳实践
            for device_type in device_types:
                if device_type == 'protection':
                    # 保护设备最佳实践
                    protection_practices = self._check_protection_best_practices(data, context)
                    results.extend(protection_practices)
                elif device_type == 'control':
                    # 控制设备最佳实践
                    control_practices = self._check_control_best_practices(data, context)
                    results.extend(control_practices)
            
            # 基于协议检查最佳实践
            if 'GOOSE' in protocols:
                goose_practices = self._check_goose_best_practices(data, context)
                results.extend(goose_practices)
            
            if 'SMV' in protocols:
                smv_practices = self._check_smv_best_practices(data, context)
                results.extend(smv_practices)
            
            self.validation_stats['adaptive_rules_generated'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行最佳实践验证失败: {e}")
            return []
    
    def _check_protection_best_practices(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """检查保护设备最佳实践"""
        results = []
        
        # 检查保护四性要求
        four_characteristics_check = self._check_four_characteristics(data, context)
        results.extend(four_characteristics_check)
        
        # 检查保护配置要求
        config_check = self._check_protection_config(data, context)
        results.extend(config_check)
        
        return results
    
    def _check_four_characteristics(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """检查保护四性（选择性、速动性、灵敏性、可靠性）"""
        results = []
        
        # 选择性检查
        if hasattr(data, 'ieds'):
            for ied in data.ieds:
                if hasattr(ied, 'logical_nodes'):
                    # 检查保护逻辑节点配置
                    protection_lns = [ln for ln in ied.logical_nodes 
                                    if hasattr(ln, 'lnClass') and 
                                    ln.lnClass.startswith(('PTOC', 'PDIF', 'PDIS'))]
                    
                    for ln in protection_lns:
                        # 检查是否有跳闸逻辑节点配合
                        if not any(hasattr(other_ln, 'lnClass') and other_ln.lnClass == 'PTRC' 
                                  for other_ln in ied.logical_nodes):
                            result = ValidationResult(
                                rule_id="protection_selectivity",
                                passed=False,
                                message=f"保护装置 {ied.name} 的 {ln.lnClass} 缺少跳闸逻辑节点PTRC",
                                severity="warning"
                            )
                            results.append(result)
        
        return results
    
    def _check_protection_config(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """检查保护配置要求"""
        results = []
        
        # 检查主保护配置
        if hasattr(data, 'ieds'):
            for ied in data.ieds:
                if hasattr(ied, 'logical_nodes'):
                    # 检查是否有主保护
                    main_protection_lns = [ln for ln in ied.logical_nodes 
                                         if hasattr(ln, 'lnClass') and 
                                         ln.lnClass in ['PTOC', 'PDIF', 'PDIS']]
                    
                    if not main_protection_lns:
                        result = ValidationResult(
                            rule_id="main_protection_missing",
                            passed=False,
                            message=f"保护装置 {ied.name} 缺少主保护配置",
                            severity="error"
                        )
                        results.append(result)
        
        return results
    
    def _check_control_best_practices(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """检查控制设备最佳实践"""
        results = []
        
        # 检查防跳回路配置
        if hasattr(data, 'ieds'):
            for ied in data.ieds:
                if hasattr(ied, 'logical_nodes'):
                    # 检查是否有控制开关和断路器逻辑节点
                    cswi_lns = [ln for ln in ied.logical_nodes 
                               if hasattr(ln, 'lnClass') and ln.lnClass == 'CSWI']
                    xcbR_lns = [ln for ln in ied.logical_nodes 
                               if hasattr(ln, 'lnClass') and ln.lnClass == 'XCBR']
                    
                    if cswi_lns and not xcbR_lns:
                        result = ValidationResult(
                            rule_id="control_circuit_incomplete",
                            passed=False,
                            message=f"控制装置 {ied.name} 有控制开关但缺少断路器逻辑节点",
                            severity="warning"
                        )
                        results.append(result)
        
        return results
    
    def _check_goose_best_practices(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """检查GOOSE最佳实践"""
        results = []
        
        # 检查GOOSE配置
        if hasattr(data, 'communication'):
            if hasattr(data.communication, 'subnets'):
                for subnet in data.communication.subnets:
                    if hasattr(subnet, 'connected_aps'):
                        for cap in subnet.connected_aps:
                            if hasattr(cap, 'gse'):
                                for gse in cap.gse:
                                    # 检查GOOSE应用ID
                                    if not hasattr(gse, 'appID') or not gse.appID:
                                        result = ValidationResult(
                                            rule_id="goose_appid_missing",
                                            passed=False,
                                            message="GOOSE配置缺少应用ID",
                                            severity="error"
                                        )
                                        results.append(result)
        
        return results
    
    def _check_smv_best_practices(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """检查SMV最佳实践"""
        results = []
        
        # 检查SMV配置
        if hasattr(data, 'communication'):
            if hasattr(data.communication, 'subnets'):
                for subnet in data.communication.subnets:
                    if hasattr(subnet, 'connected_aps'):
                        for cap in subnet.connected_aps:
                            if hasattr(cap, 'smv'):
                                for smv in cap.smv:
                                    # 检查SMV应用ID
                                    if not hasattr(smv, 'appID') or not smv.appID:
                                        result = ValidationResult(
                                            rule_id="smv_appid_missing",
                                            passed=False,
                                            message="SMV配置缺少应用ID",
                                            severity="error"
                                        )
                                        results.append(result)
        
        return results
    
    def _execute_adaptive_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行自适应验证"""
        results = []
        
        try:
            # 基于上下文生成自适应规则
            adaptive_rules = self._generate_adaptive_rules(context)
            
            for rule in adaptive_rules:
                result = rule.validate(data)
                result.details = result.details or {}
                result.details['adaptive_rule'] = True
                results.append(result)
            
            self.validation_stats['adaptive_rules_generated'] += len(adaptive_rules)
            return results
            
        except Exception as e:
            logger.error(f"执行自适应验证失败: {e}")
            return []
    
    def _generate_adaptive_rules(self, context: Dict[str, Any]) -> List:
        """生成自适应规则"""
        adaptive_rules = []
        
        try:
            # 基于设备类型生成规则
            device_types = context.get('device_types', [])
            for device_type in device_types:
                if device_type == 'protection':
                    # 生成保护设备特定规则
                    rule = self._create_protection_device_rule()
                    if rule:
                        adaptive_rules.append(rule)
                elif device_type == 'measurement':
                    # 生成测量设备特定规则
                    rule = self._create_measurement_device_rule()
                    if rule:
                        adaptive_rules.append(rule)
            
            # 基于协议生成规则
            protocols = context.get('protocols', [])
            if 'GOOSE' in protocols:
                rule = self._create_goose_protocol_rule()
                if rule:
                    adaptive_rules.append(rule)
            
            if 'SMV' in protocols:
                rule = self._create_smv_protocol_rule()
                if rule:
                    adaptive_rules.append(rule)
            
        except Exception as e:
            logger.warning(f"生成自适应规则失败: {e}")
        
        return adaptive_rules
    
    def _create_protection_device_rule(self):
        """创建保护设备规则"""
        from ...core.rules.base_rule import BaseRule
        
        class ProtectionDeviceRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_protection_device",
                    name="保护设备自适应规则",
                    description="基于保护设备特征的自适应验证规则",
                    severity="warning",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查保护设备特定配置
                if hasattr(data, 'ieds'):
                    protection_ieds = [ied for ied in data.ieds 
                                     if hasattr(ied, 'type') and 'protection' in ied.type.lower()]
                    
                    if protection_ieds:
                        # 检查保护设备是否有必要的逻辑节点
                        for ied in protection_ieds:
                            if hasattr(ied, 'logical_nodes'):
                                ln_types = [ln.type for ln in ied.logical_nodes if hasattr(ln, 'type')]
                                if not any('PTRC' in ln_type or 'PDIF' in ln_type for ln_type in ln_types):
                                    return ValidationResult(
                                        rule_id=self.rule_id,
                                        passed=False,
                                        message=f"保护设备 {ied.name} 缺少必要的保护逻辑节点",
                                        severity=self.severity
                                    )
                
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="保护设备配置检查通过",
                    severity=self.severity
                )
        
        return ProtectionDeviceRule()
    
    def _create_measurement_device_rule(self):
        """创建测量设备规则"""
        from ...core.rules.base_rule import BaseRule
        
        class MeasurementDeviceRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_measurement_device",
                    name="测量设备自适应规则",
                    description="基于测量设备特征的自适应验证规则",
                    severity="info",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查测量设备特定配置
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="测量设备配置检查通过",
                    severity=self.severity
                )
        
        return MeasurementDeviceRule()
    
    def _create_goose_protocol_rule(self):
        """创建GOOSE协议规则"""
        from ...core.rules.base_rule import BaseRule
        
        class GOOSEProtocolRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_goose_protocol",
                    name="GOOSE协议自适应规则",
                    description="基于GOOSE协议的自适应验证规则",
                    severity="warning",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查GOOSE配置
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="GOOSE协议配置检查通过",
                    severity=self.severity
                )
        
        return GOOSEProtocolRule()
    
    def _create_smv_protocol_rule(self):
        """创建SMV协议规则"""
        from ...core.rules.base_rule import BaseRule
        
        class SMVProtocolRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_smv_protocol",
                    name="SMV协议自适应规则",
                    description="基于SMV协议的自适应验证规则",
                    severity="warning",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查SMV配置
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="SMV协议配置检查通过",
                    severity=self.severity
                )
        
        return SMVProtocolRule()
    
    def analyze_circuit_logic_deeply(self, data: BaseModel, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        深度分析回路逻辑
        
        Args:
            data: 分析数据
            context: 分析上下文
            
        Returns:
            Dict[str, Any]: 深度分析结果
        """
        analysis_result = {
            'circuit_analysis': {},
            'relationship_analysis': {},
            'best_practice_analysis': {},
            'recommendations': []
        }
        
        try:
            # 准备分析上下文
            analysis_context = self._prepare_validation_context(data, context or {})
            
            # 1. 回路逻辑分析
            circuit_analysis = self.knowledge_graph.analyze_circuit_logic()
            analysis_result['circuit_analysis'] = circuit_analysis
            
            # 2. 回路关系分析
            relationship_analysis = self.knowledge_graph.analyze_circuit_relationships()
            analysis_result['relationship_analysis'] = relationship_analysis
            
            # 3. 最佳实践分析
            best_practice_analysis = self._perform_best_practice_analysis(data, analysis_context)
            analysis_result['best_practice_analysis'] = best_practice_analysis
            
            # 4. 生成改进建议
            recommendations = self._generate_deep_analysis_recommendations(
                circuit_analysis, relationship_analysis, best_practice_analysis
            )
            analysis_result['recommendations'] = recommendations
            
        except Exception as e:
            logger.error(f"深度回路逻辑分析失败: {e}")
            analysis_result['error'] = str(e)
        
        return analysis_result
    
    def _perform_best_practice_analysis(self, data: BaseModel, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行最佳实践分析"""
        analysis = {
            'protection_analysis': {},
            'control_analysis': {},
            'communication_analysis': {}
        }
        
        try:
            # 保护分析
            protection_issues = self._check_protection_best_practices(data, context)
            analysis['protection_analysis']['issues'] = [
                {
                    'rule_id': issue.rule_id,
                    'message': issue.message,
                    'severity': issue.severity
                }
                for issue in protection_issues
            ]
            
            # 控制分析
            control_issues = self._check_control_best_practices(data, context)
            analysis['control_analysis']['issues'] = [
                {
                    'rule_id': issue.rule_id,
                    'message': issue.message,
                    'severity': issue.severity
                }
                for issue in control_issues
            ]
            
            # 通信分析
            protocols = context.get('protocols', [])
            communication_issues = []
            
            if 'GOOSE' in protocols:
                goose_issues = self._check_goose_best_practices(data, context)
                communication_issues.extend(goose_issues)
            
            if 'SMV' in protocols:
                smv_issues = self._check_smv_best_practices(data, context)
                communication_issues.extend(smv_issues)
            
            analysis['communication_analysis']['issues'] = [
                {
                    'rule_id': issue.rule_id,
                    'message': issue.message,
                    'severity': issue.severity
                }
                for issue in communication_issues
            ]
            
        except Exception as e:
            logger.error(f"最佳实践分析失败: {e}")
        
        return analysis
    
    def _generate_deep_analysis_recommendations(self, 
                                              circuit_analysis: Dict[str, Any],
                                              relationship_analysis: Dict[str, Any],
                                              best_practice_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成深度分析建议"""
        recommendations = []
        
        try:
            # 基于回路分析生成建议
            incomplete_circuits = circuit_analysis.get('incomplete_circuits', 0)
            if incomplete_circuits > 0:
                recommendations.append({
                    'type': 'circuit_improvement',
                    'priority': 'high',
                    'description': f'发现 {incomplete_circuits} 个不完整的回路',
                    'recommendation': '检查并完善回路配置，确保所有必需的逻辑节点都已正确连接'
                })
            
            # 基于关系分析生成建议
            shared_entities = relationship_analysis.get('shared_entities', {})
            if shared_entities:
                recommendations.append({
                    'type': 'relationship_optimization',
                    'priority': 'medium',
                    'description': f'发现 {len(shared_entities)} 个共享实体',
                    'recommendation': '分析共享实体的使用情况，优化回路设计以减少不必要的共享'
                })
            
            # 基于最佳实践分析生成建议
            protection_issues = best_practice_analysis.get('protection_analysis', {}).get('issues', [])
            if protection_issues:
                error_count = len([issue for issue in protection_issues if issue['severity'] == 'error'])
                warning_count = len([issue for issue in protection_issues if issue['severity'] == 'warning'])
                
                if error_count > 0 or warning_count > 0:
                    recommendations.append({
                        'type': 'protection_best_practice',
                        'priority': 'high' if error_count > 0 else 'medium',
                        'description': f'保护配置发现 {error_count} 个错误和 {warning_count} 个警告',
                        'recommendation': '根据GB/T 14285标准检查保护配置，确保满足四性要求'
                    })
            
        except Exception as e:
            logger.error(f"生成深度分析建议失败: {e}")
        
        return recommendations
    
    def get_knowledge_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {
            'knowledge_base': self.knowledge_base.get_statistics(),
            'knowledge_graph': self.knowledge_graph.get_statistics(),
            'rule_engine': self.knowledge_rule_engine.get_rule_statistics(),
            'validation_stats': self.validation_stats.copy()
        }
        
        return stats
    
    def update_knowledge(self, knowledge_data: Dict[str, Any]) -> bool:
        """
        更新知识库
        
        Args:
            knowledge_data: 知识数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 导入知识数据
            success = self.knowledge_base.import_knowledge(
                knowledge_data.get('data', ''),
                knowledge_data.get('format', 'json')
            )
            
            if success:
                # 重新构建知识图谱
                self.knowledge_graph = self.graph_builder.rebuild_graph()
                
                # 清除规则缓存
                self.knowledge_rule_engine.clear_cache()
                
                logger.info("知识库更新成功")
            
            return success
            
        except Exception as e:
            logger.error(f"知识库更新失败: {e}")
            return False
