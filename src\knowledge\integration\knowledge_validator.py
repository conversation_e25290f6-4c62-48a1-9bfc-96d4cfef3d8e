"""
基于知识库的验证器
集成知识库功能到现有验证系统
"""

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..base.knowledge_base import KnowledgeBase
from ..base.storage_engine import SQLiteStorageEngine
from ..graph.graph_builder import GraphBuilder
from ..reasoning.inference_engine import InferenceEngine
from ..reasoning.rule_engine import KnowledgeRuleEngine
from ...core.rules.base_rule import ValidationResult
from ...core.models.base_model import BaseModel


logger = logging.getLogger(__name__)


class KnowledgeBasedValidator:
    """基于知识库的验证器"""
    
    def __init__(self, knowledge_db_path: str = "data/knowledge.db"):
        """
        初始化知识库验证器
        
        Args:
            knowledge_db_path: 知识库数据库路径
        """
        try:
            # 初始化存储引擎
            self.storage_engine = SQLiteStorageEngine(knowledge_db_path)
            
            # 初始化知识库
            self.knowledge_base = KnowledgeBase(self.storage_engine)
            
            # 构建知识图谱
            self.graph_builder = GraphBuilder()
            self.knowledge_graph = self.graph_builder.build_graph(self.knowledge_base)
            
            # 初始化推理引擎
            self.inference_engine = InferenceEngine(self.knowledge_graph)
            
            # 初始化知识规则引擎
            self.knowledge_rule_engine = KnowledgeRuleEngine(self.knowledge_graph)
            
            # 验证统计
            self.validation_stats = {
                'total_validations': 0,
                'knowledge_rules_applied': 0,
                'inference_suggestions': 0,
                'adaptive_rules_generated': 0
            }
            
            logger.info("知识库验证器初始化完成")
            
        except Exception as e:
            logger.error(f"知识库验证器初始化失败: {e}")
            raise
    
    def validate_with_knowledge(self, 
                               data: BaseModel,
                               context: Dict[str, Any] = None) -> List[ValidationResult]:
        """
        使用知识库进行验证
        
        Args:
            data: 验证数据
            context: 验证上下文
            
        Returns:
            List[ValidationResult]: 验证结果列表
        """
        results = []
        
        try:
            self.validation_stats['total_validations'] += 1
            
            # 准备验证上下文
            validation_context = self._prepare_validation_context(data, context or {})
            
            # 1. 执行知识规则验证
            knowledge_results = self._execute_knowledge_rules(data, validation_context)
            results.extend(knowledge_results)
            
            # 2. 执行推理验证
            inference_results = self._execute_inference_validation(data, validation_context)
            results.extend(inference_results)
            
            # 3. 执行自适应验证
            adaptive_results = self._execute_adaptive_validation(data, validation_context)
            results.extend(adaptive_results)
            
            logger.info(f"知识库验证完成，共 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"知识库验证失败: {e}")
            return []
    
    def _prepare_validation_context(self, data: BaseModel, context: Dict[str, Any]) -> Dict[str, Any]:
        """准备验证上下文"""
        validation_context = context.copy()
        
        try:
            # 从数据中提取上下文信息
            if hasattr(data, 'header') and data.header:
                validation_context['file_type'] = 'SCD'
                validation_context['version'] = getattr(data.header, 'version', '')
                validation_context['revision'] = getattr(data.header, 'revision', '')
            
            # 提取设备信息
            if hasattr(data, 'ieds') and data.ieds:
                device_types = set()
                manufacturers = set()
                for ied in data.ieds:
                    if hasattr(ied, 'type'):
                        device_types.add(ied.type)
                    if hasattr(ied, 'manufacturer'):
                        manufacturers.add(ied.manufacturer)
                
                validation_context['device_types'] = list(device_types)
                validation_context['manufacturers'] = list(manufacturers)
            
            # 提取通信信息
            if hasattr(data, 'communication') and data.communication:
                protocols = set()
                if hasattr(data.communication, 'subnets'):
                    for subnet in data.communication.subnets:
                        if hasattr(subnet, 'type'):
                            protocols.add(subnet.type)
                
                validation_context['protocols'] = list(protocols)
            
            # 添加标准信息
            validation_context['applicable_standards'] = [
                'IEC 61850-6', 'IEC 61850-7-1', 'IEC 61850-8-1'
            ]
            
        except Exception as e:
            logger.warning(f"准备验证上下文失败: {e}")
        
        return validation_context
    
    def _execute_knowledge_rules(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行知识规则验证"""
        try:
            results = self.knowledge_rule_engine.execute_knowledge_rules(data, context)
            self.validation_stats['knowledge_rules_applied'] += len(results)
            return results
        except Exception as e:
            logger.error(f"执行知识规则失败: {e}")
            return []
    
    def _execute_inference_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行推理验证"""
        results = []
        
        try:
            # 推理适用的规则
            applicable_rules = self.inference_engine.infer_applicable_rules(context)
            
            for rule_entity, confidence in applicable_rules:
                # 生成动态规则并执行
                dynamic_rule = self.knowledge_rule_engine.generate_dynamic_rule(rule_entity)
                if dynamic_rule:
                    result = dynamic_rule.validate(data)
                    
                    # 添加推理信息
                    result.details = result.details or {}
                    result.details['inference_confidence'] = confidence
                    result.details['inferred_rule'] = True
                    
                    results.append(result)
            
            self.validation_stats['inference_suggestions'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行推理验证失败: {e}")
            return []
    
    def _execute_adaptive_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行自适应验证"""
        results = []
        
        try:
            # 基于上下文生成自适应规则
            adaptive_rules = self._generate_adaptive_rules(context)
            
            for rule in adaptive_rules:
                result = rule.validate(data)
                result.details = result.details or {}
                result.details['adaptive_rule'] = True
                results.append(result)
            
            self.validation_stats['adaptive_rules_generated'] += len(adaptive_rules)
            return results
            
        except Exception as e:
            logger.error(f"执行自适应验证失败: {e}")
            return []
    
    def _generate_adaptive_rules(self, context: Dict[str, Any]) -> List:
        """生成自适应规则"""
        adaptive_rules = []
        
        try:
            # 基于设备类型生成规则
            device_types = context.get('device_types', [])
            for device_type in device_types:
                if device_type == 'protection':
                    # 生成保护设备特定规则
                    rule = self._create_protection_device_rule()
                    if rule:
                        adaptive_rules.append(rule)
                elif device_type == 'measurement':
                    # 生成测量设备特定规则
                    rule = self._create_measurement_device_rule()
                    if rule:
                        adaptive_rules.append(rule)
            
            # 基于协议生成规则
            protocols = context.get('protocols', [])
            if 'GOOSE' in protocols:
                rule = self._create_goose_protocol_rule()
                if rule:
                    adaptive_rules.append(rule)
            
            if 'SMV' in protocols:
                rule = self._create_smv_protocol_rule()
                if rule:
                    adaptive_rules.append(rule)
            
        except Exception as e:
            logger.warning(f"生成自适应规则失败: {e}")
        
        return adaptive_rules
    
    def _create_protection_device_rule(self):
        """创建保护设备规则"""
        from ...core.rules.base_rule import BaseRule
        
        class ProtectionDeviceRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_protection_device",
                    name="保护设备自适应规则",
                    description="基于保护设备特征的自适应验证规则",
                    severity="warning",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查保护设备特定配置
                if hasattr(data, 'ieds'):
                    protection_ieds = [ied for ied in data.ieds 
                                     if hasattr(ied, 'type') and 'protection' in ied.type.lower()]
                    
                    if protection_ieds:
                        # 检查保护设备是否有必要的逻辑节点
                        for ied in protection_ieds:
                            if hasattr(ied, 'logical_nodes'):
                                ln_types = [ln.type for ln in ied.logical_nodes if hasattr(ln, 'type')]
                                if not any('PTRC' in ln_type or 'PDIF' in ln_type for ln_type in ln_types):
                                    return ValidationResult(
                                        rule_id=self.rule_id,
                                        passed=False,
                                        message=f"保护设备 {ied.name} 缺少必要的保护逻辑节点",
                                        severity=self.severity
                                    )
                
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="保护设备配置检查通过",
                    severity=self.severity
                )
        
        return ProtectionDeviceRule()
    
    def _create_measurement_device_rule(self):
        """创建测量设备规则"""
        from ...core.rules.base_rule import BaseRule
        
        class MeasurementDeviceRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_measurement_device",
                    name="测量设备自适应规则",
                    description="基于测量设备特征的自适应验证规则",
                    severity="info",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查测量设备特定配置
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="测量设备配置检查通过",
                    severity=self.severity
                )
        
        return MeasurementDeviceRule()
    
    def _create_goose_protocol_rule(self):
        """创建GOOSE协议规则"""
        from ...core.rules.base_rule import BaseRule
        
        class GOOSEProtocolRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_goose_protocol",
                    name="GOOSE协议自适应规则",
                    description="基于GOOSE协议的自适应验证规则",
                    severity="warning",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查GOOSE配置
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="GOOSE协议配置检查通过",
                    severity=self.severity
                )
        
        return GOOSEProtocolRule()
    
    def _create_smv_protocol_rule(self):
        """创建SMV协议规则"""
        from ...core.rules.base_rule import BaseRule
        
        class SMVProtocolRule(BaseRule):
            def __init__(self):
                super().__init__(
                    rule_id="adaptive_smv_protocol",
                    name="SMV协议自适应规则",
                    description="基于SMV协议的自适应验证规则",
                    severity="warning",
                    category="adaptive"
                )
            
            def validate(self, data) -> ValidationResult:
                # 检查SMV配置
                return ValidationResult(
                    rule_id=self.rule_id,
                    passed=True,
                    message="SMV协议配置检查通过",
                    severity=self.severity
                )
        
        return SMVProtocolRule()
    
    def get_knowledge_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        stats = {
            'knowledge_base': self.knowledge_base.get_statistics(),
            'knowledge_graph': self.knowledge_graph.get_statistics(),
            'rule_engine': self.knowledge_rule_engine.get_rule_statistics(),
            'validation_stats': self.validation_stats.copy()
        }
        
        return stats
    
    def update_knowledge(self, knowledge_data: Dict[str, Any]) -> bool:
        """
        更新知识库
        
        Args:
            knowledge_data: 知识数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 导入知识数据
            success = self.knowledge_base.import_knowledge(
                knowledge_data.get('data', ''),
                knowledge_data.get('format', 'json')
            )
            
            if success:
                # 重新构建知识图谱
                self.knowledge_graph = self.graph_builder.rebuild_graph()
                
                # 清除规则缓存
                self.knowledge_rule_engine.clear_cache()
                
                logger.info("知识库更新成功")
            
            return success
            
        except Exception as e:
            logger.error(f"知识库更新失败: {e}")
            return False
