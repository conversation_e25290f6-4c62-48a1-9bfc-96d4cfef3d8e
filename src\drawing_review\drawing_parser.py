"""
图纸解析引擎
支持DWG、DXF、PDF格式的图纸文件解析
"""

import logging
import os
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from abc import ABC, abstractmethod

from .drawing_models import (
    DrawingDocument, DrawingElement, Line, Arc, Circle, Text, Dimension, Block, Layer,
    Point, Color, ElementType, LineType
)

logger = logging.getLogger(__name__)


class DrawingParser(ABC):
    """图纸解析器基类"""
    
    def __init__(self):
        """初始化解析器"""
        self.supported_formats = []
        self.current_document = None
        
    @abstractmethod
    def parse_file(self, file_path: str) -> Optional[DrawingDocument]:
        """解析图纸文件"""
        pass
    
    @abstractmethod
    def is_supported(self, file_path: str) -> bool:
        """检查是否支持该文件格式"""
        pass
    
    def _get_file_extension(self, file_path: str) -> str:
        """获取文件扩展名"""
        return Path(file_path).suffix.lower()


class DXFParser(DrawingParser):
    """DXF文件解析器"""
    
    def __init__(self):
        """初始化DXF解析器"""
        super().__init__()
        self.supported_formats = ['.dxf']
        
        # 尝试导入ezdxf库
        try:
            import ezdxf
            self.ezdxf = ezdxf
            self.available = True
            logger.info("DXF解析器初始化成功")
        except ImportError:
            self.ezdxf = None
            self.available = False
            logger.warning("ezdxf库未安装，DXF解析功能不可用")
    
    def is_supported(self, file_path: str) -> bool:
        """检查是否支持DXF文件"""
        return self.available and self._get_file_extension(file_path) in self.supported_formats
    
    def parse_file(self, file_path: str) -> Optional[DrawingDocument]:
        """解析DXF文件"""
        if not self.is_supported(file_path):
            logger.error(f"不支持的文件格式: {file_path}")
            return None
        
        try:
            logger.info(f"开始解析DXF文件: {file_path}")
            
            # 读取DXF文件
            doc = self.ezdxf.readfile(file_path)
            
            # 创建图纸文档
            drawing_doc = DrawingDocument(
                file_path=file_path,
                file_type="dxf",
                title=self._extract_title(doc),
                units=self._extract_units(doc)
            )
            
            # 解析图层
            drawing_doc.layers = self._parse_layers(doc)
            
            # 解析图元
            drawing_doc.elements = self._parse_entities(doc)
            
            # 解析块定义
            drawing_doc.blocks = self._parse_blocks(doc)
            
            # 设置统计信息
            drawing_doc.viewport_bounds = self._calculate_bounds(drawing_doc.elements)
            
            logger.info(f"DXF文件解析完成: {len(drawing_doc.elements)} 个图元")
            return drawing_doc
            
        except Exception as e:
            logger.error(f"DXF文件解析失败: {e}")
            return None
    
    def _extract_title(self, doc) -> str:
        """提取图纸标题"""
        try:
            header = doc.header
            return header.get('$DWGTITLE', '')
        except:
            return ""
    
    def _extract_units(self, doc) -> str:
        """提取图纸单位"""
        try:
            header = doc.header
            units_code = header.get('$INSUNITS', 4)  # 默认毫米
            units_map = {
                1: "inch",
                2: "feet", 
                4: "mm",
                5: "cm",
                6: "m"
            }
            return units_map.get(units_code, "mm")
        except:
            return "mm"
    
    def _parse_layers(self, doc) -> Dict[str, Layer]:
        """解析图层"""
        layers = {}
        
        try:
            for layer in doc.layers:
                color = self._parse_color(layer.color)
                line_type = self._parse_line_type(layer.linetype)
                
                layers[layer.dxf.name] = Layer(
                    name=layer.dxf.name,
                    color=color,
                    line_type=line_type,
                    visible=not layer.is_off(),
                    locked=layer.is_locked(),
                    frozen=layer.is_frozen()
                )
        except Exception as e:
            logger.error(f"图层解析失败: {e}")
        
        return layers
    
    def _parse_entities(self, doc) -> List[DrawingElement]:
        """解析图元"""
        elements = []
        
        try:
            modelspace = doc.modelspace()
            
            for entity in modelspace:
                element = self._parse_entity(entity)
                if element:
                    elements.append(element)
                    
        except Exception as e:
            logger.error(f"图元解析失败: {e}")
        
        return elements
    
    def _parse_entity(self, entity) -> Optional[DrawingElement]:
        """解析单个图元"""
        try:
            entity_type = entity.dxftype()
            
            if entity_type == 'LINE':
                return self._parse_line(entity)
            elif entity_type == 'ARC':
                return self._parse_arc(entity)
            elif entity_type == 'CIRCLE':
                return self._parse_circle(entity)
            elif entity_type in ['TEXT', 'MTEXT']:
                return self._parse_text(entity)
            elif entity_type.startswith('DIMENSION'):
                return self._parse_dimension(entity)
            elif entity_type == 'POLYLINE':
                return self._parse_polyline(entity)
            else:
                # 其他类型的图元暂时跳过
                return None
                
        except Exception as e:
            logger.error(f"图元解析失败: {e}")
            return None
    
    def _parse_line(self, entity) -> Line:
        """解析直线"""
        start_point = Point(entity.dxf.start.x, entity.dxf.start.y, entity.dxf.start.z)
        end_point = Point(entity.dxf.end.x, entity.dxf.end.y, entity.dxf.end.z)
        
        return Line(
            start_point=start_point,
            end_point=end_point,
            layer=entity.dxf.layer,
            color=self._parse_color(entity.dxf.color),
            line_type=self._parse_line_type(entity.dxf.linetype)
        )
    
    def _parse_arc(self, entity) -> Arc:
        """解析圆弧"""
        center = Point(entity.dxf.center.x, entity.dxf.center.y, entity.dxf.center.z)
        
        return Arc(
            center=center,
            radius=entity.dxf.radius,
            start_angle=entity.dxf.start_angle,
            end_angle=entity.dxf.end_angle,
            layer=entity.dxf.layer,
            color=self._parse_color(entity.dxf.color),
            line_type=self._parse_line_type(entity.dxf.linetype)
        )
    
    def _parse_circle(self, entity) -> Circle:
        """解析圆"""
        center = Point(entity.dxf.center.x, entity.dxf.center.y, entity.dxf.center.z)
        
        return Circle(
            center=center,
            radius=entity.dxf.radius,
            layer=entity.dxf.layer,
            color=self._parse_color(entity.dxf.color),
            line_type=self._parse_line_type(entity.dxf.linetype)
        )
    
    def _parse_text(self, entity) -> Text:
        """解析文本"""
        position = Point(entity.dxf.insert.x, entity.dxf.insert.y, entity.dxf.insert.z)
        
        return Text(
            content=entity.dxf.text,
            position=position,
            height=entity.dxf.height,
            rotation=entity.dxf.rotation,
            layer=entity.dxf.layer,
            color=self._parse_color(entity.dxf.color)
        )
    
    def _parse_dimension(self, entity) -> Dimension:
        """解析尺寸标注"""
        # 简化实现，实际需要根据不同类型的尺寸标注进行解析
        return Dimension(
            dim_type="linear",
            layer=entity.dxf.layer,
            color=self._parse_color(entity.dxf.color)
        )
    
    def _parse_polyline(self, entity) -> Optional[DrawingElement]:
        """解析多段线"""
        # 简化实现，将多段线转换为多个直线段
        # 实际应该创建专门的Polyline类
        return None
    
    def _parse_blocks(self, doc) -> Dict[str, Block]:
        """解析块定义"""
        blocks = {}
        
        try:
            for block in doc.blocks:
                if not block.name.startswith('*'):  # 跳过匿名块
                    block_elements = []
                    for entity in block:
                        element = self._parse_entity(entity)
                        if element:
                            block_elements.append(element)
                    
                    blocks[block.name] = Block(
                        name=block.name,
                        base_point=Point(block.dxf.base_point.x, block.dxf.base_point.y),
                        elements=block_elements
                    )
        except Exception as e:
            logger.error(f"块定义解析失败: {e}")
        
        return blocks
    
    def _parse_color(self, color_index: int) -> Color:
        """解析颜色"""
        # AutoCAD标准颜色映射
        color_map = {
            1: (255, 0, 0),    # 红色
            2: (255, 255, 0),  # 黄色
            3: (0, 255, 0),    # 绿色
            4: (0, 255, 255),  # 青色
            5: (0, 0, 255),    # 蓝色
            6: (255, 0, 255),  # 洋红
            7: (255, 255, 255), # 白色
            8: (128, 128, 128), # 灰色
            9: (192, 192, 192)  # 浅灰
        }
        
        if color_index in color_map:
            r, g, b = color_map[color_index]
            return Color(r=r, g=g, b=b, index=color_index)
        else:
            return Color(r=255, g=255, b=255, index=color_index)
    
    def _parse_line_type(self, linetype_name: str) -> LineType:
        """解析线型"""
        linetype_map = {
            'CONTINUOUS': LineType.CONTINUOUS,
            'DASHED': LineType.DASHED,
            'DOTTED': LineType.DOTTED,
            'DASHDOT': LineType.DASHDOT,
            'CENTER': LineType.CENTER,
            'PHANTOM': LineType.PHANTOM,
            'HIDDEN': LineType.HIDDEN
        }
        
        return linetype_map.get(linetype_name.upper(), LineType.CONTINUOUS)
    
    def _calculate_bounds(self, elements: List[DrawingElement]) -> Optional[tuple]:
        """计算图纸边界"""
        if not elements:
            return None
        
        try:
            bounds = [elem.get_bounds() for elem in elements]
            min_points = [bound[0] for bound in bounds]
            max_points = [bound[1] for bound in bounds]
            
            min_x = min(p.x for p in min_points)
            min_y = min(p.y for p in min_points)
            max_x = max(p.x for p in max_points)
            max_y = max(p.y for p in max_points)
            
            return Point(min_x, min_y), Point(max_x, max_y)
        except:
            return None


class DWGParser(DrawingParser):
    """DWG文件解析器"""
    
    def __init__(self):
        """初始化DWG解析器"""
        super().__init__()
        self.supported_formats = ['.dwg']
        
        # DWG文件需要特殊的库支持，如ODA File Converter
        # 这里提供一个基础框架
        self.available = False
        logger.warning("DWG解析器需要额外的库支持")
    
    def is_supported(self, file_path: str) -> bool:
        """检查是否支持DWG文件"""
        return self.available and self._get_file_extension(file_path) in self.supported_formats
    
    def parse_file(self, file_path: str) -> Optional[DrawingDocument]:
        """解析DWG文件"""
        if not self.is_supported(file_path):
            logger.error(f"DWG解析器不可用或不支持的文件格式: {file_path}")
            return None
        
        # TODO: 实现DWG文件解析
        # 可以通过以下方式实现：
        # 1. 使用ODA File Converter将DWG转换为DXF
        # 2. 使用专门的DWG解析库
        # 3. 调用AutoCAD COM接口
        
        logger.warning("DWG文件解析功能待实现")
        return None


class PDFParser(DrawingParser):
    """PDF文件解析器"""
    
    def __init__(self):
        """初始化PDF解析器"""
        super().__init__()
        self.supported_formats = ['.pdf']
        
        # 尝试导入PDF处理库
        try:
            import PyPDF2
            import pdfplumber
            self.PyPDF2 = PyPDF2
            self.pdfplumber = pdfplumber
            self.available = True
            logger.info("PDF解析器初始化成功")
        except ImportError:
            self.PyPDF2 = None
            self.pdfplumber = None
            self.available = False
            logger.warning("PDF处理库未安装，PDF解析功能不可用")
    
    def is_supported(self, file_path: str) -> bool:
        """检查是否支持PDF文件"""
        return self.available and self._get_file_extension(file_path) in self.supported_formats
    
    def parse_file(self, file_path: str) -> Optional[DrawingDocument]:
        """解析PDF文件"""
        if not self.is_supported(file_path):
            logger.error(f"不支持的文件格式: {file_path}")
            return None
        
        try:
            logger.info(f"开始解析PDF文件: {file_path}")
            
            # 创建图纸文档
            drawing_doc = DrawingDocument(
                file_path=file_path,
                file_type="pdf"
            )
            
            # 使用pdfplumber解析PDF内容
            with self.pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # 提取文本
                    text_elements = self._extract_text_from_page(page, page_num)
                    drawing_doc.elements.extend(text_elements)
                    
                    # 提取线条（如果PDF包含矢量图形）
                    line_elements = self._extract_lines_from_page(page, page_num)
                    drawing_doc.elements.extend(line_elements)
            
            logger.info(f"PDF文件解析完成: {len(drawing_doc.elements)} 个元素")
            return drawing_doc
            
        except Exception as e:
            logger.error(f"PDF文件解析失败: {e}")
            return None
    
    def _extract_text_from_page(self, page, page_num: int) -> List[Text]:
        """从PDF页面提取文本"""
        text_elements = []
        
        try:
            # 提取文本及其位置信息
            chars = page.chars
            for char in chars:
                if char['text'].strip():  # 跳过空白字符
                    text_elem = Text(
                        content=char['text'],
                        position=Point(char['x0'], char['top']),
                        height=char['size'],
                        font=char.get('fontname', 'Unknown'),
                        layer=f"PDF_Page_{page_num}"
                    )
                    text_elements.append(text_elem)
        except Exception as e:
            logger.error(f"PDF文本提取失败: {e}")
        
        return text_elements
    
    def _extract_lines_from_page(self, page, page_num: int) -> List[Line]:
        """从PDF页面提取线条"""
        line_elements = []
        
        try:
            # 提取线条信息
            lines = page.lines
            for line in lines:
                line_elem = Line(
                    start_point=Point(line['x0'], line['top']),
                    end_point=Point(line['x1'], line['bottom']),
                    layer=f"PDF_Page_{page_num}",
                    line_weight=line.get('linewidth', 0.25)
                )
                line_elements.append(line_elem)
        except Exception as e:
            logger.error(f"PDF线条提取失败: {e}")
        
        return line_elements


class DrawingParserFactory:
    """图纸解析器工厂"""
    
    def __init__(self):
        """初始化解析器工厂"""
        self.parsers = {
            '.dxf': DXFParser(),
            '.dwg': DWGParser(),
            '.pdf': PDFParser()
        }
    
    def get_parser(self, file_path: str) -> Optional[DrawingParser]:
        """根据文件类型获取解析器"""
        file_ext = Path(file_path).suffix.lower()
        parser = self.parsers.get(file_ext)
        
        if parser and parser.is_supported(file_path):
            return parser
        else:
            return None
    
    def parse_file(self, file_path: str) -> Optional[DrawingDocument]:
        """解析图纸文件"""
        parser = self.get_parser(file_path)
        if parser:
            return parser.parse_file(file_path)
        else:
            logger.error(f"没有可用的解析器处理文件: {file_path}")
            return None
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        supported = []
        for ext, parser in self.parsers.items():
            if parser.available:
                supported.append(ext)
        return supported
