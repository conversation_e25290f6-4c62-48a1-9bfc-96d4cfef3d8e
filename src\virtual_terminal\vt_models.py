"""
虚端子表数据模型
定义虚端子、虚连接等核心数据结构
"""

import logging
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)


class TerminalType(Enum):
    """端子类型"""
    INPUT = "input"          # 输入端子
    OUTPUT = "output"        # 输出端子
    BIDIRECTIONAL = "bidirectional"  # 双向端子


class ConnectionType(Enum):
    """连接类型"""
    GOOSE = "GOOSE"         # GOOSE连接
    SMV = "SMV"             # 采样值连接
    MMS = "MMS"             # MMS连接
    HARDWIRED = "hardwired" # 硬接线连接


class SignalType(Enum):
    """信号类型"""
    DIGITAL = "digital"     # 数字信号
    ANALOG = "analog"       # 模拟信号
    STATUS = "status"       # 状态信号
    COMMAND = "command"     # 命令信号
    MEASUREMENT = "measurement"  # 测量信号


@dataclass
class VirtualTerminal:
    """虚端子"""
    terminal_id: str                    # 端子ID
    device_name: str                    # 设备名称
    logical_device: str                 # 逻辑设备
    logical_node: str                   # 逻辑节点
    data_object: str                    # 数据对象
    data_attribute: Optional[str] = None # 数据属性
    terminal_type: TerminalType = TerminalType.OUTPUT
    signal_type: SignalType = SignalType.DIGITAL
    data_type: Optional[str] = None     # 数据类型
    functional_constraint: Optional[str] = None  # 功能约束
    description: Optional[str] = None   # 描述
    unit: Optional[str] = None          # 单位
    range_min: Optional[float] = None   # 最小值
    range_max: Optional[float] = None   # 最大值
    default_value: Optional[Any] = None # 默认值
    is_critical: bool = False           # 是否关键
    tags: List[str] = field(default_factory=list)  # 标签
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.terminal_id:
            self.terminal_id = self._generate_terminal_id()
    
    def _generate_terminal_id(self) -> str:
        """生成端子ID"""
        parts = [
            self.device_name,
            self.logical_device,
            self.logical_node,
            self.data_object
        ]
        
        if self.data_attribute:
            parts.append(self.data_attribute)
        
        return "/".join(parts)
    
    def get_full_path(self) -> str:
        """获取完整路径"""
        return self.terminal_id
    
    def is_compatible_with(self, other: 'VirtualTerminal') -> bool:
        """检查与另一个端子的兼容性"""
        # 基本兼容性检查
        if self.data_type and other.data_type:
            if self.data_type != other.data_type:
                return False
        
        # 信号类型兼容性
        if self.signal_type != other.signal_type:
            return False
        
        # 端子类型兼容性（输出可以连接到输入）
        if self.terminal_type == TerminalType.OUTPUT and other.terminal_type == TerminalType.INPUT:
            return True
        elif self.terminal_type == TerminalType.INPUT and other.terminal_type == TerminalType.OUTPUT:
            return True
        elif self.terminal_type == TerminalType.BIDIRECTIONAL or other.terminal_type == TerminalType.BIDIRECTIONAL:
            return True
        
        return False


@dataclass
class VirtualConnection:
    """虚连接"""
    connection_id: str                  # 连接ID
    source_terminal: VirtualTerminal    # 源端子
    target_terminal: VirtualTerminal    # 目标端子
    connection_type: ConnectionType     # 连接类型
    description: Optional[str] = None   # 描述
    is_active: bool = True              # 是否激活
    priority: int = 0                   # 优先级
    delay_ms: Optional[float] = None    # 延迟（毫秒）
    quality_factor: float = 1.0         # 质量因子
    configuration: Dict[str, Any] = field(default_factory=dict)  # 配置参数
    created_time: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.connection_id:
            self.connection_id = self._generate_connection_id()
    
    def _generate_connection_id(self) -> str:
        """生成连接ID"""
        return f"{self.source_terminal.terminal_id}→{self.target_terminal.terminal_id}"
    
    def validate_connection(self) -> List[str]:
        """验证连接有效性"""
        issues = []
        
        # 检查端子兼容性
        if not self.source_terminal.is_compatible_with(self.target_terminal):
            issues.append("源端子与目标端子不兼容")
        
        # 检查连接类型与端子类型的匹配
        if self.connection_type == ConnectionType.GOOSE:
            if self.source_terminal.functional_constraint not in ['ST', 'MX', 'CO']:
                issues.append("GOOSE连接的源端子功能约束不正确")
        
        elif self.connection_type == ConnectionType.SMV:
            if self.source_terminal.functional_constraint != 'MX':
                issues.append("SMV连接的源端子功能约束应为MX")
        
        # 检查数据类型
        if self.source_terminal.data_type and self.target_terminal.data_type:
            if self.source_terminal.data_type != self.target_terminal.data_type:
                issues.append(f"数据类型不匹配: {self.source_terminal.data_type} vs {self.target_terminal.data_type}")
        
        return issues
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            'connection_id': self.connection_id,
            'source': self.source_terminal.get_full_path(),
            'target': self.target_terminal.get_full_path(),
            'type': self.connection_type.value,
            'description': self.description,
            'is_active': self.is_active,
            'priority': self.priority,
            'delay_ms': self.delay_ms,
            'quality_factor': self.quality_factor,
            'created_time': self.created_time.isoformat()
        }


@dataclass
class VirtualTerminalTable:
    """虚端子表"""
    table_id: str                       # 表ID
    name: str                           # 表名称
    description: Optional[str] = None   # 描述
    terminals: List[VirtualTerminal] = field(default_factory=list)
    connections: List[VirtualConnection] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_time: datetime = field(default_factory=datetime.now)
    modified_time: datetime = field(default_factory=datetime.now)
    version: str = "1.0"
    
    def add_terminal(self, terminal: VirtualTerminal) -> bool:
        """添加端子"""
        try:
            # 检查是否已存在
            if self.get_terminal_by_id(terminal.terminal_id):
                logger.warning(f"端子已存在: {terminal.terminal_id}")
                return False
            
            self.terminals.append(terminal)
            self.modified_time = datetime.now()
            logger.debug(f"添加端子: {terminal.terminal_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加端子失败: {e}")
            return False
    
    def add_connection(self, connection: VirtualConnection) -> bool:
        """添加连接"""
        try:
            # 验证连接
            issues = connection.validate_connection()
            if issues:
                logger.warning(f"连接验证失败: {issues}")
                return False
            
            # 检查是否已存在
            if self.get_connection_by_id(connection.connection_id):
                logger.warning(f"连接已存在: {connection.connection_id}")
                return False
            
            self.connections.append(connection)
            self.modified_time = datetime.now()
            logger.debug(f"添加连接: {connection.connection_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加连接失败: {e}")
            return False
    
    def get_terminal_by_id(self, terminal_id: str) -> Optional[VirtualTerminal]:
        """根据ID获取端子"""
        for terminal in self.terminals:
            if terminal.terminal_id == terminal_id:
                return terminal
        return None
    
    def get_connection_by_id(self, connection_id: str) -> Optional[VirtualConnection]:
        """根据ID获取连接"""
        for connection in self.connections:
            if connection.connection_id == connection_id:
                return connection
        return None
    
    def get_terminals_by_device(self, device_name: str) -> List[VirtualTerminal]:
        """获取指定设备的所有端子"""
        return [t for t in self.terminals if t.device_name == device_name]
    
    def get_connections_by_device(self, device_name: str) -> List[VirtualConnection]:
        """获取指定设备相关的所有连接"""
        return [c for c in self.connections 
                if c.source_terminal.device_name == device_name 
                or c.target_terminal.device_name == device_name]
    
    def get_input_terminals(self) -> List[VirtualTerminal]:
        """获取所有输入端子"""
        return [t for t in self.terminals if t.terminal_type == TerminalType.INPUT]
    
    def get_output_terminals(self) -> List[VirtualTerminal]:
        """获取所有输出端子"""
        return [t for t in self.terminals if t.terminal_type == TerminalType.OUTPUT]
    
    def get_unconnected_terminals(self) -> List[VirtualTerminal]:
        """获取未连接的端子"""
        connected_terminals = set()
        
        for connection in self.connections:
            connected_terminals.add(connection.source_terminal.terminal_id)
            connected_terminals.add(connection.target_terminal.terminal_id)
        
        return [t for t in self.terminals if t.terminal_id not in connected_terminals]
    
    def get_connections_by_type(self, connection_type: ConnectionType) -> List[VirtualConnection]:
        """根据类型获取连接"""
        return [c for c in self.connections if c.connection_type == connection_type]
    
    def validate_table(self) -> List[str]:
        """验证表的完整性"""
        issues = []
        
        # 检查端子ID唯一性
        terminal_ids = [t.terminal_id for t in self.terminals]
        if len(terminal_ids) != len(set(terminal_ids)):
            issues.append("存在重复的端子ID")
        
        # 检查连接ID唯一性
        connection_ids = [c.connection_id for c in self.connections]
        if len(connection_ids) != len(set(connection_ids)):
            issues.append("存在重复的连接ID")
        
        # 检查连接的端子是否存在
        for connection in self.connections:
            if not self.get_terminal_by_id(connection.source_terminal.terminal_id):
                issues.append(f"连接的源端子不存在: {connection.source_terminal.terminal_id}")
            
            if not self.get_terminal_by_id(connection.target_terminal.terminal_id):
                issues.append(f"连接的目标端子不存在: {connection.target_terminal.terminal_id}")
        
        # 验证每个连接
        for connection in self.connections:
            connection_issues = connection.validate_connection()
            issues.extend([f"连接 {connection.connection_id}: {issue}" for issue in connection_issues])
        
        return issues
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        devices = set(t.device_name for t in self.terminals)
        
        terminal_types = {}
        for terminal_type in TerminalType:
            terminal_types[terminal_type.value] = len([t for t in self.terminals 
                                                     if t.terminal_type == terminal_type])
        
        connection_types = {}
        for connection_type in ConnectionType:
            connection_types[connection_type.value] = len([c for c in self.connections 
                                                         if c.connection_type == connection_type])
        
        signal_types = {}
        for signal_type in SignalType:
            signal_types[signal_type.value] = len([t for t in self.terminals 
                                                 if t.signal_type == signal_type])
        
        return {
            'total_terminals': len(self.terminals),
            'total_connections': len(self.connections),
            'total_devices': len(devices),
            'unconnected_terminals': len(self.get_unconnected_terminals()),
            'terminal_types': terminal_types,
            'connection_types': connection_types,
            'signal_types': signal_types,
            'devices': list(devices)
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'table_id': self.table_id,
            'name': self.name,
            'description': self.description,
            'version': self.version,
            'created_time': self.created_time.isoformat(),
            'modified_time': self.modified_time.isoformat(),
            'metadata': self.metadata,
            'terminals': [
                {
                    'terminal_id': t.terminal_id,
                    'device_name': t.device_name,
                    'logical_device': t.logical_device,
                    'logical_node': t.logical_node,
                    'data_object': t.data_object,
                    'data_attribute': t.data_attribute,
                    'terminal_type': t.terminal_type.value,
                    'signal_type': t.signal_type.value,
                    'data_type': t.data_type,
                    'functional_constraint': t.functional_constraint,
                    'description': t.description,
                    'unit': t.unit,
                    'range_min': t.range_min,
                    'range_max': t.range_max,
                    'default_value': t.default_value,
                    'is_critical': t.is_critical,
                    'tags': t.tags
                } for t in self.terminals
            ],
            'connections': [c.get_connection_info() for c in self.connections],
            'statistics': self.get_statistics()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VirtualTerminalTable':
        """从字典创建"""
        table = cls(
            table_id=data['table_id'],
            name=data['name'],
            description=data.get('description'),
            version=data.get('version', '1.0'),
            metadata=data.get('metadata', {})
        )
        
        # 解析时间
        if 'created_time' in data:
            table.created_time = datetime.fromisoformat(data['created_time'])
        if 'modified_time' in data:
            table.modified_time = datetime.fromisoformat(data['modified_time'])
        
        # 添加端子
        for terminal_data in data.get('terminals', []):
            terminal = VirtualTerminal(
                terminal_id=terminal_data['terminal_id'],
                device_name=terminal_data['device_name'],
                logical_device=terminal_data['logical_device'],
                logical_node=terminal_data['logical_node'],
                data_object=terminal_data['data_object'],
                data_attribute=terminal_data.get('data_attribute'),
                terminal_type=TerminalType(terminal_data['terminal_type']),
                signal_type=SignalType(terminal_data['signal_type']),
                data_type=terminal_data.get('data_type'),
                functional_constraint=terminal_data.get('functional_constraint'),
                description=terminal_data.get('description'),
                unit=terminal_data.get('unit'),
                range_min=terminal_data.get('range_min'),
                range_max=terminal_data.get('range_max'),
                default_value=terminal_data.get('default_value'),
                is_critical=terminal_data.get('is_critical', False),
                tags=terminal_data.get('tags', [])
            )
            table.add_terminal(terminal)
        
        # 添加连接（需要先有端子）
        for connection_data in data.get('connections', []):
            # 解析连接的源和目标
            if '→' in connection_data.get('source', ''):
                source_id = connection_data['source']
                target_id = connection_data['target']
            else:
                # 从connection_id解析
                parts = connection_data['connection_id'].split('→')
                if len(parts) == 2:
                    source_id = parts[0]
                    target_id = parts[1]
                else:
                    continue

            source_terminal = table.get_terminal_by_id(source_id)
            target_terminal = table.get_terminal_by_id(target_id)

            if source_terminal and target_terminal:
                connection = VirtualConnection(
                    connection_id=connection_data['connection_id'],
                    source_terminal=source_terminal,
                    target_terminal=target_terminal,
                    connection_type=ConnectionType(connection_data['type']),
                    description=connection_data.get('description'),
                    is_active=connection_data.get('is_active', True),
                    priority=connection_data.get('priority', 0),
                    delay_ms=connection_data.get('delay_ms'),
                    quality_factor=connection_data.get('quality_factor', 1.0)
                )

                if 'created_time' in connection_data:
                    connection.created_time = datetime.fromisoformat(connection_data['created_time'])

                table.add_connection(connection)
        
        return table
