"""
规则引擎单元测试
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from src.core.rules import (
    BaseRule, RuleResult, RuleContext, RuleSeverity, RuleCategory,
    ValidationError, ValidationWarning, ValidationInfo,
    RuleRegistry, RuleEngine, RuleExecutor, ExecutionConfig,
    ReportGenerator, ValidationReport
)
from src.core.models import IED, SubStation, VoltageLevel


class TestBaseRule:
    """测试基础规则类"""
    
    def test_rule_creation(self):
        """测试规则创建"""
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                return RuleResult(rule_id=self.rule_id, success=True)
        
        rule = TestRule(
            rule_id="TEST001",
            name="测试规则",
            description="这是一个测试规则",
            category=RuleCategory.CUSTOM,
            severity=RuleSeverity.ERROR
        )
        
        assert rule.rule_id == "TEST001"
        assert rule.name == "测试规则"
        assert rule.description == "这是一个测试规则"
        assert rule.category == RuleCategory.CUSTOM
        assert rule.severity == RuleSeverity.ERROR
        assert rule.enabled is True
    
    def test_rule_validation(self):
        """测试规则验证"""
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                result = RuleResult(rule_id=self.rule_id, success=True)
                result.add_error("测试错误", context.get_path_string())
                return result
        
        rule = TestRule(rule_id="TEST001", name="测试规则", description="测试")
        context = RuleContext(data="test_data")
        
        result = rule.validate(context)
        
        assert result.rule_id == "TEST001"
        assert result.success is False
        assert len(result.get_errors()) == 1
        assert result.get_errors()[0].message == "测试错误"


class TestRuleResult:
    """测试规则结果类"""
    
    def test_rule_result_creation(self):
        """测试规则结果创建"""
        result = RuleResult(rule_id="TEST001", success=True)
        
        assert result.rule_id == "TEST001"
        assert result.success is True
        assert len(result.issues) == 0
        assert result.execution_time == 0.0
    
    def test_add_issues(self):
        """测试添加问题"""
        result = RuleResult(rule_id="TEST001", success=True)
        
        # 添加错误
        result.add_error("错误消息", "test.path")
        assert len(result.get_errors()) == 1
        assert result.success is False
        
        # 添加警告
        result.add_warning("警告消息", "test.path")
        assert len(result.get_warnings()) == 1
        
        # 添加信息
        result.add_info("信息消息", "test.path")
        assert len(result.get_infos()) == 1
        
        assert len(result.issues) == 3


class TestRuleContext:
    """测试规则上下文类"""
    
    def test_context_creation(self):
        """测试上下文创建"""
        data = {"test": "data"}
        context = RuleContext(data=data)
        
        assert context.data == data
        assert context.global_context == {}
        assert context.config == {}
        assert context.path == []
        assert context.parent is None
    
    def test_path_string(self):
        """测试路径字符串"""
        context = RuleContext(data="test", path=["root", "child", "grandchild"])
        assert context.get_path_string() == "root.child.grandchild"
        
        empty_context = RuleContext(data="test")
        assert empty_context.get_path_string() == "root"
    
    def test_child_context(self):
        """测试子上下文"""
        parent_context = RuleContext(
            data="parent",
            global_context={"global": "data"},
            path=["root"]
        )
        
        child_context = parent_context.create_child_context("child", "child_data")
        
        assert child_context.data == "child_data"
        assert child_context.global_context == {"global": "data"}
        assert child_context.path == ["root", "child"]
        assert child_context.parent == parent_context


class TestRuleRegistry:
    """测试规则注册表"""
    
    def test_registry_creation(self):
        """测试注册表创建"""
        registry = RuleRegistry()
        
        assert len(registry.get_all_rules()) == 0
        assert len(registry.get_enabled_rules()) == 0
    
    def test_rule_registration(self):
        """测试规则注册"""
        registry = RuleRegistry()
        
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                return RuleResult(rule_id=self.rule_id, success=True)
        
        rule = TestRule(rule_id="TEST001", name="测试规则", description="测试")
        registry.register(rule)
        
        assert len(registry.get_all_rules()) == 1
        assert registry.get_rule("TEST001") == rule
    
    def test_duplicate_registration(self):
        """测试重复注册"""
        registry = RuleRegistry()
        
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                return RuleResult(rule_id=self.rule_id, success=True)
        
        rule1 = TestRule(rule_id="TEST001", name="规则1", description="测试")
        rule2 = TestRule(rule_id="TEST001", name="规则2", description="测试")
        
        registry.register(rule1)
        
        with pytest.raises(ValueError):
            registry.register(rule2)
    
    def test_rule_unregistration(self):
        """测试规则注销"""
        registry = RuleRegistry()
        
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                return RuleResult(rule_id=self.rule_id, success=True)
        
        rule = TestRule(rule_id="TEST001", name="测试规则", description="测试")
        registry.register(rule)
        
        assert registry.unregister("TEST001") is True
        assert len(registry.get_all_rules()) == 0
        assert registry.unregister("TEST001") is False
    
    def test_dependency_resolution(self):
        """测试依赖解析"""
        registry = RuleRegistry()
        
        class RuleA(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                return RuleResult(rule_id=self.rule_id, success=True)
        
        class RuleB(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                return RuleResult(rule_id=self.rule_id, success=True)
            
            def get_dependencies(self) -> list:
                return ["RULE_A"]
        
        rule_a = RuleA(rule_id="RULE_A", name="规则A", description="测试")
        rule_b = RuleB(rule_id="RULE_B", name="规则B", description="测试")
        
        registry.register(rule_a)
        registry.register(rule_b)
        
        order = registry.resolve_execution_order(["RULE_A", "RULE_B"])
        assert order == ["RULE_A", "RULE_B"]


class TestRuleExecutor:
    """测试规则执行器"""
    
    def test_executor_creation(self):
        """测试执行器创建"""
        registry = RuleRegistry()
        executor = RuleExecutor(registry)
        
        assert executor.registry == registry
    
    def test_single_rule_execution(self):
        """测试单个规则执行"""
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                result = RuleResult(rule_id=self.rule_id, success=True)
                result.add_info("测试信息", context.get_path_string())
                return result
        
        rule = TestRule(rule_id="TEST001", name="测试规则", description="测试")
        executor = RuleExecutor()
        context = RuleContext(data="test_data")
        
        result = executor.execute_rule(rule, context)
        
        assert result.rule_id == "TEST001"
        assert result.success is True
        assert len(result.get_infos()) == 1
        assert result.execution_time > 0
    
    def test_rule_exception_handling(self):
        """测试规则异常处理"""
        class FailingRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                raise Exception("规则执行失败")
        
        rule = FailingRule(rule_id="FAIL001", name="失败规则", description="测试")
        executor = RuleExecutor()
        context = RuleContext(data="test_data")
        
        result = executor.execute_rule(rule, context)
        
        assert result.rule_id == "FAIL001"
        assert result.success is False
        assert len(result.get_errors()) == 1
        assert "规则执行异常" in result.get_errors()[0].message


class TestRuleEngine:
    """测试规则引擎"""
    
    def test_engine_creation(self):
        """测试引擎创建"""
        engine = RuleEngine()
        
        assert engine.registry is not None
        assert engine.executor is not None
    
    def test_data_validation(self):
        """测试数据验证"""
        # 创建测试规则
        class TestRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                result = RuleResult(rule_id=self.rule_id, success=True)
                if isinstance(context.data, str) and "error" in context.data:
                    result.add_error("发现错误数据", context.get_path_string())
                return result
            
            def is_applicable(self, context: RuleContext) -> bool:
                return isinstance(context.data, str)
        
        # 创建引擎并注册规则
        engine = RuleEngine()
        rule = TestRule(rule_id="TEST001", name="测试规则", description="测试")
        engine.registry.register(rule)
        
        # 测试正常数据
        result = engine.validate("normal_data")
        assert len(result.get_errors()) == 0
        
        # 测试错误数据
        result = engine.validate("error_data")
        assert len(result.get_errors()) == 1


class TestReportGenerator:
    """测试报告生成器"""
    
    def test_generator_creation(self):
        """测试生成器创建"""
        generator = ReportGenerator()
        
        assert generator.templates is not None
        assert 'html' in generator.templates
        assert 'markdown' in generator.templates
    
    def test_report_generation(self):
        """测试报告生成"""
        from src.core.rules.engine import ExecutionResult
        
        # 创建模拟执行结果
        execution_result = ExecutionResult()
        execution_result.executed_rules = 5
        execution_result.skipped_rules = 1
        execution_result.failed_rules = 2
        execution_result.total_time = 1.5
        
        # 添加一些模拟问题
        rule_result = RuleResult(rule_id="TEST001", success=False)
        rule_result.add_error("测试错误", "test.path")
        rule_result.add_warning("测试警告", "test.path")
        execution_result.rule_results["TEST001"] = rule_result
        
        generator = ReportGenerator()
        report = generator.generate_report(execution_result)
        
        assert report.title == "IEC61850设计验证报告"
        assert report.execution_result == execution_result
        assert len(report.sections) > 0
        assert report.statistics is not None
        assert report.metadata['total_issues'] == 2
    
    def test_report_serialization(self):
        """测试报告序列化"""
        report = ValidationReport(title="测试报告")
        
        # 测试转换为字典
        report_dict = report.to_dict()
        assert report_dict['title'] == "测试报告"
        assert 'generated_at' in report_dict
        
        # 测试转换为JSON
        json_str = report.to_json()
        assert '"title": "测试报告"' in json_str


class TestIntegration:
    """集成测试"""
    
    def test_full_validation_workflow(self):
        """测试完整验证工作流"""
        # 创建测试数据
        ied = IED(name="TestIED", manufacturer="TestManufacturer")
        
        # 创建测试规则
        class IEDRule(BaseRule):
            def validate(self, context: RuleContext) -> RuleResult:
                result = RuleResult(rule_id=self.rule_id, success=True)
                ied = context.data
                
                if not ied.manufacturer:
                    result.add_error("IED缺少制造商信息", context.get_path_string())
                
                return result
            
            def is_applicable(self, context: RuleContext) -> bool:
                return isinstance(context.data, IED)
        
        # 创建引擎并执行验证
        engine = RuleEngine()
        rule = IEDRule(rule_id="IED001", name="IED基本检查", description="检查IED基本信息")
        engine.registry.register(rule)
        
        # 执行验证
        execution_result = engine.validate(ied)
        
        # 生成报告
        generator = ReportGenerator()
        report = generator.generate_report(execution_result)
        
        # 验证结果
        assert execution_result.executed_rules == 1
        assert len(execution_result.get_errors()) == 0  # 有制造商信息，不应该有错误
        assert report.title == "IEC61850设计验证报告"
        assert len(report.sections) > 0


if __name__ == "__main__":
    pytest.main([__file__])
