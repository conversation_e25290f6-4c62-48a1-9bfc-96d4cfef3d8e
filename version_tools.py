#!/usr/bin/env python3
"""
IEC61850设计检查器版本管理工具
提供版本控制、依赖管理、兼容性检查等功能
"""

import json
import re
import subprocess
import sys
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import yaml
import semver


@dataclass
class VersionInfo:
    """版本信息数据类"""
    major: int
    minor: int
    patch: int
    prerelease: Optional[str] = None
    build: Optional[str] = None
    
    def __str__(self) -> str:
        version = f"{self.major}.{self.minor}.{self.patch}"
        if self.prerelease:
            version += f"-{self.prerelease}"
        if self.build:
            version += f"+{self.build}"
        return version
    
    @classmethod
    def from_string(cls, version_str: str) -> 'VersionInfo':
        """从版本字符串创建VersionInfo对象"""
        parsed = semver.VersionInfo.parse(version_str)
        return cls(
            major=parsed.major,
            minor=parsed.minor,
            patch=parsed.patch,
            prerelease=parsed.prerelease,
            build=parsed.build
        )


@dataclass
class ModuleInfo:
    """模块信息数据类"""
    name: str
    version: VersionInfo
    dependencies: Dict[str, str]
    api_version: str
    min_python_version: str
    compatible_versions: List[str]
    deprecated_versions: List[str]


class VersionManager:
    """版本管理器主类"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.version_config_file = project_root / "version_config.yml"
        self.load_config()
    
    def load_config(self):
        """加载版本配置"""
        if self.version_config_file.exists():
            with open(self.version_config_file, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            self.config = self.create_default_config()
            self.save_config()
    
    def create_default_config(self) -> dict:
        """创建默认版本配置"""
        return {
            'project': {
                'name': 'iec61850-design-checker',
                'version': '0.1.0',
                'description': 'IEC61850设计检查器',
                'author': 'Development Team',
                'license': 'MIT'
            },
            'modules': {},
            'dependencies': {},
            'compatibility_matrix': {},
            'release_channels': {
                'stable': {'auto_deploy': False, 'approval_required': True},
                'beta': {'auto_deploy': True, 'approval_required': False},
                'alpha': {'auto_deploy': True, 'approval_required': False}
            }
        }
    
    def save_config(self):
        """保存版本配置"""
        with open(self.version_config_file, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
    
    def get_current_version(self) -> VersionInfo:
        """获取当前项目版本"""
        version_str = self.config['project']['version']
        return VersionInfo.from_string(version_str)
    
    def bump_version(self, bump_type: str) -> VersionInfo:
        """版本号递增"""
        current = self.get_current_version()
        
        if bump_type == 'major':
            new_version = VersionInfo(current.major + 1, 0, 0)
        elif bump_type == 'minor':
            new_version = VersionInfo(current.major, current.minor + 1, 0)
        elif bump_type == 'patch':
            new_version = VersionInfo(current.major, current.minor, current.patch + 1)
        else:
            raise ValueError(f"无效的版本递增类型: {bump_type}")
        
        self.config['project']['version'] = str(new_version)
        self.save_config()
        return new_version
    
    def register_module(self, module_info: ModuleInfo):
        """注册模块版本信息"""
        self.config['modules'][module_info.name] = {
            'version': str(module_info.version),
            'dependencies': module_info.dependencies,
            'api_version': module_info.api_version,
            'min_python_version': module_info.min_python_version,
            'compatible_versions': module_info.compatible_versions,
            'deprecated_versions': module_info.deprecated_versions
        }
        self.save_config()
    
    def check_module_compatibility(self, module_name: str, target_version: str) -> bool:
        """检查模块版本兼容性"""
        if module_name not in self.config['modules']:
            return False
        
        module_config = self.config['modules'][module_name]
        compatible_versions = module_config.get('compatible_versions', [])
        
        return target_version in compatible_versions
    
    def get_dependency_tree(self) -> Dict:
        """获取依赖关系树"""
        dependency_tree = {}
        
        for module_name, module_config in self.config['modules'].items():
            dependencies = module_config.get('dependencies', {})
            dependency_tree[module_name] = dependencies
        
        return dependency_tree
    
    def validate_dependencies(self) -> List[str]:
        """验证依赖关系的一致性"""
        issues = []
        dependency_tree = self.get_dependency_tree()
        
        for module_name, dependencies in dependency_tree.items():
            for dep_name, dep_version_spec in dependencies.items():
                if dep_name in self.config['modules']:
                    actual_version = self.config['modules'][dep_name]['version']
                    if not self._version_satisfies_spec(actual_version, dep_version_spec):
                        issues.append(
                            f"模块 {module_name} 依赖 {dep_name} {dep_version_spec}, "
                            f"但实际版本为 {actual_version}"
                        )
        
        return issues
    
    def _version_satisfies_spec(self, version: str, spec: str) -> bool:
        """检查版本是否满足规范"""
        try:
            return semver.match(version, spec)
        except ValueError:
            return False


class DependencyManager:
    """依赖管理器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.requirements_file = project_root / "requirements.txt"
        self.lock_file = project_root / "requirements.lock"
    
    def lock_dependencies(self):
        """锁定依赖版本"""
        try:
            # 使用pip-tools生成精确版本锁定文件
            subprocess.run([
                sys.executable, "-m", "piptools", "compile",
                "--output-file", str(self.lock_file),
                str(self.requirements_file)
            ], check=True)
            print(f"依赖版本已锁定到 {self.lock_file}")
        except subprocess.CalledProcessError as e:
            print(f"依赖锁定失败: {e}")
    
    def check_security_vulnerabilities(self) -> List[str]:
        """检查安全漏洞"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "safety", "check",
                "--json"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return []
            else:
                vulnerabilities = json.loads(result.stdout)
                return [vuln['advisory'] for vuln in vulnerabilities]
        except Exception as e:
            print(f"安全检查失败: {e}")
            return []
    
    def update_dependencies(self, security_only: bool = False):
        """更新依赖包"""
        if security_only:
            # 只更新有安全漏洞的包
            vulnerabilities = self.check_security_vulnerabilities()
            if vulnerabilities:
                print("发现安全漏洞，正在更新相关包...")
                # 实现安全更新逻辑
            else:
                print("未发现安全漏洞")
        else:
            # 更新所有包到最新兼容版本
            print("更新所有依赖包...")
            # 实现全量更新逻辑


class ReleaseManager:
    """发布管理器"""
    
    def __init__(self, version_manager: VersionManager):
        self.version_manager = version_manager
        self.project_root = version_manager.project_root
    
    def create_release(self, version: str, channel: str = 'stable') -> bool:
        """创建发布版本"""
        try:
            # 1. 验证版本号
            version_info = VersionInfo.from_string(version)
            
            # 2. 运行测试
            if not self._run_tests():
                print("测试失败，无法创建发布版本")
                return False
            
            # 3. 创建Git标签
            self._create_git_tag(version)
            
            # 4. 构建发布包
            self._build_release_packages(version)
            
            # 5. 更新版本配置
            self.version_manager.config['project']['version'] = version
            self.version_manager.save_config()
            
            # 6. 生成发布说明
            self._generate_release_notes(version)
            
            print(f"发布版本 {version} 创建成功")
            return True
            
        except Exception as e:
            print(f"创建发布版本失败: {e}")
            return False
    
    def _run_tests(self) -> bool:
        """运行测试套件"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "pytest",
                "tests/", "--cov=src/", "--cov-report=term-missing"
            ], cwd=self.project_root)
            return result.returncode == 0
        except Exception:
            return False
    
    def _create_git_tag(self, version: str):
        """创建Git标签"""
        tag_name = f"v{version}"
        subprocess.run([
            "git", "tag", "-a", tag_name,
            "-m", f"Release version {version}"
        ], cwd=self.project_root, check=True)
    
    def _build_release_packages(self, version: str):
        """构建发布包"""
        # 实现构建逻辑
        print(f"构建版本 {version} 的发布包...")
    
    def _generate_release_notes(self, version: str):
        """生成发布说明"""
        # 实现发布说明生成逻辑
        print(f"生成版本 {version} 的发布说明...")


def main():
    """主函数 - 命令行接口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='IEC61850设计检查器版本管理工具')
    parser.add_argument('--project-root', type=Path, default=Path.cwd(),
                       help='项目根目录路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 版本管理命令
    version_parser = subparsers.add_parser('version', help='版本管理')
    version_parser.add_argument('action', choices=['show', 'bump'],
                               help='版本操作')
    version_parser.add_argument('--type', choices=['major', 'minor', 'patch'],
                               help='版本递增类型')
    
    # 依赖管理命令
    deps_parser = subparsers.add_parser('deps', help='依赖管理')
    deps_parser.add_argument('action', choices=['lock', 'check', 'update'],
                            help='依赖操作')
    deps_parser.add_argument('--security-only', action='store_true',
                            help='仅安全更新')
    
    # 发布管理命令
    release_parser = subparsers.add_parser('release', help='发布管理')
    release_parser.add_argument('version', help='发布版本号')
    release_parser.add_argument('--channel', default='stable',
                               choices=['stable', 'beta', 'alpha'],
                               help='发布渠道')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 初始化管理器
    version_manager = VersionManager(args.project_root)
    
    if args.command == 'version':
        if args.action == 'show':
            current = version_manager.get_current_version()
            print(f"当前版本: {current}")
        elif args.action == 'bump':
            if not args.type:
                print("错误: 需要指定版本递增类型 (--type)")
                return
            new_version = version_manager.bump_version(args.type)
            print(f"版本已更新到: {new_version}")
    
    elif args.command == 'deps':
        dep_manager = DependencyManager(args.project_root)
        if args.action == 'lock':
            dep_manager.lock_dependencies()
        elif args.action == 'check':
            vulnerabilities = dep_manager.check_security_vulnerabilities()
            if vulnerabilities:
                print("发现安全漏洞:")
                for vuln in vulnerabilities:
                    print(f"  - {vuln}")
            else:
                print("未发现安全漏洞")
        elif args.action == 'update':
            dep_manager.update_dependencies(args.security_only)
    
    elif args.command == 'release':
        release_manager = ReleaseManager(version_manager)
        release_manager.create_release(args.version, args.channel)


if __name__ == '__main__':
    main()
