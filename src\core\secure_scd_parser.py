"""
安全的SCD文件解析器
重构版本 - 精简、安全、高效
"""

import xml.etree.ElementTree as ET
import logging
import re
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
from xml.parsers.expat import ExpatError

# 配置安全的XML解析器
try:
    from defusedxml.ElementTree import parse as safe_parse
    SAFE_XML_AVAILABLE = True
except ImportError:
    SAFE_XML_AVAILABLE = False
    logging.warning("defusedxml not available, using standard XML parser (less secure)")


@dataclass
class ParseResult:
    """解析结果"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    file_info: Dict[str, Any] = field(default_factory=dict)


class SecureSCDParser:
    """安全的SCD解析器"""
    
    # 安全配置
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'.scd', '.xml', '.icd', '.cid'}
    DANGEROUS_ELEMENTS = {'script', 'object', 'embed', 'iframe'}
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化解析器"""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        # 安全配置
        self.max_file_size = self.config.get('max_file_size', self.MAX_FILE_SIZE)
        self.strict_mode = self.config.get('strict_mode', True)
        self.validate_schema = self.config.get('validate_schema', True)
    
    def _setup_logging(self) -> None:
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def parse_file(self, file_path: Union[str, Path]) -> ParseResult:
        """
        安全解析SCD文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            ParseResult: 解析结果
        """
        try:
            file_path = Path(file_path).resolve()  # 解析绝对路径，防止路径遍历
            
            # 安全检查
            security_check = self._security_check(file_path)
            if not security_check.success:
                return security_check
            
            # 解析XML
            if SAFE_XML_AVAILABLE:
                tree = safe_parse(file_path)
            else:
                # 使用标准解析器但禁用外部实体
                parser = ET.XMLParser()
                parser.entity = {}  # 禁用实体解析
                tree = ET.parse(file_path, parser)
            
            root = tree.getroot()
            
            # 验证根元素
            if not self._validate_root_element(root):
                return ParseResult(
                    success=False,
                    errors=["无效的SCD文件格式"]
                )
            
            # 提取数据
            data = self._extract_data(root)
            
            # 文件信息
            file_info = {
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'root_tag': root.tag,
                'namespace': self._extract_namespace(root)
            }
            
            self.logger.info(f"成功解析SCD文件: {file_path}")
            
            return ParseResult(
                success=True,
                data=data,
                file_info=file_info
            )
            
        except (ET.ParseError, ExpatError) as e:
            error_msg = f"XML解析错误: {str(e)}"
            self.logger.error(error_msg)
            return ParseResult(success=False, errors=[error_msg])
            
        except PermissionError:
            error_msg = "文件访问权限不足"
            self.logger.error(error_msg)
            return ParseResult(success=False, errors=[error_msg])
            
        except Exception as e:
            error_msg = f"解析过程发生未知错误: {str(e)}"
            self.logger.error(error_msg)
            return ParseResult(success=False, errors=[error_msg])
    
    def _security_check(self, file_path: Path) -> ParseResult:
        """安全检查"""
        errors = []
        
        # 检查文件是否存在
        if not file_path.exists():
            errors.append(f"文件不存在: {file_path}")
        
        # 检查文件扩展名
        if file_path.suffix.lower() not in self.ALLOWED_EXTENSIONS:
            errors.append(f"不支持的文件类型: {file_path.suffix}")
        
        # 检查文件大小
        try:
            if file_path.stat().st_size > self.max_file_size:
                errors.append(f"文件过大: {file_path.stat().st_size} bytes")
        except OSError:
            errors.append("无法获取文件信息")
        
        # 检查路径安全性（防止路径遍历）
        try:
            file_path.resolve().relative_to(Path.cwd())
        except ValueError:
            if self.strict_mode:
                errors.append("文件路径不安全")
        
        if errors:
            return ParseResult(success=False, errors=errors)
        
        return ParseResult(success=True)
    
    def _validate_root_element(self, root: ET.Element) -> bool:
        """验证根元素"""
        # 检查根元素标签（支持命名空间）
        if not (root.tag == 'SCL' or root.tag.endswith('}SCL')):
            return False
        
        # 检查是否包含危险元素
        for elem in root.iter():
            if elem.tag.lower() in self.DANGEROUS_ELEMENTS:
                self.logger.warning(f"发现潜在危险元素: {elem.tag}")
                if self.strict_mode:
                    return False
        
        return True
    
    def _extract_namespace(self, root: ET.Element) -> Optional[str]:
        """提取命名空间"""
        if root.tag.startswith('{'):
            return root.tag[1:root.tag.index('}')]
        return None
    
    def _extract_data(self, root: ET.Element) -> Dict[str, Any]:
        """提取SCD数据"""
        data = {
            'header': self._extract_header(root),
            'substation': self._extract_substation(root),
            'ieds': self._extract_ieds(root),
            'communication': self._extract_communication(root),
            'data_type_templates': self._extract_data_type_templates(root)
        }
        
        return data
    
    def _extract_header(self, root: ET.Element) -> Dict[str, Any]:
        """提取Header信息"""
        header_elem = root.find('.//*[local-name()="Header"]')
        if header_elem is None:
            return {}
        
        header = {}
        
        # 安全提取属性
        safe_attrs = ['id', 'version', 'revision', 'toolID', 'nameStructure']
        for attr in safe_attrs:
            value = header_elem.get(attr)
            if value:
                # 清理潜在的恶意内容
                header[attr] = self._sanitize_string(value)
        
        # 提取Text元素
        text_elem = header_elem.find('.//*[local-name()="Text"]')
        if text_elem is not None and text_elem.text:
            header['text'] = self._sanitize_string(text_elem.text)
        
        return header
    
    def _extract_substation(self, root: ET.Element) -> Dict[str, Any]:
        """提取Substation信息"""
        substation_elem = root.find('.//*[local-name()="Substation"]')
        if substation_elem is None:
            return {}
        
        substation = {}
        safe_attrs = ['name', 'desc']
        for attr in safe_attrs:
            value = substation_elem.get(attr)
            if value:
                substation[attr] = self._sanitize_string(value)
        
        return substation
    
    def _extract_ieds(self, root: ET.Element) -> List[Dict[str, Any]]:
        """提取IED信息"""
        ieds = []
        
        for ied_elem in root.findall('.//*[local-name()="IED"]'):
            ied = {}
            
            # 安全提取IED属性
            safe_attrs = ['name', 'type', 'manufacturer', 'configVersion', 'desc']
            for attr in safe_attrs:
                value = ied_elem.get(attr)
                if value:
                    ied[attr] = self._sanitize_string(value)
            
            # 提取AccessPoint
            access_points = []
            for ap_elem in ied_elem.findall('.//*[local-name()="AccessPoint"]'):
                ap = {'name': self._sanitize_string(ap_elem.get('name', ''))}
                access_points.append(ap)
            
            if access_points:
                ied['access_points'] = access_points
            
            ieds.append(ied)
        
        return ieds
    
    def _extract_communication(self, root: ET.Element) -> Dict[str, Any]:
        """提取Communication信息"""
        comm_elem = root.find('.//*[local-name()="Communication"]')
        if comm_elem is None:
            return {}
        
        communication = {}
        
        # 提取SubNetwork
        subnets = []
        for subnet_elem in comm_elem.findall('.//*[local-name()="SubNetwork"]'):
            subnet = {
                'name': self._sanitize_string(subnet_elem.get('name', '')),
                'type': self._sanitize_string(subnet_elem.get('type', ''))
            }
            subnets.append(subnet)
        
        if subnets:
            communication['subnets'] = subnets
        
        return communication
    
    def _extract_data_type_templates(self, root: ET.Element) -> Dict[str, Any]:
        """提取DataTypeTemplates信息"""
        dtt_elem = root.find('.//*[local-name()="DataTypeTemplates"]')
        if dtt_elem is None:
            return {}
        
        templates = {}
        
        # 统计各种类型的数量
        lnode_types = len(dtt_elem.findall('.//*[local-name()="LNodeType"]'))
        do_types = len(dtt_elem.findall('.//*[local-name()="DOType"]'))
        da_types = len(dtt_elem.findall('.//*[local-name()="DAType"]'))
        enum_types = len(dtt_elem.findall('.//*[local-name()="EnumType"]'))
        
        templates['statistics'] = {
            'lnode_types': lnode_types,
            'do_types': do_types,
            'da_types': da_types,
            'enum_types': enum_types
        }
        
        return templates
    
    def _sanitize_string(self, value: str, max_length: int = 1000) -> str:
        """清理字符串，防止注入攻击"""
        if not isinstance(value, str):
            return str(value)
        
        # 限制长度
        if len(value) > max_length:
            value = value[:max_length]
        
        # 移除潜在危险字符
        # 保留基本的XML字符，移除脚本相关内容
        dangerous_patterns = [
            r'<script.*?</script>',
            r'javascript:',
            r'vbscript:',
            r'on\w+\s*=',
            r'<\s*iframe',
            r'<\s*object',
            r'<\s*embed'
        ]
        
        for pattern in dangerous_patterns:
            value = re.sub(pattern, '', value, flags=re.IGNORECASE | re.DOTALL)
        
        return value.strip()


def main():
    """主函数 - 演示安全解析器"""
    print("安全SCD解析器演示")
    print("=" * 50)
    
    parser = SecureSCDParser({
        'strict_mode': True,
        'max_file_size': 10 * 1024 * 1024  # 10MB
    })
    
    # 测试解析
    test_file = "test_data/test_correct.scd"
    if Path(test_file).exists():
        result = parser.parse_file(test_file)
        
        if result.success:
            print(f"✅ 解析成功")
            print(f"   文件大小: {result.file_info.get('file_size', 0)} bytes")
            print(f"   根元素: {result.file_info.get('root_tag', 'unknown')}")
            print(f"   IED数量: {len(result.data.get('ieds', []))}")
        else:
            print(f"❌ 解析失败: {result.errors}")
    else:
        print(f"⚠️ 测试文件不存在: {test_file}")
    
    print("\n演示完成")


if __name__ == "__main__":
    main()