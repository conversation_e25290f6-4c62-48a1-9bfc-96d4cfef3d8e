"""
深度技术规程学习引擎
专注于设计图纸审查中的规程规范系统化掌握
基于GB/T 14285、DL/T 5136等标准的深度理解和应用
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class RegulationLevel(Enum):
    """规程等级"""
    NATIONAL_STANDARD = "国家标准"
    INDUSTRY_STANDARD = "行业标准"
    ENTERPRISE_STANDARD = "企业标准"
    TECHNICAL_SPECIFICATION = "技术规范"


class ComplianceLevel(Enum):
    """合规等级"""
    MANDATORY = "强制性要求"
    RECOMMENDED = "推荐性要求"
    OPTIONAL = "可选性要求"


@dataclass
class RegulationRule:
    """规程规则"""
    rule_id: str
    standard_name: str
    section: str
    content: str
    compliance_level: ComplianceLevel
    application_scope: str
    technical_requirements: Dict[str, Any]
    verification_method: str
    common_violations: List[str]


class DeepRegulationEngine:
    """深度技术规程学习引擎"""
    
    def __init__(self):
        """初始化深度规程引擎"""
        self.regulation_knowledge = self._build_regulation_knowledge()
        self.design_review_rules = self._build_design_review_rules()
        self.compliance_patterns = self._build_compliance_patterns()
        self.violation_database = self._build_violation_database()
        
        logger.info("深度技术规程学习引擎初始化完成")
    
    def _build_regulation_knowledge(self) -> Dict[str, Any]:
        """构建规程规范深度知识"""
        return {
            # GB/T 14285-2023 继电保护和安全自动装置技术规程
            'GB14285_2023': {
                'standard_info': {
                    'full_name': 'GB/T 14285-2023 继电保护和安全自动装置技术规程',
                    'effective_date': '2023-10-01',
                    'scope': '电力系统继电保护和安全自动装置',
                    'authority': '国家标准化管理委员会'
                },
                'core_principles': {
                    'protection_four_characteristics': {
                        'selectivity': {
                            'definition': '保护装置应仅切除故障元件，不影响系统正常运行部分',
                            'technical_requirements': {
                                'protection_zone': '保护范围应明确界定',
                                'coordination': '与相邻保护装置配合',
                                'backup_selectivity': '后备保护选择性'
                            },
                            'design_requirements': {
                                'zone_setting': '保护区间设置应避免死区',
                                'time_coordination': '时间配合应满足选择性要求',
                                'current_coordination': '电流配合应有足够裕度'
                            },
                            'verification_methods': [
                                '短路计算验证',
                                '保护配合分析',
                                '仿真试验验证'
                            ]
                        },
                        'speed': {
                            'definition': '保护装置应尽可能快速切除故障',
                            'time_requirements': {
                                'main_protection': {
                                    'differential_protection': '<50ms',
                                    'distance_protection': '<100ms',
                                    'pilot_protection': '<100ms'
                                },
                                'backup_protection': {
                                    'overcurrent_protection': '<500ms',
                                    'distance_backup': '<1000ms'
                                }
                            },
                            'design_considerations': {
                                'algorithm_optimization': '保护算法应优化计算速度',
                                'hardware_performance': '硬件性能应满足速度要求',
                                'communication_delay': '通信延时应在可控范围'
                            }
                        },
                        'sensitivity': {
                            'definition': '保护装置应能检测保护范围内的最小故障',
                            'sensitivity_requirements': {
                                'current_protection': {
                                    'sensitivity_coefficient': 'Ksen ≥ 1.3',
                                    'minimum_fault_current': '应能检测最小故障电流',
                                    'load_margin': '应与最大负荷电流有足够裕度'
                                },
                                'distance_protection': {
                                    'impedance_sensitivity': '阻抗测量精度要求',
                                    'fault_resistance': '应考虑故障电阻影响',
                                    'system_impedance': '应适应系统阻抗变化'
                                }
                            }
                        },
                        'reliability': {
                            'definition': '保护装置应可靠动作和可靠不动作',
                            'reliability_requirements': {
                                'availability': '可用率 ≥ 99.9%',
                                'false_operation_rate': '误动率 ≤ 0.1%',
                                'failure_to_operate_rate': '拒动率 ≤ 0.1%'
                            },
                            'design_measures': {
                                'redundancy': '重要保护双重化配置',
                                'self_monitoring': '保护装置自检功能',
                                'fault_tolerance': '容错设计和故障隔离'
                            }
                        }
                    }
                },
                'specific_protection_requirements': {
                    'transformer_protection': {
                        'differential_protection': {
                            'application_conditions': {
                                'capacity_threshold': '容量 ≥ 6.3MVA',
                                'voltage_threshold': '电压 ≥ 35kV',
                                'importance_level': '重要变压器必须配置'
                            },
                            'technical_requirements': {
                                'restraint_characteristic': '比率制动特性',
                                'harmonic_restraint': '二次谐波制动 ≥ 15%',
                                'zero_sequence_filtering': '零序电流滤除',
                                'ct_saturation_handling': 'CT饱和处理措施'
                            },
                            'design_specifications': {
                                'ct_configuration': 'CT配置和变比选择',
                                'protection_zone': '保护范围界定',
                                'setting_calculation': '定值计算方法',
                                'testing_requirements': '试验验证要求'
                            }
                        },
                        'gas_protection': {
                            'application_scope': '油浸式变压器',
                            'protection_functions': {
                                'light_gas': '轻瓦斯告警',
                                'heavy_gas': '重瓦斯跳闸',
                                'sudden_pressure': '突发压力保护'
                            },
                            'installation_requirements': {
                                'relay_position': '继电器安装位置',
                                'pipeline_slope': '连接管道坡度',
                                'sealing_requirements': '密封性要求'
                            }
                        }
                    },
                    'line_protection': {
                        'distance_protection': {
                            'application_conditions': {
                                'voltage_level': '35kV及以上线路',
                                'line_length': '线路长度 > 15km',
                                'system_importance': '重要输电线路'
                            },
                            'zone_settings': {
                                'zone_1': {
                                    'reach': '线路全长的80-85%',
                                    'time_delay': '瞬时动作(0s)',
                                    'function': '快速切除本线路故障'
                                },
                                'zone_2': {
                                    'reach': '线路全长 + 相邻线路50%',
                                    'time_delay': '0.3-0.5s',
                                    'function': '本线路后备保护'
                                },
                                'zone_3': {
                                    'reach': '相邻线路全长 + 下级线路50%',
                                    'time_delay': '1.0-1.5s',
                                    'function': '远后备保护'
                                }
                            }
                        },
                        'pilot_protection': {
                            'application_conditions': {
                                'line_importance': '重要输电线路',
                                'system_stability': '系统稳定性要求',
                                'fault_clearing_time': '快速切除要求'
                            },
                            'communication_requirements': {
                                'channel_reliability': '通道可靠性 ≥ 99.9%',
                                'transmission_delay': '传输延时 ≤ 10ms',
                                'channel_monitoring': '通道监视功能'
                            }
                        }
                    }
                }
            },
            
            # DL/T 5136-2012 火力发电厂、变电站二次接线设计技术规程
            'DL5136_2012': {
                'standard_info': {
                    'full_name': 'DL/T 5136-2012 火力发电厂、变电站二次接线设计技术规程',
                    'effective_date': '2012-06-01',
                    'scope': '火力发电厂和变电站二次接线设计',
                    'authority': '国家能源局'
                },
                'design_principles': {
                    'safety_reliability': {
                        'principle': '安全可靠是二次接线设计的首要原则',
                        'implementation_measures': {
                            'redundancy_design': '重要回路冗余配置',
                            'fault_isolation': '故障隔离和快速恢复',
                            'single_failure_criterion': '单一故障不导致系统失效',
                            'maintenance_safety': '维护作业安全保障'
                        }
                    },
                    'technical_advancement': {
                        'principle': '采用技术先进、成熟可靠的设备和方案',
                        'technology_selection': {
                            'equipment_standardization': '设备标准化和系列化',
                            'digital_technology': '数字化技术应用',
                            'communication_protocol': '标准通信协议',
                            'intelligent_functions': '智能化功能配置'
                        }
                    },
                    'economic_rationality': {
                        'principle': '在满足技术要求前提下经济合理',
                        'cost_optimization': {
                            'lifecycle_cost': '全生命周期成本考虑',
                            'investment_efficiency': '投资效益分析',
                            'maintenance_cost': '运维成本控制',
                            'upgrade_flexibility': '升级改造灵活性'
                        }
                    }
                },
                'circuit_design_requirements': {
                    'control_circuits': {
                        'power_supply_design': {
                            'voltage_levels': {
                                'primary_voltage': 'DC 220V - 主操作电源',
                                'secondary_voltage': 'DC 110V - 辅助操作电源',
                                'control_voltage': 'DC 48V/24V - 控制电源'
                            },
                            'redundancy_requirements': {
                                'dual_power_supply': '重要控制回路双电源',
                                'automatic_switching': '电源自动切换',
                                'battery_backup': '蓄电池后备电源',
                                'capacity_design': '容量按1.3倍负荷设计'
                            }
                        },
                        'circuit_configuration': {
                            'breaker_control': {
                                'trip_circuit': '跳闸回路设计要求',
                                'close_circuit': '合闸回路设计要求',
                                'anti_pumping': '防跳回路配置',
                                'position_indication': '位置指示回路'
                            },
                            'interlocking_design': {
                                'electrical_interlocking': '电气闭锁',
                                'mechanical_interlocking': '机械闭锁',
                                'program_interlocking': '程序闭锁',
                                'five_prevention': '五防功能实现'
                            }
                        }
                    },
                    'protection_circuits': {
                        'ct_circuit_design': {
                            'selection_principles': {
                                'ratio_selection': 'CT变比选择原则',
                                'accuracy_class': '精度等级要求',
                                'burden_calculation': '二次负荷计算',
                                'saturation_analysis': '饱和特性分析'
                            },
                            'wiring_requirements': {
                                'secondary_grounding': '二次回路接地',
                                'cable_selection': '电缆选择要求',
                                'connection_reliability': '连接可靠性',
                                'test_provisions': '试验接线预留'
                            }
                        },
                        'pt_circuit_design': {
                            'selection_principles': {
                                'ratio_selection': 'PT变比选择',
                                'accuracy_requirements': '精度要求',
                                'burden_capacity': '负荷容量',
                                'insulation_level': '绝缘水平'
                            },
                            'protection_measures': {
                                'fuse_protection': '熔断器保护',
                                'secondary_protection': '二次侧保护',
                                'grounding_design': '接地设计',
                                'monitoring_functions': '监视功能'
                            }
                        }
                    }
                }
            },
            
            # IEC 61850 数字化变电站通信标准
            'IEC61850': {
                'standard_info': {
                    'full_name': 'IEC 61850 变电站通信网络和系统',
                    'scope': '变电站自动化系统通信',
                    'key_features': ['互操作性', '标准化', '面向对象']
                },
                'communication_services': {
                    'goose_messaging': {
                        'function': '快速信息交换',
                        'performance_requirements': {
                            'transmission_time': '≤ 4ms',
                            'reliability': '99.99%',
                            'message_priority': '最高优先级'
                        },
                        'applications': {
                            'protection_tripping': '保护跳闸信号',
                            'interlocking_signals': '闭锁信号传输',
                            'status_information': '设备状态信息',
                            'alarm_signals': '告警信号传递'
                        }
                    },
                    'smv_sampling': {
                        'function': '采样值传输',
                        'technical_specifications': {
                            'sampling_rate': '4000Hz/12800Hz',
                            'synchronization': 'IEEE 1588时钟同步',
                            'accuracy': '±1μs同步精度'
                        },
                        'applications': {
                            'electronic_transformers': '电子式互感器',
                            'protection_sampling': '保护装置采样',
                            'measurement_systems': '测量系统数据'
                        }
                    }
                }
            }
        }
    
    def _build_design_review_rules(self) -> Dict[str, Any]:
        """构建设计审查规则"""
        return {
            'protection_system_review': {
                'configuration_review': {
                    'protection_completeness': {
                        'rule_description': '保护配置完整性检查',
                        'check_items': [
                            '主保护配置是否完整',
                            '后备保护配置是否充分',
                            '辅助保护配置是否必要',
                            '保护范围是否覆盖全部设备'
                        ],
                        'gb14285_requirements': {
                            'main_protection': '每个保护对象应配置主保护',
                            'backup_protection': '应配置近后备和远后备保护',
                            'protection_overlap': '保护范围应有适当重叠'
                        },
                        'common_violations': [
                            '缺少必要的主保护',
                            '后备保护配置不足',
                            '保护范围存在死区',
                            '保护配置过于复杂'
                        ]
                    },
                    'protection_coordination': {
                        'rule_description': '保护配合关系检查',
                        'coordination_types': {
                            'time_coordination': {
                                'requirements': '时间级差 ≥ 0.3s',
                                'verification': '通过故障计算验证',
                                'considerations': '考虑断路器动作时间'
                            },
                            'current_coordination': {
                                'requirements': '电流级差系数 ≥ 1.2',
                                'sensitivity': '灵敏系数 ≥ 1.3',
                                'margin': '配合裕度充分'
                            }
                        }
                    }
                },
                'technical_parameter_review': {
                    'ct_parameter_check': {
                        'ratio_verification': {
                            'primary_current': '一次额定电流选择',
                            'secondary_current': '二次额定电流(5A/1A)',
                            'ratio_calculation': '变比计算和验证',
                            'load_analysis': '负荷分析和校核'
                        },
                        'accuracy_verification': {
                            'protection_class': '保护级精度(5P/10P)',
                            'measurement_class': '测量级精度(0.2S/0.5S)',
                            'burden_calculation': '额定负荷计算',
                            'saturation_analysis': '饱和特性分析'
                        }
                    },
                    'pt_parameter_check': {
                        'ratio_verification': {
                            'primary_voltage': '一次额定电压',
                            'secondary_voltage': '二次额定电压(100V)',
                            'ratio_accuracy': '变比精度要求',
                            'burden_capacity': '负荷容量校核'
                        }
                    }
                }
            },
            'control_system_review': {
                'circuit_design_review': {
                    'power_supply_check': {
                        'voltage_level_verification': {
                            'dc_220v_system': 'DC 220V系统配置',
                            'dc_110v_system': 'DC 110V系统配置',
                            'voltage_tolerance': '电压波动范围',
                            'capacity_adequacy': '容量充足性'
                        },
                        'redundancy_verification': {
                            'dual_power_supply': '双电源配置',
                            'automatic_switching': '自动切换功能',
                            'battery_backup': '蓄电池备用',
                            'load_distribution': '负荷分配合理性'
                        }
                    },
                    'interlocking_review': {
                        'five_prevention_check': {
                            'prevention_1': '防误分、误合断路器',
                            'prevention_2': '防带负荷拉合隔离开关',
                            'prevention_3': '防带电挂接地线',
                            'prevention_4': '防带接地线送电',
                            'prevention_5': '防误入带电间隔'
                        },
                        'logic_verification': {
                            'interlocking_completeness': '闭锁逻辑完整性',
                            'condition_accuracy': '闭锁条件准确性',
                            'override_provisions': '解锁措施合理性'
                        }
                    }
                }
            },
            'communication_system_review': {
                'iec61850_compliance': {
                    'protocol_implementation': {
                        'goose_configuration': 'GOOSE配置检查',
                        'smv_configuration': 'SMV配置检查',
                        'mms_services': 'MMS服务配置',
                        'data_modeling': '数据模型一致性'
                    },
                    'performance_verification': {
                        'timing_requirements': '时间性能要求',
                        'reliability_requirements': '可靠性要求',
                        'interoperability': '互操作性验证'
                    }
                }
            }
        }
    
    def _build_compliance_patterns(self) -> Dict[str, Any]:
        """构建合规模式"""
        return {
            'mandatory_compliance': {
                'safety_requirements': [
                    '人身安全相关要求',
                    '设备安全相关要求',
                    '系统安全相关要求'
                ],
                'performance_requirements': [
                    '保护性能要求',
                    '控制性能要求',
                    '通信性能要求'
                ]
            },
            'recommended_practices': {
                'design_optimization': [
                    '设计优化建议',
                    '技术改进建议',
                    '维护便利性建议'
                ]
            }
        }
    
    def _build_violation_database(self) -> Dict[str, Any]:
        """构建违规数据库"""
        return {
            'common_violations': {
                'protection_violations': [
                    {
                        'violation_type': '保护配置不完整',
                        'description': '缺少必要的主保护或后备保护',
                        'severity': 'high',
                        'standard_reference': 'GB/T 14285-2023',
                        'correction_method': '补充完整的保护配置'
                    },
                    {
                        'violation_type': '保护配合不当',
                        'description': '时间级差或电流级差不满足要求',
                        'severity': 'high',
                        'standard_reference': 'GB/T 14285-2023',
                        'correction_method': '重新计算和调整保护定值'
                    }
                ],
                'control_violations': [
                    {
                        'violation_type': '闭锁逻辑缺失',
                        'description': '缺少必要的五防闭锁功能',
                        'severity': 'high',
                        'standard_reference': 'DL/T 5136-2012',
                        'correction_method': '完善闭锁逻辑设计'
                    }
                ]
            }
        }
    
    def analyze_design_compliance(self, design_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析设计合规性"""
        
        compliance_analysis = {
            'protection_compliance': self._analyze_protection_compliance(design_data),
            'control_compliance': self._analyze_control_compliance(design_data),
            'communication_compliance': self._analyze_communication_compliance(design_data),
            'overall_assessment': None
        }
        
        # 综合评估
        compliance_analysis['overall_assessment'] = self._generate_overall_assessment(compliance_analysis)
        
        return compliance_analysis
    
    def _analyze_protection_compliance(self, design_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析保护系统合规性"""
        
        protection_data = design_data.get('protection_system', {})
        compliance_issues = []
        
        # 检查保护配置完整性
        config_check = self._check_protection_configuration(protection_data)
        compliance_issues.extend(config_check)
        
        # 检查保护配合
        coordination_check = self._check_protection_coordination(protection_data)
        compliance_issues.extend(coordination_check)
        
        # 检查技术参数
        parameter_check = self._check_protection_parameters(protection_data)
        compliance_issues.extend(parameter_check)
        
        return {
            'compliance_issues': compliance_issues,
            'compliance_score': self._calculate_compliance_score(compliance_issues),
            'recommendations': self._generate_protection_recommendations(compliance_issues)
        }
    
    def _check_protection_configuration(self, protection_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查保护配置"""
        issues = []
        
        # 检查主保护配置
        main_protections = protection_data.get('main_protections', [])
        if not main_protections:
            issues.append({
                'type': 'configuration_incomplete',
                'severity': 'high',
                'description': '缺少主保护配置',
                'standard_reference': 'GB/T 14285-2023 第4.1条',
                'correction': '配置适当的主保护'
            })
        
        # 检查后备保护配置
        backup_protections = protection_data.get('backup_protections', [])
        if not backup_protections:
            issues.append({
                'type': 'backup_protection_missing',
                'severity': 'medium',
                'description': '缺少后备保护配置',
                'standard_reference': 'GB/T 14285-2023 第4.2条',
                'correction': '配置适当的后备保护'
            })
        
        return issues
    
    def _check_protection_coordination(self, protection_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查保护配合"""
        issues = []
        
        # 检查时间配合
        time_coordination = protection_data.get('time_coordination', {})
        if time_coordination:
            time_difference = time_coordination.get('time_difference', 0)
            if time_difference < 0.3:
                issues.append({
                    'type': 'time_coordination_insufficient',
                    'severity': 'high',
                    'description': f'时间级差不足: {time_difference}s < 0.3s',
                    'standard_reference': 'GB/T 14285-2023 配合要求',
                    'correction': '调整保护定值，确保时间级差≥0.3s'
                })
        
        return issues
    
    def _check_protection_parameters(self, protection_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查保护参数"""
        issues = []
        
        # 检查CT参数
        ct_parameters = protection_data.get('ct_parameters', {})
        if ct_parameters:
            # 检查变比选择
            ratio = ct_parameters.get('ratio')
            if ratio:
                primary_current = ratio.get('primary', 0)
                secondary_current = ratio.get('secondary', 0)
                if secondary_current not in [1, 5]:
                    issues.append({
                        'type': 'ct_ratio_nonstandard',
                        'severity': 'medium',
                        'description': f'CT二次电流非标准值: {secondary_current}A',
                        'standard_reference': 'DL/T 5136-2012 CT选择',
                        'correction': '选择标准的1A或5A二次电流'
                    })
        
        return issues
    
    def _analyze_control_compliance(self, design_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析控制系统合规性"""
        
        control_data = design_data.get('control_system', {})
        compliance_issues = []
        
        # 检查电源系统
        power_check = self._check_power_system(control_data)
        compliance_issues.extend(power_check)
        
        # 检查闭锁系统
        interlocking_check = self._check_interlocking_system(control_data)
        compliance_issues.extend(interlocking_check)
        
        return {
            'compliance_issues': compliance_issues,
            'compliance_score': self._calculate_compliance_score(compliance_issues),
            'recommendations': self._generate_control_recommendations(compliance_issues)
        }
    
    def _check_power_system(self, control_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查电源系统"""
        issues = []
        
        power_system = control_data.get('power_system', {})
        
        # 检查电压等级
        voltage_levels = power_system.get('voltage_levels', [])
        if 'DC_220V' not in voltage_levels:
            issues.append({
                'type': 'primary_voltage_missing',
                'severity': 'high',
                'description': '缺少DC 220V主操作电源',
                'standard_reference': 'DL/T 5136-2012 电源配置',
                'correction': '配置DC 220V操作电源'
            })
        
        # 检查冗余配置
        redundancy = power_system.get('redundancy', False)
        if not redundancy:
            issues.append({
                'type': 'power_redundancy_missing',
                'severity': 'medium',
                'description': '缺少电源冗余配置',
                'standard_reference': 'DL/T 5136-2012 可靠性要求',
                'correction': '配置双电源系统'
            })
        
        return issues
    
    def _check_interlocking_system(self, control_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查闭锁系统"""
        issues = []
        
        interlocking = control_data.get('interlocking_system', {})
        
        # 检查五防功能
        five_prevention = interlocking.get('five_prevention', {})
        required_functions = [
            'prevent_breaker_misoperation',
            'prevent_isolator_load_operation', 
            'prevent_earthing_live_operation',
            'prevent_live_earthing_operation',
            'prevent_live_area_entry'
        ]
        
        for function in required_functions:
            if not five_prevention.get(function, False):
                issues.append({
                    'type': 'five_prevention_incomplete',
                    'severity': 'high',
                    'description': f'缺少五防功能: {function}',
                    'standard_reference': 'DL/T 5136-2012 五防要求',
                    'correction': f'实现{function}功能'
                })
        
        return issues
    
    def _analyze_communication_compliance(self, design_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析通信系统合规性"""
        
        comm_data = design_data.get('communication_system', {})
        compliance_issues = []
        
        # 检查IEC61850合规性
        if comm_data.get('protocol') == 'IEC61850':
            iec61850_check = self._check_iec61850_compliance(comm_data)
            compliance_issues.extend(iec61850_check)
        
        return {
            'compliance_issues': compliance_issues,
            'compliance_score': self._calculate_compliance_score(compliance_issues),
            'recommendations': self._generate_communication_recommendations(compliance_issues)
        }
    
    def _check_iec61850_compliance(self, comm_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查IEC61850合规性"""
        issues = []
        
        # 检查GOOSE配置
        goose_config = comm_data.get('goose_configuration', {})
        if goose_config:
            transmission_time = goose_config.get('transmission_time', 0)
            if transmission_time > 4:  # ms
                issues.append({
                    'type': 'goose_timing_violation',
                    'severity': 'medium',
                    'description': f'GOOSE传输时间过长: {transmission_time}ms > 4ms',
                    'standard_reference': 'IEC 61850-5',
                    'correction': '优化网络配置，确保传输时间≤4ms'
                })
        
        return issues
    
    def _calculate_compliance_score(self, issues: List[Dict[str, Any]]) -> float:
        """计算合规得分"""
        if not issues:
            return 100.0
        
        total_deduction = 0
        for issue in issues:
            severity = issue.get('severity', 'low')
            if severity == 'high':
                total_deduction += 20
            elif severity == 'medium':
                total_deduction += 10
            else:
                total_deduction += 5
        
        score = max(0, 100 - total_deduction)
        return score
    
    def _generate_overall_assessment(self, compliance_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合评估"""
        
        protection_score = compliance_analysis['protection_compliance']['compliance_score']
        control_score = compliance_analysis['control_compliance']['compliance_score']
        communication_score = compliance_analysis['communication_compliance']['compliance_score']
        
        overall_score = (protection_score + control_score + communication_score) / 3
        
        if overall_score >= 90:
            assessment_level = 'excellent'
            assessment_description = '设计完全符合规程要求'
        elif overall_score >= 80:
            assessment_level = 'good'
            assessment_description = '设计基本符合规程要求，有少量改进空间'
        elif overall_score >= 70:
            assessment_level = 'acceptable'
            assessment_description = '设计可接受，但需要重要改进'
        else:
            assessment_level = 'poor'
            assessment_description = '设计存在重大问题，需要全面修改'
        
        return {
            'overall_score': overall_score,
            'assessment_level': assessment_level,
            'assessment_description': assessment_description,
            'component_scores': {
                'protection_system': protection_score,
                'control_system': control_score,
                'communication_system': communication_score
            }
        }
    
    def _generate_protection_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """生成保护系统建议"""
        recommendations = []
        
        for issue in issues:
            recommendations.append(f"针对{issue['description']}：{issue['correction']}")
        
        return recommendations
    
    def _generate_control_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """生成控制系统建议"""
        recommendations = []
        
        for issue in issues:
            recommendations.append(f"针对{issue['description']}：{issue['correction']}")
        
        return recommendations
    
    def _generate_communication_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """生成通信系统建议"""
        recommendations = []
        
        for issue in issues:
            recommendations.append(f"针对{issue['description']}：{issue['correction']}")
        
        return recommendations
    
    def generate_compliance_report(self, compliance_analysis: Dict[str, Any]) -> str:
        """生成合规性报告"""
        
        overall_assessment = compliance_analysis['overall_assessment']
        
        report = f"""
# 设计图纸规程合规性审查报告

## 1. 综合评估

**总体得分**: {overall_assessment['overall_score']:.1f}/100
**评估等级**: {overall_assessment['assessment_level']}
**评估结论**: {overall_assessment['assessment_description']}

### 各系统得分
- 保护系统: {overall_assessment['component_scores']['protection_system']:.1f}/100
- 控制系统: {overall_assessment['component_scores']['control_system']:.1f}/100  
- 通信系统: {overall_assessment['component_scores']['communication_system']:.1f}/100

## 2. 保护系统合规性分析

### 2.1 发现的问题
{self._format_compliance_issues(compliance_analysis['protection_compliance']['compliance_issues'])}

### 2.2 改进建议
{self._format_recommendations(compliance_analysis['protection_compliance']['recommendations'])}

## 3. 控制系统合规性分析

### 3.1 发现的问题
{self._format_compliance_issues(compliance_analysis['control_compliance']['compliance_issues'])}

### 3.2 改进建议
{self._format_recommendations(compliance_analysis['control_compliance']['recommendations'])}

## 4. 通信系统合规性分析

### 4.1 发现的问题
{self._format_compliance_issues(compliance_analysis['communication_compliance']['compliance_issues'])}

### 4.2 改进建议
{self._format_recommendations(compliance_analysis['communication_compliance']['recommendations'])}

## 5. 总体建议

基于本次审查结果，建议：
1. 优先解决高严重性问题
2. 系统性改进中等严重性问题
3. 逐步完善低严重性问题
4. 建立持续的合规性检查机制

报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return report
    
    def _format_compliance_issues(self, issues: List[Dict[str, Any]]) -> str:
        """格式化合规问题"""
        if not issues:
            return "未发现合规性问题。"
        
        formatted_issues = []
        for i, issue in enumerate(issues, 1):
            formatted_issues.append(f"""
{i}. **{issue['description']}**
   - 严重程度: {issue['severity']}
   - 标准依据: {issue['standard_reference']}
   - 纠正措施: {issue['correction']}
            """)
        
        return '\n'.join(formatted_issues)
    
    def _format_recommendations(self, recommendations: List[str]) -> str:
        """格式化建议"""
        if not recommendations:
            return "无特殊建议。"
        
        formatted_recommendations = []
        for i, rec in enumerate(recommendations, 1):
            formatted_recommendations.append(f"{i}. {rec}")
        
        return '\n'.join(formatted_recommendations)