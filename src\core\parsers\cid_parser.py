"""
CID (Configured IED Description) 文件解析器
解析IEC61850已配置IED描述文件
"""

from typing import List, Optional, Dict, Any
from lxml import etree

from .icd_parser import ICDParser, ICDDocument
from .base_parser import ParseError
from ..models import Communication


class CIDParser(ICDParser):
    """CID文件解析器"""
    
    def get_supported_file_types(self) -> List[str]:
        """获取支持的文件类型"""
        return ['.cid', '.xml']
    
    def parse_root_element(self, root: etree.Element) -> 'CIDDocument':
        """解析CID根元素"""
        # CID文件包含已配置的IED信息，通常还包含通信配置
        cid_doc = CIDDocument()
        
        # 解析Header
        header_elem = self.find_child_element(root, 'Header', required=True)
        cid_doc.header = self.parse_header(header_elem)
        
        # 解析IED
        ied_elements = self.find_child_elements(root, 'IED')
        if not ied_elements:
            raise ParseError("CID文件必须包含至少一个IED元素")
        
        for ied_elem in ied_elements:
            ied = self.parse_ied(ied_elem)
            cid_doc.ieds.append(ied)
        
        # 解析Communication（CID文件的重要特征）
        communication_elem = self.find_child_element(root, 'Communication')
        if communication_elem is not None:
            cid_doc.communication = self.parse_communication(communication_elem)
        else:
            # CID文件通常应该包含Communication配置
            self.logger.warning("CID文件缺少Communication元素")
        
        # 解析DataTypeTemplates
        templates_elem = self.find_child_element(root, 'DataTypeTemplates')
        if templates_elem is not None:
            cid_doc.data_type_templates = self.parse_data_type_templates(templates_elem)
        
        # 设置SCL属性
        cid_doc.version = self.get_required_attribute(root, 'version')
        cid_doc.revision = self.get_element_attribute(root, 'revision', 'A')
        
        return cid_doc
    
    def validate_cid_specific(self, cid_doc: 'CIDDocument') -> List[str]:
        """CID特定的验证"""
        warnings = []
        
        # 检查是否有Communication配置
        if not cid_doc.communication:
            warnings.append("CID文件缺少Communication配置，这可能影响网络通信设置")
        
        # 检查IED与Communication的一致性
        if cid_doc.communication:
            ied_names = {ied.name for ied in cid_doc.ieds}
            connected_ied_names = set()
            
            for subnet in cid_doc.communication.sub_networks:
                for connected_ap in subnet.connected_aps:
                    connected_ied_names.add(connected_ap.ied_name)
            
            # 检查是否有IED没有在Communication中配置
            unconfigured_ieds = ied_names - connected_ied_names
            if unconfigured_ieds:
                warnings.append(f"以下IED没有在Communication中配置: {', '.join(unconfigured_ieds)}")
            
            # 检查是否有Communication中引用了不存在的IED
            missing_ieds = connected_ied_names - ied_names
            if missing_ieds:
                warnings.append(f"Communication中引用了不存在的IED: {', '.join(missing_ieds)}")
        
        # 检查IED的配置完整性
        for ied in cid_doc.ieds:
            # 检查AccessPoint配置
            for ap in ied.access_points:
                if ap.server and ap.server.ldevices:
                    # 检查LDevice配置
                    for ldevice in ap.server.ldevices:
                        if not ldevice.inst:
                            warnings.append(f"IED '{ied.name}' AccessPoint '{ap.name}' LDevice '{ldevice.name}' 缺少inst属性")
        
        return warnings


class CIDDocument(ICDDocument):
    """CID文档"""
    
    def validate(self) -> None:
        """CID文档验证"""
        super().validate()
        
        # CID特定验证可以在这里添加
        pass
    
    def get_network_configuration(self) -> Optional[Dict[str, Any]]:
        """获取网络配置信息"""
        if not self.communication:
            return None
        
        return self.communication.get_network_topology()
    
    def get_ied_network_mapping(self) -> Dict[str, List[str]]:
        """获取IED与网络的映射关系"""
        mapping = {}
        
        if self.communication:
            for subnet in self.communication.sub_networks:
                for connected_ap in subnet.connected_aps:
                    ied_name = connected_ap.ied_name
                    if ied_name not in mapping:
                        mapping[ied_name] = []
                    mapping[ied_name].append(subnet.name)
        
        return mapping
    
    def get_ip_address_allocation(self) -> Dict[str, str]:
        """获取IP地址分配情况"""
        ip_allocation = {}
        
        if self.communication:
            for subnet in self.communication.sub_networks:
                for connected_ap in subnet.connected_aps:
                    ip = connected_ap.get_ip_address()
                    if ip:
                        key = f"{connected_ap.ied_name}_{connected_ap.ap_name}"
                        ip_allocation[key] = ip
        
        return ip_allocation
    
    def check_configuration_consistency(self) -> Dict[str, List[str]]:
        """检查配置一致性"""
        issues = {
            'errors': [],
            'warnings': [],
            'info': []
        }
        
        # 检查IED与Communication的一致性
        if self.communication:
            ied_names = {ied.name for ied in self.ieds}
            connected_ied_names = set()
            
            for subnet in self.communication.sub_networks:
                for connected_ap in subnet.connected_aps:
                    connected_ied_names.add(connected_ap.ied_name)
            
            # 检查不一致的情况
            unconfigured_ieds = ied_names - connected_ied_names
            if unconfigured_ieds:
                issues['warnings'].append(f"IED未配置网络连接: {', '.join(unconfigured_ieds)}")
            
            missing_ieds = connected_ied_names - ied_names
            if missing_ieds:
                issues['errors'].append(f"网络配置引用了不存在的IED: {', '.join(missing_ieds)}")
            
            # 检查IP地址冲突
            ip_conflicts = self.communication.check_global_ip_conflicts()
            if ip_conflicts:
                for ip, subnets in ip_conflicts.items():
                    issues['errors'].append(f"IP地址冲突 {ip} 在子网: {', '.join(subnets)}")
        
        # 检查IED内部配置
        for ied in self.ieds:
            # 检查AccessPoint与Communication的匹配
            if self.communication:
                ap_names = {ap.name for ap in ied.access_points}
                connected_ap_names = set()
                
                for subnet in self.communication.sub_networks:
                    for connected_ap in subnet.connected_aps:
                        if connected_ap.ied_name == ied.name:
                            connected_ap_names.add(connected_ap.ap_name)
                
                unconnected_aps = ap_names - connected_ap_names
                if unconnected_aps:
                    issues['info'].append(f"IED '{ied.name}' 的AccessPoint未连接到网络: {', '.join(unconnected_aps)}")
                
                missing_aps = connected_ap_names - ap_names
                if missing_aps:
                    issues['errors'].append(f"IED '{ied.name}' 网络配置引用了不存在的AccessPoint: {', '.join(missing_aps)}")
        
        return issues
    
    def get_deployment_summary(self) -> Dict[str, Any]:
        """获取部署摘要"""
        summary = {
            'document_info': {
                'version': self.version,
                'revision': self.revision,
                'header_id': self.header.id if self.header else None
            },
            'ieds': [],
            'network_topology': self.get_network_configuration(),
            'ip_allocation': self.get_ip_address_allocation(),
            'consistency_check': self.check_configuration_consistency()
        }
        
        # 添加每个IED的摘要信息
        for ied in self.ieds:
            ied_summary = {
                'name': ied.name,
                'type': ied.type,
                'manufacturer': ied.manufacturer,
                'config_version': ied.config_version,
                'access_points_count': len(ied.access_points),
                'logical_devices_count': len(ied.get_all_ldevices()),
                'capabilities': ied.get_capabilities()
            }
            summary['ieds'].append(ied_summary)
        
        return summary
