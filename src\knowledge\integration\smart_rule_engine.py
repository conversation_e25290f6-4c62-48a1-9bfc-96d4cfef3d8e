"""
智能规则引擎
集成知识库的智能规则处理引擎
"""

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from .knowledge_validator import KnowledgeBasedValidator
from ...core.rules.rule_engine import RuleEngine
from ...core.rules.base_rule import ValidationResult
from ...core.models.base_model import BaseModel


logger = logging.getLogger(__name__)


class SmartRuleEngine(RuleEngine):
    """智能规则引擎"""
    
    def __init__(self, knowledge_db_path: str = "data/knowledge.db"):
        """
        初始化智能规则引擎
        
        Args:
            knowledge_db_path: 知识库数据库路径
        """
        # 初始化基础规则引擎
        super().__init__()
        
        try:
            # 初始化知识库验证器
            self.knowledge_validator = KnowledgeBasedValidator(knowledge_db_path)
            
            # 智能引擎配置
            self.config = {
                'enable_knowledge_rules': True,
                'enable_inference': True,
                'enable_adaptive_rules': True,
                'confidence_threshold': 0.6,
                'max_inference_depth': 3
            }
            
            # 执行统计
            self.execution_stats = {
                'traditional_rules': 0,
                'knowledge_rules': 0,
                'inference_rules': 0,
                'adaptive_rules': 0,
                'total_executions': 0
            }
            
            logger.info("智能规则引擎初始化完成")
            
        except Exception as e:
            logger.error(f"智能规则引擎初始化失败: {e}")
            raise
    
    def validate(self, data: BaseModel, context: Dict[str, Any] = None) -> List[ValidationResult]:
        """
        智能验证
        
        Args:
            data: 验证数据
            context: 验证上下文
            
        Returns:
            List[ValidationResult]: 验证结果列表
        """
        all_results = []
        
        try:
            self.execution_stats['total_executions'] += 1
            
            # 1. 执行传统规则验证
            traditional_results = self._execute_traditional_rules(data, context)
            all_results.extend(traditional_results)
            
            # 2. 执行知识库验证
            if self.config['enable_knowledge_rules']:
                knowledge_results = self._execute_knowledge_validation(data, context)
                all_results.extend(knowledge_results)
            
            # 3. 执行回路逻辑深度分析
            if self.config['enable_circuit_logic_analysis']:
                circuit_analysis_results = self._execute_circuit_logic_analysis(data, context)
                all_results.extend(circuit_analysis_results)
            
            # 4. 执行回路关系分析
            if self.config['enable_circuit_relationship_analysis']:
                circuit_relationship_results = self._execute_circuit_relationship_analysis(data, context)
                all_results.extend(circuit_relationship_results)
            
            # 5. 结果融合和优化
            optimized_results = self._optimize_results(all_results, context)
            
            logger.info(f"智能验证完成，共 {len(optimized_results)} 个结果")
            return optimized_results
            
        except Exception as e:
            logger.error(f"智能验证失败: {e}")
            return []
    
    def _execute_traditional_rules(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行传统规则验证"""
        try:
            # 调用父类的验证方法
            results = super().validate(data, context)
            self.execution_stats['traditional_rules'] += len(results)
            
            # 为传统规则结果添加标记
            for result in results:
                result.details = result.details or {}
                result.details['rule_source'] = 'traditional'
            
            return results
            
        except Exception as e:
            logger.error(f"执行传统规则失败: {e}")
            return []
    
    def _execute_knowledge_validation(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行知识库验证"""
        try:
            results = self.knowledge_validator.validate_with_knowledge(data, context)
            
            # 统计不同类型的规则
            for result in results:
                details = result.details or {}
                if details.get('inferred_rule'):
                    self.execution_stats['inference_rules'] += 1
                elif details.get('adaptive_rule'):
                    self.execution_stats['adaptive_rules'] += 1
                else:
                    self.execution_stats['knowledge_rules'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"执行知识库验证失败: {e}")
            return []
    
    def _execute_circuit_logic_analysis(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行回路逻辑分析"""
        try:
            # 使用知识验证器进行深度回路逻辑分析
            analysis_result = self.knowledge_validator.analyze_circuit_logic_deeply(data, context)
            
            results = []
            
            # 将分析结果转换为验证结果
            circuit_analysis = analysis_result.get('circuit_analysis', {})
            incomplete_circuits = circuit_analysis.get('incomplete_circuits', 0)
            
            if incomplete_circuits > 0:
                result = ValidationResult(
                    rule_id="circuit_logic_analysis",
                    passed=False,
                    message=f"发现 {incomplete_circuits} 个不完整的回路",
                    severity="warning",
                    details={
                        'analysis_type': 'circuit_logic',
                        'incomplete_circuits': incomplete_circuits,
                        'analysis_result': circuit_analysis
                    }
                )
                results.append(result)
            
            # 添加回路分析建议作为信息性结果
            recommendations = analysis_result.get('recommendations', [])
            for recommendation in recommendations:
                if recommendation.get('priority') in ['high', 'medium']:
                    severity = 'warning' if recommendation.get('priority') == 'high' else 'info'
                    result = ValidationResult(
                        rule_id="circuit_logic_recommendation",
                        passed=True,
                        message=recommendation.get('recommendation', ''),
                        severity=severity,
                        details={
                            'analysis_type': 'circuit_logic_recommendation',
                            'recommendation': recommendation
                        }
                    )
                    results.append(result)
            
            self.execution_stats['knowledge_rules'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行回路逻辑分析失败: {e}")
            return []
    
    def _execute_circuit_relationship_analysis(self, data: BaseModel, context: Dict[str, Any]) -> List[ValidationResult]:
        """执行回路关系分析"""
        try:
            results = []
            
            # 获取回路关系分析结果
            relationship_analysis = self.knowledge_validator.knowledge_graph.analyze_circuit_relationships()
            
            # 检查冲突关系
            conflicting_relationships = relationship_analysis.get('conflicting_relationships', [])
            for conflict in conflicting_relationships:
                result = ValidationResult(
                    rule_id="circuit_relationship_conflict",
                    passed=False,
                    message=f"检测到回路关系冲突: {conflict.get('description', '')}",
                    severity="error",
                    details={
                        'analysis_type': 'circuit_relationship',
                        'conflict_details': conflict
                    }
                )
                results.append(result)
            
            # 检查共享实体
            shared_entities = relationship_analysis.get('shared_entities', {})
            if shared_entities:
                result = ValidationResult(
                    rule_id="circuit_relationship_shared",
                    passed=True,
                    message=f"检测到 {len(shared_entities)} 个共享实体",
                    severity="info",
                    details={
                        'analysis_type': 'circuit_relationship',
                        'shared_entities_count': len(shared_entities),
                        'shared_entities': shared_entities
                    }
                )
                results.append(result)
            
            self.execution_stats['knowledge_rules'] += len(results)
            return results
            
        except Exception as e:
            logger.error(f"执行回路关系分析失败: {e}")
            return []
    
    def _optimize_results(self, results: List[ValidationResult], context: Dict[str, Any]) -> List[ValidationResult]:
        """优化验证结果"""
        try:
            # 1. 去重
            deduplicated_results = self._deduplicate_results(results)
            
            # 2. 优先级排序
            prioritized_results = self._prioritize_results(deduplicated_results)
            
            # 3. 结果聚合
            aggregated_results = self._aggregate_results(prioritized_results)
            
            # 4. 置信度过滤
            filtered_results = self._filter_by_confidence(aggregated_results)
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"结果优化失败: {e}")
            return results
    
    def _deduplicate_results(self, results: List[ValidationResult]) -> List[ValidationResult]:
        """去重验证结果"""
        seen_messages = set()
        deduplicated = []
        
        for result in results:
            # 基于消息内容去重
            message_key = f"{result.rule_id}:{result.message}"
            if message_key not in seen_messages:
                seen_messages.add(message_key)
                deduplicated.append(result)
            else:
                # 如果是重复消息，保留置信度更高的
                for i, existing in enumerate(deduplicated):
                    if f"{existing.rule_id}:{existing.message}" == message_key:
                        existing_confidence = existing.details.get('inference_confidence', 1.0) if existing.details else 1.0
                        new_confidence = result.details.get('inference_confidence', 1.0) if result.details else 1.0
                        
                        if new_confidence > existing_confidence:
                            deduplicated[i] = result
                        break
        
        return deduplicated
    
    def _prioritize_results(self, results: List[ValidationResult]) -> List[ValidationResult]:
        """按优先级排序结果"""
        def get_priority_score(result: ValidationResult) -> float:
            score = 0.0
            
            # 严重程度权重
            severity_weights = {'error': 3.0, 'warning': 2.0, 'info': 1.0}
            score += severity_weights.get(result.severity, 1.0)
            
            # 规则来源权重
            if result.details:
                source = result.details.get('rule_source', 'knowledge')
                source_weights = {'traditional': 1.0, 'knowledge': 0.8, 'inference': 0.6, 'adaptive': 0.4}
                score += source_weights.get(source, 0.5)
                
                # 置信度权重
                confidence = result.details.get('inference_confidence', 1.0)
                score += confidence
            
            return score
        
        return sorted(results, key=get_priority_score, reverse=True)
    
    def _aggregate_results(self, results: List[ValidationResult]) -> List[ValidationResult]:
        """聚合相关结果"""
        # 按规则类别聚合
        category_groups = {}
        
        for result in results:
            category = getattr(result, 'category', 'general')
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(result)
        
        aggregated = []
        
        for category, group_results in category_groups.items():
            if len(group_results) > 5:  # 如果同类别结果太多，创建汇总结果
                error_count = sum(1 for r in group_results if r.severity == 'error')
                warning_count = sum(1 for r in group_results if r.severity == 'warning')
                
                if error_count > 0 or warning_count > 0:
                    summary_result = ValidationResult(
                        rule_id=f"summary_{category}",
                        passed=error_count == 0,
                        message=f"{category}类别验证汇总: {error_count}个错误, {warning_count}个警告",
                        severity='error' if error_count > 0 else 'warning',
                        details={
                            'category': category,
                            'error_count': error_count,
                            'warning_count': warning_count,
                            'aggregated_results': [r.dict() for r in group_results]
                        }
                    )
                    aggregated.append(summary_result)
                else:
                    aggregated.extend(group_results)
            else:
                aggregated.extend(group_results)
        
        return aggregated
    
    def _filter_by_confidence(self, results: List[ValidationResult]) -> List[ValidationResult]:
        """按置信度过滤结果"""
        filtered = []
        
        for result in results:
            confidence = 1.0  # 默认置信度
            
            if result.details:
                confidence = result.details.get('inference_confidence', 1.0)
            
            # 应用置信度阈值
            if confidence >= self.config['confidence_threshold']:
                filtered.append(result)
            else:
                logger.debug(f"过滤低置信度结果: {result.message} (置信度: {confidence})")
        
        return filtered
    
    def get_smart_recommendations(self, data: BaseModel, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        获取智能推荐
        
        Args:
            data: 验证数据
            context: 验证上下文
            
        Returns:
            List[Dict[str, Any]]: 推荐列表
        """
        recommendations = []
        
        try:
            # 基于知识图谱的推荐
            if hasattr(self.knowledge_validator, 'inference_engine'):
                # 获取相关实体推荐
                entity_recommendations = self.knowledge_validator.inference_engine.get_recommendation(
                    entity_id="current_validation",
                    recommendation_type="related"
                )
                
                for entity in entity_recommendations:
                    recommendations.append({
                        'type': 'related_entity',
                        'title': entity.name,
                        'description': entity.description,
                        'confidence': entity.confidence,
                        'source': 'knowledge_graph'
                    })
            
            # 基于回路逻辑分析的推荐
            circuit_analysis_recommendations = self._get_circuit_analysis_recommendations(data, context)
            recommendations.extend(circuit_analysis_recommendations)
            
            # 基于验证历史的推荐
            historical_recommendations = self._get_historical_recommendations(context)
            recommendations.extend(historical_recommendations)
            
            # 基于最佳实践的推荐
            best_practice_recommendations = self._get_best_practice_recommendations(data, context)
            recommendations.extend(best_practice_recommendations)
            
            return recommendations[:10]  # 限制推荐数量
            
        except Exception as e:
            logger.error(f"获取智能推荐失败: {e}")
            return []
    
    def _get_circuit_analysis_recommendations(self, data: BaseModel, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取基于回路分析的推荐"""
        recommendations = []
        
        try:
            # 执行深度回路分析
            analysis_result = self.knowledge_validator.analyze_circuit_logic_deeply(data, context)
            
            # 获取分析建议
            analysis_recommendations = analysis_result.get('recommendations', [])
            recommendations.extend(analysis_recommendations)
            
        except Exception as e:
            logger.warning(f"获取回路分析推荐失败: {e}")
        
        return recommendations
    
    def _get_historical_recommendations(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取基于历史的推荐"""
        recommendations = []
        
        # 这里可以基于验证历史数据生成推荐
        # 简化实现
        recommendations.append({
            'type': 'historical',
            'title': '常见配置建议',
            'description': '基于历史验证数据的配置建议',
            'confidence': 0.7,
            'source': 'historical_data'
        })
        
        return recommendations
    
    def _get_best_practice_recommendations(self, data: BaseModel, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取最佳实践推荐"""
        recommendations = []
        
        # 基于最佳实践生成推荐
        recommendations.append({
            'type': 'best_practice',
            'title': 'IEC61850最佳实践',
            'description': '遵循IEC61850标准的最佳实践建议',
            'confidence': 0.9,
            'source': 'best_practices'
        })
        
        return recommendations
    
    def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        stats = {
            'execution_stats': self.execution_stats.copy(),
            'config': self.config.copy(),
            'knowledge_stats': self.knowledge_validator.get_knowledge_statistics()
        }
        
        return stats
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """
        更新引擎配置
        
        Args:
            new_config: 新配置
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 扩展配置选项
            extended_config = {
                'enable_knowledge_rules': True,
                'enable_inference': True,
                'enable_adaptive_rules': True,
                'enable_circuit_logic_analysis': True,  # 新增：启用回路逻辑分析
                'enable_circuit_relationship_analysis': True,  # 新增：启用回路关系分析
                'confidence_threshold': 0.6,
                'max_inference_depth': 3
            }
            
            extended_config.update(new_config)
            self.config.update(extended_config)
            
            logger.info("智能规则引擎配置已更新")
            return True
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
