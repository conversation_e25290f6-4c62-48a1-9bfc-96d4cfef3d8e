#!/usr/bin/env python3
"""
性能监控脚本
实时监控系统性能指标并提供调优建议
"""

import time
import psutil
import logging
import json
import argparse
from datetime import datetime, timedelta
from pathlib import Path
import threading
from typing import Dict, List, Any, Optional
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.performance.performance_optimizer import PerformanceOptimizer


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: int = 60, output_file: Optional[str] = None):
        """
        初始化性能监控器
        
        Args:
            interval: 监控间隔（秒）
            output_file: 输出文件路径
        """
        self.interval = interval
        self.output_file = output_file
        self.running = False
        self.metrics_history = []
        self.alerts = []
        
        # 性能阈值配置
        self.thresholds = {
            'cpu_usage': 80.0,      # CPU使用率阈值
            'memory_usage': 85.0,   # 内存使用率阈值
            'disk_usage': 90.0,     # 磁盘使用率阈值
            'response_time': 5.0,   # 响应时间阈值（秒）
            'error_rate': 5.0       # 错误率阈值（%）
        }
        
        # 设置日志
        self._setup_logging()
        
        logger.info(f"性能监控器初始化完成 - 间隔: {interval}s")
    
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        logger.info("开始性能监控")
        
        try:
            while self.running:
                metrics = self._collect_metrics()
                self._analyze_metrics(metrics)
                self._save_metrics(metrics)
                
                time.sleep(self.interval)
                
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在停止监控...")
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        logger.info("性能监控已停止")
        
        # 生成监控报告
        self._generate_report()
    
    def _collect_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        try:
            # 系统指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 进程指标
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            
            # 网络指标
            network = psutil.net_io_counters()
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'cpu_usage': cpu_percent,
                    'memory_total': memory.total,
                    'memory_used': memory.used,
                    'memory_percent': memory.percent,
                    'disk_total': disk.total,
                    'disk_used': disk.used,
                    'disk_percent': (disk.used / disk.total) * 100,
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
                },
                'process': {
                    'pid': process.pid,
                    'cpu_percent': process_cpu,
                    'memory_rss': process_memory.rss,
                    'memory_vms': process_memory.vms,
                    'memory_percent': process.memory_percent(),
                    'num_threads': process.num_threads(),
                    'num_fds': process.num_fds() if hasattr(process, 'num_fds') else 0
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            }
            
            # 应用特定指标
            app_metrics = self._collect_app_metrics()
            if app_metrics:
                metrics['application'] = app_metrics
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            return {}
    
    def _collect_app_metrics(self) -> Optional[Dict[str, Any]]:
        """收集应用特定指标"""
        try:
            # 这里可以添加应用特定的性能指标收集
            # 例如：数据库连接数、缓存命中率、请求队列长度等
            
            app_metrics = {
                'active_connections': 0,  # 活跃连接数
                'cache_hit_rate': 0.0,    # 缓存命中率
                'queue_length': 0,        # 队列长度
                'error_count': 0,         # 错误计数
                'request_count': 0        # 请求计数
            }
            
            return app_metrics
            
        except Exception as e:
            logger.error(f"收集应用指标失败: {e}")
            return None
    
    def _analyze_metrics(self, metrics: Dict[str, Any]):
        """分析性能指标并生成告警"""
        if not metrics:
            return
        
        alerts = []
        
        # 检查CPU使用率
        cpu_usage = metrics['system']['cpu_usage']
        if cpu_usage > self.thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_high',
                'severity': 'warning',
                'message': f'CPU使用率过高: {cpu_usage:.1f}%',
                'threshold': self.thresholds['cpu_usage'],
                'current': cpu_usage,
                'timestamp': metrics['timestamp']
            })
        
        # 检查内存使用率
        memory_percent = metrics['system']['memory_percent']
        if memory_percent > self.thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_high',
                'severity': 'warning',
                'message': f'内存使用率过高: {memory_percent:.1f}%',
                'threshold': self.thresholds['memory_usage'],
                'current': memory_percent,
                'timestamp': metrics['timestamp']
            })
        
        # 检查磁盘使用率
        disk_percent = metrics['system']['disk_percent']
        if disk_percent > self.thresholds['disk_usage']:
            alerts.append({
                'type': 'disk_high',
                'severity': 'critical',
                'message': f'磁盘使用率过高: {disk_percent:.1f}%',
                'threshold': self.thresholds['disk_usage'],
                'current': disk_percent,
                'timestamp': metrics['timestamp']
            })
        
        # 检查进程内存使用
        process_memory_mb = metrics['process']['memory_rss'] / 1024 / 1024
        if process_memory_mb > 2048:  # 超过2GB
            alerts.append({
                'type': 'process_memory_high',
                'severity': 'warning',
                'message': f'进程内存使用过高: {process_memory_mb:.1f}MB',
                'threshold': 2048,
                'current': process_memory_mb,
                'timestamp': metrics['timestamp']
            })
        
        # 保存告警
        for alert in alerts:
            self.alerts.append(alert)
            logger.warning(f"性能告警: {alert['message']}")
        
        # 生成调优建议
        if alerts:
            recommendations = self._generate_recommendations(alerts)
            for rec in recommendations:
                logger.info(f"调优建议: {rec}")
    
    def _generate_recommendations(self, alerts: List[Dict[str, Any]]) -> List[str]:
        """生成性能调优建议"""
        recommendations = []
        
        for alert in alerts:
            alert_type = alert['type']
            
            if alert_type == 'cpu_high':
                recommendations.extend([
                    "考虑增加CPU核心数或升级CPU",
                    "检查是否有CPU密集型任务可以优化",
                    "启用多线程处理以分散CPU负载",
                    "考虑使用缓存减少计算量"
                ])
            
            elif alert_type == 'memory_high':
                recommendations.extend([
                    "增加系统内存",
                    "启用流式处理减少内存占用",
                    "优化数据结构和算法",
                    "定期执行垃圾回收",
                    "考虑使用内存映射文件"
                ])
            
            elif alert_type == 'disk_high':
                recommendations.extend([
                    "清理临时文件和日志",
                    "增加磁盘空间",
                    "启用日志轮转",
                    "压缩或归档旧数据"
                ])
            
            elif alert_type == 'process_memory_high':
                recommendations.extend([
                    "检查内存泄漏",
                    "优化数据缓存策略",
                    "分批处理大数据集",
                    "启用内存限制配置"
                ])
        
        return list(set(recommendations))  # 去重
    
    def _save_metrics(self, metrics: Dict[str, Any]):
        """保存性能指标"""
        self.metrics_history.append(metrics)
        
        # 保持历史记录在合理范围内
        if len(self.metrics_history) > 1440:  # 24小时的分钟数
            self.metrics_history.pop(0)
        
        # 写入文件
        if self.output_file:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(metrics, ensure_ascii=False) + '\n')
            except Exception as e:
                logger.error(f"写入性能指标文件失败: {e}")
    
    def _generate_report(self):
        """生成性能监控报告"""
        if not self.metrics_history:
            return
        
        report = {
            'summary': self._generate_summary(),
            'alerts': self.alerts,
            'recommendations': self._generate_overall_recommendations(),
            'generated_at': datetime.now().isoformat()
        }
        
        # 保存报告
        report_file = f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"性能监控报告已生成: {report_file}")
            
        except Exception as e:
            logger.error(f"生成性能报告失败: {e}")
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成性能摘要"""
        if not self.metrics_history:
            return {}
        
        # 计算平均值、最大值、最小值
        cpu_values = [m['system']['cpu_usage'] for m in self.metrics_history if 'system' in m]
        memory_values = [m['system']['memory_percent'] for m in self.metrics_history if 'system' in m]
        disk_values = [m['system']['disk_percent'] for m in self.metrics_history if 'system' in m]
        
        summary = {
            'monitoring_duration': len(self.metrics_history) * self.interval,
            'total_alerts': len(self.alerts),
            'cpu_usage': {
                'avg': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                'max': max(cpu_values) if cpu_values else 0,
                'min': min(cpu_values) if cpu_values else 0
            },
            'memory_usage': {
                'avg': sum(memory_values) / len(memory_values) if memory_values else 0,
                'max': max(memory_values) if memory_values else 0,
                'min': min(memory_values) if memory_values else 0
            },
            'disk_usage': {
                'avg': sum(disk_values) / len(disk_values) if disk_values else 0,
                'max': max(disk_values) if disk_values else 0,
                'min': min(disk_values) if disk_values else 0
            }
        }
        
        return summary
    
    def _generate_overall_recommendations(self) -> List[str]:
        """生成整体调优建议"""
        if not self.metrics_history:
            return []
        
        recommendations = []
        
        # 基于历史数据分析
        cpu_avg = sum(m['system']['cpu_usage'] for m in self.metrics_history if 'system' in m) / len(self.metrics_history)
        memory_avg = sum(m['system']['memory_percent'] for m in self.metrics_history if 'system' in m) / len(self.metrics_history)
        
        if cpu_avg > 60:
            recommendations.append("CPU平均使用率较高，建议优化计算密集型操作")
        
        if memory_avg > 70:
            recommendations.append("内存平均使用率较高，建议优化内存使用")
        
        if len(self.alerts) > 10:
            recommendations.append("告警频繁，建议全面检查系统配置")
        
        # 添加通用建议
        recommendations.extend([
            "定期监控系统性能指标",
            "建立性能基线和告警阈值",
            "实施自动化性能调优",
            "定期更新和优化应用程序"
        ])
        
        return recommendations
    
    def _setup_logging(self):
        """设置日志"""
        global logger
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/performance_monitor.log', encoding='utf-8')
            ]
        )
        
        logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='IEC61850智能设计检查器性能监控')
    parser.add_argument('--interval', type=int, default=60, help='监控间隔（秒）')
    parser.add_argument('--output', type=str, help='输出文件路径')
    parser.add_argument('--duration', type=int, help='监控持续时间（秒）')
    
    args = parser.parse_args()
    
    # 确保日志目录存在
    Path('logs').mkdir(exist_ok=True)
    
    # 创建性能监控器
    monitor = PerformanceMonitor(
        interval=args.interval,
        output_file=args.output
    )
    
    try:
        if args.duration:
            # 定时监控
            def stop_after_duration():
                time.sleep(args.duration)
                monitor.stop_monitoring()
            
            timer = threading.Timer(args.duration, stop_after_duration)
            timer.start()
        
        # 开始监控
        monitor.start_monitoring()
        
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"监控失败: {e}")


if __name__ == "__main__":
    main()
