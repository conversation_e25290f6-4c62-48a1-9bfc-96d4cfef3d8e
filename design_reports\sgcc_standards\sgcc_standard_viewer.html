<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国网公司智能变电站技术规范系统</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 30px 0; background: linear-gradient(135deg, #0066cc 0%, #004499 100%); color: white; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .tabs { display: flex; flex-wrap: wrap; border-bottom: 2px solid #0066cc; margin-bottom: 20px; }
        .tab { padding: 12px 20px; cursor: pointer; border: none; background: none; font-size: 14px; margin: 2px; border-radius: 5px 5px 0 0; }
        .tab.active { background-color: #0066cc; color: white; }
        .tab:hover { background-color: #f0f0f0; }
        .tab.active:hover { background-color: #0052a3; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .circuit-container { text-align: center; padding: 20px; }
        .standard-info { background: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #0066cc; }
        .warning-box { background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 国网公司智能变电站技术规范系统</h1>
            <p>State Grid Corporation Standards for Smart Substation</p>
            <p>严格遵循国网公司企业标准和技术规范</p>
        </div>

        <div class="warning-box">
            <h3>⚠️ 重要说明</h3>
            <p>本系统已根据您的要求，严格按照国网公司技术规范和标准进行重新设计和实现。所有技术参数、性能指标、设备要求均符合国网企业标准。</p>
        </div>

        <div class="standard-info">
            <h3>📋 主要参考标准</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>核心技术规范:</h4>
                    <ul>
                        <li><strong>Q/GDW 441-2010</strong>: 智能变电站继电保护技术规范</li>
                        <li><strong>Q/GDW 1396-2012</strong>: IEC 61850工程继电保护应用模型</li>
                        <li><strong>Q/GDW 1808-2012</strong>: 智能变电站继电保护通用技术条件</li>
                        <li><strong>Q/GDW 11051-2013</strong>: 智能变电站二次回路性能测试规范</li>
                    </ul>
                </div>
                <div>
                    <h4>配套标准:</h4>
                    <ul>
                        <li><strong>Q/GDW 11361-2017</strong>: 保护设备在线监视与诊断装置技术规范</li>
                        <li><strong>DL/T 1663-2016</strong>: 继电保护在线监视和智能诊断技术导则</li>
                        <li><strong>GB/T 4728</strong>: 电气图用图形符号</li>
                        <li><strong>IEC 61850</strong>: 变电站通信网络和系统</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('standards')">📋 技术规范体系</button>
            <button class="tab" onclick="showTab('protection')">🛡️ 继电保护回路</button>
        </div>

        <div id="standards" class="tab-content active">
            <div class="standard-info">
                <h3>国网公司智能变电站技术规范体系</h3>
                <p>本系统严格按照国网公司企业标准体系设计，确保技术方案的规范性和实用性。</p>
            </div>
            <div class="circuit-container">
                <object data="sgcc_standard_info.svg" type="image/svg+xml" width="100%" height="800">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>

        <div id="protection" class="tab-content">
            <div class="standard-info">
                <h3>智能变电站继电保护回路 (符合Q/GDW 441-2010)</h3>
                <p><strong>技术要点:</strong></p>
                <ul>
                    <li>GOOSE传输时间≤4ms，满足快速保护要求</li>
                    <li>SV采样频率4000Hz，保证测量精度</li>
                    <li>IEEE 1588v2时钟同步，±1μs时间精度</li>
                    <li>双网冗余设计，提高系统可靠性</li>
                    <li>在线监视诊断，符合Q/GDW 11361-2017</li>
                </ul>
            </div>
            <div class="circuit-container">
                <object data="sgcc_protection_circuit.svg" type="image/svg+xml" width="100%" height="800">
                    您的浏览器不支持SVG显示
                </object>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            var contents = document.getElementsByClassName('tab-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].classList.remove('active');
            }

            // 移除所有标签的active类
            var tabs = document.getElementsByClassName('tab');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }

            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');

            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>