"""
通信网络规则
实现通信网络相关的验证规则
"""

import ipaddress
from typing import List, Set, Dict, Any, Tuple
from ..models import Communication, SubNetwork, ConnectedAP, Address
from ..parsers.scd_parser import SCLDocument
from .base import (
    BaseRule, RuleContext, RuleResult, RuleCategory, RuleSeverity,
    rule, applicable_to
)


class CommunicationRules:
    """通信网络规则集合"""
    
    @staticmethod
    @rule(
        rule_id="COM001",
        name="IP地址冲突检查",
        description="检查网络中是否存在IP地址冲突",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(Communication)
    def check_ip_conflicts(context: RuleContext) -> RuleResult:
        """检查IP地址冲突"""
        result = RuleResult(rule_id="COM001", success=True)
        communication = context.data
        
        # 收集所有IP地址
        ip_to_devices = {}
        
        for subnet in communication.sub_networks:
            for connected_ap in subnet.connected_aps:
                ip = connected_ap.get_ip_address()
                if ip:
                    device_id = f"{connected_ap.ied_name}_{connected_ap.ap_name}"
                    
                    if ip in ip_to_devices:
                        # 发现IP冲突
                        existing_device = ip_to_devices[ip]
                        result.add_error(
                            f"IP地址冲突: {ip} 被 {existing_device} 和 {device_id} 同时使用",
                            f"{context.get_path_string()}.{subnet.name}",
                            suggestion=f"为 {device_id} 分配不同的IP地址"
                        )
                    else:
                        ip_to_devices[ip] = device_id
        
        if not result.has_errors():
            result.add_info(
                f"检查了 {len(ip_to_devices)} 个IP地址，未发现冲突",
                context.get_path_string()
            )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="COM002",
        name="子网配置检查",
        description="检查子网配置的合理性",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(SubNetwork)
    def check_subnet_config(context: RuleContext) -> RuleResult:
        """检查子网配置"""
        result = RuleResult(rule_id="COM002", success=True)
        subnet = context.data
        
        # 检查子网名称
        if not subnet.name:
            result.add_error("子网名称不能为空", context.get_path_string())
        
        # 检查子网类型
        valid_types = ['8-MMS', '8-GOOSE', '8-SMV', 'IP']
        if subnet.type and subnet.type not in valid_types:
            result.add_warning(
                f"子网类型 '{subnet.type}' 不是标准类型",
                context.get_path_string(),
                details=f"标准类型包括: {', '.join(valid_types)}",
                suggestion="使用标准子网类型"
            )
        
        # 检查连接的设备数量
        device_count = len(subnet.connected_aps)
        if device_count == 0:
            result.add_warning(
                f"子网 '{subnet.name}' 没有连接任何设备",
                context.get_path_string(),
                suggestion="移除未使用的子网或添加设备连接"
            )
        elif device_count > 100:
            result.add_warning(
                f"子网 '{subnet.name}' 连接了 {device_count} 个设备，数量较多",
                context.get_path_string(),
                suggestion="考虑将设备分布到多个子网以提高性能"
            )
        
        # 检查IP地址范围一致性
        ip_addresses = []
        for connected_ap in subnet.connected_aps:
            ip = connected_ap.get_ip_address()
            if ip:
                try:
                    ip_obj = ipaddress.IPv4Address(ip)
                    ip_addresses.append(ip_obj)
                except ipaddress.AddressValueError:
                    result.add_error(
                        f"无效的IP地址: {ip} (设备: {connected_ap.ied_name}_{connected_ap.ap_name})",
                        context.get_path_string()
                    )
        
        # 检查IP地址是否在同一网段
        if len(ip_addresses) > 1:
            subnets_found = set()
            for ip in ip_addresses:
                # 假设使用/24子网掩码
                network = ipaddress.IPv4Network(f"{ip}/24", strict=False)
                subnets_found.add(network)
            
            if len(subnets_found) > 1:
                result.add_warning(
                    f"子网 '{subnet.name}' 中的设备IP地址跨越多个网段: {', '.join(str(net) for net in subnets_found)}",
                    context.get_path_string(),
                    suggestion="确保同一子网中的设备使用相同网段的IP地址"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="COM003",
        name="网络拓扑合理性检查",
        description="检查网络拓扑结构的合理性",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(Communication)
    def check_network_topology(context: RuleContext) -> RuleResult:
        """检查网络拓扑合理性"""
        result = RuleResult(rule_id="COM003", success=True)
        communication = context.data
        
        if not communication.sub_networks:
            result.add_warning(
                "通信配置中没有定义子网",
                context.get_path_string(),
                suggestion="定义至少一个子网用于设备通信"
            )
            return result
        
        # 分析网络类型分布
        network_types = {}
        for subnet in communication.sub_networks:
            net_type = subnet.type or 'Unknown'
            if net_type not in network_types:
                network_types[net_type] = []
            network_types[net_type].append(subnet.name)
        
        # 检查是否有站控层网络
        has_station_network = any(
            net_type in ['8-MMS', 'IP'] 
            for net_type in network_types.keys()
        )
        
        if not has_station_network:
            result.add_warning(
                "没有发现站控层网络(MMS/IP)",
                context.get_path_string(),
                suggestion="通常需要MMS网络用于站控层通信"
            )
        
        # 检查是否有过程层网络
        has_process_network = any(
            net_type in ['8-GOOSE', '8-SMV'] 
            for net_type in network_types.keys()
        )
        
        if not has_process_network:
            result.add_info(
                "没有发现过程层网络(GOOSE/SMV)",
                context.get_path_string(),
                suggestion="如果需要过程层通信，请配置GOOSE或SMV网络"
            )
        
        # 检查网络隔离
        for net_type, subnets in network_types.items():
            if len(subnets) > 1:
                result.add_info(
                    f"网络类型 '{net_type}' 有多个子网: {', '.join(subnets)}",
                    context.get_path_string(),
                    suggestion="确认是否需要网络隔离或冗余"
                )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="COM004",
        name="设备网络连接检查",
        description="检查设备的网络连接配置",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(SCLDocument)
    def check_device_network_connections(context: RuleContext) -> RuleResult:
        """检查设备网络连接"""
        result = RuleResult(rule_id="COM004", success=True)
        scl_doc = context.data
        
        if not scl_doc.communication or not scl_doc.ieds:
            return result
        
        # 收集网络中连接的设备
        connected_devices = set()
        for subnet in scl_doc.communication.sub_networks:
            for connected_ap in subnet.connected_aps:
                connected_devices.add(connected_ap.ied_name)
        
        # 检查每个IED是否有网络连接
        for ied in scl_doc.ieds:
            if ied.name not in connected_devices:
                result.add_warning(
                    f"IED '{ied.name}' 没有网络连接配置",
                    f"{context.get_path_string()}.ieds",
                    suggestion="为IED配置网络连接以实现通信功能"
                )
        
        # 检查网络中引用的IED是否存在
        ied_names = {ied.name for ied in scl_doc.ieds}
        for subnet in scl_doc.communication.sub_networks:
            for connected_ap in subnet.connected_aps:
                if connected_ap.ied_name not in ied_names:
                    result.add_error(
                        f"子网 '{subnet.name}' 引用了不存在的IED: {connected_ap.ied_name}",
                        f"{context.get_path_string()}.communication"
                    )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="COM005",
        name="MAC地址检查",
        description="检查MAC地址的有效性和唯一性",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.ERROR
    )
    @applicable_to(Communication)
    def check_mac_addresses(context: RuleContext) -> RuleResult:
        """检查MAC地址"""
        result = RuleResult(rule_id="COM005", success=True)
        communication = context.data
        
        mac_to_devices = {}
        mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
        
        import re
        
        for subnet in communication.sub_networks:
            for connected_ap in subnet.connected_aps:
                mac = connected_ap.get_mac_address()
                if mac:
                    device_id = f"{connected_ap.ied_name}_{connected_ap.ap_name}"
                    
                    # 检查MAC地址格式
                    if not re.match(mac_pattern, mac):
                        result.add_error(
                            f"无效的MAC地址格式: {mac} (设备: {device_id})",
                            f"{context.get_path_string()}.{subnet.name}",
                            suggestion="MAC地址应为 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式"
                        )
                        continue
                    
                    # 检查MAC地址唯一性
                    if mac in mac_to_devices:
                        existing_device = mac_to_devices[mac]
                        result.add_error(
                            f"MAC地址冲突: {mac} 被 {existing_device} 和 {device_id} 同时使用",
                            f"{context.get_path_string()}.{subnet.name}"
                        )
                    else:
                        mac_to_devices[mac] = device_id
        
        return result
    
    @staticmethod
    @rule(
        rule_id="COM006",
        name="VLAN配置检查",
        description="检查VLAN配置的合理性",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.WARNING
    )
    @applicable_to(Communication)
    def check_vlan_config(context: RuleContext) -> RuleResult:
        """检查VLAN配置"""
        result = RuleResult(rule_id="COM006", success=True)
        communication = context.data
        
        vlan_usage = {}
        
        for subnet in communication.sub_networks:
            subnet_vlans = set()
            
            for connected_ap in subnet.connected_aps:
                vlan_id = connected_ap.get_vlan_id()
                if vlan_id is not None:
                    subnet_vlans.add(vlan_id)
                    
                    if vlan_id not in vlan_usage:
                        vlan_usage[vlan_id] = []
                    vlan_usage[vlan_id].append(f"{subnet.name}:{connected_ap.ied_name}")
                    
                    # 检查VLAN ID范围
                    if not (1 <= vlan_id <= 4094):
                        result.add_error(
                            f"无效的VLAN ID: {vlan_id} (设备: {connected_ap.ied_name})",
                            f"{context.get_path_string()}.{subnet.name}",
                            suggestion="VLAN ID应在1-4094范围内"
                        )
            
            # 检查同一子网中的VLAN一致性
            if len(subnet_vlans) > 1:
                result.add_warning(
                    f"子网 '{subnet.name}' 中使用了多个VLAN: {sorted(subnet_vlans)}",
                    f"{context.get_path_string()}.{subnet.name}",
                    suggestion="同一子网中的设备通常应使用相同的VLAN"
                )
        
        # 检查VLAN跨子网使用
        for vlan_id, usage in vlan_usage.items():
            if len(usage) > 1:
                subnets_using_vlan = set(usage_item.split(':')[0] for usage_item in usage)
                if len(subnets_using_vlan) > 1:
                    result.add_info(
                        f"VLAN {vlan_id} 被多个子网使用: {', '.join(subnets_using_vlan)}",
                        context.get_path_string(),
                        suggestion="确认VLAN跨子网使用是否符合网络设计"
                    )
        
        return result
    
    @staticmethod
    @rule(
        rule_id="COM007",
        name="网络性能检查",
        description="检查网络配置对性能的影响",
        category=RuleCategory.COMMUNICATION,
        severity=RuleSeverity.INFO
    )
    @applicable_to(Communication)
    def check_network_performance(context: RuleContext) -> RuleResult:
        """检查网络性能"""
        result = RuleResult(rule_id="COM007", success=True)
        communication = context.data
        
        for subnet in communication.sub_networks:
            device_count = len(subnet.connected_aps)
            
            # 根据网络类型给出性能建议
            if subnet.type == '8-GOOSE':
                if device_count > 50:
                    result.add_warning(
                        f"GOOSE子网 '{subnet.name}' 连接了 {device_count} 个设备，可能影响实时性",
                        f"{context.get_path_string()}.{subnet.name}",
                        suggestion="考虑将GOOSE设备分布到多个子网"
                    )
                
                # 检查GOOSE优先级配置
                priorities = []
                for connected_ap in subnet.connected_aps:
                    priority = connected_ap.get_vlan_priority()
                    if priority is not None:
                        priorities.append(priority)
                
                if priorities and max(priorities) < 4:
                    result.add_info(
                        f"GOOSE子网 '{subnet.name}' 的VLAN优先级较低",
                        f"{context.get_path_string()}.{subnet.name}",
                        suggestion="GOOSE消息建议使用高优先级(4-7)"
                    )
            
            elif subnet.type == '8-SMV':
                if device_count > 20:
                    result.add_warning(
                        f"SMV子网 '{subnet.name}' 连接了 {device_count} 个设备，带宽可能不足",
                        f"{context.get_path_string()}.{subnet.name}",
                        suggestion="SMV网络需要高带宽，考虑减少设备数量或使用千兆网络"
                    )
            
            elif subnet.type == '8-MMS':
                if device_count > 200:
                    result.add_info(
                        f"MMS子网 '{subnet.name}' 连接了 {device_count} 个设备，数量较多",
                        f"{context.get_path_string()}.{subnet.name}",
                        suggestion="考虑网络分段以提高管理效率"
                    )
        
        return result


# 自动注册所有规则
def register_communication_rules():
    """注册所有通信规则"""
    from .registry import rule_registry
    import inspect
    
    for name, method in inspect.getmembers(CommunicationRules, predicate=inspect.isfunction):
        if hasattr(method, 'rule_id'):
            try:
                rule_registry.register(method)
            except Exception as e:
                print(f"注册规则 {name} 失败: {e}")


# 在模块加载时自动注册规则
register_communication_rules()
