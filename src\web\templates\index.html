{% extends "base.html" %}

{% block title %}{{ app_name }} - 智能变电站二次设计验证工具{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<div class="hero-section bg-gradient-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-bolt me-3"></i>
                    {{ app_name }}
                </h1>
                <p class="lead mb-4">
                    专业的智能变电站二次设计验证与分析工具
                </p>
                <p class="mb-4">
                    解决IEC61850虚端子连接检查难题，让复杂的配置文件分析变得直观简单
                </p>
                <div class="d-flex gap-3">
                    <a href="{{ url_for('main.upload_page') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-upload me-2"></i>
                        开始验证
                    </a>
                    <a href="{{ url_for('main.help_page') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-book me-2"></i>
                        使用指南
                    </a>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-network-wired display-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 项目使命说明 -->
<div class="container mb-5">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info border-0 shadow-sm">
                <div class="row align-items-center">
                    <div class="col-md-1 text-center">
                        <i class="fas fa-lightbulb fa-2x text-warning"></i>
                    </div>
                    <div class="col-md-11">
                        <h5 class="alert-heading mb-2">
                            <i class="fas fa-target me-2"></i>
                            项目使命
                        </h5>
                        <p class="mb-2">
                            <strong>解决智能变电站二次设计的实际痛点：</strong>
                            由于智能变电站采用IEC61850标准，传统的电气二次设计失去了直观性，
                            特别是虚端子的出现，使得传统的各种回路难以进行直观检查。
                        </p>
                        <p class="mb-0">
                            本项目旨在通过智能分析和可视化技术，帮助工程师快速诊断配置错误，
                            改正设计问题，提高智能变电站的设计质量和可靠性。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 核心功能卡片 -->
<div class="container mb-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="fw-bold">核心功能</h2>
            <p class="text-muted">专为智能变电站工程师设计的专业工具</p>
        </div>
    </div>
    
    <div class="row g-4">
        <!-- 文件验证 -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 shadow-sm border-0 feature-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-file-check fa-2x"></i>
                    </div>
                    <h5 class="card-title">智能文件验证</h5>
                    <p class="card-text text-muted">
                        自动解析SCD/ICD/CID文件，执行专业验证规则，
                        快速发现配置错误和潜在问题
                    </p>
                    <a href="{{ url_for('main.upload_page') }}" class="btn btn-outline-primary">
                        立即验证
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 可视化分析 -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 shadow-sm border-0 feature-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-project-diagram fa-2x"></i>
                    </div>
                    <h5 class="card-title">网络拓扑可视化</h5>
                    <p class="card-text text-muted">
                        将抽象的虚端子连接转化为直观的网络拓扑图，
                        让复杂的设备关系一目了然
                    </p>
                    <a href="{{ url_for('main.visualize_page') }}" class="btn btn-outline-success">
                        查看示例
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 回路追踪 -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 shadow-sm border-0 feature-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-route fa-2x"></i>
                    </div>
                    <h5 class="card-title">虚拟回路追踪</h5>
                    <p class="card-text text-muted">
                        追踪保护、测量、控制回路的数据流向，
                        恢复传统回路检查的直观性
                    </p>
                    <a href="{{ url_for('main.visualize_page') }}" class="btn btn-outline-warning">
                        了解更多
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 报告生成 -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 shadow-sm border-0 feature-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-info text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                    <h5 class="card-title">专业报告生成</h5>
                    <p class="card-text text-muted">
                        生成详细的验证报告，包含错误诊断、
                        修复建议和统计分析
                    </p>
                    <a href="{{ url_for('main.reports_page') }}" class="btn btn-outline-info">
                        查看报告
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4 mt-2">
        <!-- 图纸审查 -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 shadow-sm border-0 feature-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-purple text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-drafting-compass fa-2x"></i>
                    </div>
                    <h5 class="card-title">图纸审查</h5>
                    <p class="card-text text-muted">
                        审查二次图纸设计规范，支持DWG、DXF、PDF格式，
                        智能检测不符合标准的设计
                    </p>
                    <a href="{{ url_for('main.drawing_review_page') }}" class="btn btn-outline-purple">
                        开始审查
                    </a>
                </div>
            </div>
        </div>

        <!-- 统一审查 -->
        <div class="col-md-6 col-lg-3">
            <div class="card h-100 shadow-sm border-0 feature-card">
                <div class="card-body text-center p-4">
                    <div class="feature-icon bg-gradient text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-search-plus fa-2x"></i>
                    </div>
                    <h5 class="card-title">统一审查</h5>
                    <p class="card-text text-muted">
                        智能识别文件类型，统一审查配置文件和图纸，
                        一站式解决所有审查需求
                    </p>
                    <a href="{{ url_for('main.unified_review_page') }}" class="btn btn-outline-gradient">
                        开始审查
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 解决的问题 -->
<div class="bg-light py-5 mb-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="fw-bold">解决的实际问题</h2>
                <p class="text-muted">针对智能变电站设计中的具体挑战</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-6">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="problem-icon bg-danger text-white rounded-circle">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="text-danger">传统问题</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-times text-danger me-2"></i>虚端子连接缺乏直观性</li>
                            <li><i class="fas fa-times text-danger me-2"></i>传统回路检查方法失效</li>
                            <li><i class="fas fa-times text-danger me-2"></i>配置错误难以发现</li>
                            <li><i class="fas fa-times text-danger me-2"></i>专业知识门槛高</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="solution-icon bg-success text-white rounded-circle">
                            <i class="fas fa-check"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="text-success">我们的解决方案</h5>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>智能配置文件分析</li>
                            <li><i class="fas fa-check text-success me-2"></i>可视化网络拓扑展示</li>
                            <li><i class="fas fa-check text-success me-2"></i>自动错误诊断和修复建议</li>
                            <li><i class="fas fa-check text-success me-2"></i>直观的用户界面设计</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速开始 -->
<div class="container mb-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="fw-bold">快速开始</h2>
            <p class="text-muted">三步完成配置文件验证</p>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-4">
            <div class="text-center">
                <div class="step-number bg-primary text-white rounded-circle mx-auto mb-3">1</div>
                <h5>上传文件</h5>
                <p class="text-muted">
                    支持SCD、ICD、CID格式的IEC61850配置文件
                </p>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="text-center">
                <div class="step-number bg-primary text-white rounded-circle mx-auto mb-3">2</div>
                <h5>自动验证</h5>
                <p class="text-muted">
                    智能规则引擎自动执行专业验证规则
                </p>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="text-center">
                <div class="step-number bg-primary text-white rounded-circle mx-auto mb-3">3</div>
                <h5>查看结果</h5>
                <p class="text-muted">
                    获得详细的验证报告和修复建议
                </p>
            </div>
        </div>
    </div>
    
    <div class="row mt-5">
        <div class="col-12 text-center">
            <a href="{{ url_for('main.upload_page') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-rocket me-2"></i>
                立即开始验证
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.feature-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.problem-icon, .solution-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-number {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
}
</style>
{% endblock %}
