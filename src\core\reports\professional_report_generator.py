"""
专业级报告生成器
生成详尽的技术检查报告，包含完整的标准引用和详细的问题描述
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class StandardReference:
    """标准引用"""
    standard_number: str
    standard_title: str
    clause_number: str
    clause_title: str
    page_number: Optional[str] = None
    section_content: str = ""
    requirement_level: str = "强制性"  # 强制性、推荐性
    effective_date: Optional[str] = None
    
    def format_citation(self) -> str:
        """格式化引用"""
        citation = f"{self.standard_number} 《{self.standard_title}》"
        if self.clause_number:
            citation += f" 第{self.clause_number}条"
        if self.clause_title:
            citation += f" {self.clause_title}"
        if self.page_number:
            citation += f"（第{self.page_number}页）"
        return citation


@dataclass 
class TechnicalIssue:
    """技术问题"""
    issue_id: str
    issue_type: str
    severity: str  # critical, high, medium, low
    title: str
    
    # 详细问题描述
    problem_description: str
    technical_analysis: str
    root_cause_analysis: str
    impact_assessment: str
    risk_evaluation: str
    
    # 标准引用
    violated_standards: List[StandardReference] = field(default_factory=list)
    
    # 解决方案
    solution_description: str = ""
    implementation_steps: List[str] = field(default_factory=list)
    technical_requirements: List[str] = field(default_factory=list)
    verification_methods: List[str] = field(default_factory=list)
    
    # 位置信息
    location_path: str = ""
    affected_components: List[str] = field(default_factory=list)
    
    # 时间信息
    detected_time: datetime = field(default_factory=datetime.now)
    
    def format_detailed_description(self) -> str:
        """格式化详细描述"""
        return f"""
问题详细描述：
{self.problem_description}

技术分析：
{self.technical_analysis}

根本原因分析：
{self.root_cause_analysis}

影响评估：
{self.impact_assessment}

风险评价：
{self.risk_evaluation}
        """.strip()
    
    def format_solution_details(self) -> str:
        """格式化解决方案详情"""
        solution_text = f"解决方案描述：\n{self.solution_description}\n"
        
        if self.implementation_steps:
            solution_text += "\n实施步骤：\n"
            for i, step in enumerate(self.implementation_steps, 1):
                solution_text += f"{i}. {step}\n"
        
        if self.technical_requirements:
            solution_text += "\n技术要求：\n"
            for req in self.technical_requirements:
                solution_text += f"• {req}\n"
        
        if self.verification_methods:
            solution_text += "\n验证方法：\n"
            for method in self.verification_methods:
                solution_text += f"• {method}\n"
        
        return solution_text.strip()


class ProfessionalReportGenerator:
    """专业级报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.standard_database = self._load_standard_database()
        self.issue_templates = self._load_issue_templates()
        
        logger.info("专业级报告生成器初始化完成")
    
    def _load_standard_database(self) -> Dict[str, Any]:
        """加载标准数据库"""
        return {
            'GB14285-2023': {
                'full_title': '继电保护和安全自动装置技术规程',
                'clauses': {
                    '4.1.1': {
                        'title': '选择性要求',
                        'content': '继电保护装置应具有选择性，即仅切除故障元件，保证电力系统其他部分继续正常运行',
                        'page': '15',
                        'requirement_level': '强制性'
                    },
                    '4.1.2': {
                        'title': '速动性要求', 
                        'content': '继电保护装置应具有速动性，主保护动作时间应小于100ms，后备保护动作时间应小于500ms',
                        'page': '16',
                        'requirement_level': '强制性'
                    },
                    '5.2.1': {
                        'title': 'CT二次回路要求',
                        'content': '电流互感器二次回路在任何情况下都不得开路，应设置短路片或短路开关',
                        'page': '28',
                        'requirement_level': '强制性'
                    }
                }
            },
            'DL5136-2012': {
                'full_title': '火力发电厂、变电站二次接线设计技术规程',
                'clauses': {
                    '6.2.1': {
                        'title': '控制回路设计原则',
                        'content': '控制回路应简单可靠，操作电源宜采用直流220V或110V',
                        'page': '42',
                        'requirement_level': '推荐性'
                    },
                    '7.1.3': {
                        'title': 'CT二次负荷要求',
                        'content': '电流互感器二次负荷不应超过其额定二次负荷的75%',
                        'page': '58',
                        'requirement_level': '强制性'
                    }
                }
            }
        }  
  
    def _load_issue_templates(self) -> Dict[str, Any]:
        """加载问题模板"""
        return {
            'ct_secondary_open': {
                'title': 'CT二次回路开路',
                'problem_template': '''
在{location}发现电流互感器二次回路存在开路现象。具体表现为：
1. CT二次绕组与保护装置之间的连接回路不完整
2. 缺少必要的短路片或短路开关
3. 二次回路接线端子存在松动或断线
4. 回路中存在开路点，导致二次电流无法正常流通

该问题会导致CT二次侧产生高电压，不仅危及人身安全，还会影响保护装置的正常工作。
                ''',
                'technical_analysis_template': '''
电流互感器的工作原理基于电磁感应定律。当一次侧有电流流过时，会在铁芯中产生磁通，
进而在二次侧感应出电流。如果二次回路开路，二次电流为零，根据安培环路定律：
H₁I₁ = H₂I₂ = 0（当I₂=0时）

此时一次电流产生的磁通无法被二次电流的去磁作用抵消，导致：
1. 铁芯磁通密度急剧增加，可能达到饱和
2. 根据法拉第电磁感应定律 e = -dΦ/dt，二次侧会产生很高的感应电压
3. 电压可能达到数千伏，对设备和人员造成危险
4. 同时CT的测量精度完全丧失，保护装置无法获得正确的电流信息
                ''',
                'impact_assessment_template': '''
CT二次回路开路的影响包括：

安全影响：
• 二次侧高电压（可达数kV）对运维人员构成电击危险
• 可能引起CT二次设备损坏，产生火灾隐患
• 影响变电站整体安全运行水平

保护影响：
• 保护装置无法获得正确的电流信息，可能导致保护拒动
• 在系统故障时，保护装置可能无法正确动作，扩大事故范围
• 影响保护装置的"四性"要求，特别是可靠性和灵敏性

经济影响：
• 可能导致大面积停电，造成重大经济损失
• CT及相关设备损坏的维修成本
• 因保护拒动导致的设备损坏成本
                '''
            },
            'protection_coordination_error': {
                'title': '保护配合不当',
                'problem_template': '''
在{location}发现继电保护配合存在问题。具体表现为：
1. 保护动作时间级差不满足要求
2. 保护动作电流级差配合不当
3. 保护范围存在重叠或死区
4. 上下级保护之间缺乏有效配合

该问题违反了GB/T 14285-2023关于保护选择性的基本要求。
                ''',
                'technical_analysis_template': '''
保护配合是确保电力系统安全稳定运行的关键技术。根据保护配合原理：

时间配合原理：
• 下级保护动作时间 + 断路器动作时间 + 安全裕度 < 上级保护动作时间
• 一般要求时间级差Δt ≥ 0.3~0.5s
• 当前配置的时间级差为{current_time_diff}s，不满足要求

电流配合原理：
• 上级保护整定值应大于下级保护整定值的配合系数倍
• 配合系数Krel一般取1.2~1.3
• 当前配置的配合系数为{current_coordination_factor}，不满足要求

选择性分析：
• 保护范围应明确划分，避免重叠和死区
• 当前配置存在{overlap_zones}处重叠区域
                '''
            }
        }
    
    def generate_detailed_issue(self, issue_data: Dict[str, Any]) -> TechnicalIssue:
        """生成详细的技术问题"""
        
        issue_type = issue_data.get('type', 'unknown')
        template = self.issue_templates.get(issue_type, {})
        
        # 创建标准引用
        violated_standards = []
        for std_ref in issue_data.get('standard_violations', []):
            standard = self._create_standard_reference(
                std_ref['standard'], 
                std_ref['clause']
            )
            if standard:
                violated_standards.append(standard)
        
        # 生成详细问题
        issue = TechnicalIssue(
            issue_id=issue_data.get('id', f"issue_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            issue_type=issue_type,
            severity=issue_data.get('severity', 'medium'),
            title=template.get('title', issue_data.get('title', '未知问题')),
            
            problem_description=self._format_problem_description(issue_data, template),
            technical_analysis=self._format_technical_analysis(issue_data, template),
            root_cause_analysis=self._format_root_cause_analysis(issue_data),
            impact_assessment=self._format_impact_assessment(issue_data, template),
            risk_evaluation=self._format_risk_evaluation(issue_data),
            
            violated_standards=violated_standards,
            
            solution_description=self._generate_solution_description(issue_data),
            implementation_steps=self._generate_implementation_steps(issue_data),
            technical_requirements=self._generate_technical_requirements(issue_data),
            verification_methods=self._generate_verification_methods(issue_data),
            
            location_path=issue_data.get('location', ''),
            affected_components=issue_data.get('affected_components', [])
        )
        
        return issue
    
    def _create_standard_reference(self, standard_num: str, clause_num: str) -> Optional[StandardReference]:
        """创建标准引用"""
        
        std_data = self.standard_database.get(standard_num)
        if not std_data:
            return None
        
        clause_data = std_data['clauses'].get(clause_num)
        if not clause_data:
            return None
        
        return StandardReference(
            standard_number=standard_num,
            standard_title=std_data['full_title'],
            clause_number=clause_num,
            clause_title=clause_data['title'],
            page_number=clause_data.get('page'),
            section_content=clause_data['content'],
            requirement_level=clause_data.get('requirement_level', '强制性')
        )
    
    def _format_problem_description(self, issue_data: Dict[str, Any], template: Dict[str, Any]) -> str:
        """格式化问题描述"""
        
        problem_template = template.get('problem_template', '')
        if problem_template:
            return problem_template.format(
                location=issue_data.get('location', '未指定位置'),
                **issue_data
            ).strip()
        
        return issue_data.get('description', '问题描述缺失')
    
    def _format_technical_analysis(self, issue_data: Dict[str, Any], template: Dict[str, Any]) -> str:
        """格式化技术分析"""
        
        analysis_template = template.get('technical_analysis_template', '')
        if analysis_template:
            return analysis_template.format(**issue_data).strip()
        
        return "需要进一步的技术分析"
    
    def _format_root_cause_analysis(self, issue_data: Dict[str, Any]) -> str:
        """格式化根本原因分析"""
        
        root_causes = issue_data.get('root_causes', [])
        if root_causes:
            analysis = "根本原因分析：\n"
            for i, cause in enumerate(root_causes, 1):
                analysis += f"{i}. {cause}\n"
            return analysis.strip()
        
        return "根本原因：设计或施工过程中未严格按照相关标准执行"
    
    def _format_impact_assessment(self, issue_data: Dict[str, Any], template: Dict[str, Any]) -> str:
        """格式化影响评估"""
        
        impact_template = template.get('impact_assessment_template', '')
        if impact_template:
            return impact_template.format(**issue_data).strip()
        
        return "该问题可能影响系统的安全稳定运行"
    
    def _format_risk_evaluation(self, issue_data: Dict[str, Any]) -> str:
        """格式化风险评价"""
        
        severity = issue_data.get('severity', 'medium')
        
        risk_levels = {
            'critical': '极高风险：可能导致系统崩溃或重大安全事故',
            'high': '高风险：可能导致设备损坏或局部停电',
            'medium': '中等风险：可能影响系统正常运行',
            'low': '低风险：对系统运行影响较小'
        }
        
        return risk_levels.get(severity, '风险等级待评估')
    
    def _generate_solution_description(self, issue_data: Dict[str, Any]) -> str:
        """生成解决方案描述"""
        
        issue_type = issue_data.get('type', 'unknown')
        
        solutions = {
            'ct_secondary_open': '''
针对CT二次回路开路问题，应采取以下解决措施：

1. 立即断开相关CT一次侧电源，确保安全
2. 检查CT二次回路的完整性，查找开路点
3. 在CT二次侧安装短路片或短路开关
4. 重新连接断开的回路，确保接线牢固
5. 进行CT二次回路绝缘测试和回路完整性测试
6. 建立CT二次回路完整性监视系统

该解决方案严格按照GB/T 14285-2023第5.2.1条的要求执行。
            ''',
            'protection_coordination_error': '''
针对保护配合不当问题，应采取以下解决措施：

1. 重新进行系统短路计算，获得准确的故障电流数据
2. 根据保护配合原则，重新整定保护定值
3. 确保时间级差满足Δt ≥ 0.3~0.5s的要求
4. 确保电流级差满足配合系数Krel ≥ 1.2的要求
5. 进行保护配合校验，消除保护死区和重叠区
6. 更新保护装置定值并进行现场试验验证

该解决方案严格按照GB/T 14285-2023第4.1.1条选择性要求执行。
            '''
        }
        
        return solutions.get(issue_type, '需要制定针对性的解决方案').strip()
    
    def _generate_implementation_steps(self, issue_data: Dict[str, Any]) -> List[str]:
        """生成实施步骤"""
        
        issue_type = issue_data.get('type', 'unknown')
        
        steps_map = {
            'ct_secondary_open': [
                '制定详细的施工方案和安全措施',
                '申请停电工作票，确保作业安全',
                '使用万用表检查CT二次回路连续性',
                '安装符合标准的短路片或短路开关',
                '重新连接所有松动或断开的接线端子',
                '进行2500V绝缘电阻测试，阻值应大于1MΩ',
                '进行CT二次回路完整性测试',
                '恢复送电并验证保护装置工作正常',
                '更新相关技术档案和图纸'
            ],
            'protection_coordination_error': [
                '收集系统运行方式和设备参数',
                '使用专业软件进行短路电流计算',
                '根据计算结果重新整定保护定值',
                '在保护装置上修改定值设置',
                '进行保护装置校验试验',
                '验证保护配合的正确性',
                '更新保护定值单和相关文档',
                '对运行人员进行技术交底'
            ]
        }
        
        return steps_map.get(issue_type, ['制定解决方案', '实施整改措施', '验证整改效果'])
    
    def _generate_technical_requirements(self, issue_data: Dict[str, Any]) -> List[str]:
        """生成技术要求"""
        
        issue_type = issue_data.get('type', 'unknown')
        
        requirements_map = {
            'ct_secondary_open': [
                'CT二次回路绝缘电阻应大于1MΩ（DC 2500V测试）',
                '短路片额定电流应不小于CT额定二次电流的10倍',
                '接线端子压接力矩应符合制造商要求',
                '回路标识应清晰、准确、不易脱落',
                '应设置CT二次回路完整性监视装置'
            ],
            'protection_coordination_error': [
                '保护动作时间级差应满足Δt ≥ 0.3~0.5s',
                '保护电流配合系数应满足Krel ≥ 1.2',
                '保护范围应覆盖被保护设备的100%',
                '相邻保护区域重叠部分应小于10%',
                '保护装置精度等级应满足0.2级要求'
            ]
        }
        
        return requirements_map.get(issue_type, ['应符合相关标准要求'])
    
    def _generate_verification_methods(self, issue_data: Dict[str, Any]) -> List[str]:
        """生成验证方法"""
        
        issue_type = issue_data.get('type', 'unknown')
        
        methods_map = {
            'ct_secondary_open': [
                '使用数字万用表测量CT二次回路电阻，应小于4Ω',
                '使用绝缘电阻测试仪测量绝缘电阻，应大于1MΩ',
                '进行CT变比测试，误差应在±0.5%以内',
                '检查保护装置电流显示，应与实际电流一致',
                '进行保护装置自检，应无异常报警'
            ],
            'protection_coordination_error': [
                '使用继电保护测试仪验证保护动作时间',
                '进行保护装置整组试验，验证动作逻辑',
                '使用故障录波器记录实际故障时的动作情况',
                '进行保护配合分析，确认无死区和重叠',
                '定期进行保护装置定值核查'
            ]
        }
        
        return methods_map.get(issue_type, ['按照相关标准进行验证'])
    
    def generate_professional_report(self, issues: List[TechnicalIssue], 
                                   project_info: Dict[str, Any]) -> str:
        """生成专业报告"""
        
        report_header = self._generate_report_header(project_info)
        executive_summary = self._generate_executive_summary(issues)
        detailed_issues = self._generate_detailed_issues_section(issues)
        recommendations = self._generate_recommendations_section(issues)
        appendices = self._generate_appendices_section(issues)
        
        report = f"""
{report_header}

{executive_summary}

{detailed_issues}

{recommendations}

{appendices}
        """.strip()
        
        return report