"""
SCD文件解析模块 - 独立模块设计
负责IEC 61850 SCD文件的解析和验证
"""

import xml.etree.ElementTree as ET
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from pathlib import Path


@dataclass
class SCDParseResult:
    """SCD解析结果"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


@dataclass
class SCDValidationResult:
    """SCD验证结果"""
    is_valid: bool
    schema_errors: List[str] = None
    structure_errors: List[str] = None
    
    def __post_init__(self):
        if self.schema_errors is None:
            self.schema_errors = []
        if self.structure_errors is None:
            self.structure_errors = []


class SCDParser:
    """SCD文件解析器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化SCD解析器
        
        Args:
            config: 配置参数，包含解析选项
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def parse_scd_file(self, file_path: Union[str, Path]) -> SCDParseResult:
        """
        解析SCD文件
        
        Args:
            file_path: SCD文件路径
            
        Returns:
            SCDParseResult: 解析结果
        """
        try:
            file_path = Path(file_path)
            
            # 检查文件存在性
            if not file_path.exists():
                return SCDParseResult(
                    success=False,
                    errors=[f"文件不存在: {file_path}"]
                )
            
            # 解析XML文件
            self.logger.info(f"开始解析SCD文件: {file_path}")
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # 验证根元素 (考虑命名空间)
            if not (root.tag == 'SCL' or root.tag.endswith('}SCL')):
                return SCDParseResult(
                    success=False,
                    errors=[f"根元素不是SCL，实际为: {root.tag}，不是有效的SCD文件"]
                )
            
            # 提取SCD数据
            scd_data = self._extract_scd_data(root)
            
            self.logger.info("SCD文件解析完成")
            return SCDParseResult(
                success=True,
                data=scd_data
            )
            
        except ET.ParseError as e:
            error_msg = f"XML解析错误: {e}"
            self.logger.error(error_msg)
            return SCDParseResult(
                success=False,
                errors=[error_msg]
            )
        except Exception as e:
            error_msg = f"解析过程中发生错误: {e}"
            self.logger.error(error_msg)
            return SCDParseResult(
                success=False,
                errors=[error_msg]
            )
    
    def _extract_scd_data(self, root: ET.Element) -> Dict[str, Any]:
        """
        从XML根元素提取SCD数据
        
        Args:
            root: XML根元素
            
        Returns:
            Dict: 提取的SCD数据
        """
        scd_data = {
            'header': self._extract_header(root),
            'substation': self._extract_substation(root),
            'ieds': self._extract_ieds(root),
            'communication': self._extract_communication(root),
            'data_type_templates': self._extract_data_type_templates(root)
        }
        
        return scd_data
    
    def _extract_header(self, root: ET.Element) -> Dict[str, Any]:
        """提取Header信息"""
        header_elem = root.find('Header')
        if header_elem is None:
            return {}
        
        return {
            'id': header_elem.get('id', ''),
            'version': header_elem.get('version', ''),
            'revision': header_elem.get('revision', ''),
            'toolID': header_elem.get('toolID', ''),
            'nameStructure': header_elem.get('nameStructure', 'IEDName')
        }
    
    def _extract_substation(self, root: ET.Element) -> Dict[str, Any]:
        """提取Substation信息"""
        substation_elem = root.find('Substation')
        if substation_elem is None:
            return {}
        
        return {
            'name': substation_elem.get('name', ''),
            'desc': substation_elem.get('desc', ''),
            'voltage_levels': self._extract_voltage_levels(substation_elem)
        }
    
    def _extract_voltage_levels(self, substation_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取电压等级信息"""
        voltage_levels = []
        
        for vl_elem in substation_elem.findall('VoltageLevel'):
            vl_data = {
                'name': vl_elem.get('name', ''),
                'desc': vl_elem.get('desc', ''),
                'nomFreq': vl_elem.get('nomFreq', '50'),
                'numPhases': vl_elem.get('numPhases', '3'),
                'bays': self._extract_bays(vl_elem)
            }
            voltage_levels.append(vl_data)
        
        return voltage_levels
    
    def _extract_bays(self, vl_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取间隔信息"""
        bays = []
        
        for bay_elem in vl_elem.findall('Bay'):
            bay_data = {
                'name': bay_elem.get('name', ''),
                'desc': bay_elem.get('desc', ''),
                'conducting_equipment': self._extract_conducting_equipment(bay_elem)
            }
            bays.append(bay_data)
        
        return bays
    
    def _extract_conducting_equipment(self, bay_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取导电设备信息"""
        equipment = []
        
        for eq_elem in bay_elem.findall('ConductingEquipment'):
            eq_data = {
                'name': eq_elem.get('name', ''),
                'type': eq_elem.get('type', ''),
                'desc': eq_elem.get('desc', '')
            }
            equipment.append(eq_data)
        
        return equipment
    
    def _extract_ieds(self, root: ET.Element) -> List[Dict[str, Any]]:
        """提取IED信息"""
        ieds = []
        
        for ied_elem in root.findall('IED'):
            ied_data = {
                'name': ied_elem.get('name', ''),
                'type': ied_elem.get('type', ''),
                'manufacturer': ied_elem.get('manufacturer', ''),
                'configVersion': ied_elem.get('configVersion', ''),
                'logical_devices': self._extract_logical_devices(ied_elem)
            }
            ieds.append(ied_data)
        
        return ieds
    
    def _extract_logical_devices(self, ied_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取逻辑设备信息"""
        logical_devices = []
        
        access_point = ied_elem.find('AccessPoint')
        if access_point is not None:
            server = access_point.find('Server')
            if server is not None:
                for ld_elem in server.findall('LDevice'):
                    ld_data = {
                        'inst': ld_elem.get('inst', ''),
                        'desc': ld_elem.get('desc', ''),
                        'logical_nodes': self._extract_logical_nodes(ld_elem)
                    }
                    logical_devices.append(ld_data)
        
        return logical_devices
    
    def _extract_logical_nodes(self, ld_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取逻辑节点信息"""
        logical_nodes = []
        
        for ln_elem in ld_elem.findall('LN'):
            ln_data = {
                'lnClass': ln_elem.get('lnClass', ''),
                'inst': ln_elem.get('inst', ''),
                'prefix': ln_elem.get('prefix', ''),
                'desc': ln_elem.get('desc', ''),
                'lnType': ln_elem.get('lnType', '')
            }
            logical_nodes.append(ln_data)
        
        # 也处理LN0
        ln0_elem = ld_elem.find('LN0')
        if ln0_elem is not None:
            ln0_data = {
                'lnClass': 'LLN0',
                'inst': '',
                'prefix': '',
                'desc': ln0_elem.get('desc', ''),
                'lnType': ln0_elem.get('lnType', '')
            }
            logical_nodes.insert(0, ln0_data)
        
        return logical_nodes
    
    def _extract_communication(self, root: ET.Element) -> Dict[str, Any]:
        """提取Communication信息"""
        comm_elem = root.find('Communication')
        if comm_elem is None:
            return {}
        
        return {
            'subnets': self._extract_subnets(comm_elem)
        }
    
    def _extract_subnets(self, comm_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取子网信息"""
        subnets = []
        
        for subnet_elem in comm_elem.findall('SubNetwork'):
            subnet_data = {
                'name': subnet_elem.get('name', ''),
                'type': subnet_elem.get('type', ''),
                'desc': subnet_elem.get('desc', ''),
                'connected_aps': self._extract_connected_aps(subnet_elem)
            }
            subnets.append(subnet_data)
        
        return subnets
    
    def _extract_connected_aps(self, subnet_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取连接的接入点信息"""
        connected_aps = []
        
        for ap_elem in subnet_elem.findall('ConnectedAP'):
            ap_data = {
                'iedName': ap_elem.get('iedName', ''),
                'apName': ap_elem.get('apName', ''),
                'desc': ap_elem.get('desc', ''),
                'gse': self._extract_gse(ap_elem),
                'smv': self._extract_smv(ap_elem)
            }
            connected_aps.append(ap_data)
        
        return connected_aps
    
    def _extract_gse(self, ap_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取GSE信息"""
        gse_list = []
        
        for gse_elem in ap_elem.findall('GSE'):
            gse_data = {
                'cbName': gse_elem.get('cbName', ''),
                'ldInst': gse_elem.get('ldInst', ''),
                'desc': gse_elem.get('desc', ''),
                'address': self._extract_address(gse_elem)
            }
            gse_list.append(gse_data)
        
        return gse_list
    
    def _extract_smv(self, ap_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取SMV信息"""
        smv_list = []
        
        for smv_elem in ap_elem.findall('SMV'):
            smv_data = {
                'cbName': smv_elem.get('cbName', ''),
                'ldInst': smv_elem.get('ldInst', ''),
                'desc': smv_elem.get('desc', ''),
                'address': self._extract_address(smv_elem)
            }
            smv_list.append(smv_data)
        
        return smv_list
    
    def _extract_address(self, elem: ET.Element) -> Dict[str, Any]:
        """提取地址信息"""
        address_elem = elem.find('Address')
        if address_elem is None:
            return {}
        
        address_data = {}
        for p_elem in address_elem.findall('P'):
            p_type = p_elem.get('type', '')
            p_value = p_elem.text or ''
            address_data[p_type] = p_value
        
        return address_data
    
    def _extract_data_type_templates(self, root: ET.Element) -> Dict[str, Any]:
        """提取DataTypeTemplates信息"""
        dtt_elem = root.find('DataTypeTemplates')
        if dtt_elem is None:
            return {}
        
        return {
            'lnode_types': self._extract_lnode_types(dtt_elem),
            'do_types': self._extract_do_types(dtt_elem),
            'da_types': self._extract_da_types(dtt_elem),
            'enum_types': self._extract_enum_types(dtt_elem)
        }
    
    def _extract_lnode_types(self, dtt_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取LNodeType信息"""
        lnode_types = []
        
        for lnt_elem in dtt_elem.findall('LNodeType'):
            lnt_data = {
                'id': lnt_elem.get('id', ''),
                'lnClass': lnt_elem.get('lnClass', ''),
                'desc': lnt_elem.get('desc', ''),
                'data_objects': []
            }
            
            for do_elem in lnt_elem.findall('DO'):
                do_data = {
                    'name': do_elem.get('name', ''),
                    'type': do_elem.get('type', ''),
                    'desc': do_elem.get('desc', '')
                }
                lnt_data['data_objects'].append(do_data)
            
            lnode_types.append(lnt_data)
        
        return lnode_types
    
    def _extract_do_types(self, dtt_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取DOType信息"""
        do_types = []
        
        for dot_elem in dtt_elem.findall('DOType'):
            dot_data = {
                'id': dot_elem.get('id', ''),
                'cdc': dot_elem.get('cdc', ''),
                'desc': dot_elem.get('desc', ''),
                'data_attributes': []
            }
            
            for da_elem in dot_elem.findall('DA'):
                da_data = {
                    'name': da_elem.get('name', ''),
                    'fc': da_elem.get('fc', ''),
                    'bType': da_elem.get('bType', ''),
                    'type': da_elem.get('type', ''),
                    'desc': da_elem.get('desc', '')
                }
                dot_data['data_attributes'].append(da_data)
            
            do_types.append(dot_data)
        
        return do_types
    
    def _extract_da_types(self, dtt_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取DAType信息"""
        da_types = []
        
        for dat_elem in dtt_elem.findall('DAType'):
            dat_data = {
                'id': dat_elem.get('id', ''),
                'desc': dat_elem.get('desc', ''),
                'bdas': []
            }
            
            for bda_elem in dat_elem.findall('BDA'):
                bda_data = {
                    'name': bda_elem.get('name', ''),
                    'bType': bda_elem.get('bType', ''),
                    'type': bda_elem.get('type', ''),
                    'desc': bda_elem.get('desc', '')
                }
                dat_data['bdas'].append(bda_data)
            
            da_types.append(dat_data)
        
        return da_types
    
    def _extract_enum_types(self, dtt_elem: ET.Element) -> List[Dict[str, Any]]:
        """提取EnumType信息"""
        enum_types = []
        
        for et_elem in dtt_elem.findall('EnumType'):
            et_data = {
                'id': et_elem.get('id', ''),
                'desc': et_elem.get('desc', ''),
                'enum_vals': []
            }
            
            for ev_elem in et_elem.findall('EnumVal'):
                ev_data = {
                    'ord': ev_elem.get('ord', ''),
                    'desc': ev_elem.get('desc', ''),
                    'value': ev_elem.text or ''
                }
                et_data['enum_vals'].append(ev_data)
            
            enum_types.append(et_data)
        
        return enum_types
    
    def validate_scd_schema(self, scd_data: Dict[str, Any]) -> SCDValidationResult:
        """
        验证SCD数据的Schema符合性
        
        Args:
            scd_data: SCD数据
            
        Returns:
            SCDValidationResult: 验证结果
        """
        try:
            schema_errors = []
            structure_errors = []
            
            # 检查必要的顶级元素
            required_elements = ['header', 'substation', 'ieds']
            for element in required_elements:
                if element not in scd_data or not scd_data[element]:
                    schema_errors.append(f"缺少必要元素: {element}")
            
            # 检查Header结构
            if 'header' in scd_data:
                header = scd_data['header']
                if not header.get('id'):
                    structure_errors.append("Header缺少id属性")
                if not header.get('version'):
                    structure_errors.append("Header缺少version属性")
            
            # 检查IED结构
            if 'ieds' in scd_data:
                for i, ied in enumerate(scd_data['ieds']):
                    if not ied.get('name'):
                        structure_errors.append(f"IED[{i}]缺少name属性")
                    if not ied.get('logical_devices'):
                        structure_errors.append(f"IED[{i}]缺少逻辑设备配置")
            
            is_valid = len(schema_errors) == 0 and len(structure_errors) == 0
            
            return SCDValidationResult(
                is_valid=is_valid,
                schema_errors=schema_errors,
                structure_errors=structure_errors
            )
            
        except Exception as e:
            self.logger.error(f"Schema验证过程中发生错误: {e}")
            return SCDValidationResult(
                is_valid=False,
                schema_errors=[f"验证过程错误: {e}"]
            )


def main():
    """主函数 - 模块测试"""
    # 创建测试用的SCD解析器
    parser = SCDParser()
    
    print("SCD解析模块独立测试")
    print("=" * 40)
    
    # 测试配置
    test_config = {
        'validate_schema': True,
        'extract_full_data': True
    }
    
    print(f"配置参数: {test_config}")
    print("模块初始化成功")
    
    # 模拟SCD数据验证
    mock_scd_data = {
        'header': {'id': 'test', 'version': '2007'},
        'substation': {'name': 'TestSubstation'},
        'ieds': [{'name': 'TestIED', 'logical_devices': []}]
    }
    
    validation_result = parser.validate_scd_schema(mock_scd_data)
    print(f"Schema验证结果: {validation_result.is_valid}")
    
    return {
        'module_name': 'SCD解析模块',
        'status': 'success',
        'validation_result': validation_result
    }


if __name__ == "__main__":
    result = main()
    print(f"模块测试完成: {result}")