# 二次图纸审查功能

## 功能概述

本系统新增了专业的二次图纸审查功能，支持对DWG、DXF、PDF格式的图纸文件进行智能规范检查，帮助工程师快速发现设计中不符合标准的问题。

## 主要特性

### 1. 多格式支持
- **DXF格式**: 完全支持，基于ezdxf库解析
- **DWG格式**: 框架已准备，需要额外库支持
- **PDF格式**: 框架已准备，需要PDF处理库

### 2. 智能规范检查
- **线型标准**: 检查线型是否符合GB/T 4457.4标准
- **线宽规范**: 验证线宽比例关系
- **文字标注**: 检查文字高度、字体等规范
- **图层管理**: 验证图层命名和使用规范
- **尺寸标注**: 检查尺寸标注的完整性
- **设备符号**: 识别和验证电气设备符号

### 3. 可视化展示
- **交互式查看器**: 在线查看图纸和问题位置
- **SVG渲染**: 矢量图形显示
- **问题标记**: 直观显示问题位置
- **分类过滤**: 按严重程度过滤问题

### 4. 专业报告
- **HTML报告**: 详细的审查报告
- **JSON数据**: 结构化的问题数据
- **统计分析**: 合规性评分和统计

## 技术架构

### 核心模块

1. **drawing_models.py**: 图纸数据模型
   - DrawingDocument: 图纸文档
   - DrawingElement: 图元基类
   - ReviewIssue: 审查问题
   - ReviewResult: 审查结果

2. **drawing_parser.py**: 图纸解析引擎
   - DXFParser: DXF文件解析器
   - DWGParser: DWG文件解析器
   - PDFParser: PDF文件解析器
   - DrawingParserFactory: 解析器工厂

3. **standards_knowledge.py**: 规范知识库
   - StandardsKnowledgeBase: 标准规范库
   - StandardRule: 规范规则定义
   - 内置GB/T、DL/T等标准

4. **drawing_analyzer.py**: 智能分析器
   - DrawingAnalyzer: 图纸内容分析
   - 元素识别和分类
   - 布局和连接分析

5. **compliance_checker.py**: 规范检查器
   - ComplianceChecker: 规范检查引擎
   - ComplianceRule: 检查规则基类
   - 多种专业检查规则

6. **review_engine.py**: 审查引擎
   - DrawingReviewEngine: 主审查引擎
   - 整合解析、分析、检查功能
   - 自动修复建议

7. **visualization.py**: 可视化模块
   - DrawingVisualizer: 可视化生成器
   - SVG和HTML输出
   - 交互式查看器

### Web集成

- **API端点**: `/api/drawing-review/*`
- **前端页面**: `/drawing-review`
- **文件上传**: 支持拖拽上传
- **实时预览**: 即时显示审查结果

## 使用方法

### 1. 启动应用
```bash
python run_app.py
```

### 2. 访问图纸审查
打开浏览器访问: `http://localhost:5000/drawing-review`

### 3. 上传图纸
- 拖拽文件到上传区域
- 或点击选择文件按钮
- 支持最大50MB文件

### 4. 配置检查选项
- 选择检查分类
- 设置严重程度过滤
- 点击"开始审查"

### 5. 查看结果
- 查看统计概览
- 浏览问题列表
- 使用可视化查看器
- 导出审查报告

## API接口

### 上传文件
```
POST /api/drawing-review/upload
Content-Type: multipart/form-data
```

### 执行审查
```
POST /api/drawing-review/review
Content-Type: application/json
{
  "file_path": "path/to/file",
  "check_categories": ["线型", "文字标注"],
  "severity_filter": "warning"
}
```

### 生成可视化
```
POST /api/drawing-review/visualization
Content-Type: application/json
{
  "file_path": "path/to/file",
  "type": "interactive"
}
```

### 获取支持格式
```
GET /api/drawing-review/formats
```

### 获取检查分类
```
GET /api/drawing-review/categories
```

## 配置要求

### Python依赖
```
flask>=2.0.0
ezdxf>=0.17.0  # DXF文件支持
PyPDF2>=2.0.0  # PDF文件支持（可选）
pdfplumber>=0.6.0  # PDF文件支持（可选）
```

### 安装依赖
```bash
pip install ezdxf PyPDF2 pdfplumber
```

## 标准规范

### 支持的标准
- **GB/T 4457.4-2002**: 机械制图图线标准
- **GB/T 14665-2012**: 图纸幅面和格式
- **DL/T 5136-2012**: 火力发电厂二次系统设计技术规程

### 检查规则
1. **线型规范**: 外轮廓线、中心线、隐藏线等
2. **线宽标准**: 粗线、中线、细线比例关系
3. **文字规范**: 高度、字体、间距标准
4. **图层管理**: 命名规范、图层用途
5. **尺寸标注**: 标注完整性和格式
6. **设备符号**: 标准电气符号库

## 扩展开发

### 添加新的检查规则
1. 在`standards_knowledge.py`中定义规则
2. 在`compliance_checker.py`中实现检查逻辑
3. 注册到规范检查器

### 支持新的文件格式
1. 在`drawing_parser.py`中实现新的解析器
2. 继承`DrawingParser`基类
3. 注册到解析器工厂

### 自定义可视化
1. 扩展`visualization.py`中的方法
2. 添加新的渲染模式
3. 自定义样式和交互

## 测试验证

运行测试脚本验证功能:
```bash
python test_drawing_review.py
```

测试内容包括:
- 解析器工厂测试
- 标准知识库测试
- 审查引擎测试
- 可视化功能测试
- API集成测试

## 注意事项

1. **文件格式支持**: 当前完全支持DXF格式，DWG和PDF需要额外库
2. **性能考虑**: 大文件可能需要较长处理时间
3. **标准更新**: 规范标准可能需要定期更新
4. **自定义规则**: 可根据项目需求添加特定检查规则

## 未来规划

1. **完善DWG支持**: 集成ODA File Converter
2. **增强PDF解析**: 提升PDF图纸识别能力
3. **AI辅助识别**: 使用机器学习识别设备符号
4. **云端服务**: 支持云端图纸审查
5. **移动端支持**: 开发移动端查看器

## 技术支持

如有问题或建议，请联系开发团队或提交Issue。
