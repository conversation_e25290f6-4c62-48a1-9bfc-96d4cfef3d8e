"""
图纸审查API端点
提供图纸审查相关的RESTful API
"""

import os
import logging
from flask import Blueprint, request, jsonify, send_file
from werkzeug.utils import secure_filename
import tempfile
from pathlib import Path

from ...drawing_review.review_engine import DrawingReviewEngine
from ...drawing_review.visualization import DrawingVisualizer, create_review_dashboard
from ...drawing_review.drawing_models import ReviewSeverity

logger = logging.getLogger(__name__)

# 创建蓝图
drawing_review_bp = Blueprint('drawing_review', __name__, url_prefix='/api/drawing-review')

# 初始化审查引擎
review_engine = DrawingReviewEngine()
visualizer = DrawingVisualizer()

# 支持的文件格式
ALLOWED_EXTENSIONS = {'.dwg', '.dxf', '.pdf'}


def allowed_file(filename):
    """检查文件格式是否支持"""
    return Path(filename).suffix.lower() in ALLOWED_EXTENSIONS


@drawing_review_bp.route('/supported-formats', methods=['GET'])
def get_supported_formats():
    """获取支持的文件格式"""
    try:
        formats = review_engine.get_supported_formats()
        return jsonify({
            'success': True,
            'data': {
                'formats': formats,
                'descriptions': {
                    '.dwg': 'AutoCAD图纸文件',
                    '.dxf': 'AutoCAD交换格式文件',
                    '.pdf': 'PDF图纸文件'
                }
            }
        })
    except Exception as e:
        logger.error(f"获取支持格式失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/upload', methods=['POST'])
def upload_drawing():
    """上传图纸文件"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({
                'success': False, 
                'error': f'不支持的文件格式，支持的格式: {", ".join(ALLOWED_EXTENSIONS)}'
            }), 400
        
        # 保存文件到临时目录
        filename = secure_filename(file.filename)
        temp_dir = tempfile.mkdtemp()
        file_path = os.path.join(temp_dir, filename)
        file.save(file_path)
        
        # 返回文件信息
        file_info = {
            'filename': filename,
            'file_path': file_path,
            'file_size': os.path.getsize(file_path),
            'file_type': Path(filename).suffix.lower()
        }
        
        return jsonify({
            'success': True,
            'data': file_info,
            'message': '文件上传成功'
        })
        
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/review', methods=['POST'])
def review_drawing():
    """审查图纸"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        check_categories = data.get('check_categories', [])
        severity_filter = data.get('severity_filter')
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400
        
        # 转换严重程度过滤器
        severity_enum = None
        if severity_filter:
            try:
                severity_enum = ReviewSeverity(severity_filter)
            except ValueError:
                return jsonify({'success': False, 'error': '无效的严重程度过滤器'}), 400
        
        # 执行审查
        review_result = review_engine.review_drawing(
            file_path=file_path,
            check_categories=check_categories if check_categories else None,
            severity_filter=severity_enum
        )
        
        if not review_result:
            return jsonify({'success': False, 'error': '图纸审查失败'}), 500
        
        # 生成响应数据
        response_data = {
            'review_id': review_result.review_id,
            'summary': review_result.get_summary(),
            'issues': [
                {
                    'issue_id': issue.issue_id,
                    'severity': issue.severity.value,
                    'category': issue.category,
                    'title': issue.title,
                    'description': issue.description,
                    'suggestion': issue.suggestion,
                    'auto_fixable': issue.auto_fixable,
                    'standard_reference': issue.standard_reference,
                    'standard_clause': issue.standard_clause,
                    'location': {
                        'x': issue.location.x,
                        'y': issue.location.y
                    } if issue.location else None
                }
                for issue in review_result.issues
            ]
        }
        
        return jsonify({
            'success': True,
            'data': response_data,
            'message': f'图纸审查完成，发现 {len(review_result.issues)} 个问题'
        })
        
    except Exception as e:
        logger.error(f"图纸审查失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/categories', methods=['GET'])
def get_check_categories():
    """获取检查分类"""
    try:
        categories = review_engine.get_available_check_categories()
        return jsonify({
            'success': True,
            'data': {
                'categories': categories,
                'descriptions': {
                    '线型': '检查线型是否符合标准',
                    '线宽': '检查线宽是否符合规范',
                    '文字标注': '检查文字标注格式',
                    '图层': '检查图层命名和使用',
                    '尺寸标注': '检查尺寸标注规范',
                    '设备符号': '检查电气设备符号',
                    '连接': '检查线路连接规范'
                }
            }
        })
    except Exception as e:
        logger.error(f"获取检查分类失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/standards', methods=['GET'])
def get_standards_info():
    """获取标准信息"""
    try:
        standards_summary = review_engine.get_standards_summary()
        return jsonify({
            'success': True,
            'data': standards_summary
        })
    except Exception as e:
        logger.error(f"获取标准信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/auto-fix', methods=['POST'])
def auto_fix_issues():
    """自动修复问题"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400
        
        # 先执行审查
        review_result = review_engine.review_drawing(file_path)
        if not review_result:
            return jsonify({'success': False, 'error': '图纸审查失败'}), 500
        
        # 执行自动修复
        fix_result = review_engine.auto_fix_issues(review_result)
        
        return jsonify({
            'success': True,
            'data': fix_result,
            'message': f'自动修复完成，修复了 {fix_result["fixed_issues"]} 个问题'
        })
        
    except Exception as e:
        logger.error(f"自动修复失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/visualization', methods=['POST'])
def generate_visualization():
    """生成可视化"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        visualization_type = data.get('type', 'interactive')  # 'svg' 或 'interactive'
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400
        
        # 执行审查
        review_result = review_engine.review_drawing(file_path)
        if not review_result:
            return jsonify({'success': False, 'error': '图纸审查失败'}), 500
        
        # 生成可视化
        if visualization_type == 'svg':
            content = visualizer.generate_review_visualization(review_result)
            content_type = 'image/svg+xml'
        else:
            content = visualizer.generate_interactive_viewer(review_result)
            content_type = 'text/html'
        
        return jsonify({
            'success': True,
            'data': {
                'content': content,
                'content_type': content_type,
                'review_summary': review_result.get_summary()
            }
        })
        
    except Exception as e:
        logger.error(f"生成可视化失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/report', methods=['POST'])
def generate_report():
    """生成审查报告"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        report_format = data.get('format', 'html')  # 'html', 'json', 'pdf'
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件路径无效'}), 400
        
        # 执行审查
        review_result = review_engine.review_drawing(file_path)
        if not review_result:
            return jsonify({'success': False, 'error': '图纸审查失败'}), 500
        
        # 生成报告
        report_content = review_engine.generate_review_report(review_result, report_format)
        
        if not report_content:
            return jsonify({'success': False, 'error': '报告生成失败'}), 500
        
        # 确定内容类型
        content_types = {
            'html': 'text/html',
            'json': 'application/json',
            'pdf': 'application/pdf'
        }
        
        return jsonify({
            'success': True,
            'data': {
                'content': report_content,
                'content_type': content_types.get(report_format, 'text/plain'),
                'format': report_format
            }
        })
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/batch-review', methods=['POST'])
def batch_review():
    """批量审查图纸"""
    try:
        data = request.get_json()
        file_paths = data.get('file_paths', [])
        
        if not file_paths:
            return jsonify({'success': False, 'error': '没有提供文件路径'}), 400
        
        # 验证文件路径
        valid_paths = []
        for path in file_paths:
            if os.path.exists(path) and allowed_file(path):
                valid_paths.append(path)
        
        if not valid_paths:
            return jsonify({'success': False, 'error': '没有有效的文件路径'}), 400
        
        # 批量审查
        review_results = review_engine.review_multiple_drawings(valid_paths)
        
        # 生成响应数据
        results_data = []
        for result in review_results:
            results_data.append({
                'file_path': result.drawing_document.file_path,
                'review_id': result.review_id,
                'summary': result.get_summary()
            })
        
        # 生成仪表板
        dashboard_html = create_review_dashboard(review_results)
        
        return jsonify({
            'success': True,
            'data': {
                'results': results_data,
                'dashboard': dashboard_html,
                'total_files': len(valid_paths),
                'successful_reviews': len(review_results)
            },
            'message': f'批量审查完成，成功审查 {len(review_results)}/{len(valid_paths)} 个文件'
        })
        
    except Exception as e:
        logger.error(f"批量审查失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.route('/engine-stats', methods=['GET'])
def get_engine_stats():
    """获取引擎统计信息"""
    try:
        stats = review_engine.get_engine_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取引擎统计失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500


@drawing_review_bp.errorhandler(413)
def file_too_large(error):
    """文件过大错误处理"""
    return jsonify({
        'success': False,
        'error': '文件过大，请上传小于50MB的文件'
    }), 413


@drawing_review_bp.errorhandler(Exception)
def handle_exception(error):
    """全局异常处理"""
    logger.error(f"API异常: {error}")
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500
