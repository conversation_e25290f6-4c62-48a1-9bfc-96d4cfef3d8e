"""
合并工具
提供智能的配置文件合并功能，处理冲突解决
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import xml.etree.ElementTree as ET
import copy

from .comparison_models import (
    ComparisonResult, DiffItem, DiffType, DiffCategory,
    MergeConflict, MergeResult
)
from .file_comparator import FileComparator
from ..core.parsers.scd_parser import SCDParser


logger = logging.getLogger(__name__)


class MergeTool:
    """合并工具"""
    
    def __init__(self):
        """初始化合并工具"""
        self.comparator = FileComparator()
        self.parser = SCDParser()
        self.merge_strategies = self._load_merge_strategies()
        self.conflict_resolvers = self._load_conflict_resolvers()
        
        logger.info("合并工具初始化完成")
    
    def merge_files(self,
                   source_file: str,
                   target_file: str,
                   output_file: str,
                   merge_options: Optional[Dict[str, Any]] = None) -> MergeResult:
        """
        合并两个文件
        
        Args:
            source_file: 源文件路径
            target_file: 目标文件路径
            output_file: 输出文件路径
            merge_options: 合并选项
            
        Returns:
            MergeResult: 合并结果
        """
        try:
            logger.info(f"开始合并文件: {source_file} + {target_file} -> {output_file}")
            
            # 创建合并结果
            merge_id = f"merge_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            result = MergeResult(
                merge_id=merge_id,
                source_file=source_file,
                target_file=target_file,
                output_file=output_file
            )
            
            # 解析文件
            source_data = self._parse_file(source_file)
            target_data = self._parse_file(target_file)
            
            if not source_data or not target_data:
                result.success = False
                result.error_message = "文件解析失败"
                return result
            
            # 对比文件找出差异
            comparison = self.comparator.compare_parsed_data(
                source_data, target_data, source_file, target_file
            )
            
            # 执行合并
            merged_data, conflicts = self._perform_merge(
                source_data, target_data, comparison, merge_options or {}
            )
            
            # 处理冲突
            for conflict in conflicts:
                result.add_conflict(conflict)
            
            # 生成输出文件
            if merged_data:
                success = self._write_merged_file(merged_data, output_file)
                if not success:
                    result.success = False
                    result.error_message = "输出文件写入失败"
            else:
                result.success = False
                result.error_message = "合并数据生成失败"
            
            # 生成统计信息
            result.merge_statistics = self._generate_merge_statistics(
                comparison, conflicts, merged_data
            )
            
            logger.info(f"文件合并完成: {len(conflicts)} 个冲突")
            return result
            
        except Exception as e:
            logger.error(f"文件合并失败: {e}")
            return MergeResult(
                merge_id="error",
                source_file=source_file,
                target_file=target_file,
                output_file=output_file,
                success=False,
                error_message=str(e)
            )
    
    def merge_with_resolution(self,
                            source_file: str,
                            target_file: str,
                            output_file: str,
                            conflict_resolutions: Dict[str, str],
                            merge_options: Optional[Dict[str, Any]] = None) -> MergeResult:
        """
        使用冲突解决方案进行合并
        
        Args:
            source_file: 源文件路径
            target_file: 目标文件路径
            output_file: 输出文件路径
            conflict_resolutions: 冲突解决方案 {conflict_id: resolution}
            merge_options: 合并选项
            
        Returns:
            MergeResult: 合并结果
        """
        try:
            # 首先进行常规合并
            result = self.merge_files(source_file, target_file, output_file, merge_options)
            
            # 应用冲突解决方案
            for conflict_id, resolution in conflict_resolutions.items():
                if result.resolve_conflict(conflict_id, resolution):
                    logger.debug(f"解决冲突: {conflict_id} -> {resolution}")
            
            # 如果所有冲突都已解决，重新生成输出文件
            if not result.has_unresolved_conflicts():
                # 重新解析和合并
                source_data = self._parse_file(source_file)
                target_data = self._parse_file(target_file)
                
                merged_data = self._apply_resolutions(
                    source_data, target_data, result.resolved_conflicts
                )
                
                if merged_data:
                    self._write_merged_file(merged_data, output_file)
                    result.success = True
                    logger.info("所有冲突已解决，合并完成")
            
            return result
            
        except Exception as e:
            logger.error(f"冲突解决合并失败: {e}")
            result.success = False
            result.error_message = str(e)
            return result
    
    def preview_merge(self,
                     source_file: str,
                     target_file: str,
                     merge_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        预览合并结果
        
        Args:
            source_file: 源文件路径
            target_file: 目标文件路径
            merge_options: 合并选项
            
        Returns:
            Dict[str, Any]: 预览结果
        """
        try:
            # 解析文件
            source_data = self._parse_file(source_file)
            target_data = self._parse_file(target_file)
            
            # 对比文件
            comparison = self.comparator.compare_parsed_data(
                source_data, target_data, source_file, target_file
            )
            
            # 分析合并策略
            merge_plan = self._analyze_merge_plan(comparison, merge_options or {})
            
            # 识别潜在冲突
            potential_conflicts = self._identify_potential_conflicts(comparison)
            
            return {
                'comparison_summary': comparison.get_statistics(),
                'merge_plan': merge_plan,
                'potential_conflicts': [c.to_dict() for c in potential_conflicts],
                'estimated_complexity': self._estimate_merge_complexity(comparison),
                'recommendations': self._generate_merge_recommendations(comparison)
            }
            
        except Exception as e:
            logger.error(f"合并预览失败: {e}")
            return {'error': str(e)}
    
    def _parse_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """解析文件"""
        try:
            return self.parser.parse_file(file_path)
        except Exception as e:
            logger.error(f"文件解析失败 {file_path}: {e}")
            return None
    
    def _perform_merge(self,
                      source_data: Dict[str, Any],
                      target_data: Dict[str, Any],
                      comparison: ComparisonResult,
                      options: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], List[MergeConflict]]:
        """执行合并"""
        try:
            # 从源数据开始
            merged_data = copy.deepcopy(source_data)
            conflicts = []
            
            # 处理每个差异
            for diff in comparison.diff_items:
                conflict = self._apply_diff_to_merge(merged_data, diff, target_data, options)
                if conflict:
                    conflicts.append(conflict)
            
            return merged_data, conflicts
            
        except Exception as e:
            logger.error(f"合并执行失败: {e}")
            return None, []
    
    def _apply_diff_to_merge(self,
                           merged_data: Dict[str, Any],
                           diff: DiffItem,
                           target_data: Dict[str, Any],
                           options: Dict[str, Any]) -> Optional[MergeConflict]:
        """将差异应用到合并数据"""
        try:
            # 根据差异类型选择合并策略
            strategy = self._select_merge_strategy(diff, options)
            
            if strategy == "auto_accept_target":
                # 自动接受目标值
                self._apply_target_value(merged_data, diff)
                return None
                
            elif strategy == "auto_accept_source":
                # 保持源值不变
                return None
                
            elif strategy == "manual_resolution":
                # 需要手动解决
                return self._create_merge_conflict(diff, target_data)
                
            elif strategy == "smart_merge":
                # 智能合并
                return self._smart_merge_diff(merged_data, diff, target_data)
            
            return None
            
        except Exception as e:
            logger.error(f"差异应用失败: {e}")
            return None
    
    def _select_merge_strategy(self, diff: DiffItem, options: Dict[str, Any]) -> str:
        """选择合并策略"""
        # 检查用户指定的策略
        if 'default_strategy' in options:
            return options['default_strategy']
        
        # 基于差异类型选择策略
        if diff.diff_type == DiffType.ADDED:
            return "auto_accept_target"  # 新增项通常接受
        
        elif diff.diff_type == DiffType.REMOVED:
            return "manual_resolution"  # 删除项需要确认
        
        elif diff.diff_type == DiffType.MODIFIED:
            # 根据严重程度决定
            if diff.severity.value in ['critical', 'major']:
                return "manual_resolution"
            else:
                return "auto_accept_target"
        
        return "manual_resolution"
    
    def _apply_target_value(self, merged_data: Dict[str, Any], diff: DiffItem):
        """应用目标值到合并数据"""
        try:
            # 解析路径并设置值
            path_parts = diff.path.split('/')
            current = merged_data
            
            # 导航到目标位置
            for part in path_parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # 设置值
            if diff.new_value is not None:
                current[path_parts[-1]] = diff.new_value
            elif diff.diff_type == DiffType.REMOVED:
                if path_parts[-1] in current:
                    del current[path_parts[-1]]
            
        except Exception as e:
            logger.error(f"应用目标值失败: {e}")
    
    def _create_merge_conflict(self,
                             diff: DiffItem,
                             target_data: Dict[str, Any]) -> MergeConflict:
        """创建合并冲突"""
        conflict_type = "value_conflict"
        
        if diff.diff_type == DiffType.REMOVED:
            conflict_type = "deletion_conflict"
        elif diff.diff_type == DiffType.ADDED:
            conflict_type = "addition_conflict"
        elif diff.diff_type == DiffType.MODIFIED:
            conflict_type = "modification_conflict"
        
        resolution_options = self._get_resolution_options(diff)
        recommended_resolution = self._get_recommended_resolution(diff)
        
        return MergeConflict(
            conflict_id="",  # 将自动生成
            path=diff.path,
            description=diff.description,
            source_value=diff.old_value,
            target_value=diff.new_value,
            conflict_type=conflict_type,
            resolution_options=resolution_options,
            recommended_resolution=recommended_resolution,
            context={
                'diff_id': diff.diff_id,
                'category': diff.category.value,
                'severity': diff.severity.value
            }
        )
    
    def _smart_merge_diff(self,
                         merged_data: Dict[str, Any],
                         diff: DiffItem,
                         target_data: Dict[str, Any]) -> Optional[MergeConflict]:
        """智能合并差异"""
        try:
            # 对于某些类型的差异，可以进行智能合并
            if diff.category == DiffCategory.HEADER:
                # 头部信息通常可以智能合并
                return self._merge_header_diff(merged_data, diff)
            
            elif diff.category == DiffCategory.DATA_OBJECT:
                # 数据对象可以尝试智能合并
                return self._merge_data_object_diff(merged_data, diff, target_data)
            
            else:
                # 其他情况创建冲突
                return self._create_merge_conflict(diff, target_data)
            
        except Exception as e:
            logger.error(f"智能合并失败: {e}")
            return self._create_merge_conflict(diff, target_data)
    
    def _merge_header_diff(self,
                          merged_data: Dict[str, Any],
                          diff: DiffItem) -> Optional[MergeConflict]:
        """合并头部差异"""
        # 对于头部信息，通常选择较新的版本
        if 'version' in diff.path or 'revision' in diff.path:
            # 版本信息通常接受目标值
            self._apply_target_value(merged_data, diff)
            return None
        else:
            # 其他头部信息可能需要手动处理
            return None
    
    def _merge_data_object_diff(self,
                               merged_data: Dict[str, Any],
                               diff: DiffItem,
                               target_data: Dict[str, Any]) -> Optional[MergeConflict]:
        """合并数据对象差异"""
        # 对于数据对象，检查是否为兼容的变更
        if diff.diff_type == DiffType.MODIFIED:
            # 如果只是描述性信息的变更，可以自动接受
            if 'desc' in diff.path:
                self._apply_target_value(merged_data, diff)
                return None
        
        # 其他情况需要手动处理
        return None
    
    def _apply_resolutions(self,
                          source_data: Dict[str, Any],
                          target_data: Dict[str, Any],
                          resolved_conflicts: List[MergeConflict]) -> Optional[Dict[str, Any]]:
        """应用冲突解决方案"""
        try:
            merged_data = copy.deepcopy(source_data)
            
            for conflict in resolved_conflicts:
                resolution = conflict.recommended_resolution
                
                if resolution == "accept_source":
                    # 保持源值
                    continue
                elif resolution == "accept_target":
                    # 应用目标值
                    self._apply_conflict_resolution(merged_data, conflict, target_data)
                elif resolution == "custom":
                    # 应用自定义值
                    self._apply_custom_resolution(merged_data, conflict)
            
            return merged_data
            
        except Exception as e:
            logger.error(f"应用解决方案失败: {e}")
            return None
    
    def _apply_conflict_resolution(self,
                                  merged_data: Dict[str, Any],
                                  conflict: MergeConflict,
                                  target_data: Dict[str, Any]):
        """应用冲突解决方案"""
        try:
            path_parts = conflict.path.split('/')
            current = merged_data
            
            # 导航到目标位置
            for part in path_parts[:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # 应用解决方案
            if conflict.recommended_resolution == "accept_target":
                current[path_parts[-1]] = conflict.target_value
            elif conflict.recommended_resolution == "accept_source":
                current[path_parts[-1]] = conflict.source_value
            
        except Exception as e:
            logger.error(f"应用冲突解决方案失败: {e}")
    
    def _apply_custom_resolution(self,
                               merged_data: Dict[str, Any],
                               conflict: MergeConflict):
        """应用自定义解决方案"""
        # 这里可以实现自定义解决方案的逻辑
        pass
    
    def _write_merged_file(self, merged_data: Dict[str, Any], output_file: str) -> bool:
        """写入合并后的文件"""
        try:
            # 这里应该根据文件格式写入
            # 简化实现，假设是JSON格式
            import json
            
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(merged_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            logger.error(f"写入合并文件失败: {e}")
            return False
    
    def _generate_merge_statistics(self,
                                 comparison: ComparisonResult,
                                 conflicts: List[MergeConflict],
                                 merged_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """生成合并统计"""
        stats = {
            'total_differences': len(comparison.diff_items),
            'auto_merged': len(comparison.diff_items) - len(conflicts),
            'conflicts': len(conflicts),
            'conflict_types': {},
            'merge_success_rate': 0.0
        }
        
        # 统计冲突类型
        for conflict in conflicts:
            conflict_type = conflict.conflict_type
            stats['conflict_types'][conflict_type] = stats['conflict_types'].get(conflict_type, 0) + 1
        
        # 计算合并成功率
        if comparison.diff_items:
            stats['merge_success_rate'] = stats['auto_merged'] / len(comparison.diff_items)
        
        return stats
    
    def _analyze_merge_plan(self,
                          comparison: ComparisonResult,
                          options: Dict[str, Any]) -> Dict[str, Any]:
        """分析合并计划"""
        plan = {
            'auto_mergeable': [],
            'requires_resolution': [],
            'high_risk': [],
            'estimated_time': "未知"
        }
        
        for diff in comparison.diff_items:
            strategy = self._select_merge_strategy(diff, options)
            
            if strategy in ["auto_accept_target", "auto_accept_source"]:
                plan['auto_mergeable'].append(diff.diff_id)
            else:
                plan['requires_resolution'].append(diff.diff_id)
            
            if diff.severity.value in ['critical', 'major']:
                plan['high_risk'].append(diff.diff_id)
        
        # 估算时间
        conflict_count = len(plan['requires_resolution'])
        if conflict_count == 0:
            plan['estimated_time'] = "< 5分钟"
        elif conflict_count < 5:
            plan['estimated_time'] = "5-15分钟"
        elif conflict_count < 20:
            plan['estimated_time'] = "15-60分钟"
        else:
            plan['estimated_time'] = "> 1小时"
        
        return plan
    
    def _identify_potential_conflicts(self, comparison: ComparisonResult) -> List[MergeConflict]:
        """识别潜在冲突"""
        potential_conflicts = []
        
        for diff in comparison.diff_items:
            # 检查是否可能产生冲突
            if self._is_likely_conflict(diff):
                conflict = self._create_merge_conflict(diff, {})
                potential_conflicts.append(conflict)
        
        return potential_conflicts
    
    def _is_likely_conflict(self, diff: DiffItem) -> bool:
        """判断是否可能产生冲突"""
        # 删除操作通常需要确认
        if diff.diff_type == DiffType.REMOVED:
            return True
        
        # 关键属性的修改
        if diff.diff_type == DiffType.MODIFIED:
            if any(keyword in diff.path.lower() for keyword in ['name', 'type', 'id']):
                return True
        
        # 高严重程度的变更
        if diff.severity.value in ['critical', 'major']:
            return True
        
        return False
    
    def _estimate_merge_complexity(self, comparison: ComparisonResult) -> str:
        """估算合并复杂度"""
        total_diffs = len(comparison.diff_items)
        critical_diffs = len(comparison.get_critical_diffs())
        structural_changes = len(comparison.get_structural_changes())
        
        complexity_score = total_diffs + critical_diffs * 2 + structural_changes * 1.5
        
        if complexity_score < 5:
            return "简单"
        elif complexity_score < 15:
            return "中等"
        elif complexity_score < 30:
            return "复杂"
        else:
            return "非常复杂"
    
    def _generate_merge_recommendations(self, comparison: ComparisonResult) -> List[str]:
        """生成合并建议"""
        recommendations = []
        
        if comparison.has_breaking_changes():
            recommendations.append("发现破坏性变更，建议谨慎处理")
        
        critical_count = len(comparison.get_critical_diffs())
        if critical_count > 0:
            recommendations.append(f"有 {critical_count} 个关键差异需要特别注意")
        
        if comparison.get_compatibility_score() < 0.7:
            recommendations.append("兼容性分数较低，建议详细测试")
        
        structural_changes = comparison.get_structural_changes()
        if len(structural_changes) > 5:
            recommendations.append("结构性变更较多，建议分阶段合并")
        
        return recommendations
    
    def _get_resolution_options(self, diff: DiffItem) -> List[str]:
        """获取解决选项"""
        options = ["accept_source", "accept_target"]
        
        if diff.diff_type == DiffType.MODIFIED:
            options.append("custom_value")
        
        if diff.diff_type == DiffType.REMOVED:
            options.extend(["keep_both", "remove_all"])
        
        return options
    
    def _get_recommended_resolution(self, diff: DiffItem) -> str:
        """获取推荐解决方案"""
        # 基于差异类型和严重程度推荐
        if diff.diff_type == DiffType.ADDED:
            return "accept_target"
        
        elif diff.diff_type == DiffType.REMOVED:
            if diff.severity.value == "critical":
                return "accept_source"  # 保守处理
            else:
                return "accept_target"
        
        elif diff.diff_type == DiffType.MODIFIED:
            if diff.severity.value in ["critical", "major"]:
                return "accept_source"  # 保守处理
            else:
                return "accept_target"
        
        return "accept_source"
    
    def _load_merge_strategies(self) -> Dict[str, Any]:
        """加载合并策略"""
        return {
            'default_strategy': 'smart_merge',
            'auto_accept_categories': ['HEADER'],
            'manual_resolve_categories': ['IED', 'COMMUNICATION'],
            'conflict_threshold': 0.7
        }
    
    def _load_conflict_resolvers(self) -> Dict[str, Any]:
        """加载冲突解决器"""
        return {
            'value_conflict': 'manual',
            'deletion_conflict': 'manual',
            'addition_conflict': 'auto_accept',
            'modification_conflict': 'smart'
        }
