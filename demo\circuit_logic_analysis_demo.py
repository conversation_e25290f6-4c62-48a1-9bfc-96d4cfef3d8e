#!/usr/bin/env python3
"""
二次回路逻辑分析演示脚本
展示如何使用增强的知识库功能进行二次系统回路逻辑关系分析
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 避免导入需要jieba的模块
import sys
original_path = sys.path[:]
try:
    # 临时移除可能引起问题的导入
    sys.modules['jieba'] = None
except:
    pass

try:
    from src.knowledge.graph.power_system_knowledge_graph import PowerSystemKnowledgeGraph
    from src.knowledge.analysis.secondary_circuit_analyzer import SecondaryCircuitAnalyzer
    from src.knowledge.integration.smart_rule_engine import SmartRuleEngine
    from src.core.unified_review_engine import UnifiedReviewEngine
except ImportError as e:
    print(f"导入模块时出错: {e}")
    # 尝试单独导入需要的模块
    try:
        from src.knowledge.graph.power_system_knowledge_graph import PowerSystemKnowledgeGraph
        print("成功导入PowerSystemKnowledgeGraph")
    except Exception as e1:
        print(f"导入PowerSystemKnowledgeGraph失败: {e1}")
    
    try:
        from src.knowledge.analysis.secondary_circuit_analyzer import SecondaryCircuitAnalyzer
        print("成功导入SecondaryCircuitAnalyzer")
    except Exception as e2:
        print(f"导入SecondaryCircuitAnalyzer失败: {e2}")
    
    try:
        from src.knowledge.integration.smart_rule_engine import SmartRuleEngine
        print("成功导入SmartRuleEngine")
    except Exception as e3:
        print(f"导入SmartRuleEngine失败: {e3}")
    
    try:
        from src.core.unified_review_engine import UnifiedReviewEngine
        print("成功导入UnifiedReviewEngine")
    except Exception as e4:
        print(f"导入UnifiedReviewEngine失败: {e4}")

# 恢复原始路径
sys.path[:] = original_path


def create_demo_scd_data():
    """创建演示用的SCD数据"""
    return {
        'header': {
            'id': 'SCL_Project',
            'version': '2007',
            'revision': 'A'
        },
        'ieds': [
            {
                'name': 'PROT_IED_001',
                'type': 'Protection',
                'manufacturer': 'TestManufacturer',
                'AccessPoint': {
                    'name': 'AP1',
                    'Server': {
                        'LDevice': {
                            'inst': 'LD1',
                            'LN0': {
                                'lnClass': 'LLN0',
                                'inst': ''
                            },
                            'LN': [
                                {
                                    'lnClass': 'PTOC',
                                    'inst': '1',
                                    'DOI': [
                                        {
                                            'name': 'Str',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        },
                                        {
                                            'name': 'Op',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        }
                                    ]
                                },
                                {
                                    'lnClass': 'PTRC',
                                    'inst': '1',
                                    'DOI': [
                                        {
                                            'name': 'Tr',
                                            'DAI': [{'name': 'general', 'Val': 'false'}]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            },
            {
                'name': 'CTRL_IED_001',
                'type': 'Control',
                'manufacturer': 'TestManufacturer',
                'AccessPoint': {
                    'name': 'AP1',
                    'Server': {
                        'LDevice': {
                            'inst': 'LD1',
                            'LN0': {
                                'lnClass': 'LLN0',
                                'inst': ''
                            },
                            'LN': [
                                {
                                    'lnClass': 'CSWI',
                                    'inst': '1',
                                    'DOI': [
                                        {
                                            'name': 'Pos',
                                            'DAI': [{'name': 'ctlVal', 'Val': 'false'}]
                                        }
                                    ]
                                },
                                {
                                    'lnClass': 'XCBR',
                                    'inst': '1',
                                    'DOI': [
                                        {
                                            'name': 'Pos',
                                            'DAI': [{'name': 'stVal', 'Val': 'false'}]
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                }
            }
        ],
        'communication': {
            'SubNetwork': {
                'name': 'SubNet1',
                'type': '8-MMS',
                'ConnectedAP': [
                    {
                        'iedName': 'PROT_IED_001',
                        'apName': 'AP1',
                        'GSE': {
                            'ldInst': 'LD1',
                            'cbName': 'GCB1',
                            'Address': {
                                'P': [
                                    {'type': 'MAC-Address', 'value': '01-0C-CD-01-00-01'},
                                    {'type': 'APPID', 'value': '0001'}
                                ]
                            }
                        }
                    },
                    {
                        'iedName': 'CTRL_IED_001',
                        'apName': 'AP1',
                        'GSE': {
                            'ldInst': 'LD1',
                            'cbName': 'GCB1',
                            'Address': {
                                'P': [
                                    {'type': 'MAC-Address', 'value': '01-0C-CD-01-00-02'},
                                    {'type': 'APPID', 'value': '0002'}
                                ]
                            }
                        }
                    }
                ]
            }
        }
    }


def demonstrate_knowledge_graph_analysis():
    """演示知识图谱分析功能"""
    print("=" * 60)
    print("🔌 电力系统知识图谱回路逻辑分析演示")
    print("=" * 60)
    
    try:
        # 创建知识图谱
        kg = PowerSystemKnowledgeGraph()
        
        # 创建演示数据
        demo_scd_data = create_demo_scd_data()
        
        # 构建知识图谱
        print("📊 构建知识图谱...")
        kg.build_from_scd(demo_scd_data)
        
        print(f"✅ 知识图谱构建完成:")
        print(f"   • 实体数量: {len(kg.entities)}")
        print(f"   • 关系数量: {len(kg.relations)}")
        print(f"   • 回路数量: {len(kg.circuits)}")
        
        # 分析回路逻辑
        print("\n🔍 分析回路逻辑...")
        analysis = kg.analyze_circuit_logic()
        print(f"✅ 回路逻辑分析完成:")
        print(f"   • 总回路数: {analysis['total_circuits']}")
        print(f"   • 完整回路: {analysis['complete_circuits']}")
        print(f"   • 不完整回路: {analysis['incomplete_circuits']}")
        
        if analysis['issues']:
            print(f"   • 发现问题:")
            for issue in analysis['issues'][:5]:  # 只显示前5个问题
                print(f"     - {issue}")
        
        # 分析回路关系
        print("\n🔗 分析回路间关系...")
        relationship_analysis = kg.analyze_circuit_relationships()
        print(f"✅ 回路关系分析完成:")
        print(f"   • 共享实体数量: {len(relationship_analysis['shared_entities'])}")
        print(f"   • 回路关系数量: {len(relationship_analysis['inter_circuit_relationships'])}")
        
        return kg
    except Exception as e:
        print(f"❌ 知识图谱分析演示失败: {e}")
        return None


def demonstrate_circuit_analyzer():
    """演示二次回路分析器功能"""
    print("\n" + "=" * 60)
    print("🔬 二次回路逻辑分析器演示")
    print("=" * 60)
    
    try:
        # 创建分析器
        analyzer = SecondaryCircuitAnalyzer()
        
        # 创建演示数据
        demo_scd_data = create_demo_scd_data()
        
        # 构建知识图谱
        print("📊 构建知识图谱...")
        analyzer.knowledge_graph.build_from_scd(demo_scd_data)
        
        # 获取回路
        circuits = analyzer.knowledge_graph.circuits
        print(f"✅ 获取到 {len(circuits)} 个回路")
        
        # 执行综合分析
        print("\n🔍 执行综合回路分析...")
        analysis_report = analyzer.generate_comprehensive_analysis_report(circuits)
        
        print(f"✅ 综合分析完成:")
        summary = analysis_report.get('summary', {})
        print(f"   • 总回路数: {summary.get('total_circuits', 0)}")
        print(f"   • 一致回路: {summary.get('consistent_circuits', 0)}")
        print(f"   • 不一致回路: {summary.get('inconsistent_circuits', 0)}")
        print(f"   • 关系分析: {summary.get('relationship_analyses', 0)}")
        print(f"   • 总问题数: {summary.get('total_issues', 0)}")
        
        # 显示建议
        recommendations = analysis_report.get('recommendations', [])
        if recommendations:
            print(f"\n💡 分析建议:")
            for i, rec in enumerate(recommendations[:5], 1):  # 只显示前5个建议
                print(f"   {i}. {rec}")
        
        # 风险评估
        risk_assessment = analysis_report.get('risk_assessment', {})
        print(f"\n⚠️  风险评估:")
        print(f"   • 整体风险等级: {risk_assessment.get('overall_risk_level', 'unknown')}")
        risk_factors = risk_assessment.get('risk_factors', [])
        if risk_factors:
            print(f"   • 风险因素:")
            for factor in risk_factors:
                print(f"     - {factor}")
        
        return analyzer
    except Exception as e:
        print(f"❌ 二次回路分析器演示失败: {e}")
        return None


def demonstrate_smart_rule_engine():
    """演示智能规则引擎的回路逻辑分析功能"""
    print("\n" + "=" * 60)
    print("⚙️  智能规则引擎回路逻辑分析演示")
    print("=" * 60)
    
    try:
        # 创建智能规则引擎
        engine = SmartRuleEngine()
        
        # 创建演示数据（模拟SCD数据对象）
        class MockSCDData:
            def __init__(self, data):
                self.data = data
                
            def to_dict(self):
                return self.data
        
        demo_scd_data = MockSCDData(create_demo_scd_data())
        
        # 执行验证
        print("🔍 执行智能验证...")
        results = engine.validate(demo_scd_data, {})
        
        print(f"✅ 验证完成，共发现 {len(results)} 个问题:")
        for i, result in enumerate(results[:10], 1):  # 只显示前10个结果
            print(f"   {i}. [{result.severity.upper()}] {result.message}")
        
        # 获取智能推荐
        print("\n💡 获取智能推荐...")
        recommendations = engine.get_smart_recommendations(demo_scd_data, {})
        
        if recommendations:
            print(f"✅ 生成 {len(recommendations)} 个智能推荐:")
            for i, rec in enumerate(recommendations[:5], 1):  # 只显示前5个推荐
                print(f"   {i}. {rec.get('title', '未知推荐')}: {rec.get('description', '')}")
        
        return engine
    except Exception as e:
        print(f"❌ 智能规则引擎演示失败: {e}")
        return None


def demonstrate_unified_review():
    """演示统一审查引擎的回路逻辑分析功能"""
    print("\n" + "=" * 60)
    print("📋 统一审查引擎回路逻辑分析演示")
    print("=" * 60)
    
    try:
        # 创建统一审查引擎
        engine = UnifiedReviewEngine()
        
        # 由于我们没有实际的SCD文件，这里只演示功能结构
        print("✅ 统一审查引擎已初始化")
        print("💡 统一审查引擎集成了以下功能:")
        print("   • 传统规则验证")
        print("   • 知识库验证")
        print("   • 回路逻辑分析")
        print("   • 回路关系分析")
        print("   • 智能推荐")
        
        # 显示可用的检查分类
        categories = engine.get_available_categories()
        print(f"\n🔍 可用检查分类:")
        print(f"   配置检查: {categories['config_categories']}")
        print(f"   图纸检查: {categories['drawing_categories']}")
        
        return engine
    except Exception as e:
        print(f"❌ 统一审查引擎演示失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 二次系统回路逻辑分析功能演示")
    print("这是智能变电站的核心功能演示")
    
    try:
        # 演示知识图谱分析
        kg = demonstrate_knowledge_graph_analysis()
        
        # 演示二次回路分析器
        analyzer = demonstrate_circuit_analyzer()
        
        # 演示智能规则引擎
        engine = demonstrate_smart_rule_engine()
        
        # 演示统一审查引擎
        review_engine = demonstrate_unified_review()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("=" * 60)
        print("✨ 增强的回路逻辑分析功能已成功集成到系统中")
        print("✨ 系统现在可以:")
        print("   • 深度分析二次回路逻辑关系")
        print("   • 识别回路配置不一致性")
        print("   • 分析回路间的关系")
        print("   • 提供专业的改进建议")
        print("   • 进行风险评估")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()